
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import App from './App.vue'
import router from './router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import utils from '@/utils/utils.js'

import VCalendar from 'v-calendar';
import 'v-calendar/style.css';
import '@/assets/v-calendar.cover.css';


import VueVirtualScroller from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

import './components/baseComponents/theme.css'
import './components/baseComponents/RBPZIndex.css'
import zhCn from 'element-plus/es/locale/lang/zh-cn'; // 引入中文语言包
import { LocalRequest } from './local_request/local_request'
const app = createApp(App)
app.config.globalProperties.$utils = utils
// 保存原始的 console.error 方法
const errorLog = console.error;

// 覆盖 console.error 方法
console.error = function (...args) {
    const message = args
        .map(arg => {
            if (arg instanceof Error) {
                return arg.stack; // 直接输出错误堆栈
            }
            try {
                if (typeof arg === 'string') {
                    return arg
                } else  {
                    try{
                        return JSON.stringify(arg)
                    }catch(e){
                        return "转换对象为字符串失败-与error无关"
                    }
                }
            } catch (e) {
                return '[Circular Structure]'; // 处理 JSON.stringify 失败的情况
            }
        })
        .join('\n');
    // 屏蔽特定警告
    if (message.includes('THREE.LineSegmentsGeometry.computeBoundingSphere(): Computed radius is NaN.') ||
        message.includes('THREE.BufferGeometry.computeBoundingBox(): Computed min/max have NaN values') ||
        message.includes('THREE.BufferGeometry.computeBoundingSphere(): Computed radius is NaN')) {
        return; // 忽略该警告
    }
    errorLog.apply(console, args);
    if (window.electron) {
        LocalRequest.writeLog("web error:" + message)
    }
};

// 保存原始的 console.log 方法
const log = console.log
// 覆盖 console.log 方法
console.log = function (...args) {
    log.apply(console, args)
    // LocalRequest.
    if (window.electron) {
        const message = args.map(arg => {
            if (typeof arg === 'string') {
                return arg
            } else  {
                try{
                    return JSON.stringify(arg)
                }catch(e){
                    return "转换对象为字符串失败-与error无关"
                }
            }
        }

        ).join(' ');

        LocalRequest.writeLog("web log:" + message)
    }

}
app.use(createPinia())
app.use(router)
window.__router__ = router
app.use(ElementPlus, { locale: zhCn })
app.use(VCalendar, {})
app.use(VueVirtualScroller)
//引入饿了么icon组件
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}
app.mount('#app')
