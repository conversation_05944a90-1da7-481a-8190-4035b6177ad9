<template>
    <div ref="bodyRef" class="marquee-body" id="marqueeBody">
        <div ref="textRef" :class="`marquee-text ${isMove?'marquee-text-scroll':''}`" id="marqueeText"
            :style="{ color: color, fontSize: fontSize,textAlign: textAlign }"> {{ text }}</div>
    </div>

</template>

<script setup>
import { nextTick, onMounted, watch, ref } from 'vue';
const props = defineProps({
    text: String,
    color: {
        default: 'var(--text-color)'
    },
    fontSize: {
        default: '16px'
    },
    textAlign: {
        default: 'left'
    }
})

const isMove = ref(false)
const textRef = ref(null)
const bodyRef = ref(null)

const speed = 40 // 每秒移动 80 像素
const moveSpeed = ref('10s')

function compareOverflow() {
  nextTick(() => {
    if (textRef.value && bodyRef.value) {
      const textWidth = textRef.value.scrollWidth
      const containerWidth = bodyRef.value.clientWidth
      let moveDistance = `-${textWidth-containerWidth}px`
      moveSpeed.value = ((textWidth-containerWidth)/speed)*10 + 's'
      isMove.value = textWidth > containerWidth
      setTimeout(() => {
        textRef.value.style.setProperty('--move-distance',moveDistance)    
      }, 200)
    }
  })
}
watch(props, (newVal, oldVal) => {
    nextTick(() => {
        compareOverflow()
    })
})

onMounted(() => {
    nextTick(() => {
        compareOverflow()
    })
})


</script>

<style lang="scss" scoped>


.marquee-body {
    width: 100%;
    overflow: hidden;
    height: auto;

    .marquee-text {
        white-space: nowrap;
        text-overflow: ellipsis;    
    }

    .marquee-text-scroll{
        animation: marquee v-bind('moveSpeed') linear infinite alternate
    }
    

}
@keyframes marquee {
    0% {
        transform: translateX(0);
    }

    100% {
        transform: translateX(var(--move-distance));
    }
}


</style>