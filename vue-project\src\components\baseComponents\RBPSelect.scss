

.rbq-select-body {
    background: var(--main-bc-color);
    border-radius: 15px;
    border: 2px solid var(--border-bar-color);
    overflow: hidden;
    padding: 10px 0px;

    .el-select-dropdown {
        padding: 0px !important;
    }

    .el-select-dropdown__item.is-selected {
        height: 48px;
        background: var(--primary-color);
        font-weight: 400;
        font-size: 21px;
        color: var(--anti-text-color);
    }

    .el-select-dropdown__item {
        height: 48px;
        font-weight: 400;
        background-color: var(--main-bc-color);
        font-size: 21px;
        color: var(--text-color);
        padding: 0px 20px;
        box-sizing: border-box;
        display: flex;
        align-items: center;

    }

    .el-popper__arrow {
        display: none;

    }
}

.rbp-select{
    .el-select__wrapper {
        background-color: var(--main-bc-color);
        border-radius: 15px;
        box-shadow: 0 0 0 2px var(--border-bar-color) inset !important;
        font-size: 21px !important;
        color: var(--text-color) !important;
        height: 54px;
    }

    .el-select__wrapper.is-focused {
        color: var(--text-color) !important;
        box-shadow: 0 0 0 2px var(--border-bar-color) inset !important;
    }

    .el-select__wrapper.hover {
        box-shadow: 0 0 0 2px var(--border-bar-color) inset !important;
    }


    .el-select__placeholder {
        color: var(--text-color);
    }

    .el-select__caret {
        color: var(--text-color);
        font-size: 21px;
    }
}