<template>
	<div class="foot" id="foot">
		<div class="left"></div>
		<div class="middle">
			<div class="item" @click="setColor('#55B57F')">
				<div   class="dot" style="background-color: #55B57F;">
					<el-icon class="el-icon-check" v-if="state.color == '#55B57F'">
						<Check/>
					</el-icon>
				</div>
			</div>
			<div class="item" @click="setColor('#409EFF')">
				<div class="dot" style="background-color: #409EFF;">
					<el-icon class="el-icon-check" v-if="state.color == '#409EFF'">
						<Check/>
					</el-icon>
				</div>
			</div>
			<div class="item" @click="setColor('#F74E59')">
				<div class="dot" style="background-color: #F74E59;">
					<el-icon class="el-icon-check" v-if="state.color == '#F74E59'">
						<Check/>
					</el-icon>
				</div>
			</div>
			<div class="item" @click="changeLineWidth">
				<div class="icon">
					<div class="line-width" :style="{width: `${state.lineWidth/2*10}px`, height: `${state.lineWidth/2*10}px`, background: state.color}"></div>
				</div>
				<div>画笔大小</div>
			</div>
			<div class="item" :class="{'active': state.showCanvas}" @click="setCanvas">
				<img class="icon" v-if="state.showCanvas" src="../assets/pizhu-active.png" >
				<img class="icon" v-else src="../assets/pizhu.png" >
				<div>板书批注</div>
			</div>
		</div>
		<div class="right">
			<div class="item" @click="close()">
				<img class="icon" src="../assets/quit.png" >
				<div>退出</div>
			</div>
			<div v-if="state.showCanvas" class="clear" :style="{bottom: 25 + 'px'}" @click="clear()">清除画布</div>

		</div>
		<Canvas v-if="state.showCanvas" ref="footcanvas" :canvasW="state.canvasW" :canvasH="state.canvasH" :lineWidth="state.lineWidth" :color="state.color"></Canvas>
	</div>
</template>

<script setup>
import $ from 'jquery'
import { reactive, ref, defineEmits, defineExpose, nextTick } from "vue" 
import Canvas from './Canvas.vue'
import { Check } from '@element-plus/icons-vue'

const footcanvas = ref(null)
const emits = defineEmits(['closeQues'])
const state = reactive({
	status: 1,
	color: '#F74E59', // #F74E59 #55B57F #409EFF
	showCanvas: false,
	canvasW: '0px',
	canvasH: '0px',
	lineWidth: 4,
	id: null
})

const close = () => {
	emits('closeQues')
}
const changeLineWidth = () => {
	if (state.lineWidth == 2) {
		state.lineWidth = 4
	} else if (state.lineWidth == 4) {
		state.lineWidth = 6
	} else {
		state.lineWidth = 2
	}
	footcanvas.value && footcanvas.value.setLineWidth(state.lineWidth)
}
const setColor = (color) => {
	
	state.color = color
	footcanvas.value && footcanvas.value.setColor(color)
}
const newCanvas = (id) => {
	state.showCanvas = false
	nextTick(() => {
		
		
		if (id) {
			state.id = id
			state.canvasW = $(id).width() + 'px'
			state.canvasH = $(id).height() + 'px'
		}else {
			state.id = null
			state.canvasW = $('#canvasWrap').width() + 'px'
			state.canvasH = $('#canvasWrap').height() + 'px'
		}
		state.showCanvas = true
		clear()
	})
}
const setCanvas = () => {
	if (state.id) {
		state.canvasW = $(state.id).width() + 'px'
		state.canvasH = $(state.id).height() + 'px'
	}else {
		state.canvasW = $('#canvasWrap').width() + 'px'
		state.canvasH = $('#canvasWrap').height() + 'px'
	}
	state.showCanvas = !state.showCanvas
}
const setCanvasSize = () => {
	state.id = null
	footcanvas.value && footcanvas.value.setKnovaSize()
	state.canvasW = $('#canvasWrap').width() + 'px'
	state.canvasH = $('#canvasWrap').height() + 'px'
}
const clear = () => {
	footcanvas.value && footcanvas.value.clearCanvas()
}
defineExpose({ newCanvas })
</script>

<style lang="scss" scoped>
	.foot {
		height: 100%;
		display: flex;
		justify-content: space-between;
		
		.left {
			margin-left: 40px;
			display: flex;
			width: 200px;
		}
		.middle {
			display: flex;
			.active {
				background-color: #1A1E28;
				color: #8BADCE;
			}
		}
		.right {
			display: flex;
			justify-content: flex-end;
			width: 200px;
			position: relative;
			.clear {
			height: 30px;
			line-height: 30px;
			padding: 0 5px;
			text-align: center;
			font-size: 14px;
			color: #2e4a66;
			border-radius: 3px;
			background: #B2D9F9;
			position: absolute;
			right: 120px;
			z-index: 100;
		}
		}
		.item {
			display: flex;
			flex-direction: column;
			width: 76px;
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			margin-right: 12px;
			color: #fff;
			font-weight: bold;
			font-size: 14px;
			.dot {
				width: 22px;
				height: 22px;
				border-radius: 11px;
				display: flex;
				align-items: center;
				justify-content: center;
				.el-icon-check {
					font-weight: bold;
					font-size: 14px;
				}
			}
			.icon {
				width: 25px;
				height: 25px;
				margin-bottom: 8px;
				display: flex;
				justify-content: center;
				align-items: center;
				.line-width {
					max-width: 25px;
					max-height: 25px;
					background-color: #F74E59;
					border-radius: 50%;
				}
			}
		}
		
	}
</style>