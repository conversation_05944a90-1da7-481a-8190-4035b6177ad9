/**
加减分按钮（点评项）
*/
<template>
    <div class="rbp-add-score-eva" @click="handleClick(item)">
        <div class="icon">
            <img :src="item.evaluationItemImage">
        </div>
        <div class="name">
            {{ item.evaluationItemName }}
        </div>
    </div>
</template>
<script setup>
import { defineProps, defineEmits } from 'vue'
import "@/components/baseComponents/RBPColors.scss";

const props = defineProps({
    item: Object
})

const emits = defineEmits(['evaluationItemClick'])
function handleClick(item) {
    emits('evaluationItemClick', item)
}

</script>
<style lang="scss" scoped>
@import "@/components/baseComponents/RBPColors.scss";

.rbp-add-score-eva {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;

    .icon {
        width: 54px;
        height: 54px;

        img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
        }
    }

    .name {
        color: var(--secondary-text-color);
        font-size: 16px;
        margin-top: 6px;
    }
}
</style>