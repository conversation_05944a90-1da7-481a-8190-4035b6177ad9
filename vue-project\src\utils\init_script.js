import { ServerUrls } from "@/server_request/server_urls";

export function initScriptJs() { 
    initZy()
    initMathJax()
}

function initZy() {
    if (window.getZyGroups) {
        return
    }
    const script = document.createElement('script');
    script.src = `${ServerUrls.getMgboardHost()}/static/quesEditor/js/zyHandle.js?ts=${Date.now()}`;
    script.async = true;
    document.head.appendChild(script);
}
function initMathJax() {
    if (window.mathJax) {
        return
    }
    window.MathJax = {
        tex: {
            inlineMath: [['$', '$'], ['\\(', '\\)']],
            displayMath: [['$$', '$$'], ['\\[', '\\]']],

        },
        svg: {
            fontCache: 'global'
        },
        loader: { load: ['input/tex', 'output/chtml'] }, // 使用 CHTML 渲染器
        chtml: {
            scale: 1.0,                  // 基础缩放
            minScale: 0.5,               // 最小缩放比例
            matchFontHeight: false,       // 避免行高问题
            adaptiveCSS: true
        },
        options: {
            enableMenu: false            // 禁用右键菜单提升性能
        }
    };
    const script = document.createElement('script');
    script.src = `http://${window.location.host}/static/tex-mml-chtml.js`;
    script.async = true;
    document.head.appendChild(script);
}