<template>
    <div style="padding: 16px; background-color: transparent;">
        <div class="login-view" @click="showList = false">
            <div class="qrcode-area">
                <div class="code-title">课堂遥控器扫描登录</div>
                <QrcodeVue class="left-qrcode" />
                <div class="soft-version" @click="centerDialogVisible = true">
                    <img src="/icon/login_info.svg" alt="" />
                </div>
            </div>
            <div class="login-body">
                <img src="/img/login/login_title.svg" alt="">
                <div class="login-body-account">
                    <RBPInput @click-drop="accountDropClick" class="account-input" font-size="30" v-model="account"
                        placeholder="请输入用户名" @input="accountChange" spellcheck="false" height="72" show-drop-icon="true"
                        item-height="54" has-drop="true" @click-item="setAccount" @delete-item="delAccount"
                        :data-list="filterAccountObj" border-radius="18" />

                </div>
                <div class="login-body-password">
                    <RBPInput class="password-input" border-radius="18" height="72" v-model="password" :font-size="30"
                        type="password" placeholder="请输入密码" />
                </div>
                <div class="login-body-remember">
                    <div class="checkbox-remember" @click="remberPwd">
                        <div class="checkbox">
                            <img v-if="remember" src="/icon/icon_checked.svg" alt="">
                            <img v-else src="/icon/icon_uncheck.svg" alt="">
                        </div>
                        <div>记住密码</div>
                    </div>
                    <div class="ver-code" v-show="uniqueId != ''">
                        <div class="ver-input">
                            <RBPInput class="code-input" font-size="21" v-model="authCode" placeholder="验证码" />
                        </div>
                        <div class="ver-img" :style="{ backgroundImage: `url(${imageData})` }" @click="codeImgClick">
                        </div>
                    </div>
                </div>
                <div class="login-body-loginbtn" @click="login">登录</div>
            </div>
            <img v-if="showExit" @click="exitClick" class="exit-close" src="/icon/icon_close.svg" alt="" />

            <div>
                <SoftwareVersion :centerDialogVisible="centerDialogVisible"
                    @updateCenterDialogVisible="updateCenterDialogVisible" @networkTestClick="networkTestClick" />
            </div>
            <div>
                <NetworkTest v-if="showNetworkTest" @close="showNetworkTest = false"></NetworkTest>
            </div>
            <Updater></Updater>
            <div v-if="schoolSelectNeeded" class="chooseSchoolBg" @click.stop="schoolSelectNeeded = false">
                <div class="chooseSchool" @click.stop>
                    <div class="chooseSchoolTitle">
                        请选择学校
                    </div>
                    <div class="chooseSchoolList">
                        <div class="chooseSchoolItem" v-for="(item, index) in schools" :key="index"
                            @click="schoolSelect(item)">
                            {{ item.schoolName }}
                        </div>
                    </div>
                </div>
            </div>
            <NetworkTips v-if="showNetworkTips" @networkTipsClose="networkTipsClose"></NetworkTips>
        </div>
    </div>
</template>
<script setup>
import { onMounted, getCurrentInstance, ref, watch, onUnmounted, onBeforeMount } from 'vue'
import SoftwareVersion from '../components/SoftwareVersion.vue'
import NetworkTest from '@/components/NetworkTest.vue'
import { loginInstance } from '@/login_instance/login_instance'
import { LoginRequest } from '@/server_request/login_request'
import { LaoshanLoginRequest } from '@/laoshan_server_request/laoshan_login_request'
import { ElLoading, ElMessage } from 'element-plus'
import { Alert } from '@/utils/alert'
import { HttpEnv, ServerHost, serverHost, ServerUrls } from '@/server_request/server_urls'
import QrcodeVue from '../components/Qrcode.vue'
import { useRouter } from 'vue-router'
import { AppVersion, UserChannel, VersionCode } from '@/app_verson'
import { remoteControl } from '@/remote_control/remote_control'
import Updater from '@/components/SystemSet/Updater.vue'
import { AppUpdater } from '@/server_request/app_updater'
import { useUpdaterStore } from '@/stores/updater_store'
import { storeToRefs } from 'pinia'
import NetworkTips from '@/components/NetworkTips/NetworkTips.vue'
import { LocalRequest } from "@/local_request/local_request"
import { tdSetup } from '@/utils/talkingdata_tool'
import { useLoginStore } from '@/stores/login_store'
import { useClassroomStore } from '@/stores/classroom'
import RBPInput from '@/components/baseComponents/RBPInput.vue'
import { initScriptJs } from '@/utils/init_script'

const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)
const loginStore = useLoginStore()
const { accountFirst, activeFirst } = storeToRefs(loginStore)
const showNetworkTips = ref(false)
const updaterStore = useUpdaterStore()
const { firstUpdate } = storeToRefs(updaterStore)
const schoolSelectNeeded = ref(false)
const schools = ref([])
const schoolId = ref(-1)
const router = useRouter()
const { proxy } = getCurrentInstance()
const account = ref('')
const password = ref('')
const authCode = ref('')
const remember = ref(false)
const showList = ref(false)
const centerDialogVisible = ref(false)
const showNetworkTest = ref(false)

const imageData = ref('')
const uniqueId = ref('')

const showExit = ref(window.electron !== undefined)

const filterAccountObj = ref(null)
watch(() => account.value, () => {
    updateAccountList()
}, { immediate: false })

function networkTipsClose() {
    showNetworkTips.value = false
}

function updateAccountList() {
    if (loginInstance.users == undefined) {
        return
    }
    const res = Object.entries(loginInstance.users).filter(([key, val]) => key.includes(account.value))
    const objRes = Object.fromEntries(res)
    filterAccountObj.value = objRes

    //发现账号 填充密码
    if (remember.value) {
        password.value = ''
        for (let key in filterAccountObj.value) {
            if (key == account.value) {
                password.value = filterAccountObj.value[key]
            }
        }
    }

    if (!accountFirst.value) {
        if (Object.keys(filterAccountObj.value).length > 0) {
            showList.value = true
        } else {
            showList.value = false
        }
    } else {
        accountFirst.value = false
    }
}

function accountDropClick() {
    showList.value = !showList.value
    if (showList.value) {
        const res = Object.entries(loginInstance.users)
        const objRes = Object.fromEntries(res)
        filterAccountObj.value = objRes
    }
}
function exitClick() {
    if (window.electron) {
        window.electron.closeWindow()
    }
}
function updateCenterDialogVisible(value) {
    centerDialogVisible.value = value
}
function networkTestClick() {
    centerDialogVisible.value = false
    showNetworkTest.value = true
}
function updateApp() {
    //需要调用接口
    AppUpdater.checkUpdate(false)
}

onBeforeMount(async () => {
    if (window.electron) {
        window.electron.unignoreMosue()
    }
})


onMounted(async () => {
    selectedClassroom.value = null
    initScriptJs()
    if (window.electron) {
        window.electron.setTransparent()
    }



    await readData()

    if (activeFirst.value) {
        activeFirst.value = false
        await serverHost.updateEnv()
        tdSetup()
        remoteControl.setup()
        doActive()
        console.log("current app version", AppVersion, VersionCode, serverHost.userChannel)
    }

    if (loginInstance.activeCode == '' && serverHost.userChannel == UserChannel.laoshan && serverHost.showActive) {
        router.push('/active')
    }

    if (firstUpdate.value) {
        firstUpdate.value = false
        setTimeout(() => {
            updateApp()
        }, 200)
    }

    if (window.electron) {
        let res = await LocalRequest.getLocalIpList()
        if (res.ips.length >= 2) {
            showNetworkTips.value = true
        }
    }
})

async function doActive() {
    // 崂山版本需要验证激活码
    if (serverHost.userChannel === UserChannel.laoshan) {
        await LoginRequest.doActive(loginInstance.activeCode, loginInstance.deviceId, false)
    }
}

async function readData() {
    await loginInstance.setup()
    account.value = loginInstance.lastAccount
    remember.value = loginInstance.remember
    if (remember.value) {
        password.value = loginInstance.lastPassword
    } else {
        password.value = ''
    }
}

onUnmounted(() => {
    if (window.electron) {
        window.electron.cancelTransparent()
    }
})

function accountChange() {
    // showList.value = true
}
async function codeImgClick() {
    const res = await LoginRequest.getAuthCode()
    if (res.code == 1) {
        imageData.value = res.data.imageData
        uniqueId.value = res.data.uniqueId
    }
}

function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function laoshanLogin() {
    let loadingInstance = ElLoading.service({ background: 'transparent' })
    const res = await LaoshanLoginRequest.login(account.value, password.value, authCode.value, uniqueId.value, schoolId.value)

    if (navigator.userAgentData?.platform === 'macOS') {
        //macOS 透明窗口生效需要时间
        await delay(3000)
    }

    if (res.code == 1) {
        if ('showAuthCode' in res.data) {
            if (res.data.showAuthCode) {
                codeImgClick()
            }
            Alert.showErrorMessage(res.data.message)
        } else {
            loginInstance.saveToken(res.data.token)
            loginInstance.saveRemember(remember.value)
            await loginInstance.saveAccount(account.value, password.value)
            pushManage()
        }
    } else if (res.code == 3) {
        schools.value = res.data.schools
        if (schools.value.length == 1) {
            schoolId.value = schools.value[0].schoolId
            await laoshanLogin()
        } else if (schools.value.length > 1) {
            schoolSelectNeeded.value = true
        }
    } else {
        Alert.showErrorMessage(res.message)
    }
    loadingInstance.close()
}
async function publicLogin() {
    let loadingInstance = ElLoading.service({ background: 'transparent' })
    const res = await LoginRequest.login(account.value, password.value, authCode.value, uniqueId.value)
    if (navigator.userAgentData?.platform === 'macOS') {
        //macOS 透明窗口生效需要时间
        await delay(3000)
    }

    if (res.code == 1) {
        if ('showAuthCode' in res.data) {
            if (res.data.showAuthCode) {
                codeImgClick()
            }
            Alert.showErrorMessage(res.data.message)
        } else {
            loginInstance.saveToken(res.data.token)
            loginInstance.saveRemember(remember.value)
            await loginInstance.saveAccount(account.value, password.value)
            pushManage()
        }
    } else {
        Alert.showErrorMessage(res.message)
    }
    loadingInstance.close()
}

function pushManage() {
    proxy.$router.push('/manage')
    LoginRequest.loginLog(true)
}
async function login() {

    if (account.value === "") {
        Alert.showErrorMessage('请输入账号')
        return
    }
    if (password.value === "") {
        Alert.showErrorMessage('请输入密码')
        return
    }
    if (serverHost.userChannel == UserChannel.public) {
        await publicLogin()
    } else if (serverHost.userChannel == UserChannel.laoshan) {
        await laoshanLogin()
    } else {
        ElMessage.error("渠道配置错误，请修改")
    }
}

function setAccount(account_input, password_input) {
    remember.value = true
    account.value = account_input
    password.value = password_input
    showList.value = false
}
async function delAccount(acc) {
    await loginInstance.removeAccount(acc)
    await readData()
    updateAccountList()
    // const res = Object.entries(loginInstance.users)
    // const objRes = Object.fromEntries(res)
    // filterAccountObj.value = objRes
    // if (acc == account.value) {
    //     account.value = ''
    //     password.value = ''
    //     remember.value = false
    // }
}

function remberPwd() {
    remember.value = !(remember.value ?? false)
    loginInstance.saveRemember(remember.value)
}
async function schoolSelect(item) {
    schoolSelectNeeded.value = false
    schoolId.value = item.schoolId
    await laoshanLogin()
}
</script>

<style lang="scss" scoped>
@import './../assets/scss/mixin.scss';

.roundedImage {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 25px;
    -webkit-border-radius: 25px;
    overflow: hidden;
    -webkit-app-region: drag;
}

.login-view {
    width: calc(100% - 36px);
    height: calc(100vh - 68px);
    padding: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    -webkit-border-radius: 36px;
    background: var(--main-bc-color);
    border-radius: 36px;
    /* 圆角设置 */
    box-shadow: 0px 0px 12px rgba($color: var(--boxshaw-main-color-rgb), $alpha: 0.15);
    -webkit-app-region: drag;

    .exit-close {
        position: fixed;
        bottom: 31px;
        right: 31px;
        cursor: pointer;
        -webkit-app-region: no-drag;
    }

    .qrcode-area {
        -webkit-app-region: drag;
        background: url('/img/login/code_background.png') no-repeat center;
        background-size: contain;
        flex: 441;
        height: 100%;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .code-title {
            font-weight: 400;
            font-size: 24px;
            color: var(--login-scan-title);
            line-height: 36px;
            margin-bottom: 65px;
        }

        .left-qrcode {
            width: 219px;
            height: 219px;
            margin-bottom: 18px;
        }

        .soft-version {
            position: absolute;
            bottom: 0px;
            right: 6px;
            cursor: pointer;
            -webkit-app-region: no-drag;
        }
    }

    .login-body {
        height: 100%;
        width: 300px;
        flex: 459;
        padding-left: 78px;
        padding-right: 66px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        -webkit-app-region: no-drag;

        .login-body-title {
            margin-top: 14px;
        }

        .login-body-account {
            width: 100%;
            height: 72px;
            margin-top: 69px;
            @include flex();

            .account-input {
                flex: 1;
                height: 100%;
            }
        }

        .login-body-password {
            margin-top: 36px;
            width: 100%;
            height: 72px;
            @include flex();

            .password-input {
                flex: 1;
                height: 100%;
                border-radius: 18px !important;
            }
        }

        .login-body-remember {
            height: 54px;
            margin-top: 18px;
            width: 100%;
            @include flex();

            .checkbox-remember {
                display: flex;
                height: 54px;
                align-items: center;
                font-weight: 400;
                font-size: 24px;
                color: var(--secondary-text-color);
                line-height: 24px;

                .checkbox {
                    margin-right: 10px;
                    height: 42px;
                    width: 42px;
                }
            }

            .ver-code {
                width: 60%;
                height: 54px;
                display: flex;
                justify-content: space-between;

                .ver-input {
                    width: 52%;
                    height: 54px;

                    .code-input {
                        width: 100%;
                        height: 100%;
                        font-size: 14px !important;
                    }
                }

                .ver-img {
                    width: 45%;
                    height: 54px;
                    cursor: pointer;
                    background-size: 100% 50px;
                }
            }
        }

        .login-body-loginbtn {
            width: 100%;
            height: 72px;
            margin-top: 30px;
            background: linear-gradient(90deg, var(--primary-color) 0%, #00E9E6 100%);
            border-radius: 18px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 27px;
            color: var(--anti-text-color);
            line-height: 40px;
        }
    }



    .chooseSchoolBg {
        height: 100%;
        width: 100%;
        background-color: rgba($color: #000000, $alpha: 0);
        opacity: 1;
        position: absolute;
        z-index: 1;
        -webkit-app-region: no-drag;

        .chooseSchool {
            position: absolute;
            width: 60%;
            height: 70%;
            background-color: white;
            box-shadow: 0px 0px 12px rgba($color: var(--boxshaw-main-color-rgb), $alpha: 0.15);
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            align-items: center;
            // -webkit-app-region: no-drag;

            .chooseSchoolTitle {
                font-size: 20px;
                height: 80px;
                line-height: 80px;
            }

            .chooseSchoolList {
                width: 100%;
                flex: 1;
                overflow-y: scroll;
                scrollbar-width: none;

                &::-webkit-scrollbar {
                    display: none;
                    /* 适用于 Chrome, Safari 和 Opera */
                }

                box-sizing: border-box;

                .chooseSchoolItem {
                    height: 60px;
                    margin: 5px 25px;
                    cursor: pointer;
                    border-radius: 6px;
                    border: 1px solid blue;
                    font-size: 20px;
                    box-sizing: border-box;
                    line-height: 60px;
                    text-align: center;
                }
            }
        }
    }
}
</style>