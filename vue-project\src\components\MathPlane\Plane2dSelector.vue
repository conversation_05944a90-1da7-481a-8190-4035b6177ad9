<template>
    <div class="plane2d-full" @click.stop="handleHideClick">
        <div class="container" @click.stop>
            <div class="instruction-text">
                选择图形后点击黑板空白处添加
            </div>
            <div class="instruction-title">
                平面几何
            </div>
            <div class="grid-content">
                <div v-for="(item, index) in items" :key="index" class="grid-item" :class="{ selected: item.selected }"
                    @click="handleItemClick(item)">
                    <div class="item-content">
                        <img :src="item.image" :alt="item.title" class="item-image" />
                        <p class="item-title">{{ item.title }}</p>
                    </div>
                </div>
            </div>
            <div class="instruction-title">
                立体几何
            </div>
            <div class="grid-content">
                <div v-for="(item, index) in items3d" :key="index" class="grid-item"
                    :class="{ selected: item.selected }" @click="handleItemClick(item)">
                    <div class="item-content">
                        <img :src="item.image" :alt="item.title" class="item-image" />
                        <p class="item-title">{{ item.title }}</p>
                    </div>
                </div>
            </div>
            <div class="instruction-title">
                光影
            </div>
            <div class="grid-content">
                <div v-for="(item, index) in lightItems" :key="index" class="grid-item"
                    :class="{ selected: item.selected }" @click="handleItemClick(item)">
                    <div class="item-content">
                        <img :src="item.image" :alt="item.title" class="item-image" />
                        <p class="item-title">{{ item.title }}</p>
                    </div>
                </div>
            </div>
            <div class="instruction-text" style="padding-left: 10px;padding-right: 10px; padding-top: 10px;">
                说明: 光影物体需要先添加灯光(可多个)。 选中灯光后，水平和垂直拖动灯光是在平面上移动。斜向移动(45度角)，左上和左下拖动灯光远离物体，右上和右下拖动灯光靠近物体。
            </div>
        </div>
    </div>
</template>



<script setup>
import { UIFrames } from '@/classroom/frame_enums';
import { MathGeometry } from '@/drawboard/math_geometry/math_root_view';
import { useDrawBoardStore } from '@/stores/drawboard_store';
import { storeToRefs } from 'pinia';
import { onMounted, ref } from 'vue';

const drawBoardStore = useDrawBoardStore()

const { plane2DSelector } = storeToRefs(drawBoardStore)

const tabbarHeight = ref(UIFrames.tabbarHeight)

const items = ref([
    {
        title: "线段",
        image: "/img/plane2d/line.svg",
        type: MathGeometry.Line,
        selected: false
    },
    {
        title: "角",
        image: "/img/plane2d/angle.svg",
        type: MathGeometry.Angle,
        selected: false
    },
    {
        title: "三角形",
        image: "/img/plane2d/triangle.svg",
        type: MathGeometry.Triangle,
        selected: false
    },
    {
        title: "圆形",
        image: "/img/plane2d/circle.svg",
        type: MathGeometry.Circle,
        selected: false
    },
    {
        title: "椭圆",
        image: "/img/plane2d/oval.svg",
        type: MathGeometry.Oval,
        selected: false
    },
    {
        title: "正方形",
        image: "/img/plane2d/square.svg",
        type: MathGeometry.Square,
        selected: false
    },
    {
        title: "矩形",
        image: "/img/plane2d/rectangle.svg",
        type: MathGeometry.Rectangle,
        selected: false
    },
    {
        title: "菱形",
        image: "/img/plane2d/diamond.svg",
        type: MathGeometry.Diamond,
        selected: false
    },
    {
        title: "平行四边形",
        image: "/img/plane2d/parallel.svg",
        type: MathGeometry.Parallel,
        selected: false
    },
    {
        title: "梯形",
        image: "/img/plane2d/trapezoid.svg",
        type: MathGeometry.Trapezoid,
        selected: false
    }
])


const items3d = ref([
    {
        title: "立方体",
        image: "/img/plane3d/cube.svg",
        type: MathGeometry.cube,
        selected: false
    },
    {
        title: "长方体",
        image: "/img/plane3d/cuboid.svg",
        type: MathGeometry.cuboid,
        selected: false
    },
    {
        title: "球体",
        image: "/img/plane3d/sphere.svg",
        type: MathGeometry.sphere,
        selected: false
    },
    {
        title: "圆柱体",
        image: "/img/plane3d/cylinder.svg",
        type: MathGeometry.cylinder,
        selected: false
    },
    {
        title: "圆锥体",
        image: "/img/plane3d/cone.svg",
        type: MathGeometry.cone,
        selected: false
    }
])

const lightItems = ref([
    {
        title: "灯光",
        image: "/img/plane3d/icon_light.svg",
        type: MathGeometry.light,
        selected: false
    },
    {
        title: "立方体(无光)",
        image: "/img/plane3d/cube.svg",
        type: MathGeometry.cubeWithLight,
        selected: false
    },
    {
        title: "长方体(无光)",
        image: "/img/plane3d/cuboid.svg",
        type: MathGeometry.cuboidWithLight,
        selected: false
    },
    {
        title: "球体(无光)",
        image: "/img/plane3d/sphere.svg",
        type: MathGeometry.sphereWithLight,
        selected: false
    },
    {
        title: "圆柱体(无光)",
        image: "/img/plane3d/cylinder.svg",
        type: MathGeometry.cylinderWithLight,
        selected: false
    },
    {
        title: "圆锥体(无光)",
        image: "/img/plane3d/cone.svg",
        type: MathGeometry.coneWithLight,
        selected: false
    }
])

onMounted(() => {

    if (plane2DSelector.value.selectedItem) {
        items.value.forEach((item) => {
            if (item.type === plane2DSelector.value.selectedItem.type) {
                item.selected = true
            }
        });
    }
})

function handleHideClick() {
    plane2DSelector.value.showSelector = false
}

function handleItemClick(clickedItem) {
    items.value.forEach((item) => {
        if (item !== clickedItem) {
            item.selected = false;
        }
    });
    items3d.value.forEach((item) => {
        if (item !== clickedItem) {
            item.selected = false;
        }
    });

    lightItems.value.forEach((item) => {
        if (item !== clickedItem) {
            item.selected = false;
        }
    });

    clickedItem.selected = !clickedItem.selected;

    if (clickedItem.selected) {
        plane2DSelector.value.selectedItem = clickedItem
        if (drawBoardStore.blackBoardPainter) {
            drawBoardStore.blackBoardPainter.mathRootView.addMathGeometry(clickedItem.type, drawBoardStore.lineColor)
        }
    }
    else {
        plane2DSelector.value.selectedItem = null
        if (drawBoardStore.blackBoardPainter) {
            drawBoardStore.blackBoardPainter.mathRootView.addMathGeometry(null, drawBoardStore.lineColor)
        }
    }
}


</script>


<style scoped>
.plane2d-full {
    position: absolute;
    z-index: var(--color-select-z-index);
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: center;
    align-items: flex-end;
    padding-bottom: v-bind("tabbarHeight + 'px'");
    box-sizing: border-box;
}

.container {
    width: 640px;
    height: 510px;
    background-color: var(--main-bc-color);
    border-radius: 26px;
    box-sizing: border-box;
    padding-left: 20px;
    padding-right: 20px;
    padding-top: 10px;
    padding-bottom: 10px;

}

.instruction-text {
    font-size: 12px;
    color: #565656;
    /* 与下方内容的间距 */
    text-align: center;
    /* 文字居中 */
}


.instruction-title {
    font-size: 12px;
    color: #a9a9a9;
    margin-bottom: 5px;
    margin-top: 12px;
    /* 与下方内容的间距 */
    text-align: left;
    /* 文字居中 */
}

.grid-content {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    /* 每行显示 4 个 */
    gap: 15px;
    /* 设置间距 */
    align-content: start;
    /* 内容从顶部开始排列 */
}

.grid-item {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px solid #ccc;
    /* 初始边框为灰色 */
    border-radius: 8px;
    /* 圆角 */
    padding: 2px;
    /* 内边距 */
    cursor: pointer;
    /* 鼠标指针变为手型 */
    transition: border-color 0.3s ease;
    /* 添加过渡效果 */
}

.grid-item.selected {
    border-color: #7fffaa;
    /* 选中时边框 */
}

.item-content {
    pointer-events: none;
    /* 父元素不可交互 */
    text-align: center;
    /* 文字居中 */
}

.item-image {
    width: 40px;
    /* 图片宽度 */
    height: 40px;
    /* 图片高度 */
}

.item-title {
    margin: 0;
    font-size: 14px;
    color: #333;
    padding-bottom: 3px;
}
</style>