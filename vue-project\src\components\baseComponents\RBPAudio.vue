<template>
    <audio ref="rbpAudioRef" :src="rbpAudioUrl" controls preload="auto" autoplay
        :style="{ display: 'none' }"></audio>
</template>
<script setup>
import { onBeforeMount, onMounted } from 'vue'
import { useRbpAudioStore } from '@/stores/rbp_audio_store'
import { storeToRefs } from 'pinia';

const rbpAudioStore = useRbpAudioStore()
const { rbpAudioRef, rbpAudioUrl } = storeToRefs(rbpAudioStore)

onMounted(() => {
    rbpAudioStore.setListener()
    rbpAudioUrl.value = 'http://' + window.location.host + '/static/sound/sliant.mp3'
    rbpAudioRef.value.load()
})

</script>
<style lang="scss" scoped>

</style>