import { PenStyle } from "@/drawboard/draw_enums";
import { UploadImagesView } from "./upload_images_view";
import { ImageDisplayView } from "./image_display_view";
import { useClassroomUIStore } from "@/stores/classroom_ui_store";
import { BoardButton } from "@/drawboard/board_button";


export class BlackboardSerializer {
    serializeLine(line) {


        let data = {
            penStyle: line.penStyle,
            parentId: line.parent.id,
            color: line.material.color.getHex(),
            linewidth: line.penStyle === PenStyle.Line2 ? line.material.linewidth : line.material.lineWidth,
            points: line.cachePoints
        };
        return data;
    }

    seriallizeAllLines(rootView) {
        let lines = rootView.totalLineCaches
        let list = []
        for (let i = 0; i < lines.length; i++) {
            let line = lines[i]


            let data = this.serializeLine(line)
            list.push(data)
        }
        return list
    }


    serializeTeachPlans(application) {
        let teachPlans = application.teachPlans;
        let list = []
        teachPlans.forEach((item, key) => {
            list.push({
                plan: item.teachPlan,
                presetX: item.position.x,
                presetY: item.position.y,
                id: item.id
            })
        });
        return list
    }

    serializeImages(application) {
        let imageViews = Array.from(application.imageCacheMap.entries());
        let list = []
        for (let [key, item] of imageViews) {
            list.push({
                imageUrl: this.dealLocalUrl(item.imageUrl + "",'images'),
                presetX: item.position.x,
                presetY: item.position.y,
                id: item.id,
            })
        }
        return list
    }

    serializeUploadViews(application) {
        let imageViews = Array.from(application.uploadImagesMap.entries());
        let list = []
        for (let [key, item] of imageViews) {
            let urlData = []
            for (let view of item.subViews) {
                if (view instanceof ImageDisplayView) {
                    let item = {
                        image: this.dealLocalUrl(view.imageUrl + "",'images'),
                        id: view.id
                    }
                    if (view.imageView) {
                        item.imageView = {
                            pos: {
                                x: view.imageView.position.x,
                                y: view.imageView.position.y,

                            },
                            scale: view.imageView.nowScale
                        }
                    }
                    urlData.push(item)
                }
            }
            list.push({
                taskId: key,
                presetX: item.position.x,
                presetY: item.position.y,
                id: item.id,
                urlData
            })
        }
        return list
    }

    serializeUploadVideos() {
        const classroomUIStore = useClassroomUIStore()
        let videoList = []
        classroomUIStore.realTimeVideo.list.forEach((e) => {
            let item = {
                url: this.dealLocalUrl(e.url,'videos'),
                taskId: e.taskId,
                time: e.time
            }
            videoList.push(item)
        })
        
        return videoList
    }

    dealLocalUrl(url,split) {
        let splitStr = `/store/${split}/`        
        if (url.includes(splitStr)) {
            return url.split(splitStr)[1]
        }
        return url
    }

    serializeMathRoot(application) {
        let mathView = application.mathRootView
        let mathList = []
        if (mathView) {

            for (let view of mathView.subViews) {
                if (view.id !== mathView.viewBtnId) {
                    let item = {
                        type: view.type,
                        size: view.size,
                        color:view.color,
                        position: {
                            x: view.position.x,
                            y: view.position.y,
                            z: view.position.z,
                        },
                        aPoint: view.aPoint,
                        bPoint: view.bPoint,
                        cPoint: view.cPoint,
                        dPoint: view.dPoint,
                        id: view.id
                    }

                    if (view.viewGroup) {
                        item.rotation = {
                            x: view.viewGroup.rotation.x,
                            y: view.viewGroup.rotation.y,
                        }
                    }
                    mathList.push(item)
                }
            }
        }
        return mathList
    }

    serializeWritingGrids(application) {
        let writeView = application.writingHelpView
        let writeList = []
        if (writeView) {
            for (let view of writeView.subViews) {
                if (view instanceof BoardButton) {
                } else {
                    let item = {
                        type: view.writeType,
                        color:view.color,
                        position: {
                            x: view.position.x,
                            y: view.position.y,
                            z: view.position.z,
                        },
                        id: view.id
                    }
                    writeList.push(item)
                }
            }
        }
        return writeList
    }



    serialize(application) {
        let rootView = application.rootView;
        let lineData = this.seriallizeAllLines(rootView);
        let teachPlanData = this.serializeTeachPlans(application);
        let imageData = this.serializeImages(application)
        let uploadImageData = this.serializeUploadViews(application)
        let uploadVideoData = this.serializeUploadVideos()
        let mathViewData = this.serializeMathRoot(application)
        let gridViewData = this.serializeWritingGrids(application)
        return {
            lastDrawX: application.lastDrawX,
            lineGroupId: rootView.totalDrawGroup.id,
            lines: lineData,
            teachPlans: teachPlanData,
            imageData,
            uploadImageData,
            uploadVideoData,
            mathViewData,
            gridViewData
        }
    }
}

export const blackboardSerializer = new BlackboardSerializer();