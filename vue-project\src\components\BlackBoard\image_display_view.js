import { BoardButton } from "@/drawboard/board_button";
import { BoardClipImage } from "@/drawboard/board_clip_image";
import { BoardLabel } from "@/drawboard/board_label";
import { BoardView } from "@/drawboard/board_view";
import { PainterOrder } from "@/drawboard/draw_enums";
import { useDrawBoardStore } from "@/stores/drawboard_store";
import * as THREE from 'three';



export class ImageDisplayView extends BoardView {
    constructor(application, pos, size, imageUrl, supportEdit = false) {
        super(application, pos, size);
        this.imageUrl = imageUrl;

        this.rotateBtnWidth = 0.03
        this.renderOrder = PainterOrder.background

        this.edit = false
        this.supportEdit = supportEdit

        this.setupUI()
    }

    setupUI() {

        let application = this.application?.deref()
        if (application) {
            let textureId = btoa(this.imageUrl)
            application.textureCache.loadImageTexture(textureId, this.imageUrl, (texture) => {
                const imgAspect = texture.image.width / texture.image.height;
                const viewAspect = this.size.width / this.size.height;
                let planeWidth, planeHeight;
                if (imgAspect > viewAspect) {
                    // 如果图片宽高比更小，则根据宽度计算
                    planeWidth = this.size.width;
                    planeHeight = this.size.width / imgAspect;
                } else {
                    // 如果图片宽高比更大，则根据高度计算
                    planeHeight = this.size.height;
                    planeWidth = this.size.height * imgAspect;
                }
                let imageView
                if (!this.supportEdit) {
                    imageView = new BoardButton(this.application, new THREE.Vector3(0, 0, 0), { width: planeWidth, height: planeHeight })
                    imageView.renderOrder = PainterOrder.background
                    imageView.setImageTexture(texture)
                    imageView.onDoubleClick(() => {
                        if (this.onDoubleClickCallback) {
                            this.onDoubleClickCallback(this)
                        }
                    })
                    imageView.setRenderOrder(PainterOrder.background)
                } else {
                    imageView = new BoardClipImage(
                        this.application,
                        new THREE.Vector3(0, 0, 0),
                        { width: planeWidth, height: planeHeight },
                        '',
                        this.size, () => {
                            if (this.onDoubleClickCallback) {
                                this.onDoubleClickCallback(this)
                            }
                        })
                    imageView.renderOrder = PainterOrder.background
                    imageView.setImageTexture(texture)
                    imageView.setRenderOrder(PainterOrder.background)
                    setTimeout(() => {
                        imageView.changeClipingLines()
                    }, 200)
                    setTimeout(() => {
                        if (this.childPos) {
                            this.setImageViewData(this.childPos, this.childScale, true)
                        }
                    }, 800)
                }

                this.imageView = imageView
                this.addSubView(imageView)
            })
        }
        const x = 0
        const y = - this.size.height / 2 - this.rotateBtnWidth / 3 * 2
        let buttonsView = new BoardView(
            this.application,
            new THREE.Vector3(x, y, 0),
            { width: this.size.width, height: this.rotateBtnWidth })
        buttonsView.renderOrder = PainterOrder.background + 1
        this.addSubView(buttonsView)

        let leftRotateBtn = BoardButton.buttonWithImageUrl(
            this.application,
            new THREE.Vector3(-this.rotateBtnWidth * 3 / 5, 0, 0),
            { width: this.rotateBtnWidth, height: this.rotateBtnWidth },
            'img/rotate_m_90.png', true)
        leftRotateBtn.setRenderOrder(PainterOrder.background + 1)
        leftRotateBtn.onClick(() => {
            this.rotateImage(- Math.PI / 2)
        })
        this.leftRotateBtn = leftRotateBtn

        buttonsView.addSubView(leftRotateBtn)

        let rightRotateBtn = BoardButton.buttonWithImageUrl(
            this.application,
            new THREE.Vector3(this.rotateBtnWidth * 3 / 5, 0, 0),
            { width: this.rotateBtnWidth, height: this.rotateBtnWidth },
            'img/rotate_a_90.png', true)
        rightRotateBtn.setRenderOrder(PainterOrder.background + 1)
        rightRotateBtn.onClick(() => {
            this.rotateImage(Math.PI / 2)
        })
        this.rightRotateBtn = rightRotateBtn
        buttonsView.addSubView(rightRotateBtn)


        let deleteButton = BoardButton.buttonWithImageUrl(
            this.application,
            new THREE.Vector3(this.rotateBtnWidth * 4, 0, 0),
            { width: this.rotateBtnWidth * 0.75, height: this.rotateBtnWidth * 0.75 },
            "img/math/delete.svg", true)
        deleteButton.setRenderOrder(PainterOrder.background + 1)
        deleteButton.onClick(() => {
            const drawBoardStore = useDrawBoardStore()
            if(drawBoardStore.imageOnEdit&&drawBoardStore.imageOnEdit.uuid == this.imageView.uuid){
                drawBoardStore.imageOnEdit = null
            }
            if (this.onDeletedCallback) {
                this.onDeletedCallback(this)
            }
        })
        buttonsView.addSubView(deleteButton)
        let editButton = BoardButton.buttonWithImageUrl(
            this.application,
            new THREE.Vector3(-this.rotateBtnWidth * 4, 0, 0),
            { width: this.rotateBtnWidth * 0.75, height: this.rotateBtnWidth * 0.75 },
            "img/svg/image_edit.svg", true)
        editButton.setRenderOrder(PainterOrder.background)
        editButton.onClick(() => {
            this.setEdit(!this.edit)
        })
        this.editButton = editButton
        this.editButton.visible = this.supportEdit
        buttonsView.addSubView(editButton)

        let prevPageButton = BoardButton.buttonWithImageUrl(
            this.application,
            new THREE.Vector3(- this.size.width / 2 - this.rotateBtnWidth / 2, 0, 0),
            { width: this.rotateBtnWidth / 1.5, height: this.rotateBtnWidth / 1.5 },
            "img/previous_image.png", true)
        prevPageButton.setRenderOrder(PainterOrder.background)
        prevPageButton.visible = false
        prevPageButton.onClick(() => {
            this.onPrevPage(this)
        })
        this.prevPageButton = prevPageButton
        this.addSubView(prevPageButton)

        let nextPageButton = BoardButton.buttonWithImageUrl(
            this.application,
            new THREE.Vector3(this.size.width / 2 + this.rotateBtnWidth / 2, 0, 0),
            { width: this.rotateBtnWidth / 1.5, height: this.rotateBtnWidth / 1.5 },
            "img/next_image.png", true)
        nextPageButton.setRenderOrder(PainterOrder.background)
        nextPageButton.visible = false
        nextPageButton.onClick(() => {
            this.onNextPage(this)
        })
        this.nextPageButton = nextPageButton
        this.addSubView(nextPageButton)

        let indexSize = { width: this.size.width, height: this.size.height / 20 }
        let indexLabel = new BoardLabel(
            this.application,
            new THREE.Vector3(0, this.size.height / 2 + indexSize.height / 2, 0),
            indexSize,
            "",
            { align: 'center', fontSize: this.size.height / 30, color: 0x000000 })
        indexLabel.visible = false
        indexLabel.setRenderOrder(PainterOrder.background)
        this.indexLabel = indexLabel
        this.addSubView(indexLabel)
    }

    // 旋转图片，传入角度（以弧度为单位），保持cover模式
    rotateImage(angle) {
        if (this.imageView) {
            // 将图片绕z轴旋转
            this.imageView.imageMesh.rotation.z += angle;
        }
    }

    setFullDisplay(isFull) {
        this.isFull = isFull
        this.prevPageButton.visible = isFull
        this.nextPageButton.visible = isFull
        this.indexLabel.visible = isFull
        if (this.imageView) {
            if (isFull) {
                this.imageView.position.set(0, 0, 0)
            }
            this.imageView.setFull(isFull)
        }
        this.editButton.visible = !isFull
        this.setEdit(false)
        this.animate()
    }

    setImageViewData(pos, scale, isChange) {
        if (!this.supportEdit) {
            return
        }
        // console.log("----------------------------------",this.imageView);

        if (this.imageView) {
            setTimeout(() => { // 晚于截屏线生成
                this.imageView.position.setX(pos.x,)
                this.imageView.position.setY(pos.y)
                this.imageView.changeScale(scale)
                this.imageView.animate()
                this.childPos = null
                this.childScale = null
            }, 400)
        } else {
            this.childPos = pos
            this.childScale = scale
        }
    }




    setFullIndex(index, length) {
        let text = "" + (index + 1) + "/" + length
        this.indexLabel.setText(text)
    }

    onDoubleClick(callback) {
        this.onDoubleClickCallback = callback
    }

    setEdit(value) {
        if (!this.supportEdit) {
            return
        }
        this.edit = value
        if (this.imageView) {
            this.imageView.setOnEdit(this.edit)
        }
        this.editButton.setImage(this.edit ? "img/svg/image_editing.svg" : "img/svg/image_edit.svg")
        const drawBoardStore = useDrawBoardStore()

        if (value) {
            this.superView.deref().cancelEditWithoutThis(this)
            drawBoardStore.imageOnEdit = this.imageView
        } else if (drawBoardStore.imageOnEdit
            && this.imageView
            && drawBoardStore.imageOnEdit.uuid == this.imageView.uuid) {
            drawBoardStore.imageOnEdit = null
        }
    }


    onNextPage(callback) {
        this.onNextPage = callback
    }

    onPrevPage(callback) {
        this.onPrevPage = callback
    }

    onDeleted(callback) {
        this.onDeletedCallback = callback
    }

    /// 检测点是否在控件中，这里的point是世界坐标
    onPointInside(point) {
        if (!this.visible) {
            return false
        }
        for (let subview of this.subViews) {
            if (subview.onPointInside(point)) {
                return true
            }
        }
        return false
    }
}