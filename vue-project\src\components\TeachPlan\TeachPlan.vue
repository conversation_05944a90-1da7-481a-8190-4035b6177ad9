<template>
    <RBPAlert title="导学案" :zIndex="getZIndex('--toolbar-top-teach-plan-z-index')" width="60%" haveBorderRadius="false"
        @close="closeTeachPlan" @click.stop>
        <template v-slot:rbpDiv>
            <div class="content-div">
                <div class="menu">
                    <div class="space"></div>
                    <div class="tree">
                        <TreeWithSwitch :dataList="treeList"></TreeWithSwitch>
                    </div>
                </div>
                <RBPLine width="6px" height="100%"></RBPLine>
                <div class="subitems">
                    <div class="items">
                        <RBPTableScroll :list="getTeachPlanList()" reverseColor="true" v-if="!isNoData()">
                            <template v-slot:rbpDiv="slotProps">
                                <div class="row" @click="teachPlanItemClick(slotProps.data)">
                                    <div class="row-left">
                                        <div class="item-title">
                                            {{ slotProps.data.theme }}
                                        </div>
                                        <div class="item-time">
                                            创建时间：{{ getDateTimeByOriginalTime(slotProps.data.createDate) }}
                                        </div>
                                    </div>
                                    <div class="row-right">
                                        <RBPButton btnText="查看" btnHaveBorder="true" backgroundColor="white">
                                        </RBPButton>
                                    </div>
                                </div>
                            </template>
                        </RBPTableScroll>
                        <RBPNoData v-if="isNoData()">
                            <template #rbpTitle>
                                <span>暂无数据</span>
                            </template>
                        </RBPNoData>
                    </div>
                    <div class="btns">
                        <RBPSegButton :options="options" :currentValue="currentValue"
                            @updateCurrentValue="updateCurrentValue">
                        </RBPSegButton>
                    </div>
                </div>
            </div>
        </template>
    </RBPAlert>
</template>
<script setup>
import { ref, computed, onMounted, getCurrentInstance, watch, markRaw } from 'vue'
import { storeToRefs } from 'pinia'
import { useClassroomStore } from '@/stores/classroom'
import TreeWithSwitch from '@/components/TeachPlan/TreeWithSwitch.vue'
import { useTeachPlanStore } from '@/stores/teach_plan'
import { ClassRoomRequest } from '@/server_request/classroom_request'
import { ElLoading, ElMessage } from 'element-plus'
import { Alert } from '@/utils/alert'
import { UIFrames } from '@/classroom/frame_enums'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { useDrawBoardStore } from '@/stores/drawboard_store'
import RBPAlert from '@/components/baseComponents/RBPAlert.vue'
import RBPLine from '@/components/baseComponents/RBPLine.vue'
import RBPSegButton from '@/components/baseComponents/RBPSegButton.vue'
import RBPTableScroll from '@/components/baseComponents/RBPTableScroll.vue'
import RBPButton from '@/components/baseComponents/RBPButton.vue'
import { getDateTimeByOriginalTime } from '@/utils/date_time'
import RBPNoData from '@/components/baseComponents/RBPNoData.vue'
import { getZIndex } from '@/components/baseComponents/RBPZIndex'

const currentValue = ref('我的导学案')
const options = ref([
    '我的导学案',
    '校本导学案'
])
const teachPlanStore = useTeachPlanStore()
const { treeList, mineTeachPlanList, schoolTeachPlanList,
    showTeachPlan, stages, currentItem, curerntStage, mineTeachPlan } = storeToRefs(teachPlanStore)

const classroomUIStore = useClassroomUIStore()
const { mainContentTopSpace } = storeToRefs(classroomUIStore)

const { proxy } = getCurrentInstance()
const calcTop = computed(() => {
    return `calc(${UIFrames.tabbarHeight}px - ${mainContentTopSpace.value}px)`
})

function updateCurrentValue(item) {
    currentValue.value = item
    if (item === '我的导学案') {
        mineTeachPlanClick()
    } else if (item === '校本导学案') {
        schoolTeachPlanClick()
    }
}

async function teachPlanItemClick(item) {
    /// 数据为空的时候，给一个弹出框提示。
            if (item.pages.length === 0) {
                Alert.showErrorMessage('当前导学案没有内容')
                return;
            }
    let loading = ElLoading.service({ background: 'transparent' })

    const res = await ClassRoomRequest.loadStage(item.paperId)
    if (res.code == 1) {
        stages.value = res.data

        currentItem.value = item
        ///阶段计时
        curerntStage.value = stages.value[0]
        let promiseList = []
        item.stages = stages
        stages.value.forEach(stage => {
            promiseList.push(ClassRoomRequest.loadQuestions(item.paperId, stage.gpsId))
        })
        Promise.all(promiseList).then(values => {
            stages.value.forEach(stage => {
                values.forEach(value => {
                    if (value.data.length > 0) {
                        if (value.data[0].stageId == stage.gpsId) {
                            stage.questions = value.data
                        }
                    }
                })
            })
            
            showTeachPlan.value = false
            const drawBoardStore = useDrawBoardStore()
            drawBoardStore.addTeachPlan(item)
            drawBoardStore.previewMode = false
        })
    }
    loading.close()
}

function getTeachPlanList() {
    if (mineTeachPlan.value) {
        return mineTeachPlanList.value
    } else {
        return schoolTeachPlanList.value
    }
}

function isNoData() {
    return getTeachPlanList().length == 0
}

function closeTeachPlan() {
    showTeachPlan.value = false
    // teachPlanStore.cleanData()
}

function mineTeachPlanClick() {
    mineTeachPlan.value = true
}

function schoolTeachPlanClick() {
    mineTeachPlan.value = false
}
</script>
<style lang="scss" scoped>
.content-div {
    height: 95%;
    width: 100%;
    display: flex;

    .menu {
        width: 40%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        scrollbar-width: none;
        margin: 10px 0;
        box-sizing: border-box;

        &::-webkit-scrollbar {
            display: none;
        }

        .space {
            flex: 1;
        }

        .tree {
            margin: 0 20px;
        }
    }

    .subitems {
        width: 60%;
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 20px 0;
        box-sizing: border-box;

        .items {
            flex: 1;
            padding: 0 20px;
            overflow-y: auto;
            position: relative;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
        }

        .btns {
            width: 100%;
            height: 100px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
}

.row {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
    cursor: pointer;

    .row-left {
        flex: 1;

        .item-title {
            font-size: 21px;
            color: var(--text-color);
        }

        .item-time {
            font-size: 18px;
            color: var(--secondary-text-color);
            margin-top: 6px;
        }
    }

    .row-right {
        margin-left: 10px;
    }
}
</style>