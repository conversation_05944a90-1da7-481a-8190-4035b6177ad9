<template>
    <div v-if="showStudentResponderScore" class="studentCategoryScore" @click.stop>
        <div class="content" @click.stop>
            <div class="score">
                <div class="add">
                    <div class="title">表扬</div>
                    <div class="list">
                        <div class="item" v-for="(item, index) in evaluationAddList" :key="index"
                            @click="evaluationItemClick(item)">
                            <div class="icon">
                                <img :src="item.evaluationItemImage">
                            </div>
                            <div class="name">
                                {{ item.evaluationItemName }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="minus">
                    <div class="title">待改进</div>
                    <div class="list">
                        <div class="item" v-for="(item, index) in evaluationMinusList" :key="index"
                            @click="evaluationItemClick(item)">
                            <div class="icon">
                                <img :src="item.evaluationItemImage">
                            </div>
                            <div class="name">
                                {{ item.evaluationItemName }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="btns">
                <div class="close" @click="close">
                    <img src="/img/icon_close.png">
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref, computed, onMounted } from 'vue'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { storeToRefs } from 'pinia'
import { useEvaluationScoreStore } from '@/stores/evaluation_score_store'
import { useClassroomStore } from '@/stores/classroom'
import roomUpdater from '@/classroom/classroom_updater.js'
import { useScoreAudioStore } from '@/stores/score_audio_store'

const scoreAudioStore = useScoreAudioStore()
const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)
const evaluationScoreStore = useEvaluationScoreStore()
const { evaluationAddList, evaluationMinusList, addScore, currentStudents } = storeToRefs(evaluationScoreStore)
const classroomUIStore = useClassroomUIStore()
const { showStudentResponderScore, mainContentTopSpace } = storeToRefs(classroomUIStore)
const calcTop = computed(() => {
    return `calc(50% + ${mainContentTopSpace.value}px)`
})

async function evaluationItemClick(item) {
    showStudentResponderScore.value = false
    ///加分显示
    addScore.value = item.evaluationItemScore

    ///1s之后隐藏
    setTimeout(async () => {

        addScore.value = ''
        currentStudents.value = []

        // 上传数据
        let success = await roomUpdater.studentsAddScoreTypeTwo(item.evaluationItemId, item.evaluationItemScore)
        if (success) {
            // 播放音频 播放动画
            scoreAudioStore.play(item.evaluationItemScore)
        }
    }, 1000)
}

function close() {
    showStudentResponderScore.value = false
    currentStudents.value.forEach((student) => {
        student.selected = false
    })
    currentStudents.value = []
    // console.log("test ... on close")
}

</script>
<style lang="scss" scoped>
.studentCategoryScore {
    position: absolute;
    height: 100%;
    width: 100%;
    background-color: rgba($color: #000000, $alpha: 0.2);
    opacity: 1;
    z-index: var(--interact-alert-z-index);

    .content {
        position: absolute;
        top: v-bind(calcTop);
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: white;
        width: 60%;
        height: 70%;
        border-radius: 10px;
        border: 1px solid #80A9CB;
        display: flex;
        flex-direction: column;
        padding: 10px 10px;
        box-sizing: border-box;

        .score {
            flex: 1;
            overflow-y: auto;

            &::-webkit-scrollbar {
                width: 16px;
                /* 垂直滚动条宽度 */
                height: 16px;
                /* 水平滚动条高度 */
            }

            /* 自定义滚动条轨道的样式 */
            &::-webkit-scrollbar-track {
                background-color: #fff;
                border-radius: 10px;
            }

            /* 自定义滚动条的滑块样式 */
            &::-webkit-scrollbar-thumb {
                background-color: #c8c8c8;
                border-radius: 10px;
                border: 3px solid transparent;
                /* 添加透明边框，增加滑块宽度 */
            }

            /* 鼠标悬停时改变滑块的颜色 */
            &::-webkit-scrollbar-thumb:hover {
                background-color: #c9c9c9;
            }

            .add {
                width: 100%;
                display: flex;
                flex-direction: column;
                border: 1px solid #C3CBD3;
                border-radius: 4px;
                box-sizing: border-box;

                .title {
                    margin-top: 30px;
                    margin-left: 42px;
                    font-size: 26px;
                }

                .list {
                    padding: 20px 0px;
                    display: grid;
                    grid-template-columns: repeat(8, 1fr);
                    gap: 10px;

                    .item {
                        height: 110px;
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                        align-items: center;
                        cursor: pointer;

                        .icon {
                            width: 80px;
                            height: 80px;

                            img {
                                width: 100%;
                                height: 100%;
                                border-radius: 50%;
                            }
                        }

                        .name {
                            color: #2E4A66;
                            font-size: 16px;

                        }
                    }
                }
            }

            .minus {
                margin-top: 10px;
                width: 100%;
                display: flex;
                flex-direction: column;
                border: 1px solid #C3CBD3;
                border-radius: 4px;
                box-sizing: border-box;

                .title {
                    margin-top: 30px;
                    margin-left: 42px;
                    font-size: 26px;
                }

                .list {
                    padding: 20px 0px;
                    display: grid;
                    grid-template-columns: repeat(8, 1fr);
                    gap: 10px;

                    .item {
                        height: 110px;
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                        align-items: center;
                        cursor: pointer;

                        .icon {
                            width: 80px;
                            height: 80px;

                            img {
                                width: 100%;
                                height: 100%;
                                border-radius: 50%;
                            }
                        }

                        .name {
                            color: #2E4A66;
                            font-size: 16px;

                        }
                    }
                }
            }
        }

        .btns {
            height: 110px;
            width: 100%;
            display: flex;
            justify-content: end;

            .close {
                cursor: pointer;
                width: 20px;
                height: 20px;
                align-self: flex-end;
                margin: 20px;

                img {
                    width: 20px;
                    height: 20px;
                }
            }
        }
    }
}
</style>