

import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { UIFrames, DisplayDirection } from '@/classroom/frame_enums'
import { useInteractStore } from '@/stores/interact_store'
import { storeToRefs } from 'pinia'
import { SystemToolbarMode } from '@/classroom/classroom_enums.js'

export const useClassroomUIStore = defineStore('classroom_ui', () => {

    const showPhetExp = ref(false)
    const showPhetExpMenu = ref(false)
    const systemToolbarMode = ref(SystemToolbarMode.big)
    const toolbarRef = ref(null)
    const systemToolbarRef = ref(null)
    const systemToolbarHalfScreenRef = ref(null)

    const interactStore = useInteractStore()
    const { showInteractSelector } = storeToRefs(interactStore)

    const displayDirection = ref(DisplayDirection.Right)
    const showSystem = ref(false)
    const showStudentList = ref(false)
    const showRandomRollCall = ref(false)
    const showTimeKeeper = ref(false)
    const showClassFlow = ref(false)
    const showScoreStatistics = ref(false)
    const showSystemSet = ref(false)
    const showClassRoomRecord = ref(false)
    const hideClassRoomRecord = ref(false)
    const showRecordType = ref('interact') // interact 为互动记录，homework 为作业记录
    const showCloudDisk = ref(false)
    const showClassRecord = ref(false) // 是否从manage界面点击课程记录
    const showScanQrCode = ref(false)
    const showUpdater = ref(false)
    const showRecordResult = ref(false)
    const showVideoPlayView = ref(false)
    const showAudioPlayView = ref(false)
    const showStudentCategoryScore = ref(false)
    const showScoreStatisticsDetail = ref(false)
    const showStudentResponderScore = ref(false)
    const showClearScreenAlert = ref(false)
    const showBluetoothAp = ref(false) //蓝牙模式
    const showTemplateProbability = ref(false)
    const showAIHelp = ref(false)

    const windowHeight = ref(window.innerHeight)
    const windowWidth = ref(window.innerWidth)
    const realTimeVideo = ref(
        {
            count:0,
            list:[],
            show:false
        }
    )
    const mainContentTopSpace = ref(0)
    const mainContentHeight = computed(() => {
        let height = windowHeight.value;
        // let height = windowHeight.value - UIFrames.tabbarHeight;
        // if (showInteractSelector.value) {
        //     height = height - UIFrames.interactSelectHeight
        // }
        return height
    })

    function updateWindowHeight() {
        windowHeight.value = window.innerHeight
        windowWidth.value = window.innerWidth
    }

    function windowAddListener() {
        window.addEventListener('resize', updateWindowHeight);
        updateWindowHeight()
    }

    function windowRemoveListener() {
        window.removeEventListener('resize', updateWindowHeight);
    }

    ///退出课堂调用
    function cleanData() {
        showScoreStatistics.value = false
        showClassFlow.value = false
        mainContentTopSpace.value = 0
        showTimeKeeper.value = false
        showRandomRollCall.value = false
        showStudentList.value = false
        showSystem.value = false
        displayDirection.value = DisplayDirection.Right
        showSystemSet.value = false
        showClassRoomRecord.value = false
        showCloudDisk.value = false
        showClassRecord.value = false
        showScanQrCode.value = false
        showUpdater.value = false
        showRecordResult.value = false
        showVideoPlayView.value = false
        showStudentCategoryScore.value = false
        showScoreStatisticsDetail.value = false
        showStudentResponderScore.value = false
        showClearScreenAlert.value = false
        showTemplateProbability.value = false
        hideClassRoomRecord.value = false
        showPhetExp.value = false
        showPhetExpMenu.value = false
        showAudioPlayView.value = false
        showAIHelp.value = false
    }

    ///wps打开关闭时改变toolbar层级
    function changeToolbarZIndex(zIndex) {
        if (toolbarRef.value) {
            toolbarRef.value.style.zIndex = zIndex
        }

        changeSystemToolbarZIndex(zIndex)
    }

    ///纸笔互动打开关闭时改变systemToolbar层级
    function changeSystemToolbarZIndex(zIndex) {
        if (systemToolbarRef.value) {
            systemToolbarRef.value.style.zIndex = zIndex
        }
    }

    function changeToolbarZIndex(zIndex) {
        if (toolbarRef.value) {
            toolbarRef.value.style.zIndex = zIndex
        }
    }

    function changeSystemToolbarHalfScreenRefZIndex(zIndex) {
        if (systemToolbarHalfScreenRef.value) {
            systemToolbarHalfScreenRef.value.style.zIndex = zIndex
        }
    }

    return {
        systemToolbarHalfScreenRef,
        systemToolbarRef,
        toolbarRef,
        displayDirection,
        showStudentList,
        showSystem,
        showRandomRollCall,
        showTimeKeeper,
        mainContentHeight,
        mainContentTopSpace,
        showClassFlow,
        showScoreStatistics,
        showSystemSet,
        windowHeight,
        windowWidth,
        realTimeVideo,
        cleanData,
        windowAddListener,
        windowRemoveListener,
        updateWindowHeight,
        changeToolbarZIndex,
        changeSystemToolbarZIndex,
        changeSystemToolbarHalfScreenRefZIndex,
        changeToolbarZIndex,
        showClassRoomRecord,
        showBluetoothAp,
        showCloudDisk,
        showRecordType,
        showClassRecord,
        showScanQrCode,
        showUpdater,
        showRecordResult,
        showVideoPlayView,
        showStudentCategoryScore,
        showScoreStatisticsDetail,
        showStudentResponderScore,
        showClearScreenAlert,
        showTemplateProbability,
        hideClassRoomRecord, 
        showPhetExp,
        showPhetExpMenu,
        showAudioPlayView,
        showAIHelp
    }
})