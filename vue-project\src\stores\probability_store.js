import { defineStore } from "pinia";
import { ref } from "vue";
import { ClassRoomRequest } from "@/server_request/classroom_request";
import { useDrawBoardStore } from "./drawboard_store";

export const useProbabilityStore = defineStore("probability", () => {

    const probabilityTemplates = ref([
        {
            name: '模板1',
            data: [10, 20, 30, 40, 50]
        },
        {
            name: '模板2',
            data: [10, 20, 30, 40, 50]
        }
    ])

    const templateName = ref(probabilityTemplates.value[0]?.name ?? '')

    const xValues = ref([10, 20, 30, 40, 50]);

    // 合并展示的X轴坐标
    const mergedXValues = ref([10, 20, 30, 40, 50]);

    // 合并展示的Y轴数值
    const mergedDataValues = ref([0.0, 0.0, 0.0, 0.0, 0.0]);

    // 是否展示合并后的数据图表
    const showMergedDisplayView = ref(false)

    // 笔迹 { studentId: [stroke] }
    const mulStrokes = ref({});

    // 笔迹点集 {studentId: points}
    const allPoints = ref({});

    // 图表展示
    const chartMap = ref({});

    function addDataToStrokes(studentId, points) {
        // 判断 allPoints 是否已经包含该 studentId
        if (allPoints.value.hasOwnProperty(studentId)) {
            // 如果存在该 studentId，向其数组中添加数据
            let all = allPoints.value[studentId];
            allPoints.value[studentId] = [...all, ...points];
        } else {
            // 如果不存在该 studentId，创建该属性并初始化为空数组，然后添加数据
            allPoints.value[studentId] = [...points];
        }
    }

    async function getStrokes(studentId) {
        let points = allPoints.value[studentId];
        let index = 0;
        let store = [];
        let gridMap = {};
        while (index < points.length) {
            let p = points[index];
            if (p.f === 'D') {
                //判断下一个点是否是M
                if (points[index + 1]?.f === 'M') {
                    store = [];
                }
            } else if (p.f === 'M') {
                store.push(p)
            } else if (p.f === 'U') {
                if (points[index - 1]?.f === 'M') {
                    if (store.length > 0) {
                        let x = store[0].x;
                        let y = store[0].y;
                        let gridIndex = getGridIndex(x * 210, y * 297);
                        if (gridIndex != null) {
                            if (gridMap[gridIndex] == undefined) {
                                gridMap[gridIndex] = []
                            }
                            let xArr = []
                            let yArr = []
                            store.forEach(p => {
                                xArr.push(parseInt(p.x * 21000));
                                yArr.push(parseInt(p.y * 29700));
                            })
                            gridMap[gridIndex].push({
                                x: xArr,
                                y: yArr
                            })
                        }
                    }
                }
            }
            index++;
        }
        // 送去后端
        let results = []
        for (let index = 0; index < 5; index++) {
            let xValue = xValues.value[index]
            if (gridMap[index]) {
                let res = await ClassRoomRequest.trailsRecog(gridMap[index])
                if (res.code == 1) {
                    results.push(parseInt(res.data.label) / xValue)
                }
                else {
                    results.push(0)
                }
            }
            else {
                results.push(0)
            }
        }
        chartMap.value[studentId] = results

        const drawBoardStore = useDrawBoardStore()
        if (drawBoardStore.paperPenPainter) {
            drawBoardStore.paperPenPainter.setProbablityEcharts(studentId, xValues.value, results)
        }
    }

    function showMergedChartWithStudents(stuList) {
        let mergedData = [];
        let mergedCounts = []
        stuList.forEach((stu, i) => {
            let data = chartMap.value[stu.studentId]
            if (data !== undefined) {
                data.forEach((item, index) => {
                    mergedData[index] = (mergedData[index] ?? 0) + item
                    mergedCounts[index] = (mergedCounts[index] ?? 0) + 1
                })
            }
        })
        mergedData.forEach((item, index) => {
            if (mergedCounts[index] !== undefined) {
                mergedData[index] = item / mergedCounts[index]
                mergedXValues.value[index] = xValues.value[index] * mergedCounts[index]
            }
            else {
                mergedData[index] = 0
                mergedXValues.value[index] = xValues.value[index]
            }
        })
        mergedDataValues.value = mergedData
        showMergedDisplayView.value = true
    }

    function getGridIndex(x, y) {
        //根据位置规定在第几个格子里面
        let gridX = 49.1;
        let gridY = 173.6;
        let gridW = 26.5;
        let gridH = 22;
        for (let index = 0; index < 5; index++) {
            if (x > gridX + gridW * (index)
                && x < gridX + gridW * (index + 1)
                && y > gridY
                && y < gridY + gridH) {
                return index;
            }
        }
        return null;
    }


    function endInteract() {
        chartMap.value = {};
        allPoints.value = {};
        templateName.value = probabilityTemplates.value[0]?.name ?? ''
    }

    function cleanData() {

        endInteract()
    }

    return {
        chartMap,
        allPoints,
        mulStrokes,
        xValues,
        mergedXValues,
        mergedDataValues,
        showMergedDisplayView,
        probabilityTemplates,
        templateName,
        endInteract,
        cleanData,
        addDataToStrokes,
        getStrokes,
        showMergedChartWithStudents
    };
});