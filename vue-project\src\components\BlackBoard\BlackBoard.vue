<template>
    <div v-if="showBoard" class="container">
        <BlackDrawBoard class="drawboard" v-if="blackBoardPainter" :painter="blackBoardPainter"></BlackDrawBoard>
        <!-- <div v-if="hideToolBar === false" class="draw-bar">
            <div class="item" @click="sceneReduct()">
                <img src="/img/scene_reduction.png">
                <span>{{ previewMode ? "取消预览" : "预览视图" }}</span>
            </div>
            <div style="width: 30px;"></div>
            <div class="item" @click="canceLine()">
                <img src="/img/line_cancel.png">
                <span>撤销笔画</span>
            </div>
        </div> -->
        <UploadImgsSelector></UploadImgsSelector>
    </div>
</template>


<script setup>
import { onBeforeMount, ref, toRefs, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useDrawBoardStore } from '@/stores/drawboard_store'
import BlackDrawBoard from './BlackDrawBoard.vue';
import { useDesktopStore } from '@/stores/desktop_store';
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import UploadImgsSelector from './UploadImgsSelector.vue';

const classroomUIStore = useClassroomUIStore()
const { mainContentHeight, mainContentTopSpace } = storeToRefs(classroomUIStore)

const showBoard = ref(true)

const drawBoardStore = useDrawBoardStore()
const { blackBoardPainter, previewMode } = storeToRefs(drawBoardStore)

const desktopStore = useDesktopStore()

const { showDesktop, drawMode, hideToolBar } = storeToRefs(desktopStore)


function sceneReduct() {

    if (previewMode.value) {
        if (blackBoardPainter.value) {
            blackBoardPainter.value.resetInstallFrame()
        }
    }
    else {
        if (blackBoardPainter.value) {
            blackBoardPainter.value.resetMaxFrame()
        }
    }
    previewMode.value = !previewMode.value

}

function canceLine() {
    if (blackBoardPainter.value) {
        blackBoardPainter.value.cancelALine()
    }
}

onBeforeMount(() => {
    showBoard.value = true
})


watch(showDesktop, () => {
    showBoard.value = !(showDesktop.value && !drawMode.value)
})

watch(drawMode, () => {
    showBoard.value = !(showDesktop.value && !drawMode.value)
})


</script>


<style lang="scss" scoped>
.container {
    position: absolute;
    width: 100%;
    height: v-bind("mainContentHeight + 'px'");
    // background-color: rgba(255, 255, 255, 0);
    display: flex;
    flex-direction: column;
    z-index: 1;

    .drawboard {
        position: absolute;
        left: 0%;
        top: 0%;
        width: 100%;
        height: v-bind("mainContentHeight + 'px'");
        z-index: 1;
    }

    .draw-bar {
        display: flex;
        position: absolute;
        justify-content: center;
        align-items: center;
        flex-direction: row;
        width: 200px;
        height: 60px;
        left: 50%;
        transform: translateX(-50%);
        /* 修正水平居中的偏移 */
        bottom: 5px;
        background-color: lightgray;
        border-radius: 5px;
        z-index: 1;

        .item {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            border-radius: 4px;
            width: 60px;

            img {
                width: 28px;
                height: 28px;
            }

            span {
                color: black;
                font-size: 11px;
                margin-top: 6px;
            }
        }
    }
}
</style>