import { BoardButton } from "../board_button";
import { BoardView } from "../board_view";
import * as THREE from 'three';
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial.js';
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry.js';
import { Line2 } from 'three/examples/jsm/lines/Line2.js';
import { BoardTool } from "../board_tool";
import { DrawZ, PainterOrder } from "../draw_enums";
import { BoardImageView } from "../board_image_view";

const MoveTag = {
    none: -1,
    move: 1,
}

// 书写辅助视图基类 - 统一版
export class WritingGridView extends BoardView {

    constructor(application, pos, size, color,copyCallBack,type) {


        
        // 初始化父类
        super(application, pos, size)
        this.copyCallBack = copyCallBack
        this.writeType = type
        this.renderOrder = PainterOrder.customDisplay
        this.onEdit = false
        this.color = color
        this.moveTag = MoveTag.none
        // 获取应用程序实例
        let app = this.application?.deref()
        // 计算按钮的大小
        this.buttonSize = app.cameraInitSize.width / 13
        // 间距系数
        this.spacingFactor = 1

        // 创建线条组
        this.lineGroup = new THREE.Group()
        this.lineGroup.renderOrder = PainterOrder.customDisplay
        this.add(this.lineGroup)

        // 创建视图按钮
        this.createViewButton(application)

        // 创建功能按钮
        this.createFunctionButtons(application)

        let rootView = application.deref().rootView
        rootView.addDrawInSubView(this)
    }

    onCopy(position){
        if(this.copyCallBack){
            this.copyCallBack(position,this.writeType,this.size.height)
        }
    }

    createViewButton(application) {
        // 计算实际内容区域的大小（排除按钮的空间和额外间距）
        let contentWidth = this.size.width - this.buttonSize * this.spacingFactor
        let contentHeight = this.size.height

        // 调整内容区域位置，向左上移动
        let offsetX = -this.buttonSize * this.spacingFactor / 2
        let offsetY = 0

        let viewButton = new BoardButton(
            application,
            new THREE.Vector3(offsetX, offsetY, 0),
            { width: contentWidth, height: contentHeight })
        viewButton.renderOrder = PainterOrder.customDisplay - 1
        viewButton.onClick(() => {
            this.setOnEdit(!this.onEdit)
            if (this.onEdit) {
                this.superView.deref().cancelEditWithout(this)
            }
        })
        this.viewButton = viewButton
        this.addSubView(viewButton)
    }

    createFunctionButtons(application) {
        let buttonSize = this.buttonSize / 4
        // 创建删除按钮，放在右下角
        let deleteButton = new BoardButton(
            application,
            new THREE.Vector3(
                this.size.width / 2 - buttonSize / 2,
                this.size.height / 2,
                0
            ),
           
            { width: buttonSize, height: buttonSize },
            true
        )
        deleteButton.setImage('img/math/delete.svg')
        deleteButton.setRenderOrder(PainterOrder.customDisplay + 1)
        deleteButton.onClick(() => {
            this.removeFromSuperView()
            this.dispose()
        })
        deleteButton.visible = false
        this.deleteButton = deleteButton
        this.addSubView(deleteButton)

        // 创建移动按钮
        let moveImageView = new BoardImageView(
            this.application,
            new THREE.Vector3(
                this.size.width / 2 - buttonSize / 2,
                -this.size.height / 2,
                0
            ),
            { width: buttonSize, height: buttonSize }, true)
        moveImageView.setImageUrl('img/plane3d/move.svg')
        moveImageView.setRenderOrder(PainterOrder.customDisplay + 1)
        moveImageView.visible = false
        this.moveImageView = moveImageView
        this.addSubView(moveImageView)

        // 创建右中角按钮
        let copyButton = new BoardButton(
            application,
            new THREE.Vector3(
                this.size.width / 2 - buttonSize / 2,
                0,
                0
            ),
            { width: buttonSize, height: buttonSize },
            true
        )
        copyButton.setImage('img/math/copy.svg')
        copyButton.setRenderOrder(PainterOrder.customDisplay + 1)
        copyButton.onClick(() => {
            this.onCopy(this.position)
            // 可以在这里添加自定义功能
        })
        copyButton.visible = false
        this.copyButton = copyButton
        this.addSubView(copyButton)
    }

    createLine(x1, y1, x2, y2) {
        const material = new LineMaterial({
            color: this.color ?? 0x00ff00,
            linewidth: 2,
            dashed: false,
        });

        let lineGeometry = new LineGeometry()
        let points = [x1, y1, 0, x2, y2, 0]

        lineGeometry.setPositions(points);

        const line = new Line2(lineGeometry, material);
        line.computeLineDistances();

        this.lineGroup.add(line)
        return line
    }

    createDashedLine(x1, y1, x2, y2, size) {
        const material = new LineMaterial({
            color: this.color ?? 0x00ff00,
            linewidth: 1,
            dashed: true,
            dashSize: size,
            gapSize: size
        });

        let lineGeometry = new LineGeometry()
        let points = [x1, y1, 0, x2, y2, 0]

        lineGeometry.setPositions(points);

        const line = new Line2(lineGeometry, material);
        line.computeLineDistances();

        this.lineGroup.add(line)
        return line
    }

    setOnEdit(onEdit) {
        this.onEdit = onEdit
        this.deleteButton.visible = this.onEdit
        this.moveImageView.visible = this.onEdit
        this.copyButton.visible = this.onEdit
        this.moveImageView.draggable = this.onEdit
        if (onEdit) {
            this.superView.deref().bringSubViewToFront(this)
        }
    }

    onPointInside(point) {
        if (!this.visible) {
            return false
        }
        if (this.onEdit) {
            for (let subView of this.subViews) {
                if (subView === this.lineGroup) {
                    continue
                }
                if (subView.onPointInside(point)) {
                    return true
                }
            }
        }
        return super.onPointInside(point)
    }

    onTouchDown(point) {
        let view = super.onTouchDown(point)
        if (!this.onEdit) {
            return view
        }
        let cvtPoint = this.convertPoint(point, DrawZ.objcZ)
        this.touchDownPoint = cvtPoint
        if (view === this.moveImageView) {
            this.moveTag = MoveTag.move
        }
        return view
    }

    onTouchMove(point) {
        if (!this.onEdit || !this.touchDownPoint) {
            return super.onTouchMove(point)
        }
        let divPoint = point
        let cvtPoint = this.convertPoint(point, DrawZ.objcZ)
        let spaceX = cvtPoint.x - this.touchDownPoint.x
        let spaceY = cvtPoint.y - this.touchDownPoint.y
        this.touchDownPoint = cvtPoint

        if (this.moveTag === MoveTag.move) {
            let x = this.position.x + spaceX;
            let y = this.position.y + spaceY;
            this.position.set(x, y, this.position.z);
            return super.onTouchMove(point);
        }

        return super.onTouchMove(divPoint)
    }

    onTouchUp(point) {
        this.touchDownPoint = null
        this.moveTag = MoveTag.none
        return super.onTouchUp(point)
    }

    dispose() {
        BoardTool.disposeGroup(this.lineGroup)
        super.dispose()
    }
} 