<template>
    <div class="rbp-no-data">
        <div class="rbp-no-data__img">
            <img src="/img/svg/icon_no_data.svg">
        </div>
        <div class="rbp-no-data__text">
            <slot name="rbpTitle"></slot>
        </div>
    </div>
</template>
<script setup>

</script>
<style lang="scss" scoped>
.rbp-no-data {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;

    .rbp-no-data__img {
        width: 269px;
        height: 246px;

        img {
            width: 100%;
            height: 100%;
        }
    }

    .rbp-no-data__text {
        font-size: 24px;
        color: var(--explanatory-text-color);
    }
}
</style>