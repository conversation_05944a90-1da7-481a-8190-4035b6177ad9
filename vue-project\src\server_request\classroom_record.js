import axios from "axios"
import { ServerUrls } from "./server_urls"
import { loginInstance } from "@/login_instance/login_instance"

export class ClassroomRecordRequest {

    static defaultConfig() {
        return {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': loginInstance.token ?? localStorage.getItem('token'),
                "Ink-Mgboard-Subject-Id": loginInstance.subjectMainId,
                "Robot-Selected-Role-Codes": "MGBOARD_TEACHER"
            }
        }
    }
    //微课程
    static async getMicro(params) {
        const url = ServerUrls.classRoomRecordMicroCourse()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //互动记录
    static async getInteraction(params) {
        const url = ServerUrls.classRoomRecordInteraction()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //删除某条记录
    static async deleteOne(params) {
        const url = ServerUrls.classRoomRecordDelete()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //互动任务进度
    static async interactionTaskProgress(params) {
        const url = ServerUrls.classRoomRecordInteractionTaskProgress()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //学生答题统计情况
    static async questionStatistics(params) {
        const url = ServerUrls.classRoomRecordQuestionStatistics()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //互动记录统计情况
    static async interactionStatistics(params) {
        const url = ServerUrls.classRoomRecordInteractionStatistics()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //互动答案添加
    static async interactionSetAnswer(params) {
        const url = ServerUrls.classRoomRecordInteractionAnswer()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //组合题作答情况
    static async groupAnswer(params) {
        const url = ServerUrls.classRoomRecordCroupStatistics()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //互动详情
    static async interactDetail(params) {
        const url = ServerUrls.classRoomRecordInteractDetail()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //互动轨迹
    static async interactTrails(params) {
        const url = ServerUrls.classRoomRecordInteractTrails()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //隋唐记录 获取与老师的精通
    static async studentInteractDeails(params) {
        const url = ServerUrls.classRoomRecordInteractDetails()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //随堂记录 获取回答批改情况
    static async answerCorrectManual(params) {
        const url = ServerUrls.classRoomRecordAnswerCorrect()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //随堂记录 获取学生回答试题
    static async studentAnswerPaper(params) {
        const url = ServerUrls.classRoomRecordStudentAnswerPaper()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //随堂记录 获取教师批注
    static async teacherInteract(params) {
        const url = ServerUrls.classRoomRecordTeacherInteract()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //随堂记录随堂测试 问题批改
    static async taskQuestionsCorrect(params) {
        const url = ServerUrls.classRoomRecordTaskQuestionsCorrect()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //随堂记录随堂测试 问题回答统计 
    static async questionsAnswerStatistic(params) {
        const url = ServerUrls.classRoomRecordQuestionsAnswerStatistic()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //随堂记录随堂测试 学生问题回答统计 
    static async studentQuestionsAnswerStatistic(params) {
        const url = ServerUrls.classRoomRecordStudentQuestionsAnswerStatistic()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //随堂记录随堂测试 问题回答轨迹页面  
    static async questionsAnswerTrails(params) {
        const url = ServerUrls.classRoomRecordQuestionAnswerTrails()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //随堂记录随堂测试 优化问题 
    static async pentrailsOptimizeQuestion(params) {
        const url = ServerUrls.classRoomRecordPentrailsOptimizeQuestion()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //随堂记录中文练字 获取数据 
    static async interactStudentWords(params) {
        const url = ServerUrls.classRoomRecordInteractStudentWords()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //随堂记录 中文练字得分情况 
    static async wordsRecordScore(params) {
        const url = ServerUrls.classRoomRecordWordsRecordScore()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //随堂记录 中文练字轨迹
    static async wordsScoreTrails(params) {
        const url = ServerUrls.classRoomRecordWordsScoreTrails()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }

    //作业记录
    static async getHomework(params) {
        const url = ServerUrls.classRoomRecordHomework()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }

    // 随堂记录日历
    static async getHistory(params) {
        const url = ServerUrls.historyUrl()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }

    // 作业讲评日历
    static async getHomeworkHistory(params) {
        const url = ServerUrls.homeworkHistoryUrl()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }

    // 作业讲评日历
    static async getDayRecords(params) {
        const url = ServerUrls.getDayRecordsUrl()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }

    // 单词听写
    static async getEnglishUsers(params) {
        const url = ServerUrls.getEnglishUsersUrl()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    // 单词听写 
    static async getEnglishStatistics(params) {
        const url = ServerUrls.getEnglishStatisticsUrl()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }

    //随堂记录 获取推题
    static async getRecommandQuestion(params) {
        const url = ServerUrls.getRecommandQuestionUrl()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            console.error("--",e)
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //随堂记录 获取报告
    static async getPaperAnalysis(params) {
        const url = ServerUrls.getPaperAnalysisUrl()
        try {
            const response = await axios.post(url, params, this.defaultConfig())
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
} 