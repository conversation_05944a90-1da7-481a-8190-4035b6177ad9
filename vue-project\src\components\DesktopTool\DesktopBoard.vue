<template>
    <div class="fullscreen-view" v-if="showTransparent" id="transparent-view"></div>
    <Vue3DraggableResizable id="desktop-toolbar" v-if="showDesktop && !showScreenShot" class="draggable" :init-w="toolW"
        :init-h="toolH" v-model:x="toolX" v-model:y="toolY" v-model:w="toolW" v-model:h="toolH" :draggable="true"
        :resizable="false" v-model:active="active" @drag-end="handleDragEnd">
        <div class="content">
            <label style="color: white; font-size: 12px;">桌面</label>
            <div style="width: 20px;"></div>
            <ImageButton @click="showDrawBoard" :title="drawText" :image="drawImage" />
            <div style="width: 18px;"></div>
            <ImageButton @click="showToolBar" title="工具栏" :image="toolbarImage" />
            <div style="width: 18px;"></div>
            <ImageButton @click="screenShotDesktop" title="截屏" image="/img/desktop/desktop_screen_shot.svg" />
            <div style="width: 18px;"></div>
            <ImageButton @click="quitDesktop" title="返回" image="/img/desktop/dekstop_quite.svg" />
            <div style="width: 16px;"></div>
        </div>
    </Vue3DraggableResizable>

</template>


<script setup>
import { UIFrames } from '@/classroom/frame_enums';
import { useDesktopStore } from '@/stores/desktop_store';

import { storeToRefs } from 'pinia';
import { nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import Vue3DraggableResizable from 'vue3-draggable-resizable'
//需引入默认样式
import 'vue3-draggable-resizable/dist/Vue3DraggableResizable.css'
import ImageButton from './ImageButton.vue';
import { useInteractStore } from '@/stores/interact_store';
import { useDrawBoardStore } from '@/stores/drawboard_store.js';
import roomUpdater from '@/classroom/classroom_updater';
import { isObjective } from '@/classroom/interact_enums';

const desktopStore = useDesktopStore()

const { showDesktop, drawMode, resetNumber, hideToolBar, showScreenShot } = storeToRefs(desktopStore)

const interactStore = useInteractStore()

const { showInteractSelector } = storeToRefs(interactStore)

const drawBoardStore = useDrawBoardStore()
const { previewMode } = storeToRefs(drawBoardStore)

const active = ref(false)

const toolW = ref(352)
const toolH = ref(62)

const toolX = ref(window.innerWidth - toolW.value - 70)
const toolY = ref(window.innerHeight - UIFrames.tabbarHeight * 2 - 160)

const showTransparent = ref(false)

const drawText = ref("打开画板")
const drawImage = ref("/img/desktop/desktop_draw_off.svg")

const toolbarImage = ref("/img/desktop/desktop_toolbar_on.svg")

let isQuit = false
onBeforeUnmount(() => {
    showTransparent.value = false
    if (window.electron) {
        removeTransparentListener()
        window.electron.unignoreMosue()
    }
})


watch(showDesktop, () => {
    showTransparent.value = showDesktop.value && !drawMode.value
    if (showDesktop.value) {
        isQuit = false
    } else if (!isQuit) {        
        isQuit = true
        removeTransparentListener()
        if (window.electron) {
            window.electron.unignoreMosue()
        }
    }
})

watch(drawMode, () => {
    showTransparent.value = showDesktop.value && !drawMode.value
    if (drawMode.value) {
        drawText.value = "关闭画板"
        drawImage.value = "/img/desktop/desktop_draw_on.svg"
        // if (window.electron) {
        //     window.electron.ignoreMouse()
        // }
    }
    else {
        drawText.value = "打开画板"
        drawImage.value = "/img/desktop/desktop_draw_off.svg"
        // if (window.electron) {
        //     window.electron.unignoreMosue()
        // }
    }
})


watch(showTransparent, (newValue) => {
    if (newValue) {
        nextTick(() => {
            addTransparentListener()
        })
    }
    else {
        removeTransparentListener()
    }
})

watch(resetNumber, () => {
    toolX.value = window.innerWidth - toolW.value - 70
    toolY.value = window.innerHeight - UIFrames.tabbarHeight * 2 - 160

    if (window.electron) {
        let x = toolX.value
        let y = toolY.value
        window.electron.setFocusPosition({ x, y })
    }
})


watch(hideToolBar, (newValue) => {
    if (window.electron) {
        // console.log("hide toolbar", newValue, roomUpdater.interact)
        if (newValue) { // 隐藏工具栏
            if (isObjective(roomUpdater.interact)) { // 客观题互动中焦点机制要保留
                window.electron.shotBarFocusWindow(true)
            }
            else {
                window.electron.shotBarFocusWindow(false)
            }
        }
        else { // 显示工具栏 显示焦点重置小窗口
            window.electron.shotBarFocusWindow(true)
        }
    }
    if (newValue) {
        toolbarImage.value = "/img/desktop/desktop_toolbar_off.svg"
    }
    else {
        toolbarImage.value = "/img/desktop/desktop_toolbar_on.svg"
    }

})

function showDrawBoard() {
    if (drawMode.value) {
        drawMode.value = false
    }
    else {
        desktopStore.showDrawBoard()
    }
}

function showToolBar() {
    hideToolBar.value = !hideToolBar.value
}

function quitDesktop() {
    isQuit = true
    removeTransparentListener()
    desktopStore.quitDesktop()
    if (window.electron) {
        window.electron.unignoreMosue()
    }
}

function ignoreMouseEvent() {
    window.electron.ignoreMouse()
}

function unignoreMouseEvent() {
    window.electron.unignoreMosue()
}

function addTransparentListener() {
    const box = document.querySelector("#transparent-view")
    box.addEventListener('mouseenter', ignoreMouseEvent)
    box.addEventListener('mouseleave', unignoreMouseEvent)
    box.addEventListener('touchstart', ignoreMouseEvent)


    const toolbar = document.querySelector("#desktop-toolbar")
    toolbar.addEventListener('mouseenter', unignoreMouseEvent)

    toolbar.addEventListener('pointerenter', unignoreMouseEvent)
}

function removeTransparentListener() {
    const box = document.querySelector("#transparent-view")
    if (box) {
        box.removeEventListener('mouseenter', ignoreMouseEvent)
        box.removeEventListener('mouseleave', unignoreMouseEvent)
        box.removeEventListener('touchdown', ignoreMouseEvent)
    }

    const toolbar = document.querySelector("#desktop-toolbar")
    if (toolbar) {
        toolbar.removeEventListener('mouseenter', unignoreMouseEvent)
        toolbar.removeEventListener('pointerenter', unignoreMouseEvent)
    }
}

function handleDragEnd() {
    if (toolX.value < 0) {
        toolX.value = 0; // 如果超出左边界，重置为 0
    } else if (toolX.value + toolW.value > window.innerWidth) {
        toolX.value = window.innerWidth - toolW.value; // 如果超出右边界，重置到屏幕右侧边界内
    }
    if (toolY.value < 0) {
        toolY.value = 0; // 如果超出上边界，重置为 0
    } else if (toolY.value + toolH.value > window.innerHeight) {
        toolY.value = window.innerHeight - toolH.value; // 如果超出下边界，重置到屏幕下侧边界内
    }
    if (window.electron) {
        let x = toolX.value
        let y = toolY.value
        window.electron.setFocusPosition({ x, y })
    }
}

async function screenShotDesktop() {
    if (window.electron) {
        // 隐藏桌面工具条
        showScreenShot.value = true
        hideToolBar.value = true
        previewMode.value = false
        setTimeout(async () => {
            let fileName = await window.electron.captureScreen()
            // // 退出桌面模式
            quitDesktop()
            let url = 'http://' + window.location.host + '/store/images/' + fileName
            const drawBoardStore = useDrawBoardStore()
            drawBoardStore.addImageToBlackBoard(url)
        }, 500)
    }
}

</script>


<style scoped>
.fullscreen-view {
    width: 100vw;
    height: 100vh;
    position: absolute;
    z-index: 0;
    background-color: transparent;
    /* border: 30px solid yellow; */
}

.draggable {
    position: absolute;
    box-shadow: none;
    z-index: 10000;
    user-select: none;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 70px;
    border: 1px solid #000;
    width: v-bind("toolW + 'px'");
    height: v-bind("toolH + 'px'");
}

.content {
    width: v-bind("toolW + 'px'");
    height: v-bind("toolH + 'px'");
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    align-content: center;
}
</style>