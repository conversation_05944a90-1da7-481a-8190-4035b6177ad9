<template>
    <div class="single-board-body">
        <div class="board-body">
            <RecordDrawBoard class="drawboard"  v-if="recordDrawBoardPainter" :painter="recordDrawBoardPainter"></RecordDrawBoard>
        </div>
        <div class="left-div"></div>
        <div class="bottom-div"></div>
        <div class="board-tool">
            <SingleToolbar></SingleToolbar>
        </div>
        <ColorSelect v-if="showColorSelect&&!showClassRoomRecord"></ColorSelect>
        <ClearScreenAlert v-if="!showClassRoomRecord"></ClearScreenAlert>
    </div>
</template>

<script setup>
import { nextTick,  onMounted, onUnmounted, } from 'vue'
import { storeToRefs } from 'pinia'
import { useDrawBoardStore } from '@/stores/drawboard_store'
import SingleToolbar from './SingleToolbar.vue'
import ClearScreenAlert from '@/components/TabBar/ClearScreenAlert.vue'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import ColorSelect from '@/components/TabBar/ColorSelect.vue'
import { useRecordDrawboardStore } from '@/stores/record_drawboard_store'
import RecordDrawBoard from './RecordBoard/RecordDrawBoard.vue'
const classroomUIStore = useClassroomUIStore()
const { showClassRoomRecord,} = storeToRefs(classroomUIStore)
const drawBoardStore = useDrawBoardStore()
const {showColorSelect} = storeToRefs(drawBoardStore)
const recordDrawBoardStore = useRecordDrawboardStore()
const { recordDrawBoardPainter } = storeToRefs(recordDrawBoardStore)
const props = defineProps({
    image:{
        default:'https://ss2.bdstatic.com/70cFvXSh_Q1YnxGkpoWK1HF6hhy/it/u=3604064158,1238835204&fm=253&gp=0.jpg'
    }
})

onMounted(()=>{
    recordDrawBoardStore.didStartBoard(window.innerWidth,window.innerHeight - 78,window.innerWidth * 0.6,window.innerHeight - 78 - 245)
    nextTick(()=>{
        if(recordDrawBoardPainter.value&&props.image){
            recordDrawBoardStore.addImageToBoard(props.image,false,true)
        }
    })
})

onUnmounted(()=>{
    recordDrawBoardStore.didEndBoard()
})
</script>

<style lang="scss" scoped>
.single-board-body{
    width: 100vw;
    height: 100vh;
    position: relative;
    background-color: #fff;
    .board-body{
        height: calc(100% - 78px);
        width: 100%;
        background-color: aquamarine;
        position: absolute;
        right: 0;
        top: 0;
    }
    .bottom-div{
        width: 60%;
        height: 245px;
        position: absolute;
        right: 0;
        bottom: 78px;
        background-color: aqua;
    }
    .left-div{
        width: 40%;
        height: calc(100% - 78px);
        position: absolute;
        left: 0;
        top: 0;
        background-color: aqua;
    }
    .board-tool{
        height: 78px;
        width: 100%;
        background-color: #000;
        position: absolute;
        bottom: 0;
    }
}
</style>