<template>
    <RBPAlert :zIndex="getZIndex('--toolbar-top-score-statistics-z-index')" :title="title + '明细'" width="60%" @close="close" @click.stop>
        <template v-slot:rbpDiv>
            <div class="content">
                <div class="head">
                    <div class="title" v-for="item in ['分数', '时间', '点评项', '点评标签']">
                        {{ item }}
                    </div>
                </div>
                <div class="con">
                    <RBPTableScroll :list="dataList" v-if="!isNoData" reverseColor="true">
                        <template v-slot:rbpDiv="slotProps">
                            <div class="row">
                                <div class="col">
                                    {{ slotProps.data.evaluationItemScore }}
                                </div>
                                <div class="col">
                                    {{ getDateTimeByOriginalTime(slotProps.data.createTime) }}
                                </div>
                                <div class="col">
                                    {{ slotProps.data.evaluationItemName }}
                                </div>
                                <div class="col">
                                    {{ slotProps.data.evaluationTagNames }}
                                </div>
                            </div>
                        </template>
                    </RBPTableScroll>
                    <RBPNoData v-else>
                        <template #rbpTitle>
                            <span>暂无数据</span>
                        </template>
                    </RBPNoData>
                </div>
            </div>
        </template>
    </RBPAlert>
</template>
<script setup>
import { ref, computed, onMounted, getCurrentInstance, watch, markRaw } from 'vue'
import { useScoreStatisticsStore } from '@/stores/score_statistics_store'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { storeToRefs } from 'pinia'
import { ClassRoomRequest } from '@/server_request/classroom_request'
import { ElLoading, ElMessage } from 'element-plus'
import { getDateTimeByOriginalTime } from '@/utils/date_time'
import RBPAlert from '@/components/baseComponents/RBPAlert.vue'
import { RBPColors } from '@/components/baseComponents/RBPColors.js'
import RBPSegButton from '@/components/baseComponents/RBPSegButton.vue'
import RBPTableScroll from '@/components/baseComponents/RBPTableScroll.vue'
import RBPNoData from '@/components/baseComponents/RBPNoData.vue'
import { getZIndex } from '@/components/baseComponents/RBPZIndex'
import { remoteControl } from '@/remote_control/remote_control'

const classroomUIStore = useClassroomUIStore()
const { showScoreStatisticsDetail, mainContentTopSpace } = storeToRefs(classroomUIStore)
const scoreStatisticsStore = useScoreStatisticsStore()
const { type, statisticId, title } = storeToRefs(scoreStatisticsStore)

const calcTop = computed(() => {
    return `calc(50% + ${mainContentTopSpace.value}px)`
})

const dataList = ref([])

onMounted(() => {
    loadData(1)
})

const isNoData = computed(() => {
    return dataList.value.length == 0
})

async function loadData(pageNo) {
    let loading = ElLoading.service({ background: 'transparent' })
    const res = await ClassRoomRequest.scoreStatisticsDetail(type.value, statisticId.value, pageNo)
    loading.close()
    if (res.code == 1) {
        dataList.value = res.data.list
    }
}

function close() {
    showScoreStatisticsDetail.value = false
    remoteControl.handleInteractState()
}
</script>
<style lang="scss" scoped>
@import "@/assets/scss/mixin.scss";

.content {
    width: 100%;
    height: 90%;
    display: flex;
    flex-direction: column;

    .head {
        height: 56px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        // background-color: yellow;
        // padding: 0 20px;

        .title {
            font-size: 30px;
            color: var(--text-color);
            width: calc(100% / 4);
            text-align: center;
        }
    }

    .con {
        flex: 1;
        overflow-y: auto;
        position: relative;
        // background-color: yellow;
        // padding: 0 20px;
    }
}

.row {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 0;

    .col {
        font-size: 27px;
        color: var(--secondary-text-color);
        width: calc(100% / 4);
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 10px;
    }
}
</style>