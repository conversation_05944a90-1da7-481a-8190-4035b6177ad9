<template>
    <div class="studentgroup">
        <div class="mainContainer">
            <div v-if="selectedGroup == '全部'" class="group-all">
                <div class="groupItem" v-for="(item, i) in groupStudentArray" :key="i">
                    <div class="groupTitle" :class="{ groupSelected: item.selected }" @click="groupClick(item)">
                        {{ item.groupName }}
                    </div>
                    <div class="groupGrid">
                        <StudentGridItem :student="stu" v-for="(stu, i) in item.groupStudent" :key="i">
                        </StudentGridItem>
                    </div>
                </div>
            </div>
            <div v-else class="group-grid">
                <StudentGridItem :student="stu" v-for="(stu, i) in groupList" :key="i"></StudentGridItem>
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useClassroomStore } from '@/stores/classroom.js'
import StudentGridItem from '@/components/StudentList/StudentGridItem.vue'
import { RBPColors } from '@/components/baseComponents/RBPColors.js'
import { remoteControl } from '@/remote_control/remote_control'
import utils from '@/utils/utils.js'

const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)
const groupStudentArray = ref([])
const groupNameList = ref([])
const groupList = ref([])
onMounted(() => {
    groupStudentArray.value = selectedClassroom.value.groupStudentArray

    groupNameList.value.push({ name: '全部', selected: true })
    groupStudentArray.value.forEach(item => {
        groupNameList.value.push({ name: item.groupName, selected: false })
    })
    groupStudentArray.value = groupStudentArray.value.sort((a, b) => {
        const nameA = a.groupName
        const nameB = b.groupName
        
        return utils.naturalCompare(nameA, nameB);
    })
})
const selectedGroup = ref('全部')
function selectGroup(item) {
    selectedGroup.value = item.name
    if (!item.selected) {
        groupNameList.value.forEach(g => {
            g.selected = false
        })
        item.selected = true

        if (item.name != '全部') {
            getListForGroupName(item.name)
        }
    }
}
function getListForGroupName(groupName) {
    groupStudentArray.value.forEach(item => {
        if (item.groupName == groupName) {
            groupList.value = item.groupStudent
        }
    })
}
function groupClick(item) {
    item.selected = !item.selected
    groupStudentArray.value.forEach(item => {
        item.groupStudent.forEach(s => {
            s.selected = false
        })
    })
    remoteControl.handleInteractState()
}
</script>
<style lang="scss" scoped>
@import "@/assets/scss/mixin.scss";

.studentgroup {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .mainContainer {
        border-radius: 26px;
        border: 1px solid var(--border-bar-color);
        box-sizing: border-box;
        padding: 20px;
        width: 100%;
        flex: 1;
        overflow-y: auto;

        .group-all {
            width: 100%;
            // height: 100%;
            display: flex;
            flex-direction: column;

            .groupItem {
                width: 100%;
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                justify-content: flex-end;

                .groupTitle {
                    padding: 0px 40px;
                    margin-left: 10px;
                    margin-top: 20px;
                    cursor: pointer;
                    background-color: var(--secondary-color);
                    border-radius: 15px;
                    color: var(--text-color);
                    height: 54px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 21px;
                }

                .groupSelected {
                    background-color: var(--primary-color);
                    color: var(--anti-text-color);
                }

                .groupGrid {
                    width: 100%;
                    display: grid;
                    grid-template-columns: repeat(10, 1fr);
                    padding-left: 10px;
                    padding-top: 12px;
                }
            }
        }

        .group-grid {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            align-items: flex-end;
        }
    }
}
</style>