import { BoardView } from "../board_view";
import { TriangleView } from "./triangle_view";
import { LineView } from "./line_view";
import { CircleView } from "./circle_view";
import * as THREE from 'three';
import { QuadrilateralType, QuadrilateralView } from "./quadrilateral_view";
import { BoardButton } from "../board_button";
import { DrawZ, PainterOrder } from "../draw_enums";
import { CubeView } from "./cube_view";
import { ConeView } from "./cone_view";
import { SphereView } from "./sphere_view";
import { CylinderView } from "./cylinder_view";
import { LightView } from "./light_view";

export const MathGeometry = {
    Line: 1,
    Angle: 2, // 角度
    Triangle: 3, // 三角形
    Circle: 4, // 圆形
    Oval: 5, // 椭圆
    Square: 6, //正方形
    Rectangle: 7, // 矩形
    Diamond: 8, // 菱形
    Parallel: 9, // 平行四边形
    Trapezoid: 10, // 梯形

    cone: 101, // 圆锥体
    cube: 102, // 立方体
    cuboid: 103, // 长方体
    cylinder: 104, // 圆柱体
    sphere: 105, // 球体

    light: 1001, // 灯光
    coneWithLight: 1002, // 圆锥体
    cubeWithLight: 1003, // 立方体
    cuboidWithLight: 1004, // 长方体
    cylinderWithLight: 1005, // 圆柱体
    sphereWithLight: 1006,  // 球体
}



export class MathRootView extends BoardView {

    constructor(application, pos, size) {
        super(application, pos, size)

        this.setup()
        this.prepareColor = 0x00ff00
        // this.addTestViews()

        // 立体几何
        // this.addSolidTestViews()

    }


    setup() {
        // 添加一个占满的 button 当选择图形后点击按钮 会添加一个图形
        let viewButton = new BoardButton(
            this.application,
            new THREE.Vector3(0, 0, 0),
            { width: this.size.width, height: this.size.height })
        viewButton.setRenderOrder(PainterOrder.bottom)
        viewButton.onClick(() => {
            // 取消所有view的编辑
            for (let view of this.subViews) {
                if (view === this.viewButton) {
                    continue
                }
                view.setOnEdit(false)
            }
            
            if (this.prepareToAdd) {
                let point = this.convertPoint(viewButton.clickPoint, DrawZ.objcZ)
                this.addPlaneViewWithType(this.prepareToAdd, point)
                this.prepareToAdd = null
                if (this.addCallback) {
                    this.addCallback()
                }
            }
            viewButton.visible = false
        })
        this.viewBtnId = viewButton.id
        viewButton.visible = false
        this.addSubView(viewButton)
        this.viewButton = viewButton
    }

    addPlaneCallback(callback) {
        this.addCallback = callback
    }

    addPlaneViewWithType(type, centerPoint,size,color) {
        
        
        let position = new THREE.Vector3(centerPoint.x, centerPoint.y, 0)
        let cameraInitSize = this.application?.deref()?.cameraInitSize
        let itemHeight = size?.height??(cameraInitSize.height * 0.4)

        switch (type) {
            case MathGeometry.Line: {
                let lineView = new LineView(
                    type,
                    this.application,
                    position,
                    size??{ width: cameraInitSize.width * 0.3, height: cameraInitSize.height * 0.1 },
                    color??this.prepareColor)
                this.addSubView(lineView)
                this.bringSubViewToFront(lineView)
                return lineView;
            }
            case MathGeometry.Angle: {
                let angleView = new TriangleView(
                    type,
                    this.application,
                    position,
                    size??{ width: cameraInitSize.height * 0.4, height: cameraInitSize.height * 0.4 },
                    color??this.prepareColor,
                    true)
                this.addSubView(angleView)
                this.bringSubViewToFront(angleView)
                return angleView;
            }
            case MathGeometry.Triangle: {
                let triangleView = new TriangleView(
                    type,
                    this.application,
                    position,
                    size??{ width: cameraInitSize.height * 0.4, height: cameraInitSize.height * 0.5 }, color??this.prepareColor)
                this.addSubView(triangleView)
                this.bringSubViewToFront(triangleView)
                return triangleView;
            }
            case MathGeometry.Circle: {
                let circleView = new CircleView(
                    type,
                    this.application,
                    position,
                    size??{ width: cameraInitSize.height * 0.4, height: cameraInitSize.height * 0.4 }, 
                    color??this.prepareColor)
                this.addSubView(circleView)
                this.bringSubViewToFront(circleView)
                return circleView;
            }
            case MathGeometry.Oval: {
                let ovalView = new CircleView(
                    type,
                    this.application,
                    position,
                    size??{ width: cameraInitSize.height * 0.4, height: cameraInitSize.height * 0.3 }, 
                    color??this.prepareColor, false,)
                this.addSubView(ovalView)
                this.bringSubViewToFront(ovalView)
                return ovalView;
            }
            case MathGeometry.Square: {
                // /// 正方形
                let squareView = new QuadrilateralView(
                    type,
                    this.application,
                    position,
                    size??{ width: cameraInitSize.height * 0.4, height: cameraInitSize.height * 0.4 },
                     QuadrilateralType.square, color??this.prepareColor)
                this.addSubView(squareView)
                this.bringSubViewToFront(squareView)
                return squareView;
            }
            case MathGeometry.Rectangle: {
                /// 矩形
                let rectangleView = new QuadrilateralView(
                    type,
                    this.application,
                    position,
                    size??{ width: cameraInitSize.height * 0.4, height: cameraInitSize.height * 0.3 },
                     QuadrilateralType.rectangle, color??this.prepareColor)
                this.addSubView(rectangleView)
                this.bringSubViewToFront(rectangleView)
                return rectangleView;
            }
            case MathGeometry.Diamond: {
                let diamondView = new QuadrilateralView(
                    type,
                    this.application,
                    position,
                    size??{ width: cameraInitSize.height * 0.4, height: cameraInitSize.height * 0.4 }, 
                    QuadrilateralType.diamond, color??this.prepareColor)
                this.addSubView(diamondView)
                this.bringSubViewToFront(diamondView)
                return diamondView;
            }
            case MathGeometry.Parallel: {
                let parallelView = new QuadrilateralView(
                    type,
                    this.application,
                    position,
                    size??{ width: cameraInitSize.height * 0.4, height: cameraInitSize.height * 0.4 }, 
                    QuadrilateralType.parallel, color??this.prepareColor)
                this.addSubView(parallelView)
                this.bringSubViewToFront(parallelView)
                return parallelView;
            }
            case MathGeometry.Trapezoid: {
                let trapezoidView = new QuadrilateralView(
                    type,
                    this.application,
                    position,
                    size??{ width: cameraInitSize.height * 0.4, height: cameraInitSize.height * 0.4 }, 
                    QuadrilateralType.trapezoid, color??this.prepareColor)
                this.addSubView(trapezoidView)
                this.bringSubViewToFront(trapezoidView)
                return trapezoidView;
            }
            case MathGeometry.cone:
            case MathGeometry.coneWithLight: {
                let coneView = new ConeView(
                    type,
                    this.application,
                    position,
                    size??{ width: itemHeight, height: itemHeight, z: itemHeight }, 
                    color??this.prepareColor, type === MathGeometry.cone)
                this.addSubView(coneView)
                this.bringSubViewToFront(coneView)
                return coneView;
            }
            case MathGeometry.cube:
            case MathGeometry.cubeWithLight:
            case MathGeometry.cuboid:
            case MathGeometry.cuboidWithLight: {
                let height = itemHeight
                if (type === MathGeometry.cuboid || type === MathGeometry.cuboidWithLight) {
                    height = itemHeight / 2
                }
                let cubeView = new CubeView(
                    type,
                    this.application,
                    position,
                    size??{ width: itemHeight, height, z: height },
                    color??this.prepareColor,
                    type === MathGeometry.cube || type === MathGeometry.cuboid)
                this.addSubView(cubeView)
                this.bringSubViewToFront(cubeView)
                return cubeView;
            }
            case MathGeometry.cylinder:
            case MathGeometry.cylinderWithLight: {
                let cylinderView = new CylinderView(
                    type,
                    this.application,
                    position,
                    size??{ width: itemHeight, height: itemHeight, z: itemHeight }, 
                    color??this.prepareColor, type === MathGeometry.cylinder)
                this.addSubView(cylinderView)
                this.bringSubViewToFront(cylinderView)
                return cylinderView;
            }
            case MathGeometry.sphere:
            case MathGeometry.sphereWithLight: {
                let sphereView = new SphereView(
                    type,
                    this.application,
                    position,
                    size??{ width: itemHeight, height: itemHeight, z: itemHeight },
                     color??this.prepareColor, type === MathGeometry.sphere)
                this.addSubView(sphereView)
                this.bringSubViewToFront(sphereView)

                return sphereView;
            }
            case MathGeometry.light: {
                let lightView = new LightView(
                    type,
                    this.application,
                    position,
                    size??{ width: itemHeight / 4, height: itemHeight / 4 })
                this.addSubView(lightView)
                return lightView;
            }
        }
    }

    addMathGeometry(type, color = 0x00ff00) {
        this.prepareToAdd = type
        this.prepareColor = color
        if (type == null) {
            this.viewButton.visible = false
        }
        else {
            this.viewButton.visible = true
        }
        
    }

    updateColor(color) {
        this.prepareColor = color
    }

    cancelEditWithout(subView) {
        for (let view of this.subViews) {
            if (view === this.viewButton) {
                continue
            }
            if (view !== subView) {
                view.setOnEdit(false)
            }
        }
        this.animate()
    }

    clearShape(){
        let tempView = [...this.subViews]
        for(let i = 0;i<tempView.length;i++){
            let view = tempView[i]
            if (view === this.viewButton) {
                continue
            }else{
                view.removeFromSuperView()
                view.dispose()
            }
        }
        tempView.length = 0
        this.animate()
    }

    onPointInside(point) {
        if (!this.visible) {
            return false
        }
        for (let subView of this.subViews) {
            if (subView.onPointInside(point)) {
                return true
            }
        }
        return false
    }

    onTouchDown(point) {
        for (let subView of this.subViews) {
            let target = subView.onTouchDown(point)
            if (target) {
                this.touchDownSubview = subView
                return target
            }
        }
        return view
    }

    onTouchMove(point) {
        if (this.touchDownSubview) {
            return this.touchDownSubview.onTouchMove(point)
        }
        return null
    }

    onTouchUp(point) {
        if (this.touchDownSubview) {
            this.touchDownSubview.onTouchUp(point)
            this.touchDownSubview = null
        }
        return null
    }


    dispose() {
        this.prepareToAdd = null
        super.dispose()
    }

    addTestViews() {
        let cameraInitSize = this.application?.deref()?.cameraInitSize

        let angleView = new TriangleView(
            MathGeometry.Angle,
            this.application,
            new THREE.Vector3(0, 0, 0),
            { width: cameraInitSize.height * 0.5, height: cameraInitSize.height * 0.5 }, this.prepareColor, true)
        this.addSubView(angleView)

        let triangleView = new TriangleView(
            MathGeometry.Triangle,
            this.application,
            new THREE.Vector3(0, 0, 0),
            { width: cameraInitSize.height * 0.4, height: cameraInitSize.height * 0.5 }, this.prepareColor)
        this.addSubView(triangleView)

        let lineView = new LineView(
            MathGeometry.Line,
            this.application,
            new THREE.Vector3(0, 0, 0),
            { width: cameraInitSize.width * 0.3, height: cameraInitSize.height * 0.1 }, this.prepareColor)
        this.addSubView(lineView)


        let circleView = new CircleView(
            MathGeometry.Circle,
            this.application,
            new THREE.Vector3(0, 0, 0),
            { width: cameraInitSize.height * 0.4, height: cameraInitSize.height * 0.4 }, this.prepareColor)
        this.addSubView(circleView)


        let ovalView = new CircleView(
            MathGeometry.Oval,
            this.application,
            new THREE.Vector3(0, 0, 0),
            { width: cameraInitSize.height * 0.4, height: cameraInitSize.height * 0.3 }, this.prepareColor, false)
        this.addSubView(ovalView)


        // /// 正方形
        let squareView = new QuadrilateralView(
            MathGeometry.Square,
            this.application,
            new THREE.Vector3(0, 0, 0),
            { width: cameraInitSize.height * 0.4, height: cameraInitSize.height * 0.4 }, QuadrilateralType.square, this.prepareColor)
        this.addSubView(squareView)


        /// 菱形
        let diamondView = new QuadrilateralView(
            MathGeometry.Diamond,
            this.application,
            new THREE.Vector3(0, 0, 0),
            { width: cameraInitSize.height * 0.4, height: cameraInitSize.height * 0.4 }, QuadrilateralType.diamond, this.prepareColor)
        this.addSubView(diamondView)

        /// 矩形
        let rectangleView = new QuadrilateralView(
            MathGeometry.Rectangle,
            this.application,
            new THREE.Vector3(0, 0, 0),
            { width: cameraInitSize.height * 0.4, height: cameraInitSize.height * 0.3 }, QuadrilateralType.rectangle, this.prepareColor)
        this.addSubView(rectangleView)


        /// 平行四边形
        let parallelView = new QuadrilateralView(
            MathGeometry.Parallel,
            this.application,
            new THREE.Vector3(0, 0, 0),
            { width: cameraInitSize.height * 0.4, height: cameraInitSize.height * 0.4 }, QuadrilateralType.parallel, this.prepareColor)
        this.addSubView(parallelView)

        let trapezoidView = new QuadrilateralView(
            MathGeometry.Trapezoid,
            this.application,
            new THREE.Vector3(0, 0, 0),
            { width: cameraInitSize.height * 0.4, height: cameraInitSize.height * 0.4 }, QuadrilateralType.trapezoid, this.prepareColor)
        this.addSubView(trapezoidView)

    }


    addSolidTestViews() {

        let itemHeight = this.application?.deref()?.cameraInitSize.height * 0.4
        let z = itemHeight / 2
        // let cubeView = new CubeView(
        //     this.application,
        //     new THREE.Vector3(0, 0, 0),
        //     { width: itemHeight, height: itemHeight, z:  z }, this.prepareColor, false)
        // this.addSubView(cubeView)

        // let coneView =  new ConeView(
        //     this.application,
        //     new THREE.Vector3(0, 0, 0),
        //     { width: itemHeight, height: itemHeight, z:  itemHeight }, this.prepareColor, false)
        // this.addSubView(coneView)

        let sphereView = new SphereView(
            MathGeometry.sphere,
            this.application,
            new THREE.Vector3(0, 0, 0),
            { width: itemHeight, height: itemHeight, z: itemHeight }, this.prepareColor, false)
        this.addSubView(sphereView)

        // let cylinderView = new CylinderView(
        //     this.application,
        //     new THREE.Vector3(0, 0, 0),
        //     { width: itemHeight, height: itemHeight, z: itemHeight }, this.prepareColor, false)
        // this.addSubView(cylinderView)

        let lightView = new LightView(
            MathGeometry.light,
            this.application,
            new THREE.Vector3(- itemHeight / 2, 0, 1),
            { width: itemHeight / 8, height: itemHeight / 8 })
        this.addSubView(lightView)
    }


}