import { BoardTool } from "./board_tool";
import { BoardView } from "./board_view";
import * as THREE from 'three';
import {DrawZ,  PainterOrder } from "./draw_enums";
import { BoardButton } from "./board_button";
const MoveTag = {
    none: -1,
    move: 1,
}
export class BoardClipImage extends BoardView {
    
    constructor(application, pos, size, url, outLineSize,onDoubleClick) {
        super(application, pos, size);
        this.onEdit = false
        this.nowScale = 1
        this.minScale = 0.5
        this.maxScale = 2
        this.scalable = false
        this.viewBtns = []
        
        // this.clipRightPlane = new THREE.Plane(pos, outLineSize.width / 2); // 隐藏 X > 0 的区域
        // this.clipLeftPlane = new THREE.Plane(pos, outLineSize.width / 2); // 隐藏 X > 0 的区域
        // this.clipTopPlane = new THREE.Plane(pos, outLineSize.height / 2);
        // this.clipBottomPlane = new THREE.Plane(pos, outLineSize.height / 2);
        this.outLineSize = outLineSize
        this.initSize = this.size
        this.onDoubleClick = onDoubleClick
        this.initA = application
        this.createViewButton(application)
        
        // 
        if(url){
            //"https://st5.depositphotos.com/1026266/70754/i/1600/depositphotos_707542630-stock-photo-businessman-touching-huge-display-internet.jpg"
            this.setImage(url)
        }
    }


    setOnEdit(onEdit) {
        this.onEdit = onEdit
        this.draggable = this.onEdit
        this.scalable = this.onEdit
        
    }

    setFull(value){
        this.setOnEdit(false)
        this.imageMesh.material.clippingPlanes = []
        this.changeScale(1)
        this.animate()
        if(!value){
            this.changeClipingLines()
        }
    }


    setImage(imageUrl) {
        this.imageUrl = imageUrl
        let textureId = btoa(imageUrl)
        let application = this.application?.deref()

        application?.textureCache.loadImageTexture(textureId, imageUrl, (texture) => {

            this.setImageTexture(texture)
        })
    }

    changeClipingLines(){
        
        const pos = new THREE.Vector3()
        this.getWorldPosition(pos)
        let  outLineSize = this.outLineSize        
        this.clipRightPlane = new THREE.Plane(new THREE.Vector3(-1, 0, 0),pos.x+outLineSize.width/ 2); // 隐藏 X > 0 的区域
        this.clipLeftPlane = new THREE.Plane(new THREE.Vector3(1, 0, 0), -pos.x+outLineSize.width / 2); // 隐藏 X > 0 的区域
        this.clipTopPlane = new THREE.Plane(new THREE.Vector3(0, -1, 0), pos.y+outLineSize.height/ 2);
        this.clipBottomPlane = new THREE.Plane(new THREE.Vector3(0, 1, 0),-pos.y+outLineSize.height / 2);
        this.imageMesh.material.clippingPlanes = [this.clipRightPlane,this.clipLeftPlane,this.clipTopPlane,this.clipBottomPlane]
        this.animate()
    }

    setImageTexture(texture) {
        this.texture = texture
        if (!this.imageMesh) {
            let imageGeometry = new THREE.PlaneGeometry(this.size.width, this.size.height)
            this.imageGeometry = imageGeometry
            this.imageMeterial = new THREE.MeshBasicMaterial({
                map: texture,
                precision: 'highp',
                clippingPlanes: [],
                clipIntersection: false,
            })
            this.imageMesh = new THREE.Mesh(imageGeometry, this.imageMeterial)
            let imageGroup = new THREE.Group()

            imageGroup.renderOrder = this.renderOrder + 1
            imageGroup.add(this.imageMesh)
            this.add(imageGroup)
            this.imageGroup = imageGroup
        }
        else {
            this.imageMesh.material.map = texture
        }
        this.animate()
    }

    changeScale(newScale) {
        if(newScale<this.minScale || newScale>this.maxScale || newScale == this.nowScale){
            return 
        }
        this.nowScale = newScale
        let z = this.imageMesh.rotation.z
        let imageGeometryNew = new THREE.PlaneGeometry(this.initSize.width * newScale, this.initSize.height * newScale)
        this.imageGroup.remove(this.imageMesh)
        this.imageMesh.clear()
        this.imageMesh = new THREE.Mesh(imageGeometryNew, this.imageMeterial)
        this.imageMesh.rotation.z = z
        this.imageGroup.add(this.imageMesh)
        this.setSize({width:this.initSize.width * newScale,height:this.initSize.height * newScale})
        this.createViewButton(this.initA)
        this.animate()
    }

    setRenderOrder(renderOrder) {
        if (this.imageGroup) {
            this.imageGroup.renderOrder = renderOrder - 1
        }
        if (this.textLabel) {
            this.textLabel.renderOrder = renderOrder
        }
        super.setRenderOrder(renderOrder)
    }

    createViewButton(application) {
        // 计算实际内容区域的大小（排除按钮的空间和额外间距）
        let contentWidth = this.size.width
        let contentHeight = this.size.height

        // 调整内容区域位置，向左上移动
        let offsetX = 0
        let offsetY = 0

        let viewButton = new BoardButton(
            application,
            new THREE.Vector3(offsetX, offsetY, 0),
            { width: contentWidth, height: contentHeight })
        viewButton.renderOrder = PainterOrder.bottom
        viewButton.onDoubleClick(()=>{
            this.onDoubleClick()
        })
        this.viewButton = viewButton
        
        this.addSubView(viewButton)
        
    }






    onClick(callback) {
        this.onClickCallback = callback
    }



    onPointInside(point) {
        if (!this.visible) {
            return false
        }
        if (this.onEdit) {
            for (let subView of this.subViews) {
                if (subView.onPointInside(point)) {
                    return true
                }
            }
        }
        return super.onPointInside(point)
    }

    onTouchDown(point) {
        let view = super.onTouchDown(point)
        if (!this.onEdit) {
            return view
        }
        let cvtPoint = this.convertPoint(point, DrawZ.objcZ)
        this.touchDownPoint = cvtPoint        
        if (view === this) {
            this.moveTag = MoveTag.move
        }
        return view
    }

    onWhell(point){
        return this
    }

    onTouchMove(point) {
        if (!this.onEdit || !this.touchDownPoint) {
            return super.onTouchMove(point)
        }
        let divPoint = point
        let cvtPoint = this.convertPoint(point, DrawZ.objcZ)
        let spaceX = cvtPoint.x - this.touchDownPoint.x
        let spaceY = cvtPoint.y - this.touchDownPoint.y
        this.touchDownPoint = cvtPoint

        if (this.moveTag === MoveTag.move) {
            let x = this.position.x + spaceX;
            let y = this.position.y + spaceY;
            let xMax = this.size.width / 2 + this.outLineSize.width / 2
            let yMax = this.size.height / 2 + this.outLineSize.height / 2
            if(x > xMax*0.9){
                x = xMax*0.9
            }  else if(x < -xMax * 0.9){
                x = -xMax * 0.9
            }
            
           if( y > yMax*0.9){
                y = yMax*0.9
           } else if(y < -yMax * 0.9){
              y  = -yMax * 0.9
            }
            this.position.set(x, y, this.position.z);
            return super.onTouchMove(point);
        }

        return super.onTouchMove(divPoint)
    }

    onTouchUp(point) {
        this.touchDownPoint = null
        this.moveTag = MoveTag.none
        return super.onTouchUp(point)
    }


    dispose() {
        BoardTool.disposeGroup(this.imageGroup)
        super.dispose()
    }

}