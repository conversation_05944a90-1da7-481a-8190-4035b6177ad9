


import { BoardColor, DrawMode, EditMode, PXLineWidth, PxEraserWidths, EraserWorldWidths, BoardLineWidth, PenStyle, PXLineWidthShow, BoardLineRange, PxEraserWidthShow, EraserWorldWidthRange } from "@/drawboard/draw_enums"
import { defineStore, storeToRefs } from "pinia"
import { computed, markRaw, ref } from "vue"
import { UIFrames, ViewStatus } from "@/classroom/frame_enums"
import { BlackBoardApplication } from "@/components/BlackBoard/backboard_application"
import { Interact } from "@/classroom/interact_enums"
import { useDesktopStore } from "./desktop_store"
import { serverHost } from "@/server_request/server_urls"
import { PaperPenApplication } from "@/components/PaperPen/paper_pen_application"
import { useBlackBoardStore } from "@/stores/black_board_store"
import { useRecordDrawboardStore } from "./record_drawboard_store"
import { blackboardSerializer } from "@/components/BlackBoard/blackboard_serializer"
import { blackboardDeserializer } from "@/components/BlackBoard/blackboard_deserializer"

/// 自由绘制状态管理
/// 管理黑板和书写互动老师批注
export const useDrawBoardStore = defineStore('draw_board_store', () => {

    const lineColor = ref(BoardColor[3]) //默认画笔为红色

    const lineWidth = ref(BoardLineRange.default)  // 0.003 这里是相对three.js 世界的比例

    const pxLineWidth = ref(PXLineWidthShow.default)

    const pxEraserWidth = ref(PxEraserWidthShow.boardInit) // 橡皮画笔粗细

    const editMode = ref(EditMode.Dragging)

    const drawMode = ref(DrawMode.pen)

    const showColorSelect = ref(false)

    const showEraserSelect = ref(false)

    const showClearSelect = ref(false)

    const imageOnEdit = ref(null)

    /// 纸笔互动画布
    const paperPenPainter = ref(null)

    const recordDrawBoard = useRecordDrawboardStore()
    const { recordDrawBoardPainter } = storeToRefs(recordDrawBoard) //随堂记录画板painter

    /// 显示2D平面图形
    const plane2DSelector = ref({
        showSelector: false, //是否显示选择框
        selectedItem: null, //选中的图形
    })

    /// 显示书写辅助线
    const writingGridsSelector = ref({
        showSelector: false, //是否显示选择框
        selectedItem: null, //选中的图形
    })

    /// 纸笔互动视图状态
    const paperViewState = ref(ViewStatus.closed)

    /// 黑板画布
    const blackBoardPainter = ref(null)

    /// 全部展示/随机展示切换
    const cancelRandom = ref(false)

    /// 当前选择的照片group 遥控器上传照片选择使用
    const imgsGroup = ref(null)

    /// 当前选择照片数组
    const uploadImageList = ref([])

    /// 刷新 照片选择列表使用
    const imgSelectorRefresh = ref(0)

    /// 黑板预览模式
    const previewMode = ref(false)

    /// 是否显示照片选择栏
    const showImgsSelector = ref({
        show: false,
        onLeft: true
    })

    const penStyle = ref(PenStyle.Line2)

    const customColor = ref('#B744D4')

    /// 打开纸笔互动时，记录画笔颜色，画笔改为红色
    const boardLineColor = ref(null)

    const serializeData = ref({})

    function setPaperpenLineColor() {
        boardLineColor.value = lineColor.value
        // console.log('setPaperpenLineColor', lineColor.value)
        if (lineColor.value == BoardColor[1]) {
            updateLineAndColor(pxLineWidth.value, BoardColor[3])
        }
    }

    function resetPaperpenLineColor() {
        updateLineAndColor(pxLineWidth.value, boardLineColor.value)
    }

    ///更新橡皮粗细
    function updateEraserWidth(width) {
        let percent = (width - PxEraserWidthShow.min)/PxEraserWidthShow.range
        let eraserWidth = EraserWorldWidthRange.min + EraserWorldWidthRange.range * percent
        pxEraserWidth.value = width
        // 更新纸笔互动书写参数
        if (paperPenPainter.value) {
            paperPenPainter.value.updateEraserWidth(eraserWidth*3)
        }
    
        // 更新单独画板书写参数
        if (recordDrawBoardPainter.value) {
            recordDrawBoardPainter.value.updateEraserWidth(eraserWidth)
        }
        // 更新黑板书写参数
        if (blackBoardPainter.value) {
            // if(!previewMode.value){
            //     eraserWidth = eraserWidth/3
            // }
            blackBoardPainter.value.updateEraserWidth(eraserWidth)
        }
        
    }

    ///更新画笔粗细和颜色
    function updateLineAndColor(width, color) {
        
        
        const  percent = (width - PXLineWidthShow.min)/PXLineWidthShow.range 
        
        const  boardLine =Math.round( BoardLineRange.range * percent + BoardLineRange.min)
        updateLineStyle(boardLine, color)
        pxLineWidth.value = width
    }

    /// 设置默认的书写参数
    function setupDefaultStyle() {
        // getDefaultColor()
        //默认颜色为红色
        lineColor.value = BoardColor[3] 
        updateLineStyle(lineWidth.value, lineColor.value)
        setEditMode(editMode.value)
        setDrawMode(drawMode.value)
        updateEraserWidth(pxEraserWidth.value)
        setPenStyle(PenStyle.Line2)
    }

    function getDefaultColor() {
        const blackBoardStore = useBlackBoardStore()
        if (blackBoardStore.color == '#ffffff') {
            lineColor.value = '#000000'
        } else {
            lineColor.value = blackBoardStore.color
        }
    }

    function resetStyle() {
        lineColor.value = BoardColor[1]
        lineWidth.value = BoardLineWidth[1]  // 0.003 这里是相对three.js 世界的比例
        pxLineWidth.value = PXLineWidth[1]
        pxEraserWidth.value = PxEraserWidths[1]
        editMode.value = EditMode.Dragging
        drawMode.value = DrawMode.pen
    }


    function updateLineStyle(width, color) {
        lineWidth.value = width
        lineColor.value = color
        // 更新纸笔互动书写参数
        if (paperPenPainter.value) {
            paperPenPainter.value.setLineStyle(width, color)
        }
        // 更新黑板书写参数
        if (blackBoardPainter.value) {
            blackBoardPainter.value.setLineStyle(width, color)
        }
        // 更新单独画板书写参数
        if (recordDrawBoardPainter.value) {

            recordDrawBoardPainter.value.setLineStyle(width, color)
        }
    }

    function setEditMode(mode) {
        editMode.value = mode
        // 更新纸笔互动书写参数
        if (paperPenPainter.value) {
            paperPenPainter.value.setEditMode(mode)
        }
        // 更新黑板书写参数
        if (blackBoardPainter.value) {
            blackBoardPainter.value.setEditMode(mode)
        }
        // 更新单独画板书写参数
        if (recordDrawBoardPainter.value) {
            recordDrawBoardPainter.value.setEditMode(mode)
        }
    }

    function setDrawMode(mode) {
        drawMode.value = mode
        if (paperPenPainter.value) {
            paperPenPainter.value.setDrawMode(mode)
        }

        if (blackBoardPainter.value) {
            blackBoardPainter.value.setDrawMode(mode)
        }
        // 更新单独画板书写参数
        if (recordDrawBoardPainter.value) {
            recordDrawBoardPainter.value.setDrawMode(mode)
        }
    }

    function setPenStyle(style) {
        penStyle.value = style
        if (paperPenPainter.value) {
            paperPenPainter.value.setPenStyle(style)
        }
        if (blackBoardPainter.value) {
            blackBoardPainter.value.setPenStyle(style)
        }
        // 更新单独画板书写参数
        if (recordDrawBoardPainter.value) {
            recordDrawBoardPainter.value.setPenStyle(style)
        }
    }


    function didStartClass() {
        if (blackBoardPainter.value) {
            blackBoardPainter.value.dispose()
            blackBoardPainter.value = null
        }
        let width = window.innerWidth * window.devicePixelRatio
        let height = UIFrames.getBlackBoardHeight() * window.devicePixelRatio
        blackBoardPainter.value = markRaw(new BlackBoardApplication(width, height, serverHost.canvasResolution))
        setupDefaultStyle()
    }

    function didStopClass() {
        if (paperPenPainter.value) {
            paperPenPainter.value.dispose()
            paperPenPainter.value = null
        }

        if (blackBoardPainter.value) {
            blackBoardPainter.value.dispose()
            blackBoardPainter.value = null
        }

        resetStyle()

        paperViewState.value = ViewStatus.closed

        cancelRandom.value = false

        imgSelectorRefresh.value = 0

        imgsGroup.value = null

        uploadImageList.value = []

        showImgsSelector.value.show = false

        showColorSelect.value = false

        showEraserSelect.value = false
        showClearSelect.value = false

        previewMode.value = false
    }

    function clearDrawBoard() {
        if (paperViewState.value === ViewStatus.normal) {
            if (paperPenPainter.value) {
                paperPenPainter.value.rootView.clearAllLines()
                paperPenPainter.value.animate()
            }
            return
        }
        if (blackBoardPainter.value) {
            blackBoardPainter.value.rootView.clearAllLines()
            blackBoardPainter.value.animate()
        }
    }

    function didStartWriteInteract(students, interact) {
        let width = window.innerWidth * window.devicePixelRatio
        let height = UIFrames.getPaperPenBoardHeight() * window.devicePixelRatio
        /// 这里要用 markRaw，vue如果监听three的对象会报错
        paperPenPainter.value = markRaw(new PaperPenApplication(students, width, height, serverHost.canvasResolution))
        if (interact === Interact.dictation) {
            paperViewState.value = ViewStatus.minimize
        }
        else {
            paperViewState.value = ViewStatus.normal
        }
        setupDefaultStyle()
    }

    function didEndInteract() {
        if (paperPenPainter.value) {
            paperPenPainter.value.dispose()
            paperPenPainter.value = null
        }
        paperViewState.value = ViewStatus.closed
        cancelRandom.value = false
    }

    function minimizePaperPenView() {
        if (paperViewState.value === ViewStatus.normal) {
            paperViewState.value = ViewStatus.minimize
        }
    }

    function normalPaperPenView() {
        if (paperViewState.value === ViewStatus.minimize) {
            paperViewState.value = ViewStatus.normal
        }
    }

    function addTeachPlan(teachPlan) {
        if (blackBoardPainter.value) {
            blackBoardPainter.value.addTeachPlan(teachPlan)

            const desktopStore = useDesktopStore()
            desktopStore.showDrawBoard()
        }
    }

    function addImageToBlackBoard(imageUrl) {
        if (blackBoardPainter.value) {
            blackBoardPainter.value.addImage(imageUrl)
        }
    }

    function saveBlackBoard() {
        if (blackBoardPainter.value) {
           serializeData.value =  blackboardSerializer.serialize(blackBoardPainter.value)
        }
    }

    function deserializeBlackBoard(data) {
        if (blackBoardPainter.value) {
            // blackboardDeserializer.deserialize(blackBoardPainter.value, data)
            // TODO test
            blackboardDeserializer.deserialize(blackBoardPainter.value, serializeData.value)
        }
    }

    return {
        lineColor, lineWidth, editMode, drawMode, pxLineWidth, showColorSelect, paperViewState,
        paperPenPainter, blackBoardPainter, cancelRandom, showImgsSelector, imgsGroup, uploadImageList, imgSelectorRefresh, previewMode,
        showEraserSelect, pxEraserWidth, penStyle, customColor, plane2DSelector,showClearSelect,writingGridsSelector,imageOnEdit,serializeData,
        setEditMode, setDrawMode, setPenStyle, minimizePaperPenView, clearDrawBoard, updateLineAndColor,
        didStartWriteInteract, didEndInteract, didStartClass, didStopClass,
        addTeachPlan, addImageToBlackBoard, normalPaperPenView, updateEraserWidth, setupDefaultStyle,
        setPaperpenLineColor, resetPaperpenLineColor, saveBlackBoard, deserializeBlackBoard
    }
})


