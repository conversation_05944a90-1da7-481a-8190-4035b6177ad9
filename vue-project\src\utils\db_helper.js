import { loginInstance } from "@/login_instance/login_instance"

// 黑板默认颜色
export const defaultColor = '#0b423d'

export const dbHelper = {

    /**
     * 黑板颜色
     */
    async getBlackBoardColorByTeacherId() {
        let data = loginInstance.configData
        if (data.blackBoardColor) {
            let colorList = JSON.parse(data.blackBoardColor)
            let colorItem = colorList.find(item => item.teacherId == loginInstance.teacher.teacherId)
            return colorItem?.color ?? defaultColor
        } else {
            return defaultColor
        }
    },

    async updateBlackBoardColorByTeacherId(color) {
        let teacherId = loginInstance.teacher.teacherId
        if(!teacherId) {
            return
        }
        let data = loginInstance.configData
        if (data.blackBoardColor) {
            let colorList = JSON.parse(data.blackBoardColor)
            let colorItem = colorList.find(item => item.teacherId == teacherId)
            if (!colorItem) {
                colorList.push({
                    teacherId: teacherId,
                    color: defaultColor
                })
            }
            data.blackBoardColor = JSON.stringify(colorList)
        } else {
            data.blackBoardColor = JSON.stringify([{
                teacherId: teacherId,
                color: defaultColor
            }])
        }

        let colorList = JSON.parse(data.blackBoardColor)
        let colorItem = colorList.find(item => item.teacherId == teacherId)
        colorItem.color = color

        data.blackBoardColor = JSON.stringify(colorList)
        loginInstance.configData = data
        await loginInstance.saveToDisk()
    },

    /**
     * 老师添加分数类型相关操作（单独加分 同时加分）
     */
    async setAddScoreTypeByTeacherId() {
        let data = loginInstance.configData
        let itemList = data.addScoreType ? JSON.parse(data.addScoreType) : []
        if (itemList.length > 0) {
            let currentItem = itemList.find(item => item.teacherId == loginInstance.teacher.teacherId)
            if (!currentItem) {
                itemList.push({
                    teacherId: loginInstance.teacher.teacherId,
                    status: '2'
                })
                data.addScoreType = JSON.stringify(itemList)
            }
        } else {
            data.addScoreType = JSON.stringify([{
                teacherId: loginInstance.teacher.teacherId,
                status: '2'
            }])
        }

        loginInstance.configData = data
        await loginInstance.saveToDisk()
    },

    async getAddScoreTypeByTeacherId() {
        let data = loginInstance.configData
        if (data.addScoreType) {
            let itemList = JSON.parse(data.addScoreType)
            return itemList.find(item => item.teacherId == loginInstance.teacher.teacherId)
        }
    },

    async updateAddScoreTypeByTeacherId(status) {
        let data = loginInstance.configData
        if (data.addScoreType) {
            let itemList = JSON.parse(data.addScoreType)
            let item = itemList.find(item => item.teacherId == loginInstance.teacher.teacherId)
            item.status = status

            data.addScoreType = JSON.stringify(itemList)
        }

        loginInstance.configData = data
        await loginInstance.saveToDisk()
    },

    /**
     * 存classType 1 行政班 2课程班
     */
    async getClassType() {
        let data = loginInstance.configData
        return data.classType ?? 1
    },

    async addClassType() {
        let data = loginInstance.configData
        if (!data.classType) {
            data.classType = 1
            loginInstance.configData = data
            await loginInstance.saveToDisk()
        }
    },

    async updateClassType(type) {
        let data = loginInstance.configData
        data.classType = type
        loginInstance.configData = data
        await loginInstance.saveToDisk()
    },

    /**
     *  存行政班年级，按照App存
     */
    async getGrade() {
        let data = loginInstance.configData
        return data.classId ?? 0
    },

    async updateGrade(classId) {
        let data = loginInstance.configData
        data.classId = classId
        loginInstance.configData = data
        await loginInstance.saveToDisk()
    },

    async addGrade() {
        let data = loginInstance.configData
        if (!data.classId) {
            data.classId = 0
            loginInstance.configData = data
            await loginInstance.saveToDisk()
        }
    },

    /**
     *  课程班年级 按照老师和科目存
     */
    async getAttendGrade(teacherId, subjectId) {
        let data = loginInstance.configData
        let attendClassList = JSON.parse(data.attendClass)
        let attendClassItem = attendClassList.find(item => item.teacherId == teacherId && item.subjectId == subjectId)
        return attendClassItem.classId
    },

    async updateAttendGrade(teacherId, subjectId, attendClassId) {
        let data = loginInstance.configData
        let attendClassList = JSON.parse(data.attendClass)
        let attendClassItem = attendClassList.find(item => item.teacherId == teacherId && item.subjectId == subjectId)
        attendClassItem.classId = attendClassId
        data.attendClass = JSON.stringify(attendClassList)

        loginInstance.configData = data
        await loginInstance.saveToDisk()
    },

    async addAttendGrade(teacherId, subjectId) {
        let data = loginInstance.configData
        if (data.attendClass) {
            let attendClassList = JSON.parse(data.attendClass)
            let attendClassItem = attendClassList.find(item => item.teacherId == teacherId && item.subjectId == subjectId)
            if (!attendClassItem) {
                attendClassList.push({
                    teacherId: teacherId,
                    subjectId: subjectId,
                    classId: 0
                })
            }
            data.attendClass = JSON.stringify(attendClassList)
        } else {
            data.attendClass = JSON.stringify([{ teacherId: teacherId, subjectId: -1, classId: 0 }])
        }

        loginInstance.configData = data
        await loginInstance.saveToDisk()
    },

    /**
     * 老师对应科目相关数据
     */
    async addUserConfig(teacherId) {
        let data = loginInstance.configData
        if (data.userConfig) {
            let userList = JSON.parse(data.userConfig)
            let userItem = userList.find(item => item.teacherId == teacherId)
            if (!userItem) {
                userList.push({
                    teacherId: teacherId,
                    subjectId: -1
                })
            }
            data.userConfig = JSON.stringify(userList)
        } else {
            data.userConfig = JSON.stringify([{ teacherId: teacherId, subjectId: -1 }])
        }

        loginInstance.configData = data
        await loginInstance.saveToDisk()
    },

    async getUserConfig(teacherId) {
        let data = loginInstance.configData
        let userList = JSON.parse(data.userConfig)
        let userItem = userList.find(item => item.teacherId == teacherId)
        return userItem
    },

    async updateUserConfig(teacherId, subjectId) {
        let data = loginInstance.configData
        if (data.userConfig) {
            let userList = JSON.parse(data.userConfig)
            let userItem = userList.find(item => item.teacherId == teacherId)
            userItem.subjectId = subjectId
            data.userConfig = JSON.stringify(userList)
        }

        loginInstance.configData = data
        await loginInstance.saveToDisk()

        // 添加对应的课程班
        this.addAttendGrade(teacherId, subjectId)
    }
}