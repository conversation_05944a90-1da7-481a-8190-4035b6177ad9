<template>
	<div class="record-word-player" ref="root">
		<div class="player" :style="{ position:'relative', width: canvasWidth + 'px', height: canvasHeight + 'px', }">
			<div class="userImg" v-show="!isPlay && step == 0"
				:style="{ width: canvasWidth + 'px', height: canvasHeight + 'px',  }">
				<img :src="userImg" height="100%" />
			</div>
			<div class="userImg" v-if="originImg" v-show="isPlay || step !== 0"
				:style="{ width: canvasWidth + 'px', height: canvasHeight + 'px',  }">
				<img :src="originImg + '?x-oss-process=image/resize,w_' + canvasHeight" height="100%" />
			</div>
			<canvas id="myCanvas" class="board" :width="canvasWidth" :height="canvasHeight"
				:style="{ position:'absolute', width: canvasWidth + 'px', height: canvasHeight + 'px', zIndex:1001}"></canvas>

			
		</div>
		<div class="menus" :style="{ width: canvasWidth + 'px',  }">

				<el-icon class="el-icon-circle-close" @click="closePlayer()">
					<CircleClose />
				</el-icon>
				<div @click="play" class="btnPlay">
					<img v-if="isPlay" src="../assets/pause.png" />
					<img v-else src="../assets/play.png" />
				</div>
				<words-slider width="100%" @pbar-drag="setPercent" @pbar-seek="setPercent" :percent="percent">
				</words-slider>
				<div class="speed">
					<span class="btn_speed" @click="setSpeed(1)" :class="{ active: speed == 1 }">4X</span>
					<span class="btn_speed" @click="setSpeed(10)" :class="{ active: speed == 10 }">2X</span>
					<span class="btn_speed" @click="setSpeed(20)" :class="{ active: speed == 20 }">1X</span>
				</div>
			</div>
	</div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, computed, nextTick } from 'vue';
import Slider from './Slider.vue';
import WordsSlider from './WordsSlider.vue';
import { ClassroomRecordRequest } from '@/server_request/classroom_record';
import { decode } from '../js/base64.js';
let timer, n, len, x1, y1, w1, x2, y2, w2, tx, ty, tw;
let gapData = 2;
let isStart = 0;
let context = null;
let ratioX = 1, ratioY = 1;
let T9W_H = 74;
let T9W_H_TAL = 82
let T10 = 75;
let C5W = 60;
let X9 = 71;
const props = defineProps({
	recordId: Number,
	userImg: String,
	originImg: String,
})
const emits = defineEmits(['closePlayer'])
let deviceWidth = 21000
let deviceHeight = 29700
const speed = ref(10)
let offset = 0
let currStuInfo = null
let currDeviceType = -1
let currPageIndex = 0
const step = ref(0)
const percent = ref(0)
let toStep = 0
let trails = []
let blocks = []
const isPlay = ref(false)

const canvasHeight = computed(() => 786)
const canvasWidth = computed(() => {
	return parseInt((canvasHeight.value) * deviceWidth / deviceHeight)
})
const gap = computed(() => Math.ceil(100 / trails.length))
onUnmounted(() => {
	clearTimeout(timer)
})
onMounted(() => {
	let speedData = localStorage.getItem("playSpeed")
	if (speedData) {
		speed.value = speedData
	}
	nextTick(() => {
		initCanvas()
	})
	locadBlocks()
})
function closePlayer() {
	isStart = 0
	emits('closePlayer')
}
function initCanvas() {
	let canvas = document.getElementById('myCanvas')
	context = canvas.getContext('2d')
	context.translate(0.5, 0.5);
	context.fillStyle = 'rgba(255, 255, 255, 0)';
}
function setPercent(percent) {
	isStart = 0
	isPlay.value = false
	context.clearRect(0, 0, canvasWidth.value, canvasHeight.value);
	toStep = parseInt(percent * trails.length / 100)
	step.value = 0
	speed.value = 0
	playTrails()
}
function checkPre() {
	currPageIndex -= 1
	initData()
}
function checkNext() {
	currPageIndex += 1
	initData()
}
function setSpeed(speedData) {
	speed.value = speedData
	localStorage.setItem("playSpeed", speedData)
}
function play() {
	if (speed.value == 0) {
		speed.value = 10
	}
	if (props.recordId) { // 中文练字逻辑
		isPlay.value = !isPlay.value
		if (isPlay.value) {
			playTrails()
		} else {
			timer && clearTimeout(timer)
			isStart = 0
		}
	} else { // 原逻辑
		let block_key = currStuInfo[currPageIndex].blockKey
		if (block_key) {
			isPlay.value = !isPlay.value
			if (isPlay.value) {
				playTrails()
			} else {
				timer && clearTimeout(timer)
				isStart = 0
			}
		} else {

		}
	}
}
function playTrails() {
	// console.log("--------------------------------",canvasWidth.value,canvasHeight.value);
	
	let stepData = step.value
	if (stepData < toStep) {
		readTrails(trails[stepData].color, trails[stepData].data, 0);
	} else {
		if (stepData < trails.length && isPlay.value) {
			readTrails(trails[stepData].color, trails[stepData].data, 0);
		} else if (stepData == trails.length) {
			step.value = 0
			percent.value = 0
			toStep = 0
			isPlay.value = false
			context.clearRect(0, 0, canvasWidth.value, canvasHeight.value);
		}
	}
}
function locadBlocks() {

	let params = {
		recordId: props.recordId,
		limit: 500
	}
	ClassroomRecordRequest.wordsScoreTrails(params).then(res => {
		resetBlocks(res.data)
	}).catch(function (error) {
		console.log(error);
	});
}
function resetBlocks(arr) {
	offset += arr.length
	if (arr != null) {
		if (arr.length > 0) {
			var blockData = [];
			if (arr == null) arr = [];

			arr.forEach(function (v) {
				if (v.data) {
					let value = v.data + ''
					
					v.data = new DataView(decode(value.replace(/-/g, "+").replace(/_/g, "/")));
					// console.log("--------------",decode(value.replace(/-/g, "+").replace(/_/g, "/")));
					
					blockData.push(v)
				}
			});
			trails = blockData;
			//如果没加载完那么继续加载
			if (arr.length === 500) {
				locadBlocks()
			}
		}
	}
	blocks = blocks.concat(arr)
}
function drawTrails(c = -65536, x, y, w, s) {
	var tx = ratioX * x;
	var ty = ratioY * y;
	var tw = w;
	let color = (c + 4294967296).toString(16).substr(-6)
	if (c === 0) {
		context.globalCompositeOperation = 'destination-out';
		tw = 10 * w
	} else {
		context.globalCompositeOperation = 'source-over';
	}
	context.lineCap = "round"
	if (s == 16 || s == 0) {
		if (isStart > 0) {
			if (isStart > 1) {
				context.lineTo(x1, y1)
			} else {
				context.lineTo(x1 + 1, y1 + 1)
			}
			context.lineWidth = w1;
			context.stroke()
		}
		isStart = 0;
		return;
	}
	if (isStart > 0) {
		x2 = ratioX * x;
		y2 = ratioY * y;
		// w2=tw;
		var d = Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));

		if (d > gapData) {
			tx = Math.round((x1 + x2) / 2 * 100) / 100;
			ty = Math.round((y1 + y2) / 2 * 100) / 100;
			if (isStart == 1) {
				context.lineTo(tx, ty);

			} else {
				context.quadraticCurveTo(x1, y1, tx, ty);
				context.lineWidth = tw;
				context.stroke();
				context.beginPath();
				// timer = setTimeout(() => {
				context.moveTo(tx, ty);
				// }, 1000);
			}
			x1 = x2;
			y1 = y2;
		}
		isStart++;
	} else {
		/* 初始化画点*/
		// isStart = 1;
		context.beginPath();
		context.lineCap = "round";
		context.strokeStyle = "#" + color; //新增
		x1 = tx;
		y1 = ty;
		context.moveTo(tx, ty); //起点
		isStart++;
	}
}
function readTrails(color, data, i = 0) {
	// console.log("--------------------------------------------",color,currDeviceType,0);
	
	
	if (data.byteLength > i) {
		var x, y, w;
		if (currDeviceType === C5W) {
			x = data.getFloat32(i, true) * canvasWidth.value / 10500;
			y = (data.getFloat32(i + 4, true)) * (canvasHeight.value / 2.25) / 6350;
			w = data.getFloat32(i + 8, true) * canvasWidth.value / 10500;
		} else if (currDeviceType === T9W_H || currDeviceType === T9W_H_TAL) {
			x = (data.getFloat32(i, true)) * canvasWidth.value / 21000;
			y = (data.getFloat32(i + 4, true)) * canvasHeight.value / 29700;
			w = data.getFloat32(i + 8, true) * canvasWidth.value / 21000;
		} else if (currDeviceType === T10) {
			x = (data.getFloat32(i, true)) * canvasWidth.value / 21000;
			y = (data.getFloat32(i + 4, true)) * canvasHeight.value / 29700;
			w = data.getFloat32(i + 8, true) * canvasWidth.value / 21000;
		} else if (currDeviceType === X9) {
			x = (data.getFloat32(i, true)) * canvasWidth.value / 21000;
			y = (data.getFloat32(i + 4, true)) * canvasHeight.value / 29700;
			w = data.getFloat32(i + 8, true) * canvasWidth.value / 21000;
		} else {
			x = data.getFloat32(i, true) * canvasWidth.value / 21000;
			y = data.getFloat32(i + 4, true) * canvasHeight.value / 29700;
			w = data.getFloat32(i + 8, true) * canvasWidth.value / 21000;
		}

		drawTrails(color, x, y, w, 17)
		if (speed.value) {
			if (timer) clearInterval(timer)
			timer = setTimeout(function () {
				i += 20
				readTrails(color, data, i)
			}, speed.value)
		} else {
			i += 20
			readTrails(color, data, i)
		}

	} else {
		drawTrails(color, 0, 0, 0, 0);
		step.value++
		if (speed.value) {
			if (timer) clearInterval(timer)
			timer = setTimeout(function () {
				playTrails()
			}, speed.value)
		} else {
			playTrails()
		}
	}
	percent.value = step.value * 100 / trails.length
}
</script>

<style lang="scss" scoped>
.record-word-player {
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;

	// #myCanvas {
	// 	position: absolute;
	// 	left: 50%;
	// 	z-index: 99;
	// }

	.userImg {
		position: absolute;
		// left: 50%;
		z-index: 1;
		background-color: #fff;
	}

	.menus {
		height: 60px;
		// position: absolute;
		// left: 50%;
		bottom: 0;
		z-index: 100;
		display: flex;
		justify-content: space-around;
		align-items: center;
		background: rgba(0, 0, 0, 0.6);
		padding: 0 0 0 30px;
		box-sizing: border-box;
	}

	.el-icon-circle-close {
		color: #fff;
		position: absolute;
		bottom: 12px;
		font-size: 30px;
		right: -50px;
	}

	.speed .btn_speed {
		display: inline-block;
		cursor: pointer;
		font-size: 18px;
		color: #fff;
		margin: 0 18px;
	}

	.speed .btn_speed.active {
		color: #2C8DF0;
	}

	.btnPlay {
		display: inline-block;
		width: 25px;
		height: 25px;
		box-sizing: border-box;
		color: #fff;
		margin-right: 15px;
		cursor: pointer;
	}

	.btnPlay img {
		width: 100%;
		height: 100%;
	}

	.btnClose {
		display: inline-block;
		width: 90px;
		height: 30px;
		line-height: 30px;
		border-radius: 6px;
		font-size: 14px;
		color: #fff;
		margin: 0 20px;
		background: rgba(73, 175, 79, 1);
	}
}
</style>