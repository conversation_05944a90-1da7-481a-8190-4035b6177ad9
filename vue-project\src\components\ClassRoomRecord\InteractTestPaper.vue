<template>
  <div class="class-room-record-test" v-if="!errorMsg">
    <div class="window" v-if="showView">
      <div class="main" v-show="!showQues">
        <Tree v-if="status == 1" :taskId="queryData.taskId" :dpr="dpr" @setQues="setQues"></Tree>
        <Pages v-if="status == 2" ref="pagesRef" :taskId="queryData.taskId" :classId="queryData.classId"
          :interactId="queryData.interactId" :statisticType="statisticType" @setShowPlayer="setShowPlayer" ></Pages>
        <Table v-if="status == 3" :taskId="queryData.taskId" :classId="queryData.classId"
          :interactId="queryData.interactId" :statisticType="statisticType"></Table>
        <ReportStatistics :interactId="queryData.interactId" :taskId="queryData.taskId" v-if="showReport"
          @close="showReport = false"></ReportStatistics>
        <RecommandQuestion ref="recommandRef" v-show="showRecommand" :interactId="queryData.interactId"
          :taskId="queryData.taskId" @close="showRecommand = false"></RecommandQuestion>
      </div>
      <div class="main main-img" id="mainImg" ref="mainImg" v-show="showQues">
        <img v-if="imgSrc" class="img" style="position: relative;" id="quesImg" ref="quesImg" :src="imgSrc"
          @load="load()">
        <div v-if="imgSrcs" class="imgs">
          <template v-if="imgSrcs[0].height === 'auto'">
            <img v-for="(item, index) in imgSrcs" :src="item.pageImg" :key="index" style="width: 100%;">
          </template>
          <template v-else>
            <img v-for="(item, index) in imgSrcs" :src="item.pageImg" :key="index"
              :style="{ height: item.h * 100 + '%' }">
          </template>
        </div>
      </div>
      <div class="foot-wrap">
        <Foot ref="footRef" @showReport="toggleReport" @showRecommand="toggleRecommand" @setStatus="setStatus"
          @exitShowImgUrl="exitShowImgUrl" @setQues="setQues" @closeQues="closeQues" @hideInteract="hideInteract"
          @footerReload="footerReload">
        </Foot>
      </div>
      <CorrectAlert :statisticType="statisticType" :taskId="queryData.taskId" @changeAnswer="judgeQuestion"></CorrectAlert>
      <div class="player-mask" v-if="showPlayer && playerInfo">
        <div class="player-wrap">
          <Player ref="playerRef" @closePlayer="closePlayer" :studentId="playerInfo.studentId"
            :taskId="playerInfo.taskId" :pageId="playerInfo.pageId" :userImg="playerInfo.userImg"
            :originImg="playerInfo.originImg">
          </Player>
        </div>
      </div>
    </div>
  </div>
  <div class="window2" v-if="showLoading && showRecordResult" v-loading="showLoading" element-loading-text="正在批改中"
    element-loading-background="rgba(122, 122, 122, 0.8)" style="width: 100%">
    <div class="progress2">
      <el-progress :text-inside="true" :stroke-width="26" :percentage="progress" />
      <RBPButton class="btn-sure" btn-text="立即查看" btn-type="big" :btn-selected="true" @click="chack"></RBPButton>
    </div>
  </div>

  <div class="window1" v-if="errorMsg">
    <div class="main ">
      <div class="block"></div>
    </div>
    <div class="foot-wrap">
      <Foot ref="footRef" @showReport="toggleReport" @showRecommand="toggleRecommand" @setStatus="setStatus"
        @exitShowImgUrl="exitShowImgUrl" @setQues="setQues" @closeQues="closeQues" @hideInteract="hideInteract"
        @footerReload="footerReload">
      </Foot>
    </div>
  </div>
</template>

<script setup>

import { ClassroomRecordRequest } from '@/server_request/classroom_record';
import ReportStatistics from './components/ReportStatistics.vue';
import RBPButton from '../baseComponents/RBPButton.vue';
import Foot from './components/Foot.vue';
import Tree from './components/Tree.vue';
import Table from './components/Table.vue';
import Pages from './components/Pages.vue';
import Player from './components/Player.vue';
import { ref, onMounted, defineEmits, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import CorrectAlert from './components/CorrectAlert.vue';
import $ from 'jquery'
import { useInteractStore } from '@/stores/interact_store';
import { useRecordDrawboardStore } from '@/stores/record_drawboard_store';
import { useClassroomUIStore } from '@/stores/classroom_ui_store';
import ReportDialog from './components/ReportDialog.vue';
import RecommandQuestion from './components/RecommandQuestion.vue';
const recordDrawboardStore = useRecordDrawboardStore()
const interactStore = useInteractStore()
const showRecordResult = useClassroomUIStore().showRecordResult
const queryData = interactStore.interactResult
//报错信息
const errorMsg = ref()
//统计类型
const statisticType = ref(0)

//展示页面
const showView = ref(false)
//组件ref
const footRef = ref()
const quesImg = ref()
const mainImg = ref()
const playerRef = ref()
const pagesRef = ref()
//加载
const showLoading = ref(false)
const imgSrc = ref()
const imgSrcs = ref()
//是否展示问题
const showQues = ref(false)
//状态  1: 讲评题目 2:学生列表
const status = ref(1)
// 播放
const showPlayer = ref(false)
const playerInfo = ref(null)
//坐标 缩放
const displacement = {
  scale: 1,
  getDistance: (start, stop) => {
    // Math.hypot()计算参数的平方根
    return Math.hypot(stop.x - start.x, stop.y - start.y);
  }
}
const progress = ref(0)
async function getProgress() {
  try {
    let res = await ClassroomRecordRequest.interactionTaskProgress({ taskIds: [queryData.taskId] })
    if (res.code == 1) {
      if (res.data[0]) {
        progress.value = parseInt(res.data[0].rate * 100)
        if (res.data[0].rate == 1) {
          clearInterval(interval.value)
          chack()
        }
      } else {
        clearInterval(interval.value)
      }
      //防止收卷太快 paperId未及时上传
    } else if (res.message != "该任务未找到对应的试卷") {
      clearInterval(interval.value)
    } else {
      // ElMessage.error("jjjjjjjjjjjjjjjjjj")
    }
  } catch (e) {
    clearInterval(interval.value)
    errorMsg.value = e.message
    ElMessage.error(e.message)
    showLoading.value = false
    //TODO handle the exception
  }
}
function chack() {
  showLoading.value = false
  getInteractDetail()
}
onUnmounted(() => {
  if (interval.value) {
    clearInterval(interval.value)
  }
  recordDrawboardStore.didEndBoard()
})
function footerReload() {
  showView.value = false
  status.value = 1
  if (showRecordResult) {
    loadRefresh()
  } else {
    getInteractDetail()
  }

}
async function getInteractDetail() {
  let params = {
    classId: queryData.classId,
    interactId: queryData.interactId
  }
  try {
    let { data } = await ClassroomRecordRequest.interactDetail(params)
    if (!data || data.statisticType === undefined) {
      errorMsg.value = "请求失败"
      ElMessage.error("请求失败")
      showView.value = false
      return
    }
    statisticType.value = data.statisticType
    recordDrawboardStore.statisticType = data.statisticType
    setTimeout(() => {
      showView.value = true
    }, 200)
    judgeQuestion()
  } catch (e) {
    errorMsg.value = e.message
    ElMessage.error(e.message)
    //TODO handle the exception
  }
}

async function judgeQuestion() {
    try {
        let { data } = await ClassroomRecordRequest.questionsAnswerStatistic({ taskId: queryData.taskId })
        let treeData = data.typeList
        let isRight = true
        for (let e of treeData) {
            for (var ques of e.questions) {
                isRight = ques.correctRate == 1
                
                if (!isRight) {
                    break
                }
            }
            if (!isRight) {
                break
            }
        }
        setTimeout(()=>{
          if (isRight) {
            $('.foot-show-item-recommand').hide()
        }else{
            $('.foot-show-item-recommand').show()
        }
        },200)
    } catch (e) {
      console.log("------------------------",e);
      
        //TODO handle the exception
    }
}
// foot相关 设置问题
function setQues() {
  localStorage.setItem('showQues', true)
  $('.foot-show-item').hide()
  if (localStorage.getItem('quesImgUrl')) {
    imgSrc.value = localStorage.getItem('quesImgUrl')
    imgSrcs.value = null
  } else if (localStorage.getItem('pageImgUrls')) {
    imgSrcs.value = JSON.parse(localStorage.getItem('pageImgUrls'))
    imgSrc.value = null
  }
  showQues.value = true

}
//退出展示图片
function exitShowImgUrl() {
  pagesRef.value && pagesRef.value.showImg()
  // this.$refs.pages && this.$refs.pages.showImg()
}
function setStatus(thisStatus) {
  if (errorMsg.value) {
    ElMessage.error(errorMsg.value)
    return
  }
  status.value = thisStatus
}

//播放器
function closePlayer() {
  showPlayer.value = false
}
function setShowPlayer(playerInfoData) {
  playerInfo.value = playerInfoData
  showPlayer.value = true
}

function load() {
  if (!quesImg.value || !mainImg.value) return;
  if (quesImg.value.naturalHeight / quesImg.value.naturalWidth > mainImg.value.offsetHeight / mainImg.value.offsetWidth) {
    $('#quesImg').css('width', 'auto')
    $('#quesImg').css('height', '100%')
  } else {
    $('#quesImg').css('width', '100%')
    $('#quesImg').css('height', 'auto')
  }
}
//关闭问题
function closeQues() {
  $('#quesImg').css({
    'transform': 'scale(1)',
    'left': '0',
    'top': '0',
  })
  $('.foot-show-item').show()
  localStorage.removeItem('showQues')
  showQues.value = false

}

const interval = ref(null)

const showReport = ref(false)
const showRecommand = ref(false)

const recommandRef = ref(null)

function toggleReport() {
  showReport.value = !showReport.value
}

function toggleRecommand() {
  showRecommand.value = !showRecommand.value
  nextTick(() => {
    if (recommandRef.value && recommandRef.value.initPaper && showRecommand.value) {
      recommandRef.value.initPaper()
    }
  })
}
onMounted(() => {
  nextTick(() => {
    $('.foot-show-item-recommand').hide()
  })
  recordDrawboardStore.didStartBoard(
    window.innerWidth * window.devicePixelRatio,
    (window.innerHeight - 78) * window.devicePixelRatio,
  )
  //有刷新 先loading一下
  if (queryData.showLoading) {
    loadRefresh()
  } else {
    getInteractDetail()
  }
})
function loadRefresh() {
  showLoading.value = true
  getProgress()
  interval.value = setInterval(() => {
    getProgress()
  }, 2000)
}
const emits = defineEmits(['hideInteract'])
function hideInteract() {
  emits('hideInteract')
}
</script>

<style lang="scss" scoped>
@import '../../assets/scss/components.scss';

.class-room-record-test {
  .progress {
    position: fixed;
    width: 50vw;
    top: 50vh;
    left: 25vw;
    margin-top: 100px;
    z-index: 1000000;
  }

  .player-mask {
    width: 100vw;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 100000;
    background-color: rgba(0, 0, 0, .5);
  }

  .window {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: var(--toolbar-bg-color);
  }



  .main-img {
    width: 100vw;
    height: calc(100vh - 75px);
    background-color: #0B423D;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .main-img img {
    vertical-align: middle;
  }

  .imgs {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }


}

.main {
  flex: 1;
  width: 100%;
}

.block {
  background-color: #0B423D;
  height: calc(100vh - 78px);
  width: 100%;
}

.foot-wrap {
  height: 78px;
  position: relative;
  z-index: 100;
  width: 100%;
}

.window1 {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;


}

.window2 {
  width: 100vw;
  height: 100vh;
  z-index: 10000001;
  position: fixed;

  .progress2 {
    position: fixed;
    width: 50vw;
    top: 50vh;
    left: 25vw;
    margin-top: 100px;
    z-index: 10000002;
    // background-color: yellow;
    display: flex;
    flex-direction: column;

    .btn-sure {
      // background-color: #409EFF;
      // color: white;
      // font-size: 16px;
      // padding: 8px 16px;
      // cursor: pointer;
      margin-top: 16px;
      margin-right: 0px;
      // border-radius: 4px;
      align-self: center;
    }
  }

}
</style>