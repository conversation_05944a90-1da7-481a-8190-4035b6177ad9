<template>
    <div class="classroom">
        <DesktopBar></DesktopBar>

        <BlackBoard></BlackBoard>
        <Times></Times>
        <Interactions></Interactions>
        <StudentResponderScore></StudentResponderScore>
        <ShowPaperPen></ShowPaperPen>
        <StudentList v-if="showStudentList"></StudentList>
        <StudentCagetoryScore></StudentCagetoryScore>
        <TeachPlan v-show="showTeachPlan"></TeachPlan>
        <ScoreStatictics v-if="showScoreStatistics"></ScoreStatictics>
        <ScoreStatisticsDetail v-if="showScoreStatisticsDetail"></ScoreStatisticsDetail>
        <TimeKeeper v-show="showTimeKeeper"></TimeKeeper>
        <ClassFlow v-if="showClassFlow"></ClassFlow>
        <ClassRoomReCord v-if="showClassRoomRecord"></ClassRoomReCord>
        <BluetoothAp v-if="showBluetoothAp && serverHost.bleApEnable"></BluetoothAp>
        <CloudDisk v-if="showCloudDisk"></CloudDisk>
        <RandomRollCall v-show="showRandomRollCall"></RandomRollCall>

        <InteractSelect v-if="showInteractSelector && hideToolBar === false"></InteractSelect>
        <ColorSelect v-if="showColorSelect"></ColorSelect>
        <Plane2dSelector v-if="plane2DSelector.showSelector"></Plane2dSelector>
        <WritingGrids v-if="writingGridsSelector.showSelector"></WritingGrids>
        <EraserSelect v-if="showEraserSelect"></EraserSelect>
        <ClearSelect v-if="showClearSelect"></ClearSelect>
        <VideoOverlay></VideoOverlay>
        <RealTimeVideoList></RealTimeVideoList>
        <WpsBoard></WpsBoard>
        <BlackToolbar id="black-board-toolbar" class="toolbar" :style="{zIndex: isPaperpen()?'var(--toolbar-test-z-index)':''}"></BlackToolbar>
        <SystemToolbar></SystemToolbar>
        <MathCalculator class="math-calculator"></MathCalculator>
        <SystemSet></SystemSet>
        <ScanQrCode></ScanQrCode>
        <Updater></Updater>
        <VideoPlayView class="videoPlayView" v-if="showVideoPlayView"></VideoPlayView>
        <AudioPlayView v-if="showAudioPlayView"></AudioPlayView>
        <ScoreAudio></ScoreAudio>
        <DownloadProgress></DownloadProgress>
        <AIHelp></AIHelp>
    </div>
    <LeftRight></LeftRight>
    <ClearScreenAlert></ClearScreenAlert>
    <System v-if="showSystem"></System>
    <RBPAudio></RBPAudio>
</template>
<script setup>
import AIHelp from '@/components/AIHelp/AIHelp.vue'
import WritingGrids from '@/components/WrintingHelp/WritingGrids.vue'
import ClearSelect from '@/components/TabBar/ClearSelect.vue'
import DownloadProgress from '@/components/CloudDisk/DownloadProgress.vue'
import { computed, onMounted, onUnmounted } from 'vue'
import VideoOverlay from '@/components/RealTimeVideo/VideoOverlay.vue'
import RealTimeVideoList from '@/components/RealTimeVideo/RealTimeVideoList.vue'
import BlackToolbar from '@/components/TabBar/TabToolbar.vue'
import SystemToolbar from '@/components/TabBar/SystemToolbar.vue'
import System from '@/components/System.vue'
import InteractSelect from '@/components/InteractSelect.vue'
import { storeToRefs } from 'pinia'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { UIFrames, ViewStatus } from '@/classroom/frame_enums'
import StudentList from '@/components/StudentList/StudentList.vue'
import TimeKeeper from '@/components/TimeKeeper/TimeKeeper.vue'
import RandomRollCall from '@/components/RandomRollCall/RandomRollCall.vue'
import TeachPlan from '@/components/TeachPlan/TeachPlan.vue'
import LeftRight from '@/components/LeftRight/LeftRight.vue'
import { useTeachPlanStore } from '@/stores/teach_plan'
import ScoreStatictics from '@/components/ScoreStatistics/ScoreStatistics.vue'
import ScoreStatisticsDetail from '@/components/ScoreStatistics/ScoreStatisticsDetail.vue'
import ColorSelect from '@/components/TabBar/ColorSelect.vue'
import { useDrawBoardStore } from '@/stores/drawboard_store'
import ClassFlow from '@/components/ClassFlow/ClassFlow.vue'
import Interactions from '@/components/Interactions.vue'
import BlackBoard from '@/components/BlackBoard/BlackBoard.vue'
import { useInteractStore } from '@/stores/interact_store'
import DesktopBar from '@/components/DesktopTool/DesktopBoard.vue'
import { useDesktopStore } from '@/stores/desktop_store'
import Times from '@/components/Times/Times.vue'
import SystemSet from '@/components/SystemSet/SystemSet.vue'
import ClassRoomReCord from '@/components/ClassRoomRecord/ClassRoomRecord.vue'
import CloudDisk from '@/components/CloudDisk/CloudDisk.vue'
import ShowPaperPen from '@/components/PaperPen/ShowPaperPen.vue'
import WpsBoard from '@/components/WpsBoard/WpsBoard.vue'
import ScanQrCode from '@/components/ScanQrCode/ScanQrCode.vue'
import Updater from '@/components/SystemSet/Updater.vue'
import VideoPlayView from '@/components/CloudDisk/VideoPlayView.vue'
import { tdRecord } from '@/utils/talkingdata_tool'
import { LocalRequest } from '@/local_request/local_request'
import StudentCagetoryScore from '@/components/StudentList/StudentCagetoryScore.vue'
import { dbHelper } from '@/utils/db_helper'
import StudentResponderScore from '@/components/Choose/StudentResponderScore.vue'
import { useEvaluationScoreStore } from '@/stores/evaluation_score_store'
import ScoreAudio from '@/components/ScoreAudio/ScoreAudio.vue'
import EraserSelect from '@/components/TabBar/EraserSelect.vue'
import ClearScreenAlert from '@/components/TabBar/ClearScreenAlert.vue'
import BluetoothAp from '@/components/BluetoothAp/BluetoothAp.vue'
import { serverHost } from '@/server_request/server_urls'
import RBPAudio from '@/components/baseComponents/RBPAudio.vue'
import Plane2dSelector from '@/components/MathPlane/Plane2dSelector.vue'
import AudioPlayView from '@/components/CloudDisk/AudioPlayView.vue'
import { remoteControl } from '@/remote_control/remote_control'
import MathCalculator from '@/components/BlackBoard/MathCalculator.vue'


const evaluationScoreStore = useEvaluationScoreStore()
const drawBoardStore = useDrawBoardStore()
const { showColorSelect, showEraserSelect, plane2DSelector,showClearSelect,writingGridsSelector,paperViewState} = storeToRefs(drawBoardStore)
const teachPlanStore = useTeachPlanStore()
const { showTeachPlan } = storeToRefs(teachPlanStore)
const classroomUIStore = useClassroomUIStore()
const { showStudentList, showSystem, showRandomRollCall, showTimeKeeper,
    showScoreStatistics, showScoreStatisticsDetail, showClassFlow,
    showClassRoomRecord, showCloudDisk, showVideoPlayView, showBluetoothAp,showAudioPlayView } = storeToRefs(classroomUIStore)

const desktopStore = useDesktopStore()

const { hideToolBar } = storeToRefs(desktopStore)

const interactStore = useInteractStore()

const { showInteractSelector } = storeToRefs(interactStore)

const tabbarHeight = computed(() => UIFrames.tabbarHeight)



onMounted(async () => {
    evaluationScoreStore.getEvaluationItemFixedList()
    evaluationScoreStore.getEvaluationItemItemList()
    classroomUIStore.windowAddListener()
    let res = await LocalRequest.getLocalIpList()
    tdRecord('开始上课', { 'ips': JSON.stringify(res.ips) })
    await dbHelper.setAddScoreTypeByTeacherId()
    
})


function isPaperpen() {
    if (interactStore.showPaperPenView() && paperViewState.value === ViewStatus.normal) {
        return true;
    } else {
        return false;
    }
}


onUnmounted(() => {
    desktopStore.quitDesktop()
    classroomUIStore.windowRemoveListener()
})
</script>
<style lang="scss">
.stop-class-box {
    width: 300px;
    .el-message-box__message {
        color: var(--text-color); /* 文字变红 */
        font-size: 18px; /* 字体加大 */
        margin-bottom: 4px;
    }
    .el-message-box__title {
        color: transparent;  /* 标题变红 */
        // font-size: 20px; /* 字体加大 */
        // font-weight: bold; /* 加粗 */
    }
    .el-message-box__status{
        font-size: 24px;
    }
    .el-message-box__btns{
        .el-button{
            width: 120px;
            height: 40px;
        }
    }
}
</style>

<style lang="scss" scoped>
.classroom {
    width: 100vw;
    height: 100vh;
    position: relative;
    overflow: hidden;

    .toolbar {
        position: absolute;
        bottom: 0%;
        left: 50%;
        transform: translateX(-50%);
        height: v-bind("tabbarHeight + 'px'");
        // width: 100%;
        z-index: var(--toolbar-z-index);
        // background-color: transparent;
    }

    .systemToolbar {
        position: absolute;
        right: 0;
        background-color: var(--main-anti-bc-alpha-color);
        z-index: var(--toolbar-z-index);
        bottom: 100px;
        border-top-left-radius: 26px;
        border-bottom-left-radius: 26px;
        padding: 10px;
        box-sizing: border-box;
    }

    .videoPlayView {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: var(--classroom-video-play);
    }

    .math-calculator {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: var(--classroom-video-play);
    }
    
}
</style>