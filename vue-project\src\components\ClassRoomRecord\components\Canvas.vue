<template>
	<div class="canvas-style" style="position: absolute;">
		<canvas id="canvas" ref="canvas" :width="props.canvasW" :height="props.canvasH" @dblclick="doubleClick()"
			@mousedown="canvasDown($event)" @mousemove="canvasMove($event)" @mouseup="canvasUp($event)"
			@mouseleave="canvasLeave($event)" @touchstart="canvasDown($event)" @touchmove="canvasMove($event)"
			@touchend="canvasUp($event)"></canvas>
		<div id="container"></div>
	</div>
</template>

<script setup>
import Konva from 'konva'
let x1, y1, x2, y2, tx, ty;
let stage, lastLine, layer;
import { nextTick, onMounted, ref, watch } from 'vue';
const props = defineProps({
	canvasW: String,
	canvasH: String,
	color: String,
	lineWidth: Number
})
//绘制与手势
const canvas = ref()
let ctx = null
let offsetX = 0
let dataColor = ''
let dbclick = 0
let strokeWidth = 0
let canvasMoveUse = false
//方法
function showKnova() {
	let width = parseInt(props.canvasW);
	let height = parseInt(props.canvasH);

	// first we need Konva core things: stage and layer
	stage = new Konva.Stage({
		container: 'container',
		width: width,
		height: height,
	});

	layer = new Konva.Layer();
	stage.add(layer);

	let isPaint = false;
	let mode = 'brush';

	stage.on('mousedown touchstart', (e) => {
		isPaint = true;
		let pos = stage.getPointerPosition();
		lastLine = new Konva.Line({
			stroke: dataColor,
			fillAfterStrokeEnabled: true,
			strokeWidth: strokeWidth,
			bezier: true,
			tension: 0.5,
			globalCompositeOperation:
				mode === 'brush' ? 'source-over' : 'destination-out',
			// round cap for smoother lines
			lineCap: 'round',
			lineJoin: 'round',
			// add point twice, so we have some drawings even on a simple click
			points: [pos.x, pos.y, pos.x, pos.y],
		});
		layer.add(lastLine);
	});

	stage.on('mouseup touchend', () => {
		isPaint = false;
	});

	// and core function - drawing
	stage.on('mousemove touchmove', (e) => {
		if (!isPaint) {
			return;
		}

		// prevent scrolling on touch devices
		e.evt.preventDefault();

		let pos = stage.getPointerPosition();
		let newPoints = lastLine.points().concat([pos.x, pos.y]);
		lastLine.points(newPoints);
	});
}
function setKnovaSize() {
	setTimeout(() => {
		// 改变画布大小
		stage.width(parseInt(props.canvasW))
		stage.height(parseInt(props.canvasH))
		// 更新画布
		stage.draw();
	}, 10)
}


function clearCanvas() {
	ctx.clearRect(0, 0, parseInt(props.canvasW), parseInt(props.canvasH))
	layer.destroyChildren()
	layer.draw()
}
function setColor(color) {
	dataColor = color
	ctx.strokeStyle = color
}
function setLineWidth(lineWidth) {
	strokeWidth = lineWidth
	ctx.lineWidth = lineWidth
}
//初始化
function show() {
	nextTick(() => {
		if (canvas.value) {
			ctx = canvas.value.getContext("2d")//设置2D渲染区域
			ctx.lineWidth = props.lineWidth//设置线的宽度
			ctx.strokeStyle = props.color
			dataColor = props.color
			offsetX = window.innerWidth - parseInt(props.canvasW)
			strokeWidth = props.lineWidth
			showKnova()
		}

	})

}
function canvasDown(e) {
	canvasMoveUse = true;
	if (e.touches && e.touches[0]) {
		e = e.touches[0]
	}
	const canvasX = e.clientX - this.offsetX;
	const canvasY = e.clientY;
	ctx.beginPath(); // 移动的起点
	ctx.moveTo(canvasX, canvasY);
	x1 = canvasX;
	y1 = canvasY;

	// 判断是否双击
	dbclick++
	if (dbclick > 2) {
		doubleClick()
	}
	setTimeout(() => {
		dbclick = 0
	}, 200)
}
function canvasMove(e) {
	if (canvasMoveUse) {
		// 只有允许移动时调用
		// const t = e.target;
		if (e.touches && e.touches[0]) {
			e = e.touches[0]
		}
		let canvasX;
		let canvasY;
		canvasX = e.clientX - this.offsetX;
		canvasY = e.clientY;
		x2 = canvasX
		y2 = canvasY
		tx = Math.round((x1 + x2) / 2 * 100) / 100;
		ty = Math.round((y1 + y2) / 2 * 100) / 100;
		ctx.quadraticCurveTo(x1, y1, tx, ty);
		// this.ctx.lineTo(canvasX, canvasY);
		ctx.stroke();
		ctx.moveTo(tx, ty);
		x1 = x2;
		y1 = y2;
	}
}
function canvasUp(e) {
	canvasMoveUse = false;
}
function canvasLeave(e) {
	canvasMoveUse = false;
}
watch(() => props.canvasH, () => {
	setColor(dataColor)
})
onMounted(() => {
	show()
})
defineExpose({ canvas, clearCanvas, setKnovaSize, setColor, setLineWidth })
</script>

<style lang="scss" scoped>
.canvas-style {
	#canvas {
		position: fixed;
		top: 0;
		right: 0;
		top: calc(423px - 50vh ); //专为英语单词听写设计，后续需要重新设计UI
	    right: calc((1357px - 100vw) / 2); //专为英语单词听写设计，后续需要重新设计UI
	}

	#container {
		position: fixed;
		top: 0;
		right: 0;
		z-index: 1;
		top: calc(423px - 50vh ); //专为英语单词听写设计，后续需要重新设计UI
	    right: calc((1357px - 100vw) / 2); //专为英语单词听写设计，后续需要重新设计UI
	}
}
</style>