<template>
    <div class="main-bluetooth-body" @click="closeWindow">
        <div class="bluetooth-ap-body" @click.stop="console.log('')">
            <div class="list-title">设备列表</div>
            <div class="content">
                <div class="list-body">
                    <div class="list-scroll">
                        <div v-for="(item, i) in devicesData" class="list-item" :key="i"
                            @click="clickDeviceItemConnecting(item)">
                            <div class="item-mac">{{ item.macAddress }}</div>
                            <div class="item-mac" v-if="item.name">{{ item.name }}</div>
                            <div class="item-state">
                                <el-text :type="getStatusTextType(item.status)">{{ deviceState(item) }}</el-text>
                            </div>
                            <el-button v-if="item.name" :disable="item.status == 2" :loading="item.status == 2"
                                @click.stop="clickDeviceItem(item)" style="width: 74px;"
                                :type="getStatusBtnType(item.status)">{{ getStatusBtn(item.status) }}</el-button>
                        </div>
                    </div>
                </div>
                <div class="setting-content">
                    <Text class="tip-text">{{ "i. 该模式需要配合蓝牙热点使用。\n 完成连接后请关闭自动连接，\n 以节省开销。" }}</Text>
                    <div class="setting-item" v-if="showAutoTest">
                        <p class="text">预设点数</p>
                        <div class="setting-flex">
                            <el-input-number class="input-content" v-model="preCount" :min="0" :controls='false'
                                size="middle" />
                            <el-button @click="startAutoTest" class="button" type="primary" size="middle">
                                开始
                            </el-button>
                            <el-button @click="stopAutoTest" class="button" type="danger" size="middle">
                                停止
                            </el-button>
                        </div>
                    </div>
                    <div class="setting-item" v-if="showAutoTest">
                        <p class="text">报点速度(毫秒)</p>
                        <div class="setting-flex">
                            <el-input-number class="input-content" v-model="countSpeed" :min="0" :controls='false'
                                size="middle" />
                            <el-button @click="clickSplit" class="button" type="danger" size="middle">
                                一键断开
                            </el-button>
                        </div>
                    </div>
                    <div style="flex: 1;"></div>
                    <div v-if="isService" class="running-btn">
                        <!-- <el-button @click="clickScan" style="width: 138px;" size="large" :type="scaning?'danger':'primary'">{{ scaning?'停止扫描':'启动扫描' }}</el-button> -->
                        <el-button @click="autoConnectDevices" style="width: 138px;" size="large"
                            :type="isAutoConnect ? 'danger' : 'primary'">{{ isAutoConnect ? '停止自动连接' : '自动连接'
                            }}</el-button>
                    </div>
                    <el-button :type="isService ? 'danger' : 'primary'" size="large"
                        @click="isService ? endService() : clickService()">{{ isService ? "停止服务" : "启动服务" }}</el-button>
                </div>
            </div>
            <div class="footer-title">
                <div class="tab_wrap">
                    <el-text class="title-text">蓝牙模式</el-text>
                </div>
                <!-- <div class="btn_quit" @click="closeWindow">
                    <img src="@/assets/img/quit2.png" />
                    返回
                </div> -->
            </div>
        </div>
    </div>
</template>

<script setup>
import { LocalRequest } from '@/local_request/local_request';
import { useClassroomUIStore } from '@/stores/classroom_ui_store';
import { useClassroomStore } from '@/stores/classroom'

import { storeToRefs } from 'pinia';
import { onMounted, onUnmounted, ref, watch } from 'vue'
import { ElLoading, ElMessage } from 'element-plus';
import { BleConnectStatus, useBleApServerStore } from '@/stores/ble_ap_store';
import { HttpEnv, serverHost } from '@/server_request/server_urls';
import { Alert } from '@/utils/alert';
const devicesData = ref([])
const classroomUIStore = useClassroomUIStore()
const { showBluetoothAp } = storeToRefs(classroomUIStore)
const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)
const bleApServerStore = useBleApServerStore()
const { bleDevices, isService, isAutoConnect, autoTest } = storeToRefs(bleApServerStore)

const preCount = ref(10000)
const countSpeed = ref(20)
const loading = ref(null)

const showAutoTest = ref(false)


onMounted(() => {
    if (serverHost.env !== HttpEnv.online) {
        showAutoTest.value = true
    }
})


watch(bleDevices, () => {

    devicesData.value = Object.values(bleDevices.value)

}, { deep: true, immediate: true })

//是否正在扫描
const scaning = ref(false)

//关闭窗口
function closeWindow() {
    showBluetoothAp.value = false
}

//点击自动化
// function clickAutoTest() {
//     autoTest.value = !autoTest.value
//     if (autoTest.value) {
//         LocalRequest.startAutoTest()
//     } else {
//         LocalRequest.stopAutoTest()
//     }
// }

function startAutoTest() {
    LocalRequest.startAutoTest()
}

function stopAutoTest() {
    LocalRequest.stopAutoTest()
}


function autoConnectDevices() {
    isAutoConnect.value = !isAutoConnect.value
    if (isAutoConnect.value) {
        bleApServerStore.startPolling()
    }
    else {
        bleApServerStore.stopPolling()
    }
}

//点击一键断开
function clickSplit() {
    LocalRequest.disconnectAllBleDevice()
}
//点击设备按钮
function clickDeviceItem(data) {
    if (data.status == BleConnectStatus.CONNECTING) {
        data.status = BleConnectStatus.DISCONNECTED
    }
    else if (data.status == BleConnectStatus.DISCONNECTED) {
        data.status = BleConnectStatus.CONNECTING
        LocalRequest.connectBleDevice(data.macAddress)
    }
    else if (data.status == BleConnectStatus.CONNECTED) {
        LocalRequest.disconnectBleDevice(data.macAddress)
    }
}

//点击连接状态的设备
function clickDeviceItemConnecting(data) {
    if (data.status != BleConnectStatus.CONNECTING) {
        return
    }
    bleApServerStore.addDevices(data.macAddress, BleConnectStatus.DISCONNECTED)
}
//点击服务
async function clickService() {
    startLoding("启动服务中...")
    let res = await LocalRequest.startBleApServer()
    loading.value.close()
    if (!(res && res.data && res.data.success)) {
        Alert.showErrorMessage((res == null ? "网络异常" : res.data.message))
    } else {
        isService.value = true
        isAutoConnect.value =  true
        bleApServerStore.startPolling()
    }
}

//停止服务
async function endService(params) {
    if (!isService.value) return;
    startLoding("停止服务中...")
    bleApServerStore.stopPolling()
    let res = await LocalRequest.stopBleApServer()
    loading.value.close()
    if (!(res && res.data && res.data.code == 1)) {
        Alert.showErrorMessage((res == null ? "网络异常" : res.data.message))
    } else {
        isService.value = false
        isAutoConnect.value =  false
        bleApServerStore.stopPolling()
        bleDevices.value = {}
    }

}

function getStatusBtn(status) {
    if (status === BleConnectStatus.CONNECTED) {
        return "断开"
    }
    return "连接"
}

function getStatusBtnType(status) {
    if (status === BleConnectStatus.CONNECTED) {
        return "danger"
    }
    return "primary"
}
function getStatusTextType(status) {
    if (status === BleConnectStatus.CONNECTED) {
        return "success"
    }
    else if (status === BleConnectStatus.DISCONNECTED) {
        return "default"
    }
    return "danger"
}

function startLoding(text) {
    loading.value = ElLoading.service({
        lock: true,
        text: text,
        background: 'rgba(0, 0, 0, 0.7)',
    })
}




//返回状态
function deviceState(item) {
    if (!item.name) {
         return "暂无绑定此MAC地址的学生，请先绑定"
    }
    switch (item.status) {
        case 0:
            return "未连接"
        case 1:
            return "已连接"
        case 2:
            return "连接中"           
    }
}


</script>

<style lang="scss" scoped>
@import '../../assets/scss/components.scss';

.main-bluetooth-body {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100vw;
    height: 100vh;
    position: fixed;
    z-index: 1001;
    background-color: rgba($color: #000000, $alpha: 0.2);
}

.bluetooth-ap-body {
    position: relative;
    background-color: #fff;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    width: calc(100vw - 25vh);
    height: 75vh;

    .title {
        display: flex;
        justify-content: center;
        font-size: 22px;
        color: #000;
        font-weight: 500;
    }

    .content {
        display: flex;
        flex: 1;
        margin-top: 16px;
        padding: 0px 24px;
        width: calc(100vw - 25vh - 48px);
        height: calc(75vh - 100px);
    }


    .list-title {
        font-size: 17px;
        font-weight: 500;
        margin-left: 24px;
        margin-top: 20px;
    }

    .list-body {
        flex: 1;
        border: 1px solid #9f9f9f;
        border-radius: 8px;
        padding: 8px 12px;
        margin-right: 24px;

        .list-scroll {
            overflow-y: auto;
            scrollbar-width: none;
            /* Firefox */
            height: 100%;
        }

        .list-scroll::-webkit-scrollbar {
            display: none;
            /* Chrome, Safari 和 Opera */
        }

        height: calc(100% - 16px);
    }

    .list-item {
        margin: 16px 0px;
        height: 56px;
        display: flex;
        align-items: center;
        padding: 0px 8px;
        color: #000;
        background-color: #DFDFDF;
        border-radius: 4px;
        justify-content: space-between;

        .item-mac {
            width: 140px;
            font-size: 15px;
        }

        .item-state {
            min-width: 80px;
            font-size: 13px;
        }
    }

    .setting-content {
        display: flex;
        flex-direction: column;
        width: 300px;

    }

    .tip-text {
        font-size: 14px;
        color: #999;
        margin-bottom: 24px;
        white-space: pre-wrap;
    }

    .setting-item {
        margin-top: 16px;

        .text {
            font-size: 14px;
            color: #000;
            margin-bottom: 8px;
        }

        .setting-flex {
            display: flex;
            justify-content: space-between;

            .input-content {
                flex: 1;
            }

            .button {
                margin-left: 5px;
                width: 102px;
            }
        }
    }

    .running-btn {
        display: flex;
        justify-content: space-between;
        margin-bottom: 36px;
    }

}
</style>