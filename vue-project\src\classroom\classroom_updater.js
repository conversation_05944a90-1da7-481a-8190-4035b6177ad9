import { LocalRequest } from "@/local_request/local_request"
import { LocalUrls } from "@/local_request/local_urls"
import { Interact, isWriteMode, isSingleObjective, InteractStatus, StuStatus, interactName, InteractMode } from "./interact_enums"
import { useAnswersStore } from '@/stores/answers_store'
import { useClassroomStore } from '@/stores/classroom'
import { useDrawBoardStore } from "@/stores/drawboard_store"
import { useTimesStore } from '@/stores/times_store'
import { ClassRoomRequest } from "@/server_request/classroom_request"
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'
import { databaseHelper, RecordType } from '@/classroom/class_flow_helper'
import { useInteractStore } from '@/stores/interact_store'
import { Alert } from '@/utils/alert'
import { useDesktopStore } from "@/stores/desktop_store"
import OSS from 'ali-oss'
import { serverHost } from '@/server_request/server_urls'
import { v4 as uuidv4 } from "uuid"
import { loginInstance } from '@/login_instance/login_instance'
import { ClassManageRequest } from '@/server_request/classmanage_request'
import { arrangeClassroomData, getGrade, arrangeMacAddress } from '@/classroom/classroom_helper'
import { remoteControl } from '@/remote_control/remote_control'
import { useOfficeFilesStore } from "@/stores/office_files_store"
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { useQrcodeStore } from '@/stores/qrcode_store'
import { tdRecord } from '@/utils/talkingdata_tool'
import { useEvaluationScoreStore } from '@/stores/evaluation_score_store'
import { STUDENT_INDEX_MAX } from '@/stores/answers_store'
import { AppUpdater } from '@/server_request/app_updater'
import { BleConnectStatus, useBleApServerStore } from "@/stores/ble_ap_store"
import { useProbabilityStore } from '@/stores/probability_store'
import { usePhetExpStore } from "@/stores/phet_exp_store"
import { webRtcServer } from "@/remote_control/webrtc_server"
import { useStudentInfoStore } from "@/stores/student_info_store"
import { useRoute } from "vue-router"
import { userNoToX10 } from "./device_user_no_util"
import { LoginRequest } from "@/server_request/login_request"

const SseEvent = {
    WritePint: "write_data",
    AnswerSubmit: "answer_submit",
    Status: "status",
    PageInfo: "page_info",
    CheckResult: 'check_result',
}


const StatusAction = {
    Online: "online",
    Offline: "offline",
    Battery: "battery",
    DeviceOn: "device_on",
    DeviceOff: "device_off",
    ScanDevice: "scan_device",
    NetworkChange: 'network_change',
    StudentCard: 'student_card'
}

class ClassroomUpdater {

    //开始上课获取
    classRecordId = 0

    currentClassroom

    startClassTime = 0 //开始上课时间

    /// 状态管理

    constructor() {
        if (!ClassroomUpdater.instance) {
            this.setup()
            ClassroomUpdater.instance = this
        }
        return ClassroomUpdater.instance
    }

    setup() {
        if (this.isSetted) {
            return
        }
        this.isSetted = true
        this.onlineDevices = []
        this.paperCountCache = new Map()
        // 启动 sse监听
        this.startSse()
    }

    startSse() {
        const url = LocalUrls.sseEventUrl()
        var sse = new EventSource(url)
        sse.addEventListener(SseEvent.Status, (e) => {
            this.handleStatus(e)
        })

        sse.addEventListener(SseEvent.WritePint, (e) => {
            this.handleWritePoint(e)
        })

        sse.addEventListener(SseEvent.AnswerSubmit, (e) => {
            this.handleAnswerDidSubmit(e)
        })

        sse.addEventListener(SseEvent.PageInfo, (e) => {
            this.handleReceivePageInfo(e)
        })

        sse.addEventListener(SseEvent.CheckResult, (e) => {
            this.handleCheckResult(e)
        })
    }

    // 启动课堂传递参数
    didStartClassroom(token, teacher, classroom) {
        this.startClassTime = Date.now()
        // 默认添加一个黑板页
        this.currentClassroom = classroom
        let drawboardStore = useDrawBoardStore()
        drawboardStore.didStartClass()
        LocalRequest.sendStartClass(token, teacher, classroom)
    }

    didStopClassroom() {
        this.currentClassroom = null
        LocalRequest.sendStopClass()
        let drawboardStore = useDrawBoardStore()
        drawboardStore.didStopClass()
        let classroomStore = useClassroomStore()
        classroomStore.cleanData()
        const desktopStore = useDesktopStore()
        desktopStore.quitDesktop()

        const officeStore = useOfficeFilesStore()
        officeStore.clear()

        const phetExpStore = usePhetExpStore()
        phetExpStore.cleanData()
    }

    /// 开启互动
    async didStartInteract(mode, interactId, taskId, questions = []) {
        if (!taskId) {
            taskId = '0'
        }
        this.interact = mode
        if (isWriteMode(mode)) {
            let students = this.currentClassroom.studentList
            let drawboardStore = useDrawBoardStore()
            drawboardStore.didStartWriteInteract(students, mode)
            const desktopStore = useDesktopStore()
            desktopStore.drawMode = false

            const officeStore = useOfficeFilesStore()
            // officeStore.minimizeOfficeView()
            if (officeStore.isFull) {
                officeStore.setFullAndNotScreen()
            }

            this.setSystemToolbarZIndexDown()

            // 如果画笔是白色，修改画笔为红色，结束互动的时候恢复白色
            drawboardStore.setPaperpenLineColor()
        }
        if (mode == Interact.multiQuestions) {
            const answerStore = useAnswersStore()

            remoteControl.sendAnswerInteractResult({ startQuestions: answerStore.startQuestions })
        }
        let classroomStore = useClassroomStore()
        classroomStore.startInteract(mode, interactId, taskId)
        if (Date.now() - this.startClassTime <= 1000 * 7) {
            // console.log('startInteract delay', (this.startClassTime + 1000 * 7) - Date.now())
            await this.delay((this.startClassTime + 1000 * 7) - Date.now());
        }
        LocalRequest.sendStartInteract(taskId, mode, questions)
        let timesStore = useTimesStore()
        timesStore.startInteractTimer()
        tdRecord('开启互动', { 'interactId': interactId, 'interactName': interactName(mode) })
    }

    /// 停止答题
    async stopAnswer() {
        await LocalRequest.sendStopInteract()
        let classroomStore = useClassroomStore()
        classroomStore.stopAnswer()
        let drawboardStore = useDrawBoardStore()
        let painter = drawboardStore.paperPenPainter
        if (painter) {
            painter.updateAllStudentState()
        }
    }

    /// 中文练字评分获取
    getWordStars() {
        if (this.starInterval) {
            clearInterval(this.starInterval)
        }
        this.wordStarsMap = new Map()
        this.starInterval = setInterval(() => {
            this.loadWordStars()
        }, 5000);
    }

    async loadWordStars() {
        let response = await ClassRoomRequest.getCalligraphyResult()
        if (response.code !== 1) {
            return
        }
        let list = response.data
        let needUpdate = false
        let drawboardStore = useDrawBoardStore()
        list.forEach((item) => {
            if (!item.words || item.words.length < 1) {
                return
            }
            const studentId = Number(item.extId)
            const student = this.currentClassroom.idStudentMap.get(studentId)
            if (!student) {
                return
            }
            if (!this.wordStarsMap.get(studentId)) {
                this.wordStarsMap.set(studentId, new Map())
            }
            const pageId = item.pageId
            let pageMap = this.wordStarsMap.get(studentId)
            if (!pageMap.get(pageId)) {
                pageMap.set(pageId, item)
                let painter = drawboardStore.paperPenPainter
                if (painter) {
                    needUpdate = true
                    painter.receiveWordStars(studentId, pageId, item)
                }
            }
            else {
                let oldItem = pageMap.get(pageId)
                if (oldItem.words.length !== item.words.length) {
                    pageMap.set(pageId, item)
                    let painter = drawboardStore.paperPenPainter
                    if (painter) {
                        needUpdate = true
                        painter.receiveWordStars(studentId, pageId, item)
                    }
                }
            }
        })
        if (needUpdate) {
            let painter = drawboardStore.paperPenPainter
            if (painter) {
                painter.animate()
            }
        }
    }

    /// 取消获取中文练字评分
    cancelGetWordStars() {
        if (this.starInterval) {
            clearInterval(this.starInterval)
        }
        this.starInterval = null
        if (this.wordStarsMap) {
            this.wordStarsMap.clear()
        }
    }

    /// 结束互动
    didStopInteract() {
        let drawboardStore = useDrawBoardStore()
        drawboardStore.didEndInteract()
        if (isWriteMode(this.interact)) {
            drawboardStore.resetPaperpenLineColor()
        }

        let classroomStore = useClassroomStore()
        classroomStore.endInteract()
        let timesStore = useTimesStore()
        timesStore.stopInteractTimer()
        this.cancelGetWordStars()

        this.paperCountCache.clear()
        this.setSystemToolbarZIndexNormal()
        this.interact = Interact.none
        const desktopStore = useDesktopStore()
        if (window.electron && desktopStore.hideToolBar) {
            window.electron.shotBarFocusWindow(false)
        }
    }

    setSystemToolbarZIndexNormal() {
        //打开纸笔互动的时候正常systemToolbar的层级
        // const classroomUIStore = useClassroomUIStore()
        // classroomUIStore.changeToolbarZIndex(getZIndex('--toolbar-z-index'))
        // classroomUIStore.changeSystemToolbarHalfScreenRefZIndex(getZIndex('--toolbar-ex-z-index'))

        const officeStore = useOfficeFilesStore()
        if (officeStore.currentFileItem && !officeStore.minimized) {
            officeStore.setZIndexOut()
        }
    }

    setSystemToolbarZIndexDown() {
        //关闭纸笔互动的时候降低systemToolbar的层级
        // const classroomUIStore = useClassroomUIStore()
        // classroomUIStore.changeToolbarZIndex(getZIndex('--toolbar-open-paperpen-z-index'))
        // classroomUIStore.changeSystemToolbarHalfScreenRefZIndex(getZIndex('--toolbar-open-paperpen-z-index'))
    }


    async startInteract(mode) {
        let loading = ElLoading.service({ background: 'transparent' })
        remoteControl.sendStartInteractResult({ interact: mode })
        let interactStore = useInteractStore()
        let imageBlob = await this.captureScreen()
        this.uploadToOSS(imageBlob).then((url) => {
            interactStore.captureScreenUrl = url
        })

        if (mode != Interact.none) {
            await this.stopInteract()
            await databaseHelper.addLines(mode)
        }

        if (mode == Interact.examMode) {
            //考试模式实际使用  随堂测不显示画布 不允许提前交卷
            interactStore.examMode = true
            mode = Interact.classTest
        } else {
            interactStore.examMode = false
        }

        if (mode == Interact.probability) {
            //纸笔互动和概率共用一个
            interactStore.isProbability = true
            mode = Interact.paperPen
        } else {
            interactStore.isProbability = false
        }

        /// 组合题 和 单词听写， 选择阶段不需要请求接口
        if (mode === Interact.multiQuestions || mode === Interact.dictation) {
            interactStore.showInteractSelector = false
            interactStore.didSelectInteract(mode, InteractStatus.none)
            loading.close()
            return
        }

        /// 实验
        if (mode === Interact.phetExp) {
            const classroomUIStore = useClassroomUIStore()
            classroomUIStore.showPhetExpMenu = true
            loading.close()
            return
        }

        const res = await ClassRoomRequest.interactStart(mode)
        if (res.code === 1) {
            interactStore.showInteractSelector = false
            await this.didStartInteract(mode, res.data.interactId, res.data.taskId)
        } else {
            Alert.showErrorMessage(res.message)
            remoteControl.sendStopInteractResult({ interact: mode })
        }
        loading.close()
    }

    async stopInteract(forceStop) {
        let interactStore = useInteractStore()
        if (isSingleObjective(interactStore.interact)) {
            if (interactStore.interact == Interact.responder) {
                remoteControl.sendStopInteractResult({ interact: interactStore.interact })
                await this.stopAnswer()
                await this.stopInteractRequest()
                this.didStopInteract()
                await databaseHelper.addLines(RecordType.endInteract)

            } else {
                if (interactStore.interactStatus == InteractStatus.undelivered) {
                    remoteControl.sendRollingInteractResult({})
                    interactStore.interactStatus = InteractStatus.underway
                    await this.stopAnswer()
                    if (!forceStop) {
                        return
                    }
                }
                if (interactStore.interactStatus == InteractStatus.underway) {
                    interactStore.testQuestionInfo = null
                    remoteControl.sendStopInteractResult({ interact: interactStore.interact })
                    let answersStore = useAnswersStore()
                    await ClassRoomRequest.setAnswerRequest(answersStore.rightAnswer)
                    await this.stopInteractRequest()
                    // await this.delay(200)
                    
                    this.didStopInteract()
                    await databaseHelper.addLines(RecordType.endInteract)
                }
            }
        } else if (interactStore.interact == Interact.multiQuestions) {
            remoteControl.sendStopInteractResult({ interact: interactStore.interact })
            await this.stopAnswer()
            await this.stopInteractRequest()
            this.didStopInteract()
            await databaseHelper.addLines(RecordType.endInteract)
        } else if (isWriteMode(interactStore.interact)) {
            if (interactStore.interact == Interact.dictation) {
                remoteControl.sendStopInteractResult({ interact: interactStore.interact })
                let classroomStore = useClassroomStore()
                let answered = []
                classroomStore.selectedClassroom.studentList.forEach(student => {
                    if (student.writePageInfo || student.writing) {
                        answered.push(student.studentNo)
                    }
                })

                let taskId = classroomStore.selectedClassroom.taskId

                await this.stopAnswer()
                await this.stopInteractRequest()
                this.didStopInteract()
                await databaseHelper.addLines(RecordType.endInteract)

                // 单词听写结果
                // let interactStore = useInteractStore()
                interactStore.interactResult = {
                    taskId: taskId,
                    type: '7',
                    answered: answered,
                }
                let classroomUIStore = useClassroomUIStore()
                classroomUIStore.showRecordResult = true
            } else {
                if (interactStore.interactStatus == InteractStatus.undelivered) {
                    interactStore.interactStatus = InteractStatus.underway
                    await this.stopAnswer()
                    await this.stopInteractRequest()
                    remoteControl.sendRollingInteractResult({})
                    if (!forceStop) {
                        return
                    }
                }
                if (interactStore.interactStatus == InteractStatus.underway) {
                    remoteControl.sendStopInteractResult({ interact: interactStore.interact })
                    if (interactStore.interact == Interact.classTest) {
                        let classroomStore = useClassroomStore()
                        let haveWriting = false
                        classroomStore.selectedClassroom.studentList.forEach(stu => {
                            if (stu.writePageInfo) {
                                haveWriting = true
                            }
                            stu.answerSubmitIndex = STUDENT_INDEX_MAX
                        })
                        if (haveWriting) {
                            // 随堂测结果
                            // let interactStore = useInteractStore()
                            interactStore.interactResult = {
                                taskId: classroomStore.selectedClassroom.taskId,
                                type: '6',
                                classId: classroomStore.selectedClassroom.classId,
                                interactId: classroomStore.selectedClassroom.interactId,
                                showLoading: true
                            }
                            let classroomUIStore = useClassroomUIStore()
                            classroomUIStore.showRecordResult = true
                        }
                    }

                    this.didStopInteract()
                    await databaseHelper.addLines(RecordType.endInteract)
                    interactStore.testQuestionInfo = null
                }
            }
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async stopInteractRequest() {
        let interactStore = useInteractStore()
        let imgUrl = interactStore.captureScreenUrl


        // if (!isWriteMode(interactStore.interact)) {
        //     imgUrl = await this.uploadToOSS()
        //     console.log('imgUrl:', imgUrl)
        // }

        let answersStore = useAnswersStore()

        if (isSingleObjective(interactStore.interact)) {

            if (interactStore.interact == Interact.responder) {
                let answerList = []
                for (let [studentId, answer] of Object.entries(answersStore.studentAnswers)) {
                    /// 通过studentId找到student
                    let student = this.currentClassroom.idStudentMap.get(parseInt(studentId))
                    answerList.push({
                        'studentId': studentId,
                        'useTime': student.respondedTime
                    })
                }
                let loadingInstance = ElLoading.service({ background: 'transparent' })
                await ClassRoomRequest.stopInteractRequest(answerList, { imgUrl: imgUrl })
                loadingInstance.close()
            }
            else {
                let answerList = []
                for (let [studentId, answer] of Object.entries(answersStore.studentAnswers)) {
                    let answerStr = ''
                    if (answer.length > 0) {
                        answerStr = answer[0].join('')
                    }
                    if (answerStr == 'RIGHT') {
                        answerStr = 'True'
                    } else if (answerStr == 'WRONG') {
                        answerStr = 'False'
                    }
                    answerList.push({
                        'studentId': studentId,
                        'answer': answerStr
                    })
                }
                let loadingInstance = ElLoading.service({ background: 'transparent' })
                if (imgUrl) {
                    await ClassRoomRequest.stopInteractRequest(answerList, { imgUrl: imgUrl })
                }
                else {
                    await ClassRoomRequest.stopInteractRequest(answerList, {})
                }
                interactStore.captureScreenUrl = null
                // await ClassRoomRequest.setAnswerRequest(answersStore.rightAnswer)
                loadingInstance.close()
            }
        } else if (interactStore.interact == Interact.multiQuestions) {
            /// 正确答案
            let comboQuestions = []
            for (let i = 0; i < answersStore.startQuestions.length; i++) {
                let mode = answersStore.startQuestions[i]
                let answer = ''
                if (Object.keys(answersStore.multiRightAnswers).length > i) {
                    answer = answersStore.multiRightAnswers[i].join('')
                }
                if (answer == 'RIGHT') {
                    answer = 'True'
                } else if (answer == 'WRONG') {
                    answer = 'False'
                }
                comboQuestions.push({
                    'answer': answer,
                    'type': mode
                })
            }

            /// 学生的选择
            let comboAnswerStus = []
            for (let i = 0; i < answersStore.startQuestions.length; i++) {
                let currentQuestion = []
                for (let [studentId, answers] of Object.entries(answersStore.multiQuestionAnswers)) {
                    let answer = answers[i]
                    let answerStr = answer.join('')
                    if (answerStr == 'RIGHT') {
                        answerStr = 'True'
                    } else if (answerStr == 'WRONG') {
                        answerStr = 'False'
                    }
                    currentQuestion.push({
                        'studentId': studentId,
                        'answer': answerStr
                    })
                }
                comboAnswerStus.push(currentQuestion)
            }
            let loadingInstance = ElLoading.service({ background: 'transparent' })
            await ClassRoomRequest.stopInteractMultiRequest(comboAnswerStus, comboQuestions, imgUrl)
            loadingInstance.close()
        } else if (isWriteMode(interactStore.interact)) {
            let answerList = []
            let offlineList = []
            let absentList = []
            let classroomStore = useClassroomStore()
            classroomStore.selectedClassroom.studentList.forEach(student => {

                if (student.stuStatus == StuStatus.online || student.writePageInfo || student.writing) {
                    const stuItem = classroomStore.selectedClassroom.idStudentMap.get(student.studentId)
                    let answerPageCount = stuItem.writePageInfo ? stuItem.writePageInfo.pageCodes.size : 0
                    if (student.studentId) {
                        answerList.push({
                            "deviceAlias": student.name,
                            "deviceMac": student.deviceMac,
                            "studentExtId": student.studentId,
                            "studentId": student.studentId,
                            answerPageCount
                        })
                    }
                    // if (this.interact == Interact.paperPen || this.interact == Interact.classTest) {
                    //     if (student.writing) {
                    //         answerList.push({
                    //             "deviceAlias": student.name,
                    //             "deviceMac": student.deviceMac,
                    //             "studentExtId": student.studentId,
                    //         })
                    //     } else {
                    //         offlineList.push({
                    //             "deviceAlias": student.name,
                    //             "deviceMac": student.deviceMac,
                    //             "studentExtId": student.studentId,
                    //         })
                    //     }
                    // } else {
                    //     answerList.push({
                    //         "deviceAlias": student.name,
                    //         "deviceMac": student.deviceMac,
                    //         "studentExtId": student.studentId,
                    //     })
                    // }
                } else {
                    offlineList.push({
                        "deviceAlias": student.name,
                        "deviceMac": student.deviceMac,
                        "studentExtId": student.studentId,
                    })
                }

            })
            let req = {
                imgUrl: imgUrl, offlineStus: offlineList,
                absentStus: absentList
            }
            if (this.getMaxUsedPaperId() > 0) {
                req.paperId = this.getMaxUsedPaperId()
            }
            await ClassRoomRequest.stopInteractRequest(answerList, req)
        }
    }

    async captureScreen() {
        if (window.electron) {
            let fileName = null
            try {
                fileName = await window.electron.captureScreen()
            }
            catch (error) {
                console.error('capture screen error:', error);
            }
            if (!fileName) {
                return null
            }
            let url = 'http://' + window.location.host + '/store/images/' + fileName
            // 使用 fetch 获取图片并转换为 Blob
            const response = await fetch(url);
            // 将响应转为 Blob
            const blob = await response.blob();
            return blob;
        } else {
            return null;
        }
    }

    async uploadToOSS(imageBlob) {

        // let drawboardStore = useDrawBoardStore()
        // let imageBlob = await drawboardStore.blackBoardPainter.painterSnapShoot()

        if (!imageBlob) {
            return ''
        }
        let res = await LocalRequest.getOssToken()
        if (res && res.code == 1) {
            // 创建 OSS 客户端
            const client = new OSS({
                region: 'oss-cn-beijing',
                accessKeyId: res.access_key,
                accessKeySecret: res.access_secret,
                bucket: 'mgboard-class-res',
                stsToken: res.security_token
            });

            let env = serverHost.env
            let classroomStore = useClassroomStore()
            let interactId = classroomStore.selectedClassroom.interactId
            let uuid = uuidv4()
            let path = `class/${env}/${interactId}/${uuid}.png`
            let result = await this.uploadImageBlob(client, imageBlob, path)
            if (result.url) {
                return result.url
            }
        }
        return ''
    }

    async uploadImageBlob(client, blob, uuid) {
        const objectKey = uuid
        try {
            const result = await client.put(objectKey, blob)
            return result
        } catch (error) {
            throw error
        }
    }

    /* 
        业务处理代码
    */
    async handleStatus(e) {
        const event = JSON.parse(e.data)
        const studentId = event.studentId
        let classroomStore = useClassroomStore()
        let drawboardStore = useDrawBoardStore()
        if (event.action === StatusAction.Online) {
            classroomStore.setStudentOnline(studentId)
            let macAddress = classroomStore.getDeviceMac(studentId)
            if (event.from === "ble") {
                this.changeBluetoothStatus(BleConnectStatus.CONNECTED, macAddress);
            }
            let painter = drawboardStore.paperPenPainter
            if (painter) {
                painter.updateStudentState(studentId)
            }
        }
        else if (event.action === StatusAction.Offline) {
            classroomStore.setStudentOffline(studentId)
            let macAddress = classroomStore.getDeviceMac(studentId)
            if (event.from === "ble") {
                this.changeBluetoothStatus(BleConnectStatus.DISCONNECTED, macAddress);
            }
            let painter = drawboardStore.paperPenPainter
            if (painter) {
                painter.updateStudentState(studentId)
            }
        }
        else if (event.action == StatusAction.ScanDevice) {
            let bleApServerStore = useBleApServerStore()
            let status = bleApServerStore.getDeviceStatus(event.mac_address)
            // scan之前可能已经调用连接，这里先查询之前的状态
            this.changeBluetoothStatus(status, event.mac_address);
            // if (!this.onlineDevices.includes(event.mac_address)) {
            //     this.onlineDevices.push(event.mac_address)                
            // }
            if (!this.onlineDevices.includes(event.mac_address)) {
                this.onlineDevices.push(event.mac_address)
            }
        }
        else if (event.action === StatusAction.Battery) {
            const battery = event.battery
            classroomStore.updateDeviceBattery(studentId, battery)
        }
        else if (event.action === StatusAction.DeviceOn) {
            if (!this.onlineDevices.includes(event.mac_address)) {
                this.onlineDevices.push(event.mac_address)
            }
        }
        else if (event.action === StatusAction.DeviceOff) {
            const index = this.onlineDevices.indexOf(event.mac_address)
            if (index >= 0) {
                this.onlineDevices.splice(index, 1)
            }
        }
        else if (event.action === StatusAction.NetworkChange) {
            if (event.type === "ip_change") {
                let qrcodeStore = useQrcodeStore()
                qrcodeStore.ipString = event.new_ip
                qrcodeStore.setQrcode()
                let res = await LocalRequest.getLocalIpList()
                tdRecord('ip变更', { 'ips': JSON.stringify(res.ips), 'newIp': event.new_ip })
            }
            else if (event.type === "network_status") {
                if (loginInstance.teacher) {
                    tdRecord("网络变化", { "connected": event.conntected })
                }
            }

        } else if (event.action === StatusAction.StudentCard) {
            // console.log("--------------------------------------------------------------------接受信息",event,this.changeStudent);
            if(this.changeStudent){
                return
            }
            this.changeStudent = true
            if (this.currentClassroom && this.currentClassroom.studentList && event.student_id && event.mac) {
                try {
                    let studentId = userNoToX10(event.student_id,event.device)
                    event.student_id = studentId
                    let item =null
                    this.currentClassroom.studentList.forEach((e)=>{
                        // console.log("--------------------------------------------------------------------学生信息",event);
                        if(e.studentNo == studentId){
                            item = e
                        }
                    })
                    // console.log("--------------------------------------------------------------------接受信息",event);

                    // console.log("--------------------------------------------------------------------",item);
                    if (item) {
                        
                        
                        if(item.deviceMac == event.mac){
                            //直接绑定一下
                            await LocalRequest.changeStudentMac('', event.mac, item.studentId)
                            this.changeStudent = false
                            return
                        }
                        //先查一下当前mac是否绑定其他学生
                        let oldItem = null
                        this.currentClassroom.studentList.forEach((e)=>{
                            if(e.deviceMac == event.mac){
                                oldItem = e
                            }
                        })
                        // console.log("--------------------------------------------------------------------",oldItem);
                        const studentInfoStore = useStudentInfoStore()
                        if(oldItem!=null){
                            let oldRes = await studentInfoStore.changeMacAddress(oldItem, "")
                            if(oldRes){
                                if (window.location.href.includes('/classroom')) {
                                    oldItem.stuStatus = StuStatus.offline
                                    // let oldChange  = await LocalRequest.changeStudentMac(oldItem.deviceMac ?? '', "", oldItem.studentId)
                                    // oldRes = oldChange&&oldChange.code == 1
                                }
                                oldItem.deviceMac = ""
                                // Alert.showSuccessMessage('保存成功')
                                arrangeMacAddress(this.currentClassroom)
                            }else{
                                this.changeStudent = false
                                ElMessage.error("绑定学生信息失败")
                                return
                            }
                        }
                        

                        
                        let res = await studentInfoStore.changeMacAddress(item, event.mac)
                        if (res) {
                            if (window.location.href.includes('/classroom')) {
                                item.stuStatus = StuStatus.offline
                                await LocalRequest.changeStudentMac(item.deviceMac ?? '', event.mac, item.studentId)
                            }
                            item.deviceMac = event.mac
                            // Alert.showSuccessMessage('保存成功')
                            arrangeMacAddress(this.currentClassroom)
                        }
                    }
                } catch (e) {
                    ElMessage.error("绑定学生信息失败")
                    console.error("绑定学生身份信息失败", e)
                }
            }
            this.changeStudent = false
            
        }
    }
    /// 修改蓝牙连接状态
    changeBluetoothStatus(status, macAddres) {
        let bleApServerStore = useBleApServerStore()
        bleApServerStore.addDevices(macAddres, status)
    }

    handleWritePoint(e) {
        if (!isWriteMode(this.interact)) {
            return
        }
        const event = JSON.parse(e.data)

        const studentId = event.studentId
        let classroomStore = useClassroomStore()
        classroomStore.studentWriting(studentId)


        let pageCode = event.pageCode
        const points = event.points
        // console.log("receive points", points)

        if (this.interact === Interact.paperPen || this.interact === Interact.dictation) {
            pageCode = 0
            classroomStore.studentWriting(event.studentId,event.pageCode)
        }

        let drawboardStore = useDrawBoardStore()
        let painter = drawboardStore.paperPenPainter
        if (painter) {
            painter.appendWritePoints(studentId, pageCode, points)
        }


        let interactStore = useInteractStore()
        if (interactStore.isProbability) {
            let probabilityStore = useProbabilityStore()
            probabilityStore.addDataToStrokes(studentId, points)
        }
    }

    handleAnswerDidSubmit(e) {
        const event = JSON.parse(e.data)
        let classroomStore = useClassroomStore()
        classroomStore.studentSubmit(event.studentId, event.answer)
        let drawboardStore = useDrawBoardStore()
        let painter = drawboardStore.paperPenPainter
        if (painter) {
            painter.updateStudentState(event.studentId)
        }

        let interactStore = useInteractStore()
        if (interactStore.isProbability) {
            let probabilityStore = useProbabilityStore()
            probabilityStore.getStrokes(event.studentId)
        }
    }

    getMaxUsedPaperId() {
        let count = 0
        let maxUsedPaperId = 0
        this.paperCountCache.forEach((value, key) => {
            if (value > count) {
                maxUsedPaperId = key
                count = value
            }
        })
        return maxUsedPaperId
    }

    handleReceivePageInfo(e) {
        const event = JSON.parse(e.data)
        let classroomStore = useClassroomStore()
        classroomStore.studentWriting(event.stu_id, event.page_code, event.paper.paperId)
        if (this.interact === Interact.paperPen || this.interact === Interact.dictation) {
            event.page_code = 0

        }
        else {
            /// 保存试卷获取次数
            const paper = event.paper
            let count = this.paperCountCache.get(paper.paperId)
            if (!count) {
                count = 1
            }
            else {
                count++
            }
            this.paperCountCache.set(paper.paperId, count)
        }
        let drawboardStore = useDrawBoardStore()
        let painter = drawboardStore.paperPenPainter
        if (painter) {
            painter.receivePageInfo(event)
        }
    }

    /// 批改回调
    handleCheckResult(e) {
        const event = JSON.parse(e.data)
        // let event =  json!({
        //     "task_id": task_id.to_string(),
        //     "student_id": stu_id,
        //     "page_id": page_id as u32,
        //     "page_code": page_code as u32,
        //     "correct_image": correct_image,
        //     "device_addr": device_addr,
        // });
        const taskId = event.task_id
        let classroomStore = useClassroomStore()
        // 非当初互动的批改回调
        if (taskId !== classroomStore.selectedClassroom.taskId) {
            return
        }
        // 在有student_id，加一下已批改
        let studentId = event.student_id
        let stu = classroomStore.selectedClassroom.idStudentMap.get(studentId)
        if (stu) {
            stu.correct = true
        }
        let drawboardStore = useDrawBoardStore()

        let painter = drawboardStore.paperPenPainter
        if (painter) {
            painter.receiveCheckResult(event)
            // 中文练字需要请求评分
            if (this.interact === Interact.chineseWriting) {
                this.getWordStars()
            }
        }
    }

    // 学生加分 开关关闭
    async studentsAddScoreTypeOne(score, retryCount = 5) {
        if (retryCount == 0) {
            return false
        }
        let classroomStore = useClassroomStore()
        let studentIds = []
        classroomStore.selectedClassroom.studentList.forEach(student => {
            if (student.selected) {
                studentIds.push(student.studentId)
            }
        })
        let rankIds = []
        classroomStore.selectedClassroom.groupStudentArray.forEach(group => {
            if (group.selected) {
                rankIds.push(group.rankId)
            }
        })
        if (studentIds.length == 0 && rankIds.length == 0) {
            Alert.showErrorMessage('请选择学生或小组')
            return false
        }
        let params = {}
        let type = 1
        if (rankIds.length > 0) {
            params.rankIds = rankIds
            type = 1
        }
        if (studentIds.length > 0) {
            params.studentIds = studentIds
            type = 2
        }
        let interactId = classroomStore.selectedClassroom.interactId
        if (interactId != undefined) {
            params.interactId = interactId
        }
        const evaluationScoreStore = useEvaluationScoreStore()
        let alone = evaluationScoreStore.alone

        let res = await ClassRoomRequest.setScoreNew(alone, score, type, params)
        if (res.code == 1) {
            classroomStore.selectedClassroom.groupStudentArray.forEach(group => {
                // 给组里面所有学生加分
                if (!alone) {
                    if (group.selected) {
                        group.groupStudent.forEach(student => {
                            student.score = (parseInt(student.score) + parseInt(score)).toString()
                        })
                    }
                }
                group.selected = false
                group.groupStudent.forEach(s => {
                    if (s.selected) {
                        s.score = (parseInt(s.score) + parseInt(score)).toString()
                    }
                    s.selected = false
                })
            })
            // Alert.showSuccessMessage('成功')
            return true
        } else {
            //如果调用失败的话，再重新调一次
            let count = retryCount - 1
            setTimeout(async () => {
                return await this.studentsAddScoreTypeOne(score, count)
            }, (5 - count) * 2000)
        }
    }

    // 学生加分 开关打开
    async studentsAddScoreTypeTwo(evaluationItemId, addScore, retryCount = 5) {
        if (retryCount == 0) {
            return false
        }
        let classroomStore = useClassroomStore()
        let studentIds = []
        classroomStore.selectedClassroom.studentList.forEach(student => {
            if (student.selected) {
                studentIds.push(student.studentId)
            }
        })
        let rankIds = []
        classroomStore.selectedClassroom.groupStudentArray.forEach(group => {
            if (group.selected) {
                rankIds.push(group.rankId)
            }
        })

        let params = {}
        let type = 1
        if (rankIds.length > 0) {
            params.rankIds = rankIds
            type = 1
        }
        if (studentIds.length > 0) {
            params.studentIds = studentIds
            type = 2
        }
        let interactId = classroomStore.selectedClassroom.interactId
        if (interactId != undefined) {
            params.interactId = interactId
        }
        const evaluationScoreStore = useEvaluationScoreStore()
        let alone = evaluationScoreStore.alone

        let res = await ClassRoomRequest.setStudentsScore(alone, evaluationItemId, type, params)
        if (res.code == 1) {
            classroomStore.selectedClassroom.groupStudentArray.forEach(group => {
                // 给组里面所有学生加分
                if (!alone) {
                    if (group.selected) {
                        group.groupStudent.forEach(student => {
                            student.score = (parseInt(student.score) + parseInt(addScore)).toString()
                        })
                    }
                }
                group.selected = false
                group.groupStudent.forEach(s => {
                    if (s.selected) {
                        s.score = (parseInt(s.score) + parseInt(addScore)).toString()
                    }
                    s.selected = false
                })
            })
            // Alert.showSuccessMessage('成功')
            return true
        } else {
            //如果调用失败的话，再重新调一次
            let count = retryCount - 1
            setTimeout(async () => {
                return await this.studentsAddScoreTypeTwo(evaluationItemId, addScore, count)
            }, (5 - count) * 2000)
        }
    }

    async studentsAddScore(stuList, score, retryCount = 5) {
        if (retryCount == 0) {
            return false
        }
        let classroomStore = useClassroomStore()
        let studentIds = []
        stuList.forEach(student => {
            studentIds.push(student.studentId)
        })

        if (studentIds.length == 0) {
            Alert.showErrorMessage('请选择学生')
            return false
        }
        let params = {}
        if (studentIds.length > 0) {
            params.studentIds = studentIds
        }
        let interactId = classroomStore.selectedClassroom.interactId
        if (interactId != undefined) {
            params.interactId = interactId
        }

        const evaluationScoreStore = useEvaluationScoreStore()
        let alone = evaluationScoreStore.alone

        let res = await ClassRoomRequest.setScoreNew(alone, score, 2, params)
        if (res.code == 1) {
            // Alert.showSuccessMessage('成功')
            return true
        } else {
            //如果调用失败的话，再重新调一次
            let count = retryCount - 1
            setTimeout(async () => {
                return await this.studentsAddScore(stuList, score, count)
            }, (5 - count) * 2000)
        }
    }

    getHandleData(params) {

    }
    ///远程扫码登录上课
    async handleStartClass(params, duringClass, mirror = false) {
        if (mirror) {
            //给遥控器发上课成功消息
            const  classroomStore = useClassroomStore()
            remoteControl.sendStartClassResult({ code: 1, message: 'success',recordId: classroomStore.selectedClassroom.classRecordId})
            return
        }
        //如果在上课中，先下课
        if (duringClass) {
            await this.stopClass()
        }

        let token = params.token
        let teacherId = params.teacherId
        let classId = params.classId
        let subjectId = params.subjectId
        loginInstance.saveToken(token)
        loginInstance.saveSubjectMainId(subjectId)

        await this.getData(classId, subjectId)
    }

    async getData(classId, subjectId) {
        let loadingInstance = ElLoading.service({ background: 'transparent' })
        const res = await ClassManageRequest.getUserInfo()
        loadingInstance.close()
        if (res.code == 1) {
            loginInstance.saveTeacher(res.data)
            loginInstance.saveEvaluation(res.data?.evaluation)
            this.setSubjectName(res.data?.subjects)
            if (loginInstance.subjectMainId == 0) {
                Alert.showErrorMessage('当前无任教学科，将退出登录')
                return
            }
            await this.getList(classId, subjectId)
        } else {
            Alert.showErrorMessage(res.message)
            //给遥控器发上课成功消息
            remoteControl.sendStartClassResult({ code: 0, message: '加入失败' })
        }
    }
    setSubjectName(subjects) {
        loginInstance.subjectName = subjects.find(item => item.subjectId == loginInstance.subjectMainId).subjectName ?? '未知'
    }
    async getList(classId, subjectId) {
        let loadingInstance = ElLoading.service({ background: 'transparent' })
        const res = await ClassManageRequest.getClassList()
        loadingInstance.close()
        if (res.code == 1) {
            let classroom = res.data.filter(item => item.classId == classId)[0]
            let classroomStore = useClassroomStore()
            classroomStore.setSelectedClassroom(classroom)
            await this.getStudents(classId, subjectId)
        } else {
            Alert.showErrorMessage(res.message)
            //给遥控器发上课成功消息
            remoteControl.sendStartClassResult({ code: 0, message: '加入失败' })
        }
    }

    async getStudents(classId, subjectId) {
        let loadingInstance = ElLoading.service({ background: 'transparent' })
        const res = await ClassManageRequest.getStudentList(classId)
        loadingInstance.close()
        if (res.code == 1) {
            let classroomStore = useClassroomStore()
            classroomStore.selectedClassroom.studentList = res.data
            arrangeClassroomData(classroomStore.selectedClassroom, res.data, loginInstance.subjectMainId)

            await this.getDeviceMac(classId, subjectId)
        } else {
            Alert.showErrorMessage(res.message)
            //给遥控器发上课成功消息
            remoteControl.sendStartClassResult({ code: 0, message: '加入失败' })
        }
    }

    async getDeviceMac(classId, subjectId) {
        let loadingInstance = ElLoading.service({ background: 'transparent' })
        const res = await ClassManageRequest.getDevices(classId)
        loadingInstance.close()
        if (res.code == 1) {
            let classroomStore = useClassroomStore()
            res.data.forEach(item => {
                let student = classroomStore.selectedClassroom.idStudentMap.get(item.studentId)
                if (student) {
                    student.deviceMac = item.device
                }
            })
            arrangeMacAddress(classroomStore.selectedClassroom)
            await this.startClass(classId, subjectId)
        } else {
            Alert.showErrorMessage(res.message)
            //给遥控器发上课成功消息
            remoteControl.sendStartClassResult({ code: 0, message: '加入失败' })
        }
    }

    async startClass(classId, subjectId) {
        let loadingInstance = ElLoading.service({ background: 'transparent' })
        
        const res = await ClassManageRequest.getStartClass(classId, subjectId)
        if (res.code == 1) {
            let classroomStore = useClassroomStore()
            classroomStore.selectedClassroom.classRecordId = res.data.classRecordId
            classroomStore.selectedClassroom.subjectId = subjectId
            let timesStore = useTimesStore()
            timesStore.startClock()
            await databaseHelper.addRecords()
            await databaseHelper.addLines(RecordType.startClass)

            if (window.electron) {
                await window.electron.fullScreen()
            }

            window.__router__.push('/classroom')

            // 遥控器进入 等待全屏后再去初始化黑板画布
            // 避免画布大小还是小窗口尺寸
            setTimeout(() => {
                this.sendClassStart()
            }, 500)

            //给遥控器发上课成功消息
            remoteControl.sendStartClassResult({ code: 1, message: 'success',recordId: classroomStore.selectedClassroom.classRecordId })
        } else if (res.code == -2001) {
            let data = res.data[0]
            let unfinishClassId = data['classId']
            let unfinishRecordId = data['classRecordId']

            await this.startNewClass(unfinishClassId, unfinishRecordId, classId, subjectId)
        } else {
            Alert.showErrorMessage('加入失败')
            //给遥控器发上课成功消息
            remoteControl.sendStartClassResult({ code: 0, message: '加入失败' })
        }
        this.changeStudent = false
        loadingInstance.close()
    }
    async sendClassStart() {
        let token = loginInstance.token
        let teacher = loginInstance.teacher
        let classroomStore = useClassroomStore()
        this.didStartClassroom(token, teacher, classroomStore.selectedClassroom)
    }

    async startNewClass(unfinishClassId, unfinishedRecordId, classId, subjectId) {
        if (unfinishClassId) {
            let loadingInstance = ElLoading.service({ background: 'transparent' })
            const res = await ClassManageRequest.classEnd(unfinishClassId, unfinishedRecordId)
            loadingInstance.close()
            if (res.code == 1) {
                this.startClass(classId, subjectId)
            } else {
                Alert.showErrorMessage('结束失败')
                //给遥控器发上课成功消息
                remoteControl.sendStartClassResult({ code: 0, message: '加入失败' })
            }
        } else {
            this.startClass(classId, subjectId)
        }
    }

    async stopClass(forceExit = false) {
       
        let classroomStore = useClassroomStore()
        let loadingInstance = ElLoading.service({ background: 'transparent' })
        let res
        if (forceExit) {
            res = { code: 1 }

        } else {
            res = await ClassManageRequest.classEnd(classroomStore.selectedClassroom.classId, classroomStore.selectedClassroom.classRecordId)
        }

        loadingInstance.close()
        if (res.code == 1) {
             //需要调用接口
            AppUpdater.checkUpdate(false, true)
            //交互数据清空
            await this.stopInteract(true)
            await databaseHelper.deleteBelowTime()
            this.changeStudent = false
            LoginRequest.loginLog(false)
            //通知遥控器下课
            remoteControl.sendStopClassResult({ code: 1, message: '结束成功' })
            const bleApServerStore = useBleApServerStore()
            bleApServerStore.forceStopBleServer()
            //清空视频
            const classroomUIStore = useClassroomUIStore()
            classroomUIStore.realTimeVideo = {
                count: 0,
                list: [],
                show: false
            }
            this.didStopClassroom()
            await databaseHelper.addLines(RecordType.endClass)
            loginInstance.cleanData()
            classroomStore.selectedClassroom = null
            window.__router__.push('/')
            if (window.electron) {
                window.electron.quiteFullScreen()
                window.electron.cancelTransparent()
            }
        } else {
            // Alert.showErrorMessage('结束失败')
            ElMessageBox.confirm("结束失败，是否强制退出", {
                confirmButtonText: '退出',
                cancelButtonText: '重试',
                customClass: 'stop-class-box',
                center: true,
                closeOnClickModal: false,
                distinguishCancelAndClose:true,
                // size:"small"
            }).then(() => {
                this.stopClass(true)
            }).catch((e) => {                
                if(e=='cancel'){
                    this.stopClass()
                }
            })
        }
    }
}
let roomUpdater = new ClassroomUpdater()
export default roomUpdater