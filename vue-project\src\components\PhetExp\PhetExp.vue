<template>
    <div class="phet-exp-container" v-if="showPhetExp">
        <RBPAlert @close="showPhetExp = false" @click.stop>
            <template v-slot:rbpDiv>
                <div class="phet-exp">
                    <iframe :src="url" width="100%" height="100%" frameborder="0"></iframe>
                    <div class="hiddenDiv"></div>
                </div>
            </template>
        </RBPAlert>
    </div>
</template>
<script setup>
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { storeToRefs } from 'pinia'
import { onMounted } from 'vue';
import { usePhetExpStore } from '@/stores/phet_exp_store'
import RBPAlert from '@/components/baseComponents/RBPAlert.vue'
import { ref } from 'vue'

const phetExpStore = usePhetExpStore()
const { url } = storeToRefs(phetExpStore)

const classroomUIStore = useClassroomUIStore()
const { showPhetExp } = storeToRefs(classroomUIStore)


</script>
<style lang="scss" scoped>
.phet-exp-container {
    position: absolute;
    height: 100%;
    width: 100%;
    z-index: var(--interact-alert-phet-z-index);
}

.phet-exp {
    width: 100%;
    height: 90%;
    position: relative;

    iframe {
        border-top-left-radius: 26px;
        border-top-right-radius: 26px;
    }

    .hiddenDiv {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 16vw;
        height: 5.5vh;
        background-color: black;
    }
}
</style>