<template>
    <div class="plane2d-full" @click.stop="handleHideClick">
        <div class="container" @click.stop>
            <div class="instruction-text">
                选择线格后点击黑板空白处添加
            </div>
            <div class="instruction-title">
                书写格子
            </div>
            <div class="grid-content">
                <div v-for="(item, index) in items" :key="index" class="grid-item" :class="{ selected: item.selected }"
                    @click="handleItemClick(item)">
                    <div class="item-content">
                        <img :src="item.image" :alt="item.title" class="item-image" />
                        <p class="item-title">{{ item.title }}</p>
                    </div>
                </div>
            </div>
            
        </div>
    </div>
</template>



<script setup>
import { UIFrames } from '@/classroom/frame_enums';
import { WrintngHelp} from '@/drawboard/writing_help/writing_help_root_view';
import { useDrawBoardStore } from '@/stores/drawboard_store';
import { storeToRefs } from 'pinia';
import { onMounted, ref } from 'vue';

const drawBoardStore = useDrawBoardStore()

const { writingGridsSelector } = storeToRefs(drawBoardStore)

const tabbarHeight = ref(UIFrames.tabbarHeight)

const items = ref([
    {
        title: "四线田字格",
        image: "/img/write_grid/four_character_grid.svg",
        type: WrintngHelp.fourCharacterGrid,
        selected: false
    },
    {
        title: "田字格",
        image: "/img/write_grid/character_grid.svg",
        type: WrintngHelp.characterGrid,
        selected: false
    },
    {
        title: "四线格",
        image: "/img/write_grid/four_line.svg",
        type: WrintngHelp.fourLine,
        selected: false
    },
    {
        title: "四线米字格",
        image: "/img/write_grid/four_mi_grid.svg",
        type: WrintngHelp.fourMiGrid,
        selected: false
    },
    {
        title: "米字格",
        image: "/img/write_grid/mi_grid.svg",
        type: WrintngHelp.miGrid,
        selected: false
    },
    {
        title: "五线谱",
        image: "/img/write_grid/musical_staff.svg",
        type: WrintngHelp.musicalStaff,
        selected: false
    },
    
])




onMounted(() => {

    if (writingGridsSelector.value.selectedItem) {
        items.value.forEach((item) => {
            if (item.type === writingGridsSelector.value.selectedItem.type) {
                item.selected = true
            }
        });
    }
})

function handleHideClick() {
    writingGridsSelector.value.showSelector = false
}

function handleItemClick(clickedItem) {
    items.value.forEach((item) => {
        if (item !== clickedItem) {
            item.selected = false;
        }
    });
    

    clickedItem.selected = !clickedItem.selected;
    
    if (clickedItem.selected) {
        writingGridsSelector.value.selectedItem = clickedItem
        if (drawBoardStore.blackBoardPainter) {
            drawBoardStore.blackBoardPainter.writingHelpView.addWrintingHelp(clickedItem.type, drawBoardStore.lineColor)
        }
    }
    else {
        writingGridsSelector.value.selectedItem = null
        if (drawBoardStore.blackBoardPainter) {
            drawBoardStore.blackBoardPainter.writingHelpView.addWrintingHelp(null, drawBoardStore.lineColor)
        }
    }
}


</script>


<style scoped>
.plane2d-full {
    position: absolute;
    z-index: var(--color-select-z-index);
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: center;
    align-items: flex-end;
    padding-bottom: v-bind("tabbarHeight + 'px'");
    box-sizing: border-box;
}

.container {
    width: 320px;
    height: 280px;
    background-color: var(--main-bc-color);
    border-radius: 26px;
    box-sizing: border-box;
    padding-left: 20px;
    padding-right: 20px;
    padding-top: 10px;
    padding-bottom: 10px;

}

.instruction-text {
    font-size: 12px;
    color: #565656;
    /* 与下方内容的间距 */
    text-align: center;
    /* 文字居中 */
}


.instruction-title {
    font-size: 12px;
    color: #a9a9a9;
    margin-bottom: 5px;
    margin-top: 12px;
    /* 与下方内容的间距 */
    text-align: left;
    /* 文字居中 */
}

.grid-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    /* 每行显示 4 个 */
    gap: 15px;
    /* 设置间距 */
    align-content: start;
    /* 内容从顶部开始排列 */
}

.grid-item {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px solid #ccc;
    /* 初始边框为灰色 */
    border-radius: 8px;
    /* 圆角 */
    padding: 2px;
    /* 内边距 */
    cursor: pointer;
    /* 鼠标指针变为手型 */
    transition: border-color 0.3s ease;
    /* 添加过渡效果 */
}

.grid-item.selected {
    border-color: #7fffaa;
    /* 选中时边框 */
}

.item-content {
    pointer-events: none;
    /* 父元素不可交互 */
    text-align: center;
    /* 文字居中 */
}

.item-image {
    width: 40px;
    /* 图片宽度 */
    height: 40px;
    /* 图片高度 */
}

.item-title {
    margin: 0;
    font-size: 14px;
    color: #333;
    padding-bottom: 3px;
}
</style>