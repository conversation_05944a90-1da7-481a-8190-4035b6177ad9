import axios from "axios"
import { ServerUrls } from "./server_urls"
import { loginInstance } from "@/login_instance/login_instance"

export class CloudDiskRequest {

    static defaultConfig() {
        return {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': loginInstance.token ?? localStorage.getItem('token'),
                "Ink-Mgboard-Subject-Id": loginInstance.subjectMainId,
                "Robot-Selected-Role-Codes": "MGBOARD_TEACHER"
            }
        }
    }
    //文件列表
    static async getList(params) {
        const url = ServerUrls.cloudDiskListUrl()
        try {
            const response = await axios.post(url, params,this.defaultConfig(), {
                timeout: 8000
            })
            return response.data
        } catch (e) {
            console.log(e)
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //文件夹
    static async getCatalogList(params) {
        const url = ServerUrls.cloudDiskCatalogUrl()
        try {
            const response = await axios.post(url, params,this.defaultConfig(), {
                timeout: 8000
            })
            return response.data
        } catch (e) {
            console.log(e)
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //获取空间
    static async getSpace(params) {
        const url = ServerUrls.cloudDiskSpaceUrl()
        try {
            const response = await axios.post(url, params,this.defaultConfig())
            return response.data
        } catch (e) {
            console.log(e)
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
    //wps信息
    static async getWpsInfo(params, isEdit) {
        const url = isEdit?ServerUrls.cloudDiskWpsEditUrl():ServerUrls.cloudDiskWpsPreviewUrl()
        try {
            const response = await axios.post(url, params,this.defaultConfig())
            return response.data
        } catch (e) {
            console.log(e)
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
} 