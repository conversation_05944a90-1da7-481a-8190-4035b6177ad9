<?xml version="1.0" encoding="UTF-8"?>
<svg width="33px" height="42px" viewBox="0 0 33 42" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>金</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="90.3205423%" id="linearGradient-1">
            <stop stop-color="#3C66E5" offset="0%"></stop>
            <stop stop-color="#5581F1" offset="9.25436581%"></stop>
            <stop stop-color="#8AA4F2" offset="19.4824219%"></stop>
            <stop stop-color="#668BF5" offset="39.0280331%"></stop>
            <stop stop-color="#6F91F7" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="5.1403326e-12%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#7CBECE" offset="0%"></stop>
            <stop stop-color="#E3F9FF" offset="24.2205811%"></stop>
            <stop stop-color="#A9EBFF" offset="43.8226318%"></stop>
            <stop stop-color="#7FC8DE" offset="100%"></stop>
        </linearGradient>
        <radialGradient cx="50%" cy="50%" fx="50%" fy="50%" r="56.3571829%" gradientTransform="translate(0.5, 0.5), scale(1, 0.9634), rotate(90), translate(-0.5, -0.5)" id="radialGradient-3">
            <stop stop-color="#969595" offset="0%"></stop>
            <stop stop-color="#EFECEC" offset="79.6597562%"></stop>
            <stop stop-color="#757575" offset="100%"></stop>
        </radialGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.1804199%" id="linearGradient-4">
            <stop stop-color="#F1F1F1" offset="0%"></stop>
            <stop stop-color="#BEC9C0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#C1C1C1" offset="0%"></stop>
            <stop stop-color="#E8E8E8" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-6" points="18.3024647 21.7975347 22.231024 22.6765182 19.3803425 25.9542871 19.842526 30.3526316 16.2226391 28.3530726 12.6027523 30.3526316 13.0649358 25.9542871 10.2142543 22.6765182 14.1428136 21.7975347 16.2226391 17.8789474"></polygon>
        <filter x="-4.2%" y="-4.0%" width="108.3%" height="108.0%" filterUnits="objectBoundingBox" id="filter-7">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.270295516   0 0 0 0 0.270295516   0 0 0 0 0.270295516  0 0 0 0.688073645 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="9互动课堂PC-展开学生" transform="translate(-810, -155)" fill-rule="nonzero">
            <g id="编组-8" transform="translate(129, 51)">
                <g id="编组-7" transform="translate(42, 104)">
                    <g id="金" transform="translate(639.893, 0)">
                        <g id="编组" transform="translate(-0, 0)">
                            <path d="M4.03621517,0 L28.4497329,0 C28.7520011,-2.49801642e-15 29.0380495,0.13672161 29.2278936,0.371934836 L31.1504298,2.75392046 C31.4649544,3.1436104 31.4427298,3.70586474 31.0984277,4.06951286 L16.9829649,18.9781071 C16.6032541,19.3791534 15.9703254,19.3964492 15.5692791,19.0167384 C15.5545032,19.0027486 15.5401577,18.988311 15.5262627,18.9734458 L1.56870998,4.04137795 C1.23984406,3.68955063 1.20846498,3.15329166 1.49405282,2.76550971 L3.23101436,0.406997768 C3.41948655,0.151083112 3.71838805,-5.26384077e-17 4.03621517,0 Z" id="路径_1366" fill="url(#linearGradient-1)"></path>
                            <polygon id="路径_1367" fill="url(#linearGradient-2)" points="24.4681753 0 16.1168304 8.87715964 7.43965945 0 3.00419243 0 16.1168304 13.5131579 29.0405269 0"></polygon>
                            <g id="编组-10" transform="translate(16.0224, 11.9539) rotate(-90) translate(-16.0224, -11.9539)translate(12.5175, 4.6776)">
                                <polygon id="路径_1368-4" fill="#E0E0E0" transform="translate(3.5049, 3.6612) scale(1, -1) translate(-3.5049, -3.6612)" points="-1.59383998e-12 7.32234302 2.31565959e-12 -8.16620591e-13 7.00978234 0.0460272289"></polygon>
                                <polygon id="路径_1369-4" fill="#C7C7C7" transform="translate(3.5049, 10.9145) scale(-1, 1) translate(-3.5049, -10.9145)" points="7.00978234 14.5526316 7.00978234 7.27631579 -2.31507591e-12 7.27631579"></polygon>
                            </g>
                            <g id="编组-10备份" transform="translate(16.0224, 37.9408) scale(1, -1) rotate(-90) translate(-16.0224, -37.9408)translate(12.5175, 30.6645)">
                                <polygon id="路径_1368-4" fill="#E0E0E0" transform="translate(3.5049, 3.6612) scale(1, -1) translate(-3.5049, -3.6612)" points="-1.59383998e-12 7.32234302 2.31565959e-12 -1.7581226e-12 7.00978234 0.0460272289"></polygon>
                                <polygon id="路径_1369-4" fill="#C7C7C7" transform="translate(3.5049, 10.9145) scale(-1, 1) translate(-3.5049, -10.9145)" points="7.00978234 14.5526316 7.00978234 7.27631579 -2.31507591e-12 7.27631579"></polygon>
                            </g>
                            <g id="编组-10" transform="translate(25.0349, 17.6711)">
                                <polygon id="路径_1368-4" fill="#E0E0E0" transform="translate(3.5049, 3.6612) scale(1, -1) translate(-3.5049, -3.6612)" points="-1.59383998e-12 7.32234302 2.31565959e-12 -1.2867907e-12 7.00978234 0.0460272289"></polygon>
                                <polygon id="路径_1369-4" fill="#C7C7C7" transform="translate(3.5049, 10.9145) scale(-1, 1) translate(-3.5049, -10.9145)" points="7.00978234 14.5526316 7.00978234 7.27631579 -2.31507591e-12 7.27631579"></polygon>
                            </g>
                            <g id="编组-10备份" transform="translate(3.5049, 24.9474) scale(1, -1) rotate(-180) translate(-3.5049, -24.9474)translate(0, 17.6711)">
                                <polygon id="路径_1368-4" fill="#E0E0E0" transform="translate(3.5049, 3.6612) scale(1, -1) translate(-3.5049, -3.6612)" points="-2.47177689e-12 7.32234302 1.43772268e-12 -1.2867907e-12 7.00978234 0.0460272289"></polygon>
                                <polygon id="路径_1369-4" fill="#C7C7C7" transform="translate(3.5049, 10.9145) scale(-1, 1) translate(-3.5049, -10.9145)" points="7.00978234 14.5526316 7.00978234 7.27631579 -3.19212218e-12 7.27631579"></polygon>
                            </g>
                        </g>
                        <path d="M16.0223658,37.4210526 C22.6710694,37.4210526 28.0391294,31.8488826 28.0391294,24.9473748 C28.0391294,18.0458671 22.6710694,12.4736715 16.0223658,12.4736715 C12.8339248,12.4689587 9.77476104,13.7816244 7.52019045,16.1219207 C5.26561986,18.4622169 4.00103752,21.6376995 4.00557766,24.9473748 C4.00557766,31.8488826 9.37366218,37.4210526 16.0223658,37.4210526 Z" id="路径" fill="url(#radialGradient-3)"></path>
                        <path d="M7.00978234,24.9731648 C7.00978234,28.3169262 8.7419509,31.4177273 11.4962169,33.0485319 C14.2521696,34.6793366 17.7148201,34.7614886 20.5483577,33.0485319 C23.3389704,31.391489 25.0530617,28.3062068 25.0349369,24.9731648 C25.0349369,19.8342949 21.020614,15.5921053 15.9826516,15.5921053 C10.9463757,15.5921053 7.00978234,19.8342949 7.00978234,24.9731648 Z" id="路径" stroke="url(#linearGradient-5)" stroke-width="1.5" fill="url(#linearGradient-4)"></path>
                        <polyline id="路径" points="15.7219404 18.0868421 17.7445707 22.2841169 22.0307445 23.1242915 19.0375289 26.5659489 19.5228216 31.1842105 15.7219404 29.1692308 11.7581396 31.1842105 12.4063519 26.5659489 9.41313629 23.1242915 13.6195834 22.2841169 15.7219404 18.0868421"></polyline>
                        <g id="路径">
                            <use fill="#A6A6A6" xlink:href="#path-6"></use>
                            <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-6"></use>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>