// import <PERSON><PERSON> from 'dexie';
import { LocalRequest } from "@/local_request/local_request"
import { loginInstance } from "@/login_instance/login_instance"

class WordDictHelper {
    constructor() {
        // this.db = new <PERSON>ie('dictation');
        // this.db.version(1).stores({
        //     words: '++id,time,interval'
        // });
    }

    async addWords(time, interval) {
        // await this.db.words.clear();
        // await this.db.words.add({
        //     time: time,
        //     interval: interval
        // });
        
        let data = await this.read()
        data.dict = JSON.stringify({
            time: time,
            interval: interval
        })

        loginInstance.dict = data.dict
        loginInstance.saveToDisk()

        if (window.location.host.includes("5173")) {
            localStorage.setItem("loginInfo", JSON.stringify(data))
        }
        // LocalRequest.saveDataToLocal(data)
    }

    async readWords() {
        // let words = await this.db.words.toArray();
        // if(words.length > 0) {
        //     return words[0];
        // }
        // return {time: 3, interval: 3}

        let data = await this.read()
        let dict = data.dict
        if(dict == undefined || dict == "") {
            return {time: 3, interval: 3}
        }else {
            return JSON.parse(dict)
        }
    }

    async read() {
        let data = await LocalRequest.readDataFromLocal()
        if ((data === null || typeof data === "string") && window.location.host.includes("5173")) {
            const dataStr = localStorage.getItem("loginInfo")
            if (dataStr) {
                data = JSON.parse(dataStr)
            }
        }
        return data
    }
}

const wordDictHelper = new WordDictHelper();
export {
    wordDictHelper
};