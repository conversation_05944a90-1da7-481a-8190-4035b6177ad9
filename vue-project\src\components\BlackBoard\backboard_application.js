import { BoardApplication } from "@/drawboard/board_application";
import { TeachPlanView } from "./teach_plan_view";
import * as THREE from 'three';
import { DrawZ, PainterOrder } from "@/drawboard/draw_enums";
import { UploadImagesView } from "./upload_images_view";
import { ImageDisplayView } from "./image_display_view";

import { MathRootView } from "@/drawboard/math_geometry/math_root_view";
import { useDrawBoardStore } from "@/stores/drawboard_store";
import { WritingHelpView } from "@/drawboard/writing_help/writing_help_root_view";

export class BlackBoardApplication extends BoardApplication {
    constructor(width, height, quality = DrawQuality.WQHD) {
        super(width, height, quality, 30)
        // 导学案
        this.teachPlans = new Map()
        // 单张上传的图片
        this.imageCacheMap = new Map()
        // 管理上传的图片组
        this.uploadImagesMap = new Map()

        this.teachPlanDataList = []
        this.imageCacheDataList = []
        this.uploadImagesDataList = []
        
        this.addMathRootView()
        this.addWrintHelpView()
    }

    addMathRootView() {
        this.mathRootView = new MathRootView(this.rootView.application, new THREE.Vector3(0, 0, 0), this.rootView.size)
        this.mathRootView.renderOrder = PainterOrder.planeGeometry
        this.mathRootView.addPlaneCallback(() => {
            
            const drawBoardStore = useDrawBoardStore()
            drawBoardStore.plane2DSelector.selectedItem = null
        })
        this.rootView.addSubView(this.mathRootView)
    }

    addWrintHelpView() {
        this.writingHelpView = new WritingHelpView(this.rootView.application, new THREE.Vector3(0, 0, 0), this.rootView.size)
        this.writingHelpView.renderOrder = PainterOrder.planeGeometry
        this.writingHelpView.addPlaneCallback(() => {
            const drawBoardStore = useDrawBoardStore()
            drawBoardStore.writingGridsSelector.selectedItem = null
        })
        this.rootView.addSubView(this.writingHelpView)
    }

    setLineStyle(width, color) {
        super.setLineStyle(width, color)
        if (this.mathRootView) {
            this.mathRootView.updateColor(color)
        }
    }

    addTeachPlan(teachPlan, presetX, presetY) {
        let planView = this.teachPlans.get(teachPlan.paperId)
        if (planView) {
            this.camera.position.set(planView.position.x, 0, DrawZ.initZ)
            this.animate()
            return
        }
        let x = this.lastDrawX + this.cameraInitSize.width / 2
        let y = 0
        if (presetX !== undefined && presetY !== undefined) {
            x = presetX
            y = presetY
        }else {
            this.lastDrawX = this.lastDrawX + this.cameraInitSize.width 
        }
        
        planView = new TeachPlanView(this.rootView.application, new THREE.Vector3(x, y, 0), this.cameraInitSize, teachPlan)
        planView.reanderOrder = PainterOrder.background
        this.teachPlans.set(teachPlan.paperId, planView)
        this.camera.position.set(x, 0, DrawZ.initZ)
        this.rootView.addSubView(planView)
        return planView
    }


    addImage(imageUrl, presetX, presetY,id) {
        const encodeUrl = encodeURIComponent(imageUrl)
        let imageView = this.imageCacheMap.get(encodeUrl)
        if (imageView) {
            this.camera.position.set(imageView.position.x, 0, DrawZ.initZ)
            this.animate()
            return
        }
        let x = this.lastDrawX + this.cameraInitSize.width / 2
        let y = 0
        if (presetX !== undefined && presetY !== undefined) {
            x = presetX
            y = presetY
        }
        else {
            this.lastDrawX = this.lastDrawX + this.cameraInitSize.width
        }

        imageView = new ImageDisplayView(this.rootView.application, new THREE.Vector3(x, y, 0), this.cameraInitSize, imageUrl)
        
        imageView.renderOrder = PainterOrder.background
        imageView.onDeleted((view) => {
            if (this.lastDrawX === view.position.x + this.cameraInitSize.width / 2) {
                this.lastDrawX = this.lastDrawX - this.cameraInitSize.width
            }
            this.rootView.removeDrawInSubView(view)
            view.removeFromSuperView()
            view.dispose()
        })
        this.imageCacheMap.set(encodeUrl,imageView)
        this.rootView.addDrawInSubView(imageView)
        this.camera.position.set(x, 0, DrawZ.initZ)
        this.resetInstallFrame()
        this.rootView.addSubView(imageView)
        return imageView
    }

    getOrCreateUploadImageView(taskId, presetX, presetY) {
        let uploadImagesView = this.uploadImagesMap.get(taskId)
        if (!uploadImagesView) {
            let x = this.lastDrawX + this.cameraInitSize.width / 2
            let y = 0
            if (presetX !== undefined && presetY !== undefined) {
                x = presetX
                y = presetY
            }
            else {
                this.lastDrawX = this.lastDrawX + this.cameraInitSize.width
            }
            uploadImagesView = new UploadImagesView(this.rootView.application, new THREE.Vector3(x, y, 0), this.cameraInitSize, taskId)
            uploadImagesView.onAllDelete(() => {
                if (this.lastDrawX === uploadImagesView.position.x + this.cameraInitSize.width / 2) {
                    this.lastDrawX = this.lastDrawX - this.cameraInitSize.width
                }
                uploadImagesView.removeFromSuperView()
                this.uploadImagesMap.delete(taskId)
                uploadImagesView.dispose()
            })

            this.uploadImagesMap.set(taskId, uploadImagesView)
            this.rootView.addSubView(uploadImagesView)
        }
        return uploadImagesView
    }

    /// 加载遥控器拍照上传图片
    addUploadPictures(taskId, imageUrls, presetX, presetY) {
        let uploadImagesView =  this.getOrCreateUploadImageView(taskId, presetX, presetY)
        uploadImagesView.addImages(imageUrls)
        const lastPos = uploadImagesView.getLastImagePosition()
        this.camera.position.set(uploadImagesView.position.x, lastPos.y, DrawZ.initZ)
        this.resetInstallFrame()
        this.animate()
        return uploadImagesView
    }

    //清除image
    clearAllImage() {
        let uploadViews = Array.from(this.uploadImagesMap.entries()).reverse()
        for (const [key, e] of uploadViews) {
            if (this.lastDrawX === e.position.x + this.cameraInitSize.width / 2) {
                this.lastDrawX = this.lastDrawX - this.cameraInitSize.width
            }
            e.removeFromSuperView()
            e.dispose()
        }

        this.uploadImagesMap.clear()

        let imageViews = Array.from(this.imageCacheMap.entries()).reverse()
        for (const [key, e] of imageViews) {
            if (this.lastDrawX === e.position.x + this.cameraInitSize.width / 2) {
                this.lastDrawX = this.lastDrawX - this.cameraInitSize.width
            }
            e.removeFromSuperView()
            e.dispose()
        }


        this.imageCacheMap.clear()
        this.animate()
    }

    //清除导学案
    clearAllTeachPlans() {
        let planViews = Array.from(this.teachPlans.entries()).reverse()
        for (const [key, e] of planViews) {
            if (this.lastDrawX === e.position.x + this.cameraInitSize.width / 2) {
                this.lastDrawX = this.lastDrawX - this.cameraInitSize.width
            }
            e.removeFromSuperView()
            e.dispose()
        }
        this.teachPlans.clear()
        this.animate()
    }

    //清除所有图形
    clearAllShape() {
        this.mathRootView.clearShape()
        this.animate()
    }

    //清除所有线格
    clearAllGrids(){
        this.writingHelpView.clearShape()
        this.animate()
    }
    /// 删除已上传的图片
    deleteUploadPicture(taskId, imageUrls) {
        let view = this.uploadImagesMap.get(taskId)
        if (view) {
            imageUrls.forEach((imageUrl) => {
                view.deleteImage(imageUrl)
            })
        }
    }


    dispose() {
        this.teachPlans.clear()
        this.imageCacheMap.clear()
        this.uploadImagesMap.clear()
        super.dispose()
    }
}