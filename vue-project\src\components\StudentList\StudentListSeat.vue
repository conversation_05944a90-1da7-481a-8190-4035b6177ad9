<template>
    <div class="studentseat">
        <div class="mainContainer">
            <div class="seat-grid" :style="{ gridTemplateColumns: 'repeat(' + rowCount + ', 1fr)' }">
                <StudentGridItem :student="stu" v-for="(stu, i) in locationStudentArray" :key="i"></StudentGridItem>
            </div>
        </div>
        <div class="desk">
            <img src="/img/svg/icon_desk.svg">
        </div>
    </div>
</template>
<script setup>
import { ref, onMounted, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useClassroomStore } from '@/stores/classroom.js'
import StudentGridItem from '@/components/StudentList/StudentGridItem.vue'
import { RBPColors } from '@/components/baseComponents/RBPColors.js'

const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)
const locationStudentArray = ref([])
const rowCount = ref(0)
const containerWidth = computed(() => {
    return `calc(100% / 10 * ${rowCount.value})`
})
onMounted(() => {
    rowCount.value = selectedClassroom.value.seatStudentMap.seatStudentArray[0].length
    locationStudentArray.value = selectedClassroom.value.seatStudentMap.seatStudentArray.flat()
})
</script>
<style lang="scss" scoped>
@import "@/assets/scss/mixin.scss";

.studentseat {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    // background-color: yellow;

    .mainContainer {
        width: 100%;
        flex: 1;
        // background-color: red;
        border-radius: 26px;
        border: 1px solid var(--border-bar-color);
        box-sizing: border-box;
        padding: 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
        overflow-y: auto;

        .seat-grid {
            width: v-bind(containerWidth);
            // max-height: 100%;
            display: grid;
            box-sizing: border-box;
            
        }
    }

    .desk {
        height: 70px;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
            width: 166px;
            height: 70px;
        }
    }
}
</style>