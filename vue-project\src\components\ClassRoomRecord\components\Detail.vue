<template>
    <div class="record-detail">
        <div class="detail">
            <div class="answers">
                <div @click="ansChange(item)" v-for="item in answers" class="ans-item" :class="item.answer == activeAns.answer?'ans-item-active':''">
                    <div class="bc-content" :class="getStyle(item)"></div>
                    <div class="ans"  >
                        <div>{{ dealAnswer(item.answer) }}</div>
                    </div>
                    <span class="num">{{ item.stus.length }}人</span>
                </div>
            </div>
            <div style="height: 120px;overflow: auto;">
                <div class="students" v-if="activeAns.stus">
                    <div class="stu" v-for="item in activeAns.stus" :class="{ 'stu-active': stuId == item.studentExtId }"
                        @click="stuDetail(item)">
                        {{ item.deviceAlias}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, defineExpose } from 'vue';
const props = defineProps({
    quesInfo: Object,
    stuId: String,
})
const emits = defineEmits(['setRandomList', 'setQuesStuInfo'])
const answers = ref([])
const activeAns = ref({})
function detailRandom() {
    let { trueStu, falseStu, unAnswerStu, unCorrectStu } = props.quesInfo
    let res = [...trueStu, ...falseStu, ...unAnswerStu, ...unCorrectStu]
    emits('setRandomList', res)
}
function ansChange(item) {
    activeAns.value = item
    if (item.stus && item.stus.length) {
        stuDetail(item.stus[0])
    } else {
        stuDetail(null)
    }
}

function dealAnswer(answer) {
    return answer.replace(/<[^>]+>/g, '')
}
function stuDetail(item) {
    emits('setQuesStuInfo', item)
}
function getAnswers() {
    let { trueStu, falseStu, unAnswerStu, unCorrectStu, quesType, quesAnswer } = props.quesInfo
    let trueOption = [], falseOption = [], res = []
    quesAnswer == '暂无' && (quesAnswer = '')
    if (quesAnswer) {
        trueOption.push({
            answer: quesAnswer,
            type: 1,
            stus: trueStu
        })
    }
    falseOption = getFalseOption(trueStu, falseStu, quesType, quesAnswer)
    const unFit = [] //将错误答案未匹配到选项的加入其他
    falseStu.forEach(item => {
        let index = falseOption.findIndex(it => {
            if (quesType == 3) {
                item.recogContent = item.recogContent == '1' ? '对' : '错'
            }
            return item.recogContent == it.answer
        })
        if (index > -1) {
            falseOption[index].stus.push(item)
        }else{
            unFit.push(item)
        }
    })
    falseOption = falseOption.sort((a, b) => { return b.stus.length - a.stus.length })
    res = [...trueOption, ...falseOption]
    if (unAnswerStu.length) {
        res = [...res, {
            answer: '未作答',
            type: -1,
            stus: unAnswerStu
        }]
    }
    if (unCorrectStu.length||unFit.length) {
        res = [...res, {
            answer: '其他',
            type: -1,
            stus: [...unFit,...unCorrectStu]
        }]
    }
    answers.value = res
    res.length && ansChange(res[0])
}
function getFalseOption(trueStu, falseStu, quesType, quesAnswer) {
    let falseOption = [], totalOption = []
    if (quesType == 2) { // 单选题
        totalOption = ['A', 'B', 'C', 'D', 'E', 'F']
        // // 判断是否有EF选项
        // let totalAnsStu = [...trueStu, ...falseStu]
        // if (totalAnsStu.find(item => {return item.recogContent == 'E' || item.recogContent == 'F'})) {
        // 	totalOption = [...totalOption, 'E', 'F']
        // }
    } else if (quesType == 5) { // 多选题
        falseStu.forEach(item => {
            if (!totalOption.find(it => { return item.recogContent == it })) {
                totalOption.push(item.recogContent)
            }
        })
    } else { // 判断题
        totalOption = ['对', '错']
    }
    totalOption.forEach(item => {
        if (item != quesAnswer) {
            falseOption.push({
                answer: item,
                type: 0,
                stus: []
            })
        }
    })
    return falseOption
}
function getStyle(item) {
    let className = ''
    if (item.type == 1) {
        className = 'right'
       
    } else if (item.type == 0) {
        className = 'error'
    } else {
       className = 'unfinished'
    }
    return className
}
watch(() => props.quesInfo, () => {
    getAnswers()
}, { immediate: true })
defineExpose({ detailRandom })
</script>

<style lang="scss" scoped>
.record-detail {
    .answers {
        display: flex;
        margin-bottom: 30px;
        // background-color: green;
    }

    .ans-item {
        width: 102px;
        height: 48px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position:relative;
        margin-left:12px;
        background:transparent;
        .bc-content{
            width: 102px;
            height: 48px;
            border-radius: 15px;
            position:absolute;
            top:0;
            z-index:0;
            opacity:0.2
        }
        .right{
            background:var(--correct-color)
        }
        .error{
            background:var(--error-color)
        }
        .unfinished{
            background:var(--unfinished-color)
        }
    }
    .ans-item-active{
        .bc-content{
            opacity:1
        }
        .ans{
            color:var(--anti-text-color)
        }
        .num{
            color:var(--anti-text-color)
        }
    }

    

    .ans {
        z-index:1;
        font-weight: 500;
        font-size: 21px;
        color: var(--text-color);
        line-height: 31px;
        text-align: center;
    }

    .num {
        z-index:1;
        font-weight: 500;
        font-size: 15px;
        color: var(--secondary-text-color);
        line-height: 21px;
        text-align: center;
    }

    .students {
        display: flex;
        flex-wrap: wrap;
        gap:6px 12px;
        margin-left:12px;
        .stu {
            width: calc((100% - 132px) / 12);
            height: 64px;
            background: var(--main-bc-color);
            border-radius: 15px;
            border: 2px solid var(--border-bar-color);
            display:flex;
            align-items:center;
            justify-content:center;
            // word-break: keep-all;
            // white-space: nowrap;
            overflow: hidden;
            box-sizing:border-box;
            font-weight: 500;
            font-size: 16px;
            padding: 0px 4px;
            color: var(--text-color);
            line-height: 31px;
            text-align: center;
        }
        .stu-active {
            background: var(--primary-color);
            color: var(--anti-text-color);
            border:none
        }
    }

    

    
}
</style>