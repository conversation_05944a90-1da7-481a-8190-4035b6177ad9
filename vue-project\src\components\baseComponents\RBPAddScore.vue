/**
加减分按钮
*/
<template>
    <div class="rbp-add-score" :class="{ 'rbp-add': props.score > 0, 'rbp-sub': props.score < 0 }">
        {{ props.score }}
    </div>
</template>
<script setup>
import { defineProps } from 'vue'
import { RBPColors } from '@/components/baseComponents/RBPColors.js'

const props = defineProps({
    score: String
})

</script>
<style lang="scss" scoped>

.rbp-add-score {
    border-radius: 54px;
    width: 54px;
    height: 54px;
    cursor: pointer;
    font-size: 21px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.rbp-add {
    background-color: var(--correct-bg-color);
    color: var(--correct-color);
}

.rbp-sub {
    background-color: var(--error-bg-color);
    color: var(--error-color);
}
</style>