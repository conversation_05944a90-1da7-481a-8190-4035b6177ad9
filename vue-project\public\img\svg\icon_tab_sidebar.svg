<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="48px" viewBox="0 0 48 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>工具栏icon/侧边栏</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="0切图" transform="translate(-1852, -1011)">
            <g id="工具栏icon/侧边栏" transform="translate(1852, 1011)">
                <rect id="矩形" stroke="#979797" fill="#D8D8D8" opacity="0" x="0.5" y="0.5" width="47" height="47"></rect>
                <g id="编组-3" transform="translate(9, 12)">
                    <g id="编组-2" transform="translate(5, 0)">
                        <g id="编组" transform="translate(12.5, 12) rotate(-90) translate(-12.5, -12)translate(0.5, -0.5)">
                            <path d="M24,4 L24,23.4166667 C24,24.2911417 23.4030667,25 22.6666667,25 L1.33333333,25 C0.596953333,25 0,24.2911417 0,23.4166667 L0,4" id="路径" stroke="#565656" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M4.5,3 C5.32845,3 6,2.32845 6,1.5 C6,0.67155 5.32845,1.49406909e-14 4.5,1.49406909e-14 C3.67155,1.49406909e-14 3,0.67155 3,1.5 C3,2.32845 3.67155,3 4.5,3 Z" id="路径" fill="#565656"></path>
                            <path d="M9.5,3 C10.32845,3 11,2.32845 11,1.5 C11,0.67155 10.32845,1.46345292e-14 9.5,1.46345292e-14 C8.67155,1.46345292e-14 8,0.67155 8,1.5 C8,2.32845 8.67155,3 9.5,3 Z" id="路径" fill="#565656"></path>
                            <path d="M14.5,3 C15.32845,3 16,2.32845 16,1.5 C16,0.67155 15.32845,1.43283676e-14 14.5,1.43283676e-14 C13.67155,1.43283676e-14 13,0.67155 13,1.5 C13,2.32845 13.67155,3 14.5,3 Z" id="路径" fill="#565656"></path>
                        </g>
                    </g>
                    <path d="M-5.5,7.42307692 C-5.5,6.36099038 -4.90304667,5.5 -4.16666667,5.5 L17.1666667,5.5 C17.9030667,5.5 18.5,6.36099038 18.5,7.42307692 L18.5,18.5 L-5.5,18.5 L-5.5,7.42307692 Z" id="路径" stroke="#565656" stroke-width="2" stroke-linejoin="round" transform="translate(6.5, 12) rotate(-90) translate(-6.5, -12)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>