import { defineStore } from "pinia";
import { ClassRoomRequest } from '@/server_request/classroom_request'
import { ElLoading } from 'element-plus'
import { ref } from "vue"
import { dbHelper } from '@/utils/db_helper'
import { remoteControl } from "@/remote_control/remote_control";

export const useEvaluationScoreStore = defineStore('evaluationScoreStore', () => {

    const addScore = ref('')
    const currentStudents = ref([])

    const evaluationList = ref([])
    const fixedCorrectList = ref([])
    const fixedErrorList = ref([])

    const evaluationAddList = ref([])
    const evaluationMinusList = ref([])

    const alone = ref(true)
    const remoteFlag = ref(0)
    let score = null

    async function queryDb() {
        let res = await dbHelper.getAddScoreTypeByTeacherId()
        if (res == undefined) {
            alone.value = true
        } else {
            if (res.status == '1') {
                alone.value = true
            } else if (res.status == '2') {
                alone.value = false
            }
        }
    }

    async function getEvaluationItemFixedList() {
        if (evaluationList.value.length == 0) {
            let loadingInstance = ElLoading.service({ background: 'transparent' })
            const res = await ClassRoomRequest.getEvaluationItemList(true)
            loadingInstance.close()
            if (res.code == 1) {
                if (res.data) {
                    evaluationList.value = res.data
                    fixedCorrectList.value = res.data.filter(item => item.evaluationItemType == true)
                    fixedErrorList.value = res.data.filter(item => item.evaluationItemType == false)
                    remoteControl.handleInteractState()
                }
            }
        }
    }

    async function getEvaluationItemItemList() {
        if (evaluationAddList.value.length == 0 && evaluationMinusList.value.length == 0) {
            let loadingInstance = ElLoading.service({ background: 'transparent' })
            const res = await ClassRoomRequest.getEvaluationItemList(false)
            loadingInstance.close()
            if (res.code == 1) {
                if (res.data) {
                    evaluationAddList.value = res.data.filter(item => item.evaluationItemType == true)
                    evaluationMinusList.value = res.data.filter(item => item.evaluationItemType == false)
                    remoteControl.handleInteractState()
                }
            }
        }
    }

    function cleanData() {
        // evaluationList.value = []
        // evaluationAddList.value = []
        // evaluationMinusList.value = []
        addScore.value = ''
        currentStudents.value = null
    }

    return {
        alone,
        evaluationList,
        evaluationAddList,
        evaluationMinusList,
        addScore,
        currentStudents,
        fixedCorrectList,
        fixedErrorList,
        remoteFlag,
        score,
        cleanData,
        getEvaluationItemItemList,
        getEvaluationItemFixedList,
        queryDb
    }
})