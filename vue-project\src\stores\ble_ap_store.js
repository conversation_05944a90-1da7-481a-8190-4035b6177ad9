import { LocalRequest } from "@/local_request/local_request"
import { defineStore, storeToRefs } from "pinia"
import { ref } from "vue"
import { useClassroomStore } from "./classroom"



/// 设备是TCP连接还是蓝牙AP连接
export const DeviceFrom = {
    TCP: 'tcp',
    BLE: 'ble'
}


export const BleConnectStatus = {
    CONNECTING: 2,
    CONNECTED: 1,
    DISCONNECTED: 0,
}

export const useBleApServerStore = defineStore('ble_ap_store', () => {

    const bleDevices = ref({})
    // 是否启动服务
    const isService = ref(false)
    // 是否自动连接
    const isAutoConnect = ref(true)
    //轮询id
    const intervalId = ref(null)

    const autoTest = ref(false)

    const timeOut = ref(null)

    function addDevices(macAddress, status) {
        if (!macAddress) {
            return
        }
        const classroomStore = useClassroomStore()
        let studentName = classroomStore.selectedClassroom?.macStudentMap?.get(macAddress)?.name ?? undefined
        bleDevices.value[macAddress] = { macAddress, status, name: studentName }
    }

    function getDeviceStatus(macAddres) {
        return bleDevices.value[macAddres]?.status ?? BleConnectStatus.DISCONNECTED
    }

    function setMacForStudent(student, macAddress) {
        let item = bleDevices.value[macAddress]
        if (item) {
            item.name = student.name
        }
    }

    //开始轮询检测
    function startPolling() {
        if (intervalId.value) {
            stopPolling()
        }
        if (timeOut.value) {
            clearTimeout(timeOut.value)
        }
        timeOut.value = setTimeout(() => {
            checkUnConnect()
            intervalId.value = setInterval(() => {
                checkUnConnect()
            }, 10000)
        }, 3000)
    }
    // 停止轮询
    function stopPolling() {
        clearInterval(intervalId.value)
        intervalId.value = null
        if (timeOut.value) {
            clearTimeout(timeOut.value)
        }
        timeOut.value = null
    }

    //检查未被连接的设备进行连接
    function checkUnConnect() {

        // let macStrings = Object.values(bleDevices.value)
        //     .filter((item) => item.status !== BleConnectStatus.CONNECTED && item.name) // 过滤掉已连接的设备
        //     .map((item) => item.macAddress); // 提取 key (mac 地址)

        // let macStrings = Object.values(bleDevices.value)
        //     .filter((item) => item.status === BleConnectStatus.CONNECTED && item.name) // 过滤掉已连接的设备
        //     .map((item) => item.macAddress); // 提取 key (mac 地址)

        const classroomStore = useClassroomStore()
        let macMap = classroomStore.selectedClassroom.macStudentMap
        // let studentMacs = [...macMap.keys()].filter(key => key !== undefined && !macStrings.includes(key));
        let studentMacs = [...macMap.keys()].filter(key => key !== undefined);

        // console.log("connect student macs" , studentMacs)

        // console.log("--------------------------------------检测", macStrings);
        if (studentMacs.length > 0) {
            LocalRequest.connectBleMultiDevice(studentMacs)
        }
        //执行多个连接
    }

    function forceStopBleServer() {
        if (isService.value) {
            stopPolling()
            LocalRequest.stopBleApServer()
            bleDevices.value = {}
            isService.value = false
        }
    }
    return {
        bleDevices,
        isService,
        isAutoConnect,
        autoTest,
        setMacForStudent,
        addDevices,
        getDeviceStatus,
        startPolling,
        stopPolling,
        forceStopBleServer
    }
})