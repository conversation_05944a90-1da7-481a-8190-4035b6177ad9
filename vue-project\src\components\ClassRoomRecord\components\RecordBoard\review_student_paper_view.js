import { BoardView } from "@/drawboard/board_view";
import * as THREE from 'three';
import { PainterOrder } from "@/drawboard/draw_enums";
import { BoardLabel } from "@/drawboard/board_label";
import { RecordSingleStudentPaperView } from "./record_single_paper_view";
export class RecordReviewStudentPaperView extends BoardView {

    constructor(application, pos, size, student) {
        super(application, pos, size)

        this.student = student
        //图片列表
        this.imageList = []

        this.setupUI()
    }


    setupUI() {
        let imageLength = this.student.images.length
        let labelSize = { width: this.size.width / 4, height: this.size.height / 30/imageLength }
        let nameLabel = new BoardLabel(
            this.application,
            new THREE.Vector3(- this.size.width / 2 + labelSize.width / 2 + 0.01, this.size.height / 2 - labelSize.height / 2, 0),
            labelSize,
            this.student.studentName,
            { fontSize: 0.024, color: 0x000000, align: 'left' })
        nameLabel.renderOrder = PainterOrder.studentName
        this.addSubView(nameLabel)
        let imageIndex = 1;
        this.student.images.forEach((e)=>{
            let imageY = (imageIndex / imageLength  - 0.5 - 1/imageLength/2)*this.size.height
            
            let imageView = new RecordSingleStudentPaperView(
                this.application,
                new THREE.Vector3(0,-imageY, 0),
                {width:this.size.width,height:this.size.height / imageLength},
                e,
                this.student.studentId
                // (area)=>{
                //     console.log("-----------------------------------",area);
                    
                //     const recordDrawBoard = useRecordDrawboardStore()
                //     const {correctArea} = storeToRefs(recordDrawBoard)
                //     area.popover = true
                //     area.studentId = this.student.studentId
                //     correctArea.value = area
                    
                // }
            )
            this.addSubView(imageView)
            this.imageList.push(imageView)
            imageIndex++
        })

    }
    //修改view
    changeView(type,visible){
        this.imageList.forEach((v)=>{
            if(v){
                v.visible = false
                this.animate()
                v.changeView(type,visible)
                v.visible = true
                this.animate()
            }
        })
        
    }

    dispose() {
        this.student= null
        this.imageList = []
        super.dispose()
    }
}