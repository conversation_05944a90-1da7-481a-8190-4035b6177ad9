<template>
    <div  id="questionSelectBox" class="rbp-questions-alert">
        <div class="rbp-content">
            <div class="rbp-title">
                <slot name="rbpTitle"></slot>
            </div>
            <RBPLine width="90%"></RBPLine>
            <div class="rbp-div">
                <slot name="rbpDiv"></slot>
            </div>
            <div class="rbp-btns">
                <slot name="rbpBtns"></slot>
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref, computed, onMounted, getCurrentInstance, watch, defineProps, defineEmits, toRefs } from 'vue'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { DisplayDirection, ViewStatus } from '@/classroom/frame_enums'
import { storeToRefs } from 'pinia'
import { UIFrames } from '@/classroom/frame_enums'
import { RBPColors } from '@/components/baseComponents/RBPColors.js'
import RBPLine from '@/components/baseComponents/RBPLine.vue'

const classroomUIStore = useClassroomUIStore()
const { displayDirection, mainContentHeight, mainContentTopSpace } = storeToRefs(classroomUIStore)

const calcTop = computed(() => {
    // return `calc(${UIFrames.timeHeight}px + ${mainContentTopSpace.value}px)`
    return `calc(20px + ${mainContentTopSpace.value}px)`
})
const contentHeight = computed(() => {
    // return `calc(${mainContentHeight.value}px - ${UIFrames.timeHeight}px)`
    return `calc(${mainContentHeight.value}px - 20px)`
})
function getBorderRadius() {
    if (displayDirection.value === DisplayDirection.Left) {
        return '0 36px 36px 0'
    } else {
        return '36px  0px  0px  36px'
    }
}
function getInteractBtnsBottom() {
    return (40 + UIFrames.pptTabHeight) + 'px'
}
</script>
<style lang="scss" scoped>
.rbp-questions-alert {
    position: absolute;
    top: v-bind(calcTop);
    width: 400px;
    height: v-bind(contentHeight);
    z-index: var(--interact-z-index);
    left: v-bind("displayDirection === DisplayDirection.Left ? '0' : null");
    right: v-bind("displayDirection === DisplayDirection.Left ? null : '0'");

    .rbp-content {
        border: 1px solid var(--border-bar-color);
        border-radius: v-bind("getBorderRadius()");
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        background-color: var(--main-bc-color);
        display: flex;
        flex-direction: column;
        align-items: center;

        .rbp-title {
            height: 62px;
            width: 100%;
        }

        .rbp-div {
            flex: 1;
            width: 100%;
            overflow: auto;
        }

        .rbp-btns {
            width: 100%;
            margin-bottom: v-bind("getInteractBtnsBottom()");
        }
    }
}
</style>