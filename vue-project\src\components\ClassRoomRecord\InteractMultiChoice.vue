<template>
  <div class="group-datail">
    <div class="boardImg" v-show="showBoard">
      <el-image :src="imgUrl" fit="contain" @click="showBoard = false"></el-image>
    </div>
    <div class="top multiple-options">
      <div class="ques" v-show="currMode == '选项统计'">
        <div class="que" v-for="(item, index) in comboQuestions" :key="index"> 
          <BaseQuestion :q-index="item.qid" size="small" :answer-student="item.stusStatistic" :is-multi="true" :ratio="item.rightProgress" :ques-type="item.type" :answer="item.answer" :options="item.answerStudents" ></BaseQuestion>
        </div>
      </div>
      <div class="stu-statistic" v-show="currMode == '学生统计'">
        <div class="item" v-for="(item, index) in comboQuestions" :key="index">
          <div class="item-tit">第{{ item.qid }}题 &nbsp;{{ getTypeName(item) }}</div>
          <div class="item-ans">
            <div class="item-ops" :class="{ 'right-ops': ops.answer && item.answer == ops.answer }"
              v-for="(ops, index) in item.stusStatistic" :style="{ 'display': ops.stus.length ? '' : 'none' }"
              :key="ops.answer">
              <div class="ops-tit">
                <span class="desc" v-if="!ops.answer">未作答 </span>
                <template v-else>
                  <span class="desc" v-if="ops.answer && item.answer == ops.answer">正确答案</span>
                  <span class="desc" v-else>其他答案</span>
                  <!-- <span class="desc" v-else style="opacity: 0;">其他答案</span> -->
                </template>
                <span v-if="ops.answer">{{ ops.answer }}：</span>
                <span> {{ ops.stus.length }} 人</span>
              </div>
              <div class="stus-wrap">
                <div class="stu-item" v-for="stu in ops.stus" :key="stu.studentId">
                  <img class="avatar" v-if="stu.headUrl" :src="stu.headUrl">
                  <img class="avatar" v-else src="@/assets/img/online.png">
                  <div class="name">{{ stu.studentName }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
    </div>

    <div class="footer-title">
			<div class="ratio-button">
				<RBPSegButton :options="['选项统计','学生统计']" :currentValue="currMode" @updateCurrentValue="changeMode">
                </RBPSegButton>
			</div>
			<RBPButton :btn-selected="true" @click="showBoard = true" btn-type="big" v-if="imgUrl" btn-text="查看黑板图">
						
			</RBPButton>
		</div>

    <el-dialog title="题目" :visible.sync="dialogVisible" width="70%">
      <div class="ques-body" v-if="question" v-html="question.quesBody"></div>
      <img v-if="imgUrl" :src="imgUrl" />
      <span slot="footer" class="dialog-footer"></span>
    </el-dialog>
  </div>
</template>
<script setup>
import RBPButton from '../baseComponents/RBPButton.vue';
import RBPSegButton from '../baseComponents/RBPSegButton.vue';
import { ClassroomRecordRequest } from '@/server_request/classroom_record';
import { onMounted, ref } from 'vue';
import { useInteractStore } from '@/stores/interact_store';
import BaseQuestion from './components/BaseQuestion.vue';
import { getTypeName } from './js/utils';
const interactStore = useInteractStore()
//题目数据
const imgUrl = ref('')
const comboQuestions = ref([])

//底部选中
const currMode = ref('选项统计')
const queryData = interactStore.interactResult

const showBoard = ref(false)
async function getStatistic() {
  try {
    let params = {
      classId: queryData.classId,
      classRecordId: queryData.classRecordId,
      interactId: queryData.interactId,
    }
    let { data } = await ClassroomRecordRequest.groupAnswer(params)
    imgUrl.value = data.imgUrl
    // data.comboQuestions = data.comboQuestions.concat(data.comboQuestions)
    data.comboQuestions.forEach(item => {
      item.answer === 'False' && (item.answer = '错')
      item.answer === 'True' && (item.answer = '对')
      item = getItemStus(item)
      if (item.answerStudents && item.stus.length) {
        item.answerStudents.forEach(ops => {
          ops.progress = ops.optionStuPOs.length / item.stus.length * 100
          ops.progress = ops.progress.toFixed(2) + '%'
        })
      }
      item = getRightProgress(item)
      item = getStusStatistic(item)
    })
    comboQuestions.value = data.comboQuestions
  } catch (e) {
    console.log(e)
  }
}
/**
 * @param {Object} item
 * 获取学生统计，先正确答案，后其他答案，最后未作答学生
 * 判断题，2个选项，对，错
 * 单选题，6个选项，abcdef
 * 多选题，有几个作答选项显示几个
 */
function getStusStatistic(item) {
  item.stusStatistic = []
  let options = []
  if (item.type == 4) {
    options = item.stus.map(stu => { return stu.answer })
  } else if (item.type == 3) {
    options = ['A', 'B', 'C', 'D', 'E', 'F']
  } else if (item.type == 2) {
    options = ['对', '错']
  }
  if (item.answer) {
    item.stusStatistic.push({
      answer: item.answer,
      stus: []
    })
  }
  options.forEach(ops => {
    if (!item.stusStatistic.find(it => { return it.answer == ops })) {
      item.stusStatistic.push({
        answer: ops,
        stus: []
      })
    }
  })
  item.stusStatistic.push({
    answer: '',
    stus: []
  })
  item.stus.forEach(stu => {
    !stu.answer && (stu.answer = '')
    let index = item.stusStatistic.findIndex(it => { return it.answer == stu.answer })
    if (index > -1) {
      item.stusStatistic[index].stus.push(stu)
    }
  })
}
// 获取每个题目中所有的学生，包含每个选项中的学生+未作答的学生
function getItemStus(item) {
  item.stus = []
  let temp = { ...item.statistic }
  if (temp.answerStudents) {
    temp.answerStudents.forEach(ops => {
      ops.progress = '0%'
      if (ops.optionStuPOs.length) {
        ops.optionStuPOs.forEach(stu => {
          if (!item.stus.find(i => { return i.studentId == stu.studentId })) {
            item.stus.push(stu)
          }
        })
      }
    })
  }
  if (temp.unAnswerStudents) {
    temp.unAnswerStudents.forEach(stu => {
      if (!item.stus.find(i => { return i.studentId == stu.studentId })) {
        item.stus.push(stu)
      }
    })
  }
  item.answerStudents = temp.answerStudents
  item.unAnswerStudents = temp.unAnswerStudents || []
  return item
}
// 计算正确率
function getRightProgress(item) {
  item.rightProgress = '0%'
  item.stus.forEach(stu => {
    stu.answer === 'False' && (stu.answer = '错')
    stu.answer === 'True' && (stu.answer = '对')
    if (stu.answer) {
      stu.answer = stu.answer.split('').sort().join('')
    }
  })
  if (item.stus.length) {
    item.rightStus = item.stus.filter(stu => { return stu.answer == item.answer&&item.answer })
    item.rightProgress = item.rightStus.length / item.stus.length * 100
    item.rightProgress = item.rightProgress.toFixed(2) + '%'
  }
  return item
}

function changeMode(e) {
	currMode.value = e;
}

function isRight(item, ops) {
  if (item.answer.includes(ops.option)) {
    return true
  }
  return false
}
onMounted(() => {
  getStatistic()
})
</script>

<style lang="scss" scoped>
@import '../../assets/scss/components.scss';

.group-datail {
  height: 100%;

  .top {
    height: calc(100% - 132px);
    overflow-y: auto;
  }

  .top::-webkit-scrollbar {
    display: none;
  }

  .boardImg {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 99;
    display: flex;
    justify-content: center;
    box-sizing: border-box;

    img {
      display: block;
      width: 90%;
      object-fit: contain;
    }

    .btn_quit_img {
      width: 100px;
      display: flex;
      font-size: 16px;
      align-items: center;
      justify-content: end;
      position: absolute;
      right: 24px;
      vertical-align: middle;
      cursor: pointer;
      bottom: 20px;
      color: white;

      img {
        width: 24px;
        height: 24px;
        margin-right: 8px;
      }
    }
  }

  .multiple-options {
    width: 100%;
    border-radius: 8px;
    background: #fff;
    box-sizing: border-box;
    position: relative;
    color: #2e4a66 !important;

    .stu-statistic {
      padding: 30px;
      .item {
        margin-bottom: 15px;
        padding: 15px 25px;
        background: var(--list-bc-color);
        border-radius: 17px;
        .item-tit {
          font-weight: 500;
          font-size: 21px;
          color: var(--text-color);
          line-height: 36px;
          margin-bottom: 20px;
        }

        .item-ans {
          padding-left: 12px;

          .item-ops {
            border-radius: 4px;
            margin-bottom: 10px;
          }

          .right-ops {
            .ops-tit{
              color:var(--correct-color)
            }
          }

          .ops-tit {
            font-weight: 500;
            font-size: 18px;
            color: var(--unfinished-color);
            margin: 12px 0;
            display: flex;
            align-items: center;
          }

          .stus-wrap {
            font-weight: 500;
            font-size: 18px;
            color: var(--text-color);
            display: flex;
            flex-wrap: wrap;
            gap: 12px 0px ;
            .stu-item {
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              width: 10%;
              height: 96px;
              .avatar {
                width: 48px;
                height: 48px;
                border-radius: 50%;
                margin-bottom: 10px;
              }
            }
          }
        }
      }
    }

    .ques {
      display: flex;
      flex-wrap: wrap;
      padding: 20px 40px;
      padding-bottom: 0px;
      width: 100%;
      overflow-y: auto;
      height: 100%;
      box-sizing: border-box;
      gap: 24px;

      .que {
        width: calc((100% - 48px) / 3);
        
        font-size: 20px;
        height: 300px;

        .bars {
          width: 100%;
          flex: 0.9;
          padding-top: 10px;
          display: flex;
          justify-content: space-around;
          border-bottom: 2px solid #ccc;
          margin-bottom: 24px;

          .bar {
            width: 15%;
            position: relative;
            max-width: 30px;

            .ans {
              position: absolute;
              width: 100%;
              text-align: center;
              bottom: -30px;
              font-size: 20px;
              font-weight: bold;
              color: #2e4a66;
            }

            .progress {
              position: absolute;
              width: 100%;
              bottom: 0;
              background-color: red;

              .stunum {
                position: absolute;
                top: -20px;
                left: 0;
                width: 100%;
                font-size: 14px;
                text-align: center;
                color: #2e4a66;
              }
            }
          }
        }

        .qname {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .ans-rate {
            font-size: 15px;
            display: flex;
            flex-direction: column;
          }
        }
      }
    }

    .btns {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      width: 100%;
      bottom: 0;
    }

    .btns button {
      background-color: rgb(139, 173, 206);
      border-color: rgb(139, 173, 206);
    }
  }

  .footer-title{
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		box-sizing: border-box;
		height: 54px;
		margin-bottom: 36px;
		margin-top: 42px;
		width: 100%;
		.ratio-button{
			position: absolute;
			left: 40px;
			display: flex;
		}
	}
}
</style>