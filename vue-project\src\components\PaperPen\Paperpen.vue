<template>
    <div class="container" id="paperpenBox" v-show="paperViewState === ViewStatus.normal">
        <div v-if="!examMode" class="content">
            <div class="title">{{ getInteractName() }}</div>
            <PaperPenBoard class="gridWrapper" v-if="paperPenPainter" :painter="paperPenPainter"></PaperPenBoard>
            <div class="line">
                <RBPButton width="120px" btnText="全部展示" @click="randomStudentBoard(false)" v-if="cancelRandom">
                </RBPButton>
                <div :style="{ width: '10px' }"></div>
                <RBPButton width="120px" btnText="随机展示" @click="randomStudentBoard(true)"></RBPButton>
                <div :style="{ width: '10px' }"></div>
                <RBPButton width="120px" btnText="选择展示" @click="showSelectedStudent()"></RBPButton>
                <div :style="{ width: '10px' }"></div>
                <RBPButton width="120px" btnText="收起" @click="hiddenPaperpenClick"></RBPButton>
                <div :style="{ width: '10px' }"></div>
                <RBPButton width="120px" :btnText="statusTitle()" :btnSelected="true" @click="stopInteractClick">
                </RBPButton>
            </div>
            <div class="show-paper" v-if="interact === Interact.paperPen">
                <div class="show-content">
                    <div class="text">显示试卷</div>
                    <el-switch size="large" v-model="showPaperImage" inline-prompt active-text="是" inactive-text="否"
                        @change="showImage" />
                </div>
                <div v-if="showProbabilityButton" :style="{ width: '10px' }"></div>
                <RBPButton v-if="showProbabilityButton" width="120px" btnText="合并概率" @click="showProbabilityBtnClick()">
                </RBPButton>
                <div v-if="HttpEnv.test == serverHost.env" style="display: flex;align-items: center;">
                    <RBPButton width="120px" btnText="开启测试" @click="auto_test_set(true)"></RBPButton>
                    <div :style="{ width: '10px' }"></div>
                    <RBPButton width="120px" :btnSelected="true" btnText="关闭测试" @click="auto_test_set(false)">
                    </RBPButton>
                    <div :style="{ width: '10px' }"></div>
                    <el-input v-model="testTime" :style="{ width: '80px' }"></el-input>
                </div>
            </div>
            <div class="show-paper"
                v-if="interact === Interact.classTest && interactStatus == InteractStatus.underway && showBtn">


                <RBPButton width="120px" :btnSelected="true" btnText="进度展示" @click="toggleProgress"></RBPButton>

            </div>
            <!-- <div class="draw-bar">
                <div class="item" @click="sceneReduct()">
                    <img src="/img/scene_reduction.png">
                    <span>还原视图</span>
                </div>
                <div style="width: 30px;"></div>
                <div class="item" @click="canceLine()">
                    <img src="/img/line_cancel.png">
                    <span>撤销笔画</span>
                </div>
            </div> -->
        </div>
        <div v-else class="content">
            <div class="title">{{ interactName(Interact.examMode) }}</div>
            <ExamMode class="examMode"></ExamMode>
            <div class="line">
                <div class="btns">
                    <div class="btn" @click="stopInteractClick">
                        {{ statusTitle() }}
                    </div>
                </div>
            </div>
        </div>

        <PaperpenProgress ref="progressRef" @close="toggleProgress"
            v-show="showProgress && interact == Interact.classTest" :bottom-height="paperPenLineBtnsHeight">
        </PaperpenProgress>
    </div>
</template>


<script setup>
import $ from "jquery"
import { ref, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useClassroomStore } from '@/stores/classroom'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { UIFrames, ViewStatus } from '@/classroom/frame_enums'
import roomUpdater from '@/classroom/classroom_updater.js'
import { interactName, InteractStatus, Interact } from '@/classroom/interact_enums.js'
import { useInteractStore } from '@/stores/interact_store'
import { DisplayDirection } from '@/classroom/frame_enums'
import PaperPenBoard from './PaperPenBoard.vue'
import { useDrawBoardStore } from '@/stores/drawboard_store'
import { Alert } from '@/utils/alert'
import ExamMode from '@/components/ExamMode/ExamMode.vue'
import RBPButton from '@/components/baseComponents/RBPButton.vue'
import { HttpEnv, serverHost } from '@/server_request/server_urls'
import { LocalRequest } from '@/local_request/local_request'
import PaperpenProgress from "./PaperpenProgress.vue"
import { useProbabilityStore } from "@/stores/probability_store"

const interactStore = useInteractStore()
const { interact, interactStatus, examMode } = storeToRefs(interactStore)
const paperPenLineBtnsHeight = ref(UIFrames.paperPenLineBtnsHeight)
const paperPenTitleHeight = ref(UIFrames.paperPenTitleHeight)

const classroomUIStore = useClassroomUIStore()
const { displayDirection, mainContentTopSpace, mainContentHeight } = storeToRefs(classroomUIStore)

const classroomStore = useClassroomStore()

const drawBoardStore = useDrawBoardStore()
const { paperPenPainter, cancelRandom, paperViewState } = storeToRefs(drawBoardStore)

const gridHeight = ref(UIFrames.getPaperPenBoardHeight())

const showPaperImage = ref(true)

const showProgress = ref(false)
const progressRef = ref(null)

const showProbabilityButton = ref(serverHost.probabilityEnable)

watch(paperPenPainter, () => {
    updateButtonState()
})

function hiddenPaperpenClick() {
    if (interactStore.testQuestionInfo) {
        setTimeout(() => {
            $("#showPaperpenBox").css('z-index', 'var(--interact-test-z-index)');
        }, 200);
    }
    paperViewState.value = ViewStatus.minimize
    drawBoardStore.resetPaperpenLineColor()
}

watch(showProgress, () => {
    if (interact.value != Interact.classTest) {
        return
    }
    setTimeout(() => {
        $("#paperpenBox").css('z-index', showProgress.value ? 'var(--interact-test-z-index)' : 'var(--interact-z-index)');
    }, 200);
})

function updateButtonState() {
    if (!paperPenPainter.value) {
        return
    }
    if (paperPenPainter.value.onCompare) {
        cancelRandom.value = true
    }
    else {
        cancelRandom.value = false
    }
}

function toggleProgress() {


    showProgress.value = !showProgress.value

}

function statusTitle() {
    if (interactStatus.value == InteractStatus.undelivered) {
        return '收卷'
    } else if (interactStatus.value == InteractStatus.underway) {

        return '结束互动'
    }
    return '课堂互动'
}
const showBtn = ref(false)
async function stopInteractClick() {
    roomUpdater.stopInteract()
    if (interactStatus.value == InteractStatus.underway && interact.value == Interact.classTest) {

        if (serverHost.aiCorrectOpen) {
            let haveWriting = false
            classroomStore.selectedClassroom.studentList.forEach(stu => {
                if (stu.writePageInfo) {
                    haveWriting = true
                }
            })
            showProgress.value = haveWriting
            showBtn.value = haveWriting
            if (haveWriting && progressRef.value && progressRef.value.getPushStream) {
                progressRef.value.getPushStream()
            }
        }
    }
}

function randomStudentBoard(random) {
    if (paperPenPainter.value) {
        if (!random) {
            paperPenPainter.value.setCompare(false, [])
            updateButtonState()
        }
        else {
            let student = classroomStore.paperRandomOneStudent()
            if (student) {
                paperPenPainter.value.setCompare(true, [student])
                updateButtonState()
            }
            else {
                Alert.showSuccessMessage('已全部展示过')
            }
        }
    }
}
//自动化测试上报间隔
const testTime = ref("10")
function auto_test_set(isOpen) {
    LocalRequest.autoTestControl(isOpen, testTime.value)
}

function showSelectedStudent() {
    let studentList = classroomStore.selectedClassroom.studentList.filter(v => v.selected)
    if (studentList.length == 0) {
        Alert.showErrorMessage('请选择学生')
        return
    }
    studentList.forEach(element => {
        element.selected = false
    })
    paperPenPainter.value.setCompare(true, studentList)
    updateButtonState()
}



function getInteractName() {
    if (interactStore.isProbability) {
        return interactName(Interact.probability)
    } else {
        return interactName(interact.value)
    }
}


function showImage(value) {
    paperPenPainter.value.showBackgroundImage(showPaperImage.value)
}


function showProbabilityBtnClick() {
    let stuList = []
    classroomStore.selectedClassroom.studentList.forEach(stu => {
        if (stu.selected) {
            stuList.push(stu)
        }
    })
    if (stuList.length > 0) {
        cancelRandom.value = true
        let probabilityStore = useProbabilityStore()
        probabilityStore.showMergedChartWithStudents(stuList)
    } else {
        Alert.showErrorMessage('请选择学生')
        return
    }
}

</script>


<style lang="scss" scoped>
.container {
    position: absolute;
    top: v-bind("mainContentTopSpace + 'px'");
    width: 100%;
    height: v-bind("mainContentHeight + 'px'");
    background-color: #29435C;
    z-index: var(--interact-z-index);

    .content {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;

        .title {
            text-align: center;
            font-size: 22px;
            font-weight: bold;
            color: white;
            height: v-bind("paperPenTitleHeight + 'px'");
            line-height: v-bind("paperPenTitleHeight + 'px'");
        }

        .gridWrapper {
            grid-area: gridWrapper;
            height: v-bind("gridHeight + 'px'");
            width: 100%;
            box-sizing: border-box;
            padding: 0 10px;
            display: flex;
            justify-content: center;
            overflow: auto;
            scrollbar-width: none;

            .grid {
                width: 60%;
                display: grid;
                place-items: center center;
            }
        }

        .gridWrapper::-webkit-scrollbar {
            display: none;
            /* 适用于 Chrome, Safari 和 Opera */
        }

        .draw-bar {
            display: flex;
            position: absolute;
            justify-content: center;
            align-items: center;
            flex-direction: row;
            width: 200px;
            height: 60px;
            left: 50%;
            transform: translateX(-50%);
            /* 修正水平居中的偏移 */
            // bottom: 5px;
            bottom: 85px;
            background-color: lightgray;
            border-radius: 5px;
            z-index: 2;

            .item {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                border-radius: 4px;
                width: 60px;

                img {
                    width: 28px;
                    height: 28px;

                }

                span {
                    color: black;
                    font-size: 11px;
                    margin-top: 6px;
                }
            }
        }

        .line {
            display: flex;
            justify-content: center;
            align-items: center;
            height: v-bind("paperPenLineBtnsHeight + 'px'");
            position: absolute;
            bottom: 0;
            left: v-bind("displayDirection === DisplayDirection.Left ? '0' : null");
            right: v-bind("displayDirection === DisplayDirection.Left ? null : '0'");
            flex-direction: v-bind("displayDirection === DisplayDirection.Left ? 'row-reverse' : 'row'");

            .random {
                width: 80px;
                height: 40px;
                line-height: 40px;
                background-color: blue;
                text-align: center;
                color: white;
                border-radius: 6px;
                cursor: pointer;
            }

            .up {
                width: 80px;
                height: 40px;
                line-height: 40px;
                background-color: orange;
                text-align: center;
                margin-left: 10px;
                margin-right: 10px;
                color: white;
                border-radius: 6px;
                cursor: pointer;
            }

            .btn {
                width: 100px;
                height: 40px;
                line-height: 40px;
                background-color: red;
                text-align: center;
                // margin-right: 10px;
                color: white;
                border-radius: 6px;
                cursor: pointer;
            }
        }

        .examMode {
            flex: 1;
        }


        .show-paper {
            display: flex;
            justify-content: center;
            align-items: center;
            height: v-bind("(paperPenLineBtnsHeight) + 'px'");
            position: absolute;
            bottom: 0;
            left: v-bind("displayDirection === DisplayDirection.Right ? '8px' : null");
            right: v-bind("displayDirection === DisplayDirection.Right ? null : '8px'");
            flex-direction: 'row';

            .show-content {
                border-radius: 8px;
                border: 1px solid white;
                display: flex;
                justify-content: center;
                align-items: center;
                padding-left: 8px;
                padding-right: 8px;
                padding-top: 5px;
                padding-bottom: 5px;
                margin-right: 10px;
            }

            .text {
                font-size: 16px;
                color: white;
                margin-right: 6px;
            }
        }
    }
}
</style>