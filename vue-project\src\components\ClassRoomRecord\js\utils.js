export function getTypeName(item) {
    if (item.type == 0) {
        return '纸笔互动'
    } else if (item.type == 1) {
        return '随堂测'
    } else if (item.type == 2) {
        return '判断题'
    } else if (item.type == 3) {
        return '单选题'
    } else if (item.type == 4) {
        return '多选题'
    } else if (item.type == 5) {
        return '抢答题'
    } else if (item.type == 6) {
        return '作业'
    } else if (item.type == 7) {
        return '单词听写'
    } else if (item.type == 9) {
        return '中文练字'
    } else if (item.type == 12) {
        return '组合题'
    } else if (item.type == 13) {
        return '投票'
    }
}

export function formatRecordDate(date, fmt) {
    if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    }
    let o = {
        'M+': date.getMonth() + 1,
        'd+': date.getDate(),
        'h+': date.getHours(),
        'm+': date.getMinutes(),
        's+': date.getSeconds()
    };
    for (let k in o) {
        if (new RegExp(`(${k})`).test(fmt)) {
            let str = o[k] + '';
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : padLeftZero(str));
        }
    }
    return fmt;
}

function padLeftZero(str) {
    return ('00' + str).substr(str.length);
}

export function calcDiffTime(diffTime) {
    let days = 0, hours = 0, minutes = 0, seconds = 0
    //计算出相差天数  
    days = Math.floor(diffTime / (24 * 3600 * 1000))
    //计算出小时数  
    let leave1 = diffTime % (24 * 3600 * 1000)    //计算天数后剩余的毫秒数  
    hours = Math.floor(leave1 / (3600 * 1000))
    //计算相差分钟数  
    let leave2 = leave1 % (3600 * 1000)        //计算小时数后剩余的毫秒数  
    minutes = Math.floor(leave2 / (60 * 1000))
    //计算相差秒数  
    let leave3 = leave2 % (60 * 1000)      //计算分钟数后剩余的毫秒数  
    seconds = Math.round(leave3 / 1000)
    let res = ''
    let lang = localStorage.getItem("sysLanguage")
    if (lang && lang == 'en') {
        days && (res += (days + ' days '))
        hours && (res += (hours + ' hours '))
        minutes && (res += (minutes + ' minutes '))
        seconds && (res += (seconds + ' seconds '))
    } else {
        days && (res += (days + '天'))
        hours && (res += (hours + '小时'))
        minutes && (res += (minutes + '分钟'))
        seconds && (res += (seconds + '秒'))
    }
    return res
}

export function scoreToStar(score) {
    // 3星： > 90； 2.5星： > 70； 2星： > 40； 1.5星： > 30； 1星： > 10； 0.5星： > 0
    if (score >= 90) {
        return 3.0;
    } else if (score >= 70) {
        return 2.5;
    } else if (score >= 40) {
        return 2;
    } else if (score >= 30) {
        return 1.5;
    } else if (score >= 10) {
        return 1.0;
    } else if (score > 0) {
        return 0.5;
    } else {
        return 0;
    }
}