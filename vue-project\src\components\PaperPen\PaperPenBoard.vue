<template>
    <div id="paperpen-drawbox"></div>
</template>


<script setup>

import { onMounted, onBeforeUnmount, toRefs } from "vue";
import { PaperPenApplication } from "./paper_pen_application";

const props = defineProps({
    painter: {
        type: PaperPenApplication,
        require: true
    }
})

const { painter } = toRefs(props)

onMounted(() => {
    const box = document.querySelector("#paperpen-drawbox")

    painter.value.insetToDom(box)

    // 添加事件监听器
    if (window.electron) {
        box.addEventListener('mousedown', onMouseDown)
        box.addEventListener('mousemove', onMouseMove)
        box.addEventListener('mouseup', onMouseUp)
        box.addEventListener('mouseover', onMouseOut)
        box.addEventListener('wheel', onWheel)
    }

    box.addEventListener('touchstart', onTouchStart)
    box.addEventListener('touchmove', onTouchMove)
    box.addEventListener('touchend', onTouchEnd)
    box.addEventListener('focusout', onMouseOut)

})


onBeforeUnmount(() => {
    const box = document.querySelector("#paperpen-drawbox")
    // 移除事件监听器
    if (window.electron) {
        box.removeEventListener('mousedown', onMouseDown)
        box.removeEventListener('mousemove', onMouseMove)
        box.removeEventListener('mouseup', onMouseUp)
        box.removeEventListener('mouseover', onMouseOut)
        box.removeEventListener('wheel', onWheel)
    }
    box.removeEventListener('touchstart', onTouchStart)
    box.removeEventListener('touchmove', onTouchMove)
    box.removeEventListener('touchend', onTouchEnd)
    box.addEventListener('focusout', onMouseOut)
})


function onWheel(event) {
    painter.value.onWheel(event)
}

function onMouseDown(event) {
    // console.log("onMouseDown", event)
    if (event?.sourceCapabilities?.firesTouchEvents ?? false) {
        return
    }
    painter.value.onMouseDown(event)
}

function onMouseMove(event) {
    if (event?.sourceCapabilities?.firesTouchEvents ?? false) {
        return
    }
    painter.value.onMouseMove(event)
}

function onMouseUp(event) {
    if (event?.sourceCapabilities?.firesTouchEvents ?? false) {
        return
    }
    painter.value.onMouseUp(event)
}

function onMouseOut(event) {
    if (event?.sourceCapabilities?.firesTouchEvents ?? false) {
        return
    }
    painter.value.onMouseOut(event)
}

function onTouchStart(event) {
    // console.log("onTouchStart", event)
    // event.preventDefault(); // 禁用默认触摸行为
    // 只阻止特定事件的默认行为
    if (event.touches.length > 1) {
        event.preventDefault();
    }
    painter.value.onTouchStart(event)
}

function onTouchMove(event) {
    // event.preventDefault(); // 禁用默认触摸行为
    // 只阻止特定事件的默认行为
    if (event.touches.length > 1) {
        event.preventDefault();
    }
    painter.value.onTouchMove(event)
}

function onTouchEnd(event) {
    // event.preventDefault(); // 禁用默认触摸行为
    // 只阻止特定事件的默认行为
    if (event.touches.length > 1) {
        event.preventDefault();
    }
    painter.value.onTouchEnd(event)
}

</script>