<template>
    <div class="class-course-list-body">
        <div class="expand">
            {{ showDate(date) }}
        </div>
        <div class="class-course-list">
            <el-collapse v-if="currDayList.length" v-for="cls in currDayList" :key="cls.classId" v-model="activeNames">
                <el-collapse-item :title="cls.className" :name="cls.classId">
                    <div class="child-item" :class="{'child-item-selected':chooseId == item.classRecordId}" v-for="item in cls.records" :key="item.classRecordId"
                        @click="checkClass(cls.classId, item)">
                        <div style="margin-left: 30px;">{{ item.subjectName }}</div>
                        <div v-if="item.endTime">
                            {{ formDataFunc(item.startTime) }} — {{ formDataFunc(item.endTime) }}
                        </div>
                        <div v-else>
                            {{ formDataFunc(item.startTime) }}进行中...
                        </div>
                        <div>{{ `互动：${item.interactNum?item.interactNum:0}次`}}</div>
                    </div>
                </el-collapse-item>
            </el-collapse>


        </div>
    </div>

</template>

<script setup>
import { onMounted, ref, watch } from 'vue';
import { ClassroomRecordRequest } from '@/server_request/classroom_record.js'
import { formatRecordDate } from '../js/utils';
import { useClassroomStore } from "@/stores/classroom.js"
import { useClassroomUIStore } from "@/stores/classroom_ui_store.js"
import { loginInstance } from "@/login_instance/login_instance"
import { ElLoading } from 'element-plus'
import { storeToRefs } from 'pinia';
const classroomStore = useClassroomStore()
const classroomUIStore = useClassroomUIStore()
const { showClassRecord } = storeToRefs(classroomUIStore)
const props = defineProps({
    date: {
        default: ""
    },
    recordId:{
        default:""
    }
})

watch(()=>props.date,()=>{
    getDay(props.date,true)
})

onMounted(()=>{
    if(props.recordId){        
        chooseId.value = props.recordId
    }
    getDay(props.date)
})
//处理今天日期
function dealNowDate(){
    const date = new Date();
    // 获取年份 (四位数的年)
    const year = date.getFullYear();
    // 获取月份 (注意，月份是从 0 开始的，所以需要加 1)
    const month = date.getMonth() + 1; // 月份范围是 0-11, 所以加 1 得到 1-12
    // 获取日期（天）
    const day = date.getDate();
    return `${year}-${month.toString().padStart(2,"0")}-${day.toString().padStart(2,"0")}`
}

//展示日期
function showDate(date) {
    if(!date){
        date = dealNowDate()
    }
    let list = (date+"").split("-")
    if(list.length==3){
        date = list[0]+"年"+list[1]+"月"+list[2]+"日"
    }
    return date;
}

const emits = defineEmits(['hideCalendar'])

const currDayList = ref([])
const activeNames = ref([])
const chooseId = ref('')

const formDataFunc = (time) => {
    let date = new Date(time.replace(/-/g, '/'));
    return formatRecordDate(date, 'hh:mm');
}

//获取当天数据
const getDay = async (date,isChange) => {
    if(!date){
        date = dealNowDate()
    }
    let list = (date+"").split("-")
    date = list.join("")
    let loading = ElLoading.service({ background: 'transparent' })
    try {
        let params = {
            date: date,
            classId: classroomStore.selectedClassroom.classId,
            subjectId:  !showClassRecord.value?classroomStore.selectedClassroom.subjectId:loginInstance.subjectMainId,
        }
        let { data } = await ClassroomRecordRequest.getDayRecords(params)
        currDayList.value = data
        if (data && data.length) {
            data.forEach(item => {
                activeNames.value.push(item.classId)
            })
            if(!chooseId.value||isChange){
                let item = data[data.length - 1]
                if(item.records&&item.records.length){
                    checkClass(item.classId,item.records[item.records.length - 1])
                }
            }
        }else{
            checkClass("","")
        }
        loading.close()
    } catch (error) {
        console.log(error)
        loading.close()
    }
}
//选择课程
const checkClass = (classId, item) => {
    let classRecordId = item.classRecordId
    if(chooseId.value!=classRecordId){
        chooseId.value = classRecordId
        emits('hideCalendar', classId, item.classRecordId,"",true)
    }
    
}
</script>

<style lang="scss" scoped>
.class-course-list-body {
    display: flex;
    flex-direction: column;
    align-items: center;

    .expand {
        width: 100%;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: end;
        min-height: 36px;
        margin-bottom: 21px;
        font-weight: 500;
        font-size: 21px;
        color: var(--secondary-text-color);
    }

    .class-course-list {
        max-height: calc(100% - 57px);
        width: 100%;
        overflow-y: auto;
        

        .child-item {
            display: flex;
            height: 59px;
            align-items: center;
            justify-content: space-between;
            box-sizing: border-box;
            padding: 0px 15px;
            font-weight: 400;
            font-size: 21px;
            color: var(--text-color);
        }

        .child-item-selected {
            background-color: var(--primary-color);
            color: var(--anti-text-color);
        }
    }

    .btn {
        margin-bottom: 36px;
        margin-right: 0px;
        margin-top: 46px;
    }
}
</style>

<style lang="scss" type="text/css">
.class-course-list {
    .el-collapse-item__header {
        align-items: center;
        height: 51px;
        background: var(--toolbar-bg-color) !important;
        border: none;
        font-weight: 500;
        font-size: 21px;
        color: var(--text-color);
        padding-left: 16px;

    }

    .el-collapse-item__arrow {
        color: var(--text-color);
        margin: 0 16px 0 auto;
        font-weight: 500;
    }

    .el-collapse-item__content {
        padding-bottom: 0px;
    }

}
</style>