/**
小按钮（带对钩图）
*/
<template>
    <div class="rbp-button" :class="{ 'btn-selected': props.btnSelected }">
        <img src="/img/svg/icon_hook.svg" v-show="props.btnSelected">
        <span>{{ props.btnText }}</span>
    </div>
</template>
<script setup>
import { computed, defineProps, onMounted, ref } from 'vue'
import { RBPColors, getColor } from '@/components/baseComponents/RBPColors.js'

const props = defineProps({
    btnText: String,
    btnSelected: Boolean,
    btnType: String,
    backgroundColor: {
        type: String,
        default: getColor('--main-bc-color'),
    },
    color: {
        type: String,
        default: getColor('--text-color'),
    },
    width: {
        type: String,
        default: '141px',
    },
    height: {
        type: String,
        default: '54px',
    },
})
</script>
<style lang="scss" scoped>
.rbp-button {
    width: v-bind("props.width");
    height: v-bind("props.height");
    background-color: v-bind("props.backgroundColor");
    border-radius: 15px;
    color: v-bind("props.color");
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 21px;
    cursor: pointer;
    position: relative;
    border: 1px solid var(--border-bar-color);

    img {
        position: absolute;
        left: 40px;
    }
}

.btn-selected {
    background-color: var(--primary-color);
    color: var(--anti-text-color);
}
</style>