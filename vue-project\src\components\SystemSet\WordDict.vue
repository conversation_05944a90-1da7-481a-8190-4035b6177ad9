<template>
    <div class="wordDict"> 
        <RBPTitleInput class="word-split" title="每个单词播放次数" width="80%" v-model="playTimes" :limit-number="true"></RBPTitleInput>
        <RBPTitleInput class="word-split" title="每个单词播放时间间隔(如3，表示3秒读一次)" width="80%" v-model="playTimeInterval" :limit-number="true"></RBPTitleInput>
        <div class="expand"></div>
        <RBPButton :btn-selected="true" btn-type="big" btn-text="完成" class="setup-btn" @click="setupClick">      
       </RBPButton>
        
    </div>
</template>
<script setup>
import { ref, computed, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useDictationStore } from '@/stores/dictation_store'
import { Alert } from '@/utils/alert'
import RBPTitleInput from '../baseComponents/RBPTitleInput.vue'
import RBPButton from '../baseComponents/RBPButton.vue'
const dictationStore = useDictationStore()
const { playTimes, playTimeInterval } = storeToRefs(dictationStore)

watch(() => playTimes.value, () => {
    const parsedValue = parseFloat(playTimes.value);
    if (!isNaN(parsedValue) && parsedValue <= 0) {
        playTimes.value = 1
    }
})
watch(() => playTimeInterval.value, () => {
    const parsedValue = parseFloat(playTimeInterval.value);
    if (!isNaN(parsedValue) && parsedValue <= 0) {
        playTimeInterval.value = 1
    }
})
async function setupClick() {
    await dictationStore.addTimesAndIntervalToDatabase()
    Alert.showSuccessMessage("设置成功")
}
</script>
<style lang="scss" scoped>
.wordDict {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    .expand{
        flex:1
    }

    .word-split{
        margin-top: 42px;
    }

    

    .setup-btn {
       margin-bottom: 36px;
       margin-right: 0px;
    }
}
</style>