<template>
  <div class="chart-wrapper">
    <!-- 左侧百分比刻度 -->
    <div class="percentage-scale">
      <div class="scale-item" v-for="(value, index) in percentageScales" :key="index">
        {{ value }}%
      </div>
    </div>

    <!-- 中间图表区域 -->
    <div class="chart-content">
      <!-- 背景网格线 -->
      <div class="grid-lines">
        <div class="grid-line" v-for="(_, index) in 6" :key="index"></div>
      </div>

      <!-- 柱状图组 -->
      <div class="bars-container">
        <div class="bar-group" v-for="(item, index) in props.dataList" :key="index">

          <div class="bar-content">
            <div :class="`bar ${getStuStatus(item.accuracy)}`" :style="{ height: `${Math.round((item.accuracy??0) * 100)}%` }">
              <div class="bar-value">{{ Math.round(item.accuracy * 100) }}%</div>
            </div>
          </div>
          <div class="bar-label">{{ item.knowledgePoint}}</div>
        </div>
      </div>
    </div>

    <!-- 右侧图例 -->
    <div class="chart-legend">
      <div class="legend-item">
        <div class="legend-color excellent"></div>
        <div class="des-chart">优秀</div>
      </div>
      <div class="legend-item">
        <div class="legend-color good"></div>
        <div class="des-chart">掌握好</div>
      </div>
      <div class="legend-item">
        <div class="legend-color need-improve"></div>
        <div class="des-chart">有待提高</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { data } from 'jquery';
import { ref } from 'vue';
const props = defineProps({
  dataList:{
    default:[]
  }
})
// 百分比刻度值
const percentageScales = [100, 80, 60, 40, 20, 0];



function getStuStatus(data){
  if(data < 0.5){
    return 'need-improve'
  }else if(data <1){
    return 'good'
  }else{
    return 'excellent'
  }
}
</script>

<style lang="scss" scoped>
.chart-wrapper {
  position: relative;
  display: flex;
  height: 228px;
  box-sizing: border-box;

  // 左侧百分比刻度
  .percentage-scale {
    padding-top: 20px;
    width: 36px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding-bottom: 16px;
    box-sizing: border-box;

    .scale-item {
      color: var(--secondary-text-color);
      font-size: 12px;
      text-align: right;
      line-height: 14px;
    }
  }

  // 中间图表区域
  .chart-content {
    flex: 1;
    position: relative;


    // 背景网格线
    .grid-lines {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      box-sizing: border-box;
      padding-bottom: 21px;
      padding-top: 26px;

      .grid-line {
        width: 100%;
        height: 1px;
        background-color: var(--border-bar-color);
      }
    }

    // 柱状图容器
    .bars-container {
      position: absolute;
      height: 100%;
      width: 100%;
      display: flex;
      justify-content: left;
      box-sizing: border-box;
      padding-top: 6px;
      overflow-y: hidden;
      overflow-x: scroll;
      scrollbar-width: none;
      /* Firefox */

      .bar-group {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 140px;
        max-width: 140px;
        padding-top: 20px;

        .bar-content {
          flex: 1;
          width: 140px;
          display: flex;
          justify-content: end;
          align-items: center;
          flex-direction: column;
          position: relative;
          

          .bar {
            width: 24px;
            position: relative;
            border-radius: 12px 12px 0 0;



            .bar-value {
              position: absolute;
              top: -20px;
              left: 50%;
              transform: translateX(-50%);
              font-size: 12px;
              color: var(--secondary-text-color);
              white-space: nowrap;
              display: none;
            }
          }
        }

        .bar-content:hover {
          .bar-value {
            display: flex;
          }
        }

        .bar-label {
          width: 120px;
          height: 21px;
          box-sizing: border-box;
          padding-top: 4px;
          text-align: center;
          font-size: 12px;
          color: var(--secondary-text-color);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .bars-content::-webkit-scrollbar {
            display: none;
            /* Chrome, Safari 和 Opera */
        }

    
  }
  .excellent {
      background-color: var(--correct-color);
    }

    .good {
      background-color: var(--primary-color);
    }

    .need-improve {
      background-color: var(--error-color);
    }

  // 右侧图例
  .chart-legend {
    width: 150px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-sizing: border-box;
    padding: 0px 24px;
    padding-top: 20px;

    .legend-item {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      .legend-color {
        width: 14px;
        height: 14px;
        border-radius: 7px;
      }

      .des-chart {
        font-size: 12px;
        color: var(--text-color);
        margin-left: 4px;
        margin-bottom: 2px;
        /* 手动微调 */
      }
    }
  }
}
</style>
