import { BoardView } from "@/drawboard/board_view";
import * as THREE from 'three';
import { PainterOrder } from "@/drawboard/draw_enums";
import { BoardLabel } from "@/drawboard/board_label";
import { StudentPageView } from "./student_page_view";
import { StuStatus } from "@/classroom/interact_enums";
import { BoardButton } from "@/drawboard/board_button";
import { STUDENT_INDEX_MAX } from '@/stores/answers_store'
import * as echarts from 'echarts';
import { BoardImageView } from "@/drawboard/board_image_view";
import { BoardTool } from "@/drawboard/board_tool";
import fontLoader from "@/drawboard/board_font";
import { TextGeometry } from 'three/addons/geometries/TextGeometry.js';

export class StudentBoardView extends BoardView {

    constructor(application, pos, size, student) {
        super(application, pos, size)

        this.student = student

        /// 使用A4纸的比例
        this.pageRatio = 210.0 / 297.0

        /// 用于显示当前页码内容
        this.currentPageCode = -1

        /// pageId转pageCode
        this.pageIdToCodeMap = new Map()
        /// pageCode映射pageInfo
        this.pageInfoMap = new Map()
        /// pageCode映射pageView
        this.pageViewsMap = new Map()
        /// 批改返回图片
        this.checkedMap = new Map()

        /// 是否显示试卷
        this.showImage = true

        this.setupUI()
    }


    setupUI() {
        let whitePlane = new THREE.PlaneGeometry(this.size.width, this.size.height)
        let whiteMaterial = new THREE.MeshBasicMaterial({
            color: 0xffffff,
            side: THREE.DoubleSide,
        })
        this.whiteMesh = new THREE.Mesh(whitePlane, whiteMaterial)
        this.whiteMesh.renderOrder = PainterOrder.background
        this.whiteMesh.position.set(0, 0, - 0.0002)
        this.add(this.whiteMesh)

        let labelSize = { width: this.size.width / 4, height: this.size.height / 30 }
        let nameLabel = new BoardLabel(
            this.application,
            new THREE.Vector3(- this.size.width / 2 + labelSize.width / 2 + 0.01, this.size.height / 2 - labelSize.height / 2, 0),
            labelSize,
            this.student.name,
            { fontSize: 0.05, color: 0x000000, align: 'left' })
        nameLabel.renderOrder = PainterOrder.studentName
        this.addSubView(nameLabel)

        let s = this.getStateTextAndColor()
        let stateLabel = new BoardLabel(
            this.application,
            new THREE.Vector3(this.size.width / 2 - labelSize.width / 2 - 0.02, this.size.height / 2 - labelSize.height / 2, 0),
            labelSize,
            s.text,
            { fontSize: 0.04, color: s.color, align: 'right' })
        stateLabel.renderOrder = PainterOrder.studentName
        this.stateLabel = stateLabel
        this.addSubView(stateLabel)

        let selectWidth = this.size.width / 15

        let selectedImageView = new BoardImageView(
            this.application,
            new THREE.Vector3(- this.size.width / 2 + selectWidth / 2, - this.size.height / 2 + selectWidth / 2, 0),
            { width: selectWidth, height: selectWidth },
            true)
        selectedImageView.setImageUrl('img/icon_asent_selected.png')
        selectedImageView.renderOrder = PainterOrder.interactive
        selectedImageView.visible = this.student.selected ?? false
        this.selectedImageView = selectedImageView
        this.addSubView(selectedImageView)

        let viewButton = new BoardButton(
            this.application,
            new THREE.Vector3(0, 0, 0),
            { width: this.size.width, height: this.size.height * 0.9 }, true)
        viewButton.renderOrder = PainterOrder.interactive
        viewButton.onDoubleClick(() => {
            if (this.onDoubleClick) {
                this.onDoubleClick(this)
            }
        })

        viewButton.onClick(() => {
            this.student.selected = !this.student.selected
            this.selectedImageView.visible = this.student.selected
        })
        this.addSubView(viewButton)


        let barSize = { width: this.size.width, height: this.size.height / 20 }
        let barView = new BoardView(
            this.application,
            new THREE.Vector3(0, - this.size.height / 2 + barSize.height / 2, 0),
            barSize)
        barView.renderOrder = PainterOrder.interactive
        barView.visible = false
        this.addSubView(barView)

        let arrowWidth = this.size.width / 15
        let prevPageButton = new BoardButton(
            this.application,
            new THREE.Vector3(- arrowWidth / 2 * 3, 0, 0),
            { width: arrowWidth, height: arrowWidth }, true)
        prevPageButton.setImage('img/page_left_arrow.png')
        prevPageButton.onClick(() => {
            if (!this.pageList) {
                return
            }
            let index = 0
            for (let i = 0; i < this.pageList.length; i++) {
                let page = this.pageList[i]
                if (page.pageCode === this.currentPageCode) {
                    index = i
                    break
                }
            }
            if (index - 1 >= 0) {
                let page = this.pageList[index - 1]
                this.getAndShowPageView(page.pageCode)
                this.animate()
            }
        })
        barView.addSubView(prevPageButton)

        let nextPageButton = new BoardButton(
            this.application,
            new THREE.Vector3(arrowWidth / 2 * 3, 0, 0),
            { width: arrowWidth, height: arrowWidth }, true)
        nextPageButton.setImage('img/page_right_arrow.png')
        nextPageButton.onClick(() => {
            if (!this.pageList) {
                return
            }
            let index = 0
            for (let i = 0; i < this.pageList.length; i++) {
                let page = this.pageList[i]
                if (page.pageCode === this.currentPageCode) {
                    index = i
                    break
                }
            }
            if (index + 1 < this.pageList.length) {
                let page = this.pageList[index + 1]
                this.getAndShowPageView(page.pageCode)
                this.animate()
            }
        })
        barView.addSubView(nextPageButton)

        let indexLabel = new BoardLabel(
            this.application,
            new THREE.Vector3(0, 0, 0),
            { width: this.size.width, height: this.size.height / 20 },
            "",
            { fontSize: 0.04, color: 0x000000, align: 'center' })
        indexLabel.renderOrder = PainterOrder.studentName
        this.indexLabel = indexLabel
        barView.addSubView(indexLabel)


        //test 
        // this.setEcharts(['A', 'B', 'C', 'D', 'E'], [0.1, 0.4, 0.7, 0.2, 1.0])

        this.barView = barView
    }

    updateSelectedState() {
        this.selectedImageView.visible = this.student.selected
    }

    onDoubleClick(callback) {
        this.onDoubleClick = callback
    }

    updateStudentState() {
        let s = this.getStateTextAndColor()
        this.stateLabel.setText(s.text)
        this.stateLabel.setTextColor(s.color)
        this.showTurnButtonIfNeeded()
    }

    showTurnButtonIfNeeded() {
        if (this.student.answerSubmitIndex === STUDENT_INDEX_MAX) {
            this.barView.visible = false
            this.animate()
            return
        }
        let codeList = [...this.pageViewsMap.keys()]
        if (codeList.length < 2) {
            this.barView.visible = false
            this.animate()
            return
        }
        this.pageList = []
        codeList.forEach((code) => {
            let info = this.pageInfoMap.get(code)
            if (info) {
                this.pageList.push({
                    pageCode: code,
                    pageId: info.page.pageId
                })
            }
            else if (code !== 0) {
                this.pageList.push({
                    pageCode: code,
                    pageId: Number.MAX_SAFE_INTEGER
                })
            }
        })
        this.pageList.sort((a, b) => a.pageId < b.pageId)
        this.barView.visible = true
        let index = 0
        for (let i = 0; i < this.pageList.length; i++) {
            let page = this.pageList[i]
            if (page.pageCode === this.currentPageCode) {
                index = i
                break
            }
        }
        this.indexLabel.setText("" + (index + 1))
    }

    getStateTextAndColor() {
        let color = 0x708090
        let text = "离线"
        if (this.student.stuStatus === StuStatus.online) {
            color = 0x00ff00
            text = "在线"
            let info = this.pageInfoMap.get(this.currentPageCode)
            if (info && this.checkedMap.has(info.page.pageId)) {
                text = "已批改"
            }
            else if (this.student.answerSubmitIndex !== STUDENT_INDEX_MAX) {
                text = "已提交"
            }
            else if (this.pageViewsMap.has(this.currentPageCode)) {
                text = "作答中"
                color = 0xFFD700
            }
            else {
                color = 0xff0000
                text = "未作答"
            }
        }
        return { text, color }
    }

    appendWritePoints(pageCode, points) {
        let pageView = this.getAndShowPageView(pageCode)
        pageView.appendWritePoints(points)
    }

    setBackgroundImageShow(show) {
        this.showImage = show
        this.pageViewsMap.forEach((item) => {
            item.showBackground(show)
        })
    }

    getAndShowPageView(pageCode) {
        if (this.currentPageCode === pageCode) {
            return this.pageViewsMap.get(pageCode)
        }
        this.pageViewsMap.forEach((item) => {
            // 先设置页面都不可见
            item.visible = false
        })

        let pageView = this.pageViewsMap.get(pageCode)
        if (!pageView) {
            pageView = new StudentPageView(this.application, new THREE.Vector3(0, 0, 0), this.size, pageCode)
            pageView.renderOrder = PainterOrder.writePoints
            this.addSubView(pageView)
            this.pageViewsMap.set(pageCode, pageView)
        }
        pageView.showBackground(this.showImage)
        this.currentPageCode = pageCode
        pageView.visible = true
        this.updateStudentState()
        return pageView
    }

    receivePaperPage(event) {
        const pageCode = event.page_code
        const pageInfo = event.page
        // const paper = event.paper
        // const offset = event.offset
        this.pageIdToCodeMap.set(pageInfo.pageId, pageCode)
        this.pageInfoMap.set(pageCode, event)

        let pageView = this.getAndShowPageView(pageCode)
        pageView.setPageInfo(pageInfo)
        pageView.showBackground(this.showImage)
        this.animate()
    }


    /// 接收批改回调
    receiveCheckResult(event) {
        const pageId = event.page_id
        if (this.checkedMap.get(pageId)) {
            return
        }
        this.checkedMap.set(pageId, event)
        const pageCode = this.pageIdToCodeMap.get(pageId)
        if (!pageCode) {
            return
        }
        let pageView = this.pageViewsMap.get(pageCode)
        // for (let [key, view] of this.pageViewsMap.entries()) {
        //     console.log("check ...", view.pageInfo?.pageId ?? "null", pageId)
        //     if (view !== pageView && pageId === view.pageInfo?.pageId) {
        //         this.pageViewsMap.delete(key)
        //         view.dispose()
        //         this.updateStudentState()
        //     }
        // }
        if (pageView) {
            pageView.setCheckResult(event.correct_image)
        }
        this.updateStudentState()
    }


    /// 中文练字批改回调
    receiveWordStars(pageId, item) {
        const pageCode = this.pageIdToCodeMap.get(pageId)
        if (!pageCode) {
            return
        }
        let pageView = this.pageViewsMap.get(pageCode)
        if (!pageView) {
            return
        }
        pageView.drawWorldStars(item)
    }


    setEcharts(xValues, dataValues) {
        // 创建一个离屏 Canvas 元素
        var canvas = document.createElement('canvas');
        canvas.width = 1260;  // 设置 canvas 的大小
        canvas.height = 594;

        this.dataValues = dataValues

        // 使用 ECharts 初始化图表
        var chart = echarts.init(canvas);
        var option = {
            backgroundColor: '#FFFFFF',  // 设置背景色为白色
            title: {
                text: '统计结果',
                textStyle: {
                    fontSize: 16,  // 设置标题字体大小
                    fontWeight: 'normal',
                    color: '#333',
                },
            },
            tooltip: {},
            xAxis: {
                data: xValues,
                axisLabel: {
                    fontSize: 36,  // 设置 X 轴字体大小为 16
                    color: '#000', // 设置 X 轴字体颜色
                },
            },
            yAxis: {
                min: 0,   // Y 轴的最小值
                max: 1,   // Y 轴的最大值
                interval: 0.25,  // 设置 Y 轴的刻度间隔为 0.25
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: ['#ccc'],
                        type: 'dashed',  // 设置辅助线为虚线
                    },
                },
                axisLabel: {
                    fontSize: 36,  // 设置 Y 轴字体大小为 16
                    color: '#000', // 设置 Y 轴字体颜色
                    formatter: function (value) {
                        return value.toFixed(2); // 保证每个刻度显示两位小数
                    },
                },
            },
            series: [{
                name: '概率',
                type: 'line',  // 折线图
                data: dataValues, // 0 到 1 的比例数据
                smooth: false,  // 平滑折线
                lineStyle: {
                    width: 6,
                    color: '#ff6347',  // 设置折线的颜色
                },
                symbolSize: 16,  // 设置折点的大小
                itemStyle: {
                    color: '#ff6347',  // 设置折线的点的颜色
                },
                label: {
                    show: true, // 显示数据点上的数值
                    position: 'top', // 数据标签显示在点的上方
                    color: '#000',  // 设置数值的颜色
                    fontSize: 36, // 设置数值的字体大小
                    formatter: function (params) {
                        return params.value.toFixed(2); // Format to 2 decimal places
                    }
                }
            }]
        };
        chart.setOption(option);

        chart.on('finished', () => {
            if (!this.chartImageView) {
                this.chartImageView = new BoardImageView(
                    this.application,
                    new THREE.Vector3(0, this.size.height / 3 - 0.05, 0),
                    { width: this.size.width, height: this.size.height / 3 })
                this.chartImageView.renderOrder = PainterOrder.studentName
                this.addSubView(this.chartImageView)
            }
            this.chartImageView.setImageUrl(chart.getDataURL())

            this.updateProbabilityValues()

            this.animate()
        });
    }

    updateProbabilityValues() {

        if (!this.chartValueView) {
            this.chartValueView = new BoardView(
                this.application,
                new THREE.Vector3(0, 0, 0),
                this.size)
            this.chartValueView.setBackgroundColor(0xff0000)
            this.chartValueView.renderOrder = PainterOrder.customDisplay
            this.addSubView(this.chartValueView)
        }

        this.chartValueView.children.forEach((item) => {
            BoardTool.disposeMesh(item)
        })
        this.chartValueView.clear()

        let spaceX = 0.12619048;
        let spaceY = 0.07407407;

        let cellWidth = spaceX * this.size.width;

        let startX = - cellWidth * this.dataValues.length / 2

        let columns = this.dataValues.length;  // 每行最多显示的数量


        /// x，y在视图中心点的
        let x = -0.26190476 * this.size.width + cellWidth * this.dataValues.length / 2
        let y = -0.1956229 * this.size.height
        this.chartValueView.position.set(x, y, 0)

        this.dataValues.forEach((value, index) => {
            let valueGeometry = new TextGeometry(value.toFixed(2) + '', {
                font: fontLoader.defaultFont,
                size: 0.03,
                depth: 0,
                bevelEnabled: false,
            });

            const valueMaterial = new THREE.MeshBasicMaterial({ color: 0x000000 });
            let valueMesh = new THREE.Mesh(valueGeometry, valueMaterial);
            valueMesh.renderOrder = PainterOrder.studentName;

            valueGeometry.computeBoundingBox();
            const boundingBox = valueGeometry.boundingBox;
            const textWidth = boundingBox.max.x - boundingBox.min.x;
            const textHeight = boundingBox.max.y - boundingBox.min.y;

            // 计算格子中心点
            let cellCenterX = startX + (index % columns) * cellWidth + cellWidth / 2;

            // 将文本居中到格子
            valueMesh.position.set(cellCenterX - textWidth / 2, 0 - textHeight / 2, 0);

            this.chartValueView.add(valueMesh);
        });
    }

    dispose() {
        this.pageIdToCodeMap.clear()
        this.pageInfoMap.clear()
        this.pageViewsMap.clear()
        super.dispose()
    }
}