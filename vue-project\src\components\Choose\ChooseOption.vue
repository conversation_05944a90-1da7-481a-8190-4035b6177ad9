<template>
    <div class="content">
        <div class="optins">
            <RBPHorizontalProgressBar :title="item.option" :accuracy="getAccuracy(i)" :selected="item.selected"
                v-for="(item, i) in optionList" :key="i" @click="optionClick(item)"></RBPHorizontalProgressBar>
        </div>
        <!-- <div class="tips">
            <div class="tips-title">选项统计</div>
            <div class="tips-content">点击查看作答学生</div>
        </div> -->
    </div>
</template>
<script setup>
import { ref, watch, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useInteractStore } from '@/stores/interact_store'
import { useAnswersStore, STUDENT_INDEX_SUBMIT, STUDENT_INDEX_MAX } from '@/stores/answers_store.js'
import { useClassroomStore } from '@/stores/classroom.js'
import { Interact } from '@/classroom/interact_enums'
import { useEvaluationScoreStore } from '@/stores/evaluation_score_store'
import RBPHorizontalProgressBar from '@/components/baseComponents/RBPHorizontalProgressBar.vue'
import { RBPColors } from '@/components/baseComponents/RBPColors.js'

const evaluationScoreStore = useEvaluationScoreStore()
const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)
const answerStore = useAnswersStore()
const { responseNumber, studentAnswers, rightAnswer, currentOption } = storeToRefs(answerStore)
const interactStore = useInteractStore()
const { showChooseOptionList, interact } = storeToRefs(interactStore)

const optionList = computed(() => {
    if (interact.value == Interact.trueFalse) {
        return [
            {
                option: '对',
                selected: rightAnswer.value.includes('对')
            },
            {
                option: '错',
                selected: rightAnswer.value.includes('错')
            },
            {
                option: '未提交',
                selected: rightAnswer.value.includes('未提交')
            }
        ]
    } else if (interact.value == Interact.vote) {
        return [
            {
                option: 'A',
                selected: true
            },
            {
                option: 'B',
                selected: true
            },
            {
                option: 'C',
                selected: true
            },
            {
                option: 'D',
                selected: true
            },
            {
                option: 'E',
                selected: true
            },
            {
                option: 'F',
                selected: true
            },
            {
                option: '未提交',
                selected: true
            }
        ]
    } else {
        return [
            {
                option: 'A',
                selected: rightAnswer.value.includes('A')
            },
            {
                option: 'B',
                selected: rightAnswer.value.includes('B')
            },
            {
                option: 'C',
                selected: rightAnswer.value.includes('C')
            },
            {
                option: 'D',
                selected: rightAnswer.value.includes('D')
            },
            {
                option: 'E',
                selected: rightAnswer.value.includes('E')
            },
            {
                option: 'F',
                selected: rightAnswer.value.includes('F')
            },
            {
                option: '未提交',
                selected: rightAnswer.value.includes('未提交')
            }
        ]
    }

})
/// 计算百分比
function getAccuracy(index) {
    let students = selectedClassroom.value.studentList.length
    let accuracy = 0
    if (optionList.value.length == 7) {
        if (index == 0) {
            accuracy = studentContainAnswer('A') / students
        } else if (index == 1) {
            accuracy = studentContainAnswer('B') / students
        } else if (index == 2) {
            accuracy = studentContainAnswer('C') / students
        } else if (index == 3) {
            accuracy = studentContainAnswer('D') / students
        } else if (index == 4) {
            accuracy = studentContainAnswer('E') / students
        } else if (index == 5) {
            accuracy = studentContainAnswer('F') / students
        } else if (index == 6) {
            accuracy = selectedClassroom.value.studentList.filter(stu =>
                stu.answerSubmitIndex === STUDENT_INDEX_SUBMIT || stu.answerSubmitIndex === STUDENT_INDEX_MAX).length / students
        }
    } else if (optionList.value.length == 3) {
        if (index == 0) {
            accuracy = studentContainAnswer('RIGHT') / students
        } else if (index == 1) {
            accuracy = studentContainAnswer('WRONG') / students
        } else if (index == 2) {
            accuracy = selectedClassroom.value.studentList.filter(stu =>
                stu.answerSubmitIndex === STUDENT_INDEX_SUBMIT || stu.answerSubmitIndex === STUDENT_INDEX_MAX).length / students
        }
    }
    return Math.round(accuracy * 100) + '%'
}

/// 当前题目的选项有多少人选了
function studentContainAnswer(opt) {
    let count = 0
    Object.values(studentAnswers.value).forEach(answer => {
        if (answer[0].sort().join('').includes(opt)) {
            count++
        }
    })
    return count
}
function optionClick(item) {
    currentOption.value = item.option
    showChooseOptionList.value = true
    evaluationScoreStore.getEvaluationItemFixedList()
}

defineExpose({optionClick})
</script>
<style lang="scss" scoped>
.content {
    position: absolute;
    width: 100%;
    height: 100%;

    .optins {
        margin-top: 10px;
    }

    .tips {
        display: flex;
        flex-direction: column;
        align-items: center;

        .tips-title {
            color: var(--secondary-text-color);
            font-size: 1.6vh;
        }

        .tips-content {
            color: var(--explanatory-text-color);
            font-size: 1.2vh;
        }
    }
}
</style>