import { useAnswersStore } from '@/stores/answers_store.js'
import { useInteractStore } from '@/stores/interact_store'
import roomUpdater from '@/classroom/classroom_updater.js'
import { Interact } from '@/classroom/interact_enums.js'
import { remoteControl } from './remote_control'

export function chooseSelectAnswerClick(opt,) {
    let option = opt.option
    const answerStore = useAnswersStore()
    const interactStore = useInteractStore()

    let item = answerStore.chooseOptionList.find(item => item.option == option)
    
    if (interactStore.interact == Interact.multiChoice) {
        item.selected = !item.selected
    } else {
        answerStore.chooseOptionList.forEach((item) => {
            item.selected = false
        })
        item.selected = true
    }

    answerStore.rightAnswer = ""
    answerStore.chooseOptionList.forEach((item) => {
        if (item.selected) {
            answerStore.rightAnswer += item.option
        }
    })
    remoteControl.sendSelectInteractResult({options:answerStore.chooseOptionList,select:option})

    if (!interactStore.teacherStopAnswer) {
        interactStore.teacherStopAnswer = true
        roomUpdater.stopInteract()
    }
}

export function chooseStopInteractClick() {
    const answerStore = useAnswersStore()
    answerStore.responseNumber = []
    roomUpdater.stopInteract(true)
}