<template>
    <div class="record-review-students-body">
        <RecordBoard ref="recordBoardRef" :students="students" :is-review="true"></RecordBoard>

    </div>
</template>

<script setup>
import { onMounted, ref, watch } from 'vue';
import RecordBoard from './RecordBoard.vue';
import { ClassroomRecordRequest } from '@/server_request/classroom_record';
import { useRecordDrawboardStore } from '@/stores/record_drawboard_store';
import { storeToRefs } from 'pinia';
import { RecordViewType } from './record_single_paper_view';
import { ElMessage } from 'element-plus';

const recordDrawboardStore = useRecordDrawboardStore()
const { recordDrawBoardPainter, correctArea } = storeToRefs(recordDrawboardStore)
const props = defineProps(
    {
        taskId: String,
        statisticType: Number,
        stus: {
            default: [],
        }
    }
)
const recordBoardRef = ref(null)

const students = ref([])


async function getStudentPaper(item) {

    let index = students.value.findIndex(it => { return it.studentId == item.studentId })

    if (index != -1) {
        students.value.splice(index, 1)
        if (recordBoardRef.value) {
            recordBoardRef.value.initBoard()
        }
        return 1
    }
    try {
        let params = {
            studentId: item.studentId,
            taskId: props.taskId,
            showPage: true
        }
        let { data } = await ClassroomRecordRequest.studentAnswerPaper(params)
        data.forEach((item, index) => {
            item.img = item.img.replace('-Mark', '')
        })
        var value = setSize(data, students.value.length + 1)
        if (value) {
            let valueData = {
                studentId: item.studentId,
                studentName: item.studentName,
                images: value
            }
            students.value.push(valueData)
            if (students.value.length > 6) {
                students.value.splice(0, students.value.length - 6)
            }
            if (recordBoardRef.value) {
                recordBoardRef.value.initBoard()
            }
            return 1

        }
    } catch (e) {
        console.log("------------------------", e);
        ElMessage.error("请求失败")
        return 0

        //TODO handle the exception
    }
}
function setSize(papers, count) {
    let papersD = JSON.parse(JSON.stringify(papers))
    let ratio = 1

    let paperWidth = window.innerWidth * window.devicePixelRatio / (count || 1)
    if (!papersD[0]) {
        return
    }
    let isPaper = papersD[0].isPaper === undefined ? true : papersD[0].isPaper
    if (isPaper) {
        ratio = paperWidth / 21000  // 21000 * 29700
    } else {
        ratio = paperWidth / 1190  // 1190 * 1684
    }
    papersD.forEach((item, index) => {
        if (item.statistics) {
            item.statistics.forEach(area => {
                area.H = parseInt(area.areaH * ratio)
                area.W = parseInt(area.areaW * ratio)
                area.X = parseInt(area.areaX * ratio)
                area.Y = parseInt(area.areaY * ratio)
            })
        }
    })
    return papersD
}


//修改view
function changeView(type, visible) {
    if (recordDrawBoardPainter.value && recordDrawBoardPainter.value.imageViewMap) {
        Object.values(recordDrawBoardPainter.value.imageViewMap).forEach((e) => {
            e.changeView(type, visible)
        })
    }


}


function setShowAnswer(data) {
    changeView(RecordViewType.answers, data)

}
function setShowOrder(data) {
    changeView(RecordViewType.orders, data)
}
function setShowMark(data) {
    changeView(RecordViewType.corrects, data)
}


defineExpose({ getStudentPaper, setShowAnswer, setShowOrder, setShowMark })
</script>

<style lang="scss" scoped>
.record-review-students-body {
    position: relative;
    width: 100%;
    height: 100%;



}
</style>