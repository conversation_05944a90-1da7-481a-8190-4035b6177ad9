<template>
    <div class="rbp-input-body" :style="{ height: `${props.height}px` }">
        <el-input ref="inputWrapper" v-model="model" @input="handleInput" :placeholder="props.placeholder"
            :class="['rbp-input-base', 'drop_outer', inputClass]"
            :input-style="{ fontSize: `${props.fontSize.length != 0 ? props.fontSize : 21}px`, }" @blur="onBlur"
            @focus="onFocus" :rows="props.rows" :type="props.type" resize="none">

        </el-input>
        <div class="drop-icon drop_outer" v-if="props.showDropIcon">
            <img src="/icon/icon_drop.svg" alt="" @click="clickDrop">
        </div>
        <div v-if="props.hasDrop && showList && props.dataList && Object.keys(props.dataList).length" class="list"
            :style="{ top: `${toNumber(props.height) + 8}px`, height: showList && props.dataList && Object.keys(props.dataList).length ? `${toNumber(props.itemHeight) * Object.keys(props.dataList).length + 20}px` : '0', maxHeight: `${toNumber(props.itemHeight) * 4 + 20}px` }">
            <div class="item" :style="{ height: `${props.itemHeight}px` }" @click="clickItem(key, value)"
                v-for="(value, key) in props.dataList" :key="key">
                <span class="name" v-if="!props.isSlot">{{ key }}</span>
                <slot v-else name="rbpItem" :itemValue="value"></slot>
                <div v-if="props.showDeleteIcon" class="close" @click.stop="clickDelete(key)">
                    <img src="/icon/icon_close_item.svg" alt="">
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, watch } from 'vue';
const model = defineModel()
const inputWrapper = ref(null);  // 创建一个 ref 引用

const showList = ref(false)
const props = defineProps({
    placeholder: {
        type: String,
        default: '',
    },
    inputClass: {
        type: String,
        default: '',   // 接受父组件传递的额外 class 名称
    },
    fontSize: {
        type: String,
        default: ''
    },
    borderRadius: {
        type: String,
        default: "",
    },
    height: {
        type: Number,
        default: 54
    },
    dataList: {
        default: {}
    },
    itemHeight: {
        type: Number,
        default: 48,
    },
    showDropIcon: {
        default: false,
    },
    hasDrop: {
        default: false,
    },
    type: {
        default: ""
    },
    showDeleteIcon: {
        default: true
    },
    limitNumber: {
        defalut: false
    },
    rows: {
        default: 2
    },
    isSlot: {
        default: false
    }
})
const emits = defineEmits('clickItem', 'deleteItem','clickDrop')
function clickItem(key, value) {
    emits('clickItem', key, value)
}

function handleInput(value) {
    if (props.limitNumber) {
        model.value = value.replace(/[^-0-9]/g, '')
    }
    emits('input')
}

function onBlur() {
    setTimeout(() => {
        showList.value = false
    }, 200)
}

function clickDelete(key) {
    emits('deleteItem', key)
}

function clickDrop() {
    showList.value = !showList.value
    if(showList.value){
        emits('clickDrop')
    }
}

function toNumber(data) {
    if (!data) {
        return 0
    }
    let value = data + ""
    if (isPositiveIntegerString(value)) {
        return parseFloat(value)
    } else {
        return 0
    }
}

function onFocus() {
    showList.value = true
}
onMounted(() => {
    changeStyle()
    // 添加全局点击事件监听器
    document.addEventListener('click', handleClickOutside);
})
onUnmounted(() => {
    // 移除全局点击事件监听器
    document.removeEventListener('click', handleClickOutside);
})
watch(props, () => {
    changeStyle()
})

function isPositiveIntegerString(value) {
    return /^[1-9]\d*$/.test(value);
}
function changeStyle() {

    const wrapper = inputWrapper.value?.$el.querySelector('.el-input__wrapper');
    if (wrapper) {
        if (props.borderRadius && isPositiveIntegerString(props.borderRadius)) {
            wrapper.style.borderRadius = props.borderRadius + 'px';  // 修改 border-radius
        }
        wrapper.style.paddingRight = (props.showDropIcon ? '60px' : '15px')
    }
}
//点击外部区域
function handleClickOutside(event) {
    // 判断点击的元素是否属于 '.inner' 类
    if (!event.target.closest('.drop_outer')) {
        // 点击的不是 '.inner' 区域，触发外部点击事件
        // console.log('点击了外部区域！');
        if (showList.value) {
            showList.value = false
        }
    } else {
        // 点击的是 '.inner' 区域，忽略外部点击事件
        // console.log('点击了内部区域，忽略外部事件');
    }
}

</script>

<style lang="scss" type="text/css">

.rbp-input-base {
    .el-input__wrapper {
        box-shadow: 0 0 0 2px var(--border-bar-color) inset;
        padding-left: 15px;
        padding-right: 15px;
        font-weight: 400;
        line-height: 36px;
        border-radius: 15px;
        text-align: left;

        .el-input__inner {
            color: var(--text-color) !important;
        }

    }

    .el-input__inner::placeholder {
        color: var(--explanatory-text-color);
    }

    .el-input__wrapper:hover {
        
        box-shadow: 0 0 0 3px var(--primary-color) inset;
    }

    .el-input__wrapper.is-focus {
        box-shadow: 0 0 0 3px var(--primary-color) inset;
    }
    .el-textarea__inner {
    
    box-shadow: 0 0 0 2px var(--border-bar-color) inset;
    border-radius: 15px;
    color: var(--text-color) !important;
    padding: 9px 15px;
    
}
.el-textarea__inner::placeholder {
        color:var(--explanatory-text-color);
    }

    .el-textarea__inner:hover {
        box-shadow: 0 0 0 3px var(--primary-color) inset;
    }

    .el-textarea__inner.is-focus {
        box-shadow: 0 0 0 3px var(--primary-color) inset;
    }
}
</style>

<style lang="scss" scoped>
@import '../../assets/scss/mixin.scss';

.rbp-input-body {
    position: relative;
    display: flex;
    align-items: center;

    .rbp-input-base {
        flex: 1;
        height: 100%;
    }

    .drop-icon {
        position: absolute;
        right: 15px;
        display: flex;
        align-items: center;
        cursor: pointer;
    }

    .list {
        width: 100%;
        height: 0;
        max-height: 30vh;
        background-color: var(--main-bc-color);
        position: absolute;
        z-index: 100;
        left: 0;
        padding: 8px 0px;
        border-radius: 15px;
        border: 2px solid var(--border-bar-color);
        transition: all 0.2s;
        overflow-y: auto;
        /* 隐藏滚动条 */
        -ms-overflow-style: none;
        /* 对 IE 和 Edge 兼容 */
        scrollbar-width: none;

        /* 对 Firefox 兼容 */
        .item {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 0px 15px;

            .name {
                color: var(--text-color);
                flex: 1;
                white-space: nowrap;
                /* 防止换行 */
                overflow: hidden;
                /* 隐藏超出部分 */
                text-overflow: ellipsis;
                /* 超出部分显示省略号 */
            }

            .close {
                cursor: pointer;
                margin-left: 15px;

            }
        }

        // .item:first-child {
        //     margin-top: 8px;
        // }
    }

    .list::-webkit-scrollbar {
        display: none;
    }
}
</style>