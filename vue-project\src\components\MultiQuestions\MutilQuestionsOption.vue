<template>
    <RBPAlert title="当前列表学生" :isSupportClose="true" width="60%" @close="optionBackClick" @click.stop>
        <template v-slot:rbpDiv>
            <div class="modal-content">
                <div class="stu-grid">
                    <StudentGridItem :student="stu" :disable="true" :isInterfaceSelection="false"
                        v-for="(stu, i) in studentList" :key="i" :show-status="false">
                    </StudentGridItem>
                    <RBPNoData v-if="studentList.length == 0">
                        <template #rbpTitle>
                            <span>暂无学生</span>
                        </template>
                    </RBPNoData>
                </div>
            </div>
        </template>
        <template #rbpBtns>
            <div class="btns">
                <div v-if="loginInstance.evaluation" class="modal-footer-score">
                    <div class="scores" v-for="(item, index) in evaluationList" :key="index">
                        <RBPAddScoreEva :item="item" @evaluationItemClick="evaluationItemClick(item)"></RBPAddScoreEva>
                        <div :style="{ width: '12px' }"></div>
                    </div>
                </div>
                <div v-else class="modal-footer-score">
                    <div class="scores" v-for="(score, index) in scoreList" :key="index">
                        <RBPAddScore :score="score" @click="addScoreClick(score, 'success')"></RBPAddScore>
                        <div :style="{ width: '12px' }"></div>
                    </div>
                </div>
                <div class="tips">
                    <span class="tips-type1">{{ currentQuestionIndex + 1 }}-{{
                        interactName(startQuestions[currentQuestionIndex]) }} 选 </span>
                    <span class="tips-type2"> {{ currentOption }} </span>
                    <span class="tips-type1"> 的学生 共计</span>
                    <span class="tips-type2"> {{ studentList.length }} </span>
                    <span class="tips-type1">人</span>
                </div>
                <div class="random">
                    <RBPButton btnText="随机点名" btnSelected="true" @click="randomClick"></RBPButton>
                </div>
            </div>
        </template>
    </RBPAlert>
</template>
<script setup>
import { defineProps, defineEmits, getCurrentInstance, onMounted, ref, watch, computed } from 'vue'
import { toRaw } from '@vue/reactivity'
import { Interact, interactName, MultiQusLecture } from '@/classroom/interact_enums.js'
import { storeToRefs } from 'pinia'
import { useAnswersStore, STUDENT_INDEX_SUBMIT, STUDENT_INDEX_MAX } from '@/stores/answers_store.js'
import { useClassroomStore } from '@/stores/classroom.js'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { Alert } from '@/utils/alert'
import roomUpdater from '@/classroom/classroom_updater.js'
import { useEvaluationScoreStore } from '@/stores/evaluation_score_store'
import { loginInstance } from '@/login_instance/login_instance'
import { useRandomRollCallStore } from '@/stores/random_roll_call_store'
import { useScoreAudioStore } from '@/stores/score_audio_store'
import RBPAlert from '@/components/baseComponents/RBPAlert.vue'
import { RBPColors } from '@/components/baseComponents/RBPColors.js'
import RBPAddScore from '@/components/baseComponents/RBPAddScore.vue'
import RBPButton from '@/components/baseComponents/RBPButton.vue'
import RBPAddScoreEva from '@/components/baseComponents/RBPAddScoreEva.vue'
import StudentGridItem from '@/components/StudentList/StudentGridItem.vue'
import RBPNoData from '@/components/baseComponents/RBPNoData.vue'

const scoreList = ref(['-5', '-3', '-1', '+1', '+3', '+5'])
const scoreAudioStore = useScoreAudioStore()
const evaluationScoreStore = useEvaluationScoreStore()
const { evaluationList } = storeToRefs(evaluationScoreStore)
const randomRollCallStore = useRandomRollCallStore()
const { sourceGroupList } = storeToRefs(randomRollCallStore)
const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)
const classroomUIStore = useClassroomUIStore()
const { mainContentTopSpace, showRandomRollCall } = storeToRefs(classroomUIStore)
const answerStore = useAnswersStore()
const { multiQuestionAnswers, multiRightAnswers, startQuestions, currentQuestionIndex, currentOption, fromLecture } = storeToRefs(answerStore)
const { proxy } = getCurrentInstance()
const calcTop = computed(() => {
    return `calc(50% + ${mainContentTopSpace.value}px)`
})
const studentList = computed(() => {
    let stuList = Array()
    // 判断是未提交的情况
    if (currentOption.value == '未提交') {
        // stuList = selectedClassroom.value.studentList.filter(stu =>
        //     stu.answerSubmitIndex === STUDENT_INDEX_SUBMIT || stu.answerSubmitIndex === STUDENT_INDEX_MAX)

        stuList = selectedClassroom.value.studentList.filter(stu => {
            let answer = multiQuestionAnswers.value[stu.studentId]
            if (answer != undefined) {
                if (answer[currentQuestionIndex.value].length > 0) {
                    return false
                } else {
                    return true
                }
            } else {
                return true
            }
        })
        return stuList
    }
    for (let [studentId, answer] of Object.entries(multiQuestionAnswers.value)) {
        let currentAnswer = answer[currentQuestionIndex.value]
        let cOption = currentOption.value
        if (currentOption.value == '对') {
            cOption = 'RIGHT'
        } else if (currentOption.value == '错') {
            cOption = 'WRONG'
        }
        if (currentAnswer.includes(cOption)) {
            let stu = selectedClassroom.value.idStudentMap.get(parseInt(studentId))
            stuList.push(stu)
        }
    }
    return stuList
})
async function addScoreClick(score, type) {
    if (studentList.value.length == 0) {
        if (type == 'error') {
            Alert.showErrorMessage('当前题目没有作答错误的学生')
        } else if (type == 'success') {
            Alert.showErrorMessage('当前题目没有作答正确的学生')
        }
        return
    }

    studentList.value.forEach(stu => {
        stu.score = String(parseInt(stu.score ?? '0') + parseInt(score))
    })

    if (type == 'error') {
        Alert.showErrorMessage(score + '分')
    } else if (type == 'success') {
        Alert.showSuccessMessage(score + '分')
    }

    //上传数据
    let success = await roomUpdater.studentsAddScore(studentList.value, score)
    if (success) {
        // 播放音频 播放动画
        scoreAudioStore.play(score)
    }
}
function optionBackClick() {
    answerStore.lecture = fromLecture.value
}
function randomClick() {
    if (studentList.value.length == 0) {
        Alert.showErrorMessage('没有学生可以随机点名')
        return
    }

    randomRollCallStore.setup(selectedClassroom.value.studentList, [...studentList.value])
    showRandomRollCall.value = true
}
async function evaluationItemClick(item) {
    studentList.value.forEach(stu => {
        stu.selected = true
    })

    // 是否有学生选中
    let haveSelected = false
    selectedClassroom.value.studentList.forEach(student => {
        if (student.selected) {
            haveSelected = true
        }
    })
    if (!haveSelected) {
        Alert.showErrorMessage('当前无选中学生')
        return
    }

    let success = await roomUpdater.studentsAddScoreTypeTwo(item.evaluationItemId, item.evaluationItemScore)
    if (success) {
        // 播放音频 播放动画
        scoreAudioStore.play(item.evaluationItemScore)
    }
}
</script>
<style lang="scss" scoped>
@import "@/assets/scss/mixin.scss";

.btns {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;

    .modal-footer-score {
        display: flex;

        .scores {
            display: flex;
        }
    }

    .tips {
        position: absolute;
        left: 20px;
        font-size: 18px;

        .tips-type1 {
            color: var(--secondary-text-color);
        }

        .tips-type2 {
            color: var(--primary-color);
        }
    }

    .random {
        position: absolute;
        right: 80px;
    }
}

.modal-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px;
    box-sizing: border-box;

    .stu-grid {
        width: 100%;
        height: 100%;
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        border-radius: 26px;
        border: 1px solid var(--border-bar-color);
        box-sizing: border-box;
        padding: 20px;
        flex: 1;
        overflow-y: auto;
        position: relative;
        grid-auto-rows: min-content;
        /* 行高根据内容调整 */
        align-content: start;
        /* 内容顶部对齐 */
    }
}
</style>