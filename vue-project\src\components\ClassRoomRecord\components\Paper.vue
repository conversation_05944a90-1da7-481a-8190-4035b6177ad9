<template>
    <div class="record-paper">
        <div class="paper-component" ref="paperComponent">
            <div v-for="(item, index) in papers" :key="index" class="paper-wrap">
                <img class="paper" width="100%" :src="item.img + '?' + Math.random()" alt=""
                    @dblclick="imgClick(item, index)" @touchstart="canvasDown($event, item, index)">
                <div class="area" :style="{ width: area.W, height: area.H, top: area.Y, left: area.X }"
                    v-for="(area, ind) in item.statistics" :key="ind" @dblclick="imgClick(item, index)"
                    @touchstart="canvasDown($event, item)">
                    <el-popover width="auto" v-if="showMark" placement="bottom-start"
                        popper-class="record-paper-edit-popper" :visible-arrow="false" trigger="click" :open-delay="200"
                        v-model="area.popover" @hide="showPopoverArea = null">
                        <el-input v-model="area.score"
                            v-if="statisticType && area.questionType == 1 && area.isHandle == 1"
                            @change="checkValue(area)" style="margin-bottom: 12px;" placeholder="得分" size="medium"
                            ref="inputRef">
                            <el-button slot="prepend" @click="checkValue(area, 0)">0</el-button>
                            <el-button slot="append" @click="checkValue(area, area.allScore)">{{ area.allScore
                                }}</el-button>
                        </el-input>
                        <el-button-group class="editBtns">
                            <el-button type="primary" @click="updateBaseRes(1, area)">
                                <img src="../assets/icon_correct.png" />
                            </el-button>
                            <el-button type="primary" v-if="area.questionType == 1" @click="updateBaseRes(2, area)">
                                <img src="../assets/icon_half.png" />
                            </el-button>
                            <el-button type="primary" @click="updateBaseRes(0, area)">
                                <img src="../assets/icon_error.png" />
                            </el-button>
                        </el-button-group>
                        <template #reference>
                            <div class="corr" @click="showPopover(area)">
                                <span v-if="area.isHandle == 0" style="font-size: 30px;">
                                    未批
                                </span>
                                <template v-else>
                                    <img class="corr_icon" :style="{ transform: `scale(${1 / size})` }"
                                        v-if="area.markPoint == 0" src="../assets/correct_wrong.png">
                                    <img class="corr_icon" :style="{ transform: `scale(${1 / size})` }"
                                        v-if="area.markPoint == 1" src="../assets/correct_right.png">
                                    <img class="corr_icon" :style="{ transform: `scale(${1 / size})` }"
                                        v-if="area.markPoint == 2" src="../assets/correct_right1.png">
                                    <span v-if="statisticType && area.questionType == 1 && area.isHandle == 1">{{
                                        area.score
                                    }}</span>
                                </template>
                            </div>
                        </template>

                    </el-popover>
                    <div class="show-ans" v-if="showAnswer && area.quesAnswer">
                        <span class="ans" v-if="area.questionType == 3">{{ area.quesAnswer == 0 ? 'x' : '✓' }}</span>
                        <img class="ans" v-else
                            :src="`https://www.zhihu.com/equation?tex=\\color{red}{${encodeURI(area.quesAnswer)}}`"
                            alt="">
                    </div>
                    <div class="order" v-if="showOrder && area.indexs > 0">{{ area.indexs }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ClassroomRecordRequest } from '@/server_request/classroom_record';
import { ref, nextTick, onMounted, watch } from 'vue';
const props = defineProps({
    stuInfo: Object,
    taskId: String,
    paperHeight: Number,
    paperImgs: Array,
    statisticType: Number
})
const emits = defineEmits(['showImg'])
const papers = ref([])
let isPaper = null // 是否是试卷作答 还是E3C作答，
let showPopoverArea = null
let oldScore = 0 // 弹出popover的题目原始得分
const showAnswer = ref(false)
const showMark = ref(true)
const showOrder = ref(false)
let dbclick = 0 // 双击flag
const size = ref(1) // 一屏展示几个学生
const paperComponent = ref()
//子组件ref
const inputRef = ref([])
function canvasDown(e, item, index) { // 触屏模拟双击事件
    dbclick++
    if (dbclick > 1) {
        imgClick(item, index)
    }
    setTimeout(() => {
        dbclick = 0
    }, 200)
}
function imgClick(item, index) {
    let originImg = props.paperImgs.find(it => { return it.indexOf(`/${item.pageCode}`) > -1 })
    let playerInfo = {
        taskId: props.taskId,
        studentId: props.stuInfo.studentId,
        pageId: item.pageId,
        userImg: item.img,
        originImg,
    }
    localStorage.setItem('showImgUrlParams', JSON.stringify({
        index: index,
        taskId: props.taskId,
        studentId: props.stuInfo.studentId,
        papers: papers.value,
        paperImgs: props.paperImgs
    }))
    localStorage.setItem('showImgUrl', true)
    emits('showImg', item.img, playerInfo, true)
}
function checkValue(area, score) {
    if (score !== undefined) {
        area.score = score
    }
    if (area.score !== '') {
        if (area.score > area.allScore) {
            area.score = area.allScore
        } else if (area.score < 0) {
            area.score = 0
        }
    } else {
        area.score = this.oldScore
    }
    area.score = parseInt(area.score)
    area.isHandle = 1
    updateBaseScore(area)
}
// 主观题修改答案
async function updateBaseScore(area) {
    area.popover = false
    let params = {
        "taskId": props.taskId,
        "paperId": area.paperId,
        "reDrawImg": false,
        "correctInfo": [{
            "qid": area.qid,
            "studentId": props.stuInfo.studentId,
            "recogScore": area.score,
            "correctResult": area.markPoint,
            "pageId": area.pageId,
        }]
    }
    try {
        let { data } = await ClassroomRecordRequest.answerCorrectManual(params)
        // this.calcScore(data)
    } catch (e) {
        //TODO handle the exception
    }
}
// 客观题修改答案
async function updateBaseRes(newMarkPoint, area) {
    area.popover = false
    if (area.markPoint != newMarkPoint || area.isHandle === 0) {
        try {
            let params = {
                "taskId": props.taskId,
                "paperId": area.paperId,
                "reDrawImg": false,
                "correctInfo": [{
                    "qid": area.qid,
                    "studentId": props.stuInfo.studentId,
                    "recogScore": newMarkPoint == 0 ? 0 : (newMarkPoint == 1 ? area.allScore : area.allScore / 2),
                    "correctResult": newMarkPoint,
                    "pageId": area.pageId,
                }]
            }
            let { data } = await ClassroomRecordRequest.answerCorrectManual(params)
            area.markPoint = newMarkPoint
            area.score = newMarkPoint == 0 ? 0 : (newMarkPoint == 1 ? area.allScore : area.allScore / 2)
            area.isHandle = 1
            // this.calcScore(data)
        } catch (e) {
            console.log(e)
            //TODO handle the exception
        }
    }
}
function calcScore(data) {
    data.forEach(item => {
        let score = 0
        item.questions.forEach(it => {
            score += it.score
        })
        calcStudentScore(item.studentId, score)
    })
}
async function getStudentPaper() {
    try {
        let params = {
            studentId: props.stuInfo.studentId,
            taskId: props.taskId,
        }
        let { data } = await ClassroomRecordRequest.studentAnswerPaper(params)
        data.forEach((item, index) => {
            item.img = item.img.replace('-Mark', '')
        })
        papers.value = data
        setSize()
    } catch (e) {
        //TODO handle the exception
    }
}
function resetSize(sizeD) {
    // size： 1单屏  2双屏
    setSize(window.innerWidth / size)
    size.value = sizeD || 1
}
const ratioPaper = ref(1)
function setSize(paperW) {
    let papersD = JSON.parse(JSON.stringify(papers.value))
    let ratio = 1

    let paperWidth = paperW ? paperW : paperComponent.value.offsetWidth
    if (!papersD[0]) {
        return
    }
    isPaper = papersD[0].isPaper === undefined ? true : papersD[0].isPaper
    if (isPaper) {
        ratio = paperWidth / 21000  // 21000 * 29700
    } else {
        ratio = paperWidth / 1190  // 1190 * 1684
    }
    ratioPaper.value = ratio
    papersD.forEach((item, index) => {
        if (item.statistics) {
            item.statistics.forEach(area => {
                area.H = parseInt(area.areaH * ratio) + 'px'
                area.W = parseInt(area.areaW * ratio) + 'px'
                area.X = parseInt(area.areaX * ratio) + 'px'
                area.Y = parseInt(area.areaY * ratio) + 'px'
            })
        }
    })
    papers.value = papersD
}
function setShowAnswer(data) {
    showAnswer.value = data
}
function setShowOrder(data) {
    showOrder.value = data
}
function setShowMark(data) {
    showMark.value = data
}
function showPopover(area) {
    showPopoverArea = area
    oldScore = area.score
    nextTick(() => {
        //获取ref序号
        if (inputRef.value.length) {
            let index = 0;
            for (let data of papers.value) {
                if (data.statistics) for (let value of data.statistics) {

                    if (value.questionType === 1 && value.isHandle === 1) {
                        if (value.qid === area.qid) {
                            if (inputRef.value[index]) {
                                let num = index;
                                setTimeout(() => {
                                    inputRef.value[num].select()
                                }, 100)
                            }
                        }
                        index++;
                    }
                }
            }
        }
        // let item = this.$refs[`input${area.qid}`]
        // if (item && item[0]) {
        //   setTimeout(() => {
        //     item[0].select()
        //   }, 100)
        // }
    })
}
function getAnswer(area) {
    if (area.questionType == 3) {
        return area.quesAnswer == 0 ? 'x' : '✓'
    } else {
        return area.quesAnswer
    }
}
onMounted(() => {
    getStudentPaper()
})
watch(() => props.stuInfo, () => {
    getStudentPaper()
}, { deep: true })
defineExpose({ setShowAnswer, setShowOrder, setShowMark, resetSize })
</script>
<style lang="scss">
.record-paper-edit-popper {
    .editBtns {
        display: flex;

    }

    .editBtns img {
        width: 30px;
        margin: 0 auto;
    }

    .editBtns .type {
        font-size: 15px;
        margin-top: 10px;
    }

    .editBtns .type.right {
        color: var(--correct-color);
    }

    .editBtns .type.half {
        color: var(--unfinished-color);
    }

    .editBtns .type.wrong {
        color: var(--error-color);
    }
}
</style>
<style lang="scss" scoped>
.record-paper {
    width: 100%;
    // background-color: yellow;

    .paper-component {
        flex: 1;
        position: relative;
        padding-bottom: 50px;
    }

    .paper-wrap {
        position: relative;
        flex: 1;
        border-right: 1px solid var(--border-bar-color);
        // width: 100%;
    }

    .paper {
        vertical-align: middle;
        box-sizing: border-box;
    }

    .area {
        position: absolute;
        background-color: rgba($color: #000000, $alpha: 0.5);
    }

    .corr {
        
        position: absolute;
        right: 0;
        bottom: 0;
        z-index: 99;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 30px;
        color: red;
        white-space: nowrap;
    }

    .corr_icon {
        width: 3vw;
        height: 3vw;
    }

    .area .el-button {
        padding: 0px 4px;
        position: absolute;
        top: 3.1vw;
        left: 10px;
    }

    .correct-dialog {
        height: calc(70vh + 110px);
    }

    .correct-popper {
        padding: 12px !important;
    }

    .correct-popper .correct-num {
        display: flex;
        background-color: #fff;
        font-size: 20px;
        margin-bottom: 5px;
        bottom: 0;
    }

    .correct-popper .correct-num .ite {
        margin-right: 5px;
    }

    .correct-popper .correct-num .num {
        font-size: 12px;
    }

    .correct-popper .correct-num .img {
        height: 30px;
        width: 30px;
    }

    .ans-order {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        pointer-events: none;
    }

    .order {
        width: 30px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: var(--primary-color);
        border-radius: 50%;
        position: relative;
        left: 10px;
        color: var(--anti-text-color);
    }

    .show-ans {
        width: 100%;
        height: 100%;
        position: absolute;
        bottom: 0;
        right: 0;
        pointer-events: none;
        color: red;
        font-size: 28px;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 100;
        display: flex;
        justify-content: flex-end;
        align-items: flex-end;
    }

    .show-ans .ans {
        height: 28px;
        background-color: #fff;
    }
}
</style>