<template>
    <div class="rbp-title-input-body" :placeholder="props.placeholder">
        <div class="title">{{ props.title }}</div>
        <RBPInput v-model="inputModel" :placeholder="props.placeholder" :rows="props.rows" :height="props.height" :type="props.type" :limit-number="props.limitNumber"></RBPInput>
    </div>
</template>

<script setup>
import { watch,ref, onMounted } from 'vue';
import RBPInput from './RBPInput.vue';


onMounted(()=>{
    inputModel.value = model.value
})

const inputModel = ref('')
const model = defineModel()
watch(inputModel,()=>{
    model.value = inputModel.value
})

watch(model,()=>{
    if(model.value!=inputModel.value){
        inputModel.value = model.value
    }
})
const props = defineProps({
    placeholder: {
        type: String,
        default: '',
    },
    type:{
        default:""
    },
    limitNumber:{
        defalut:false
    },
    width:{
        default:""
    },
    title:{
        default:""
    },
    height: {
        type: Number,
        default: 54
    },
    rows: {
        default: 2
    },
})

</script>

<style lang="scss" scoped>
.rbp-title-input-body{
    width: v-bind("props.width");
    display: flex;
    flex-direction: column;
    .title{
        font-weight: 400;
        font-size: 21px;
        color: var(--text-color);
        line-height: 36px;
        margin-bottom: 8px;
    }
}
</style>