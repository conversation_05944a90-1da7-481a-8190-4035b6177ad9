import { LocalRequest } from "@/local_request/local_request"
import { ControlOptions, RtcOptions, LoginOptions, InteractOptions } from "./control_options"
import { webRtcServer } from "./webrtc_server"
import { remoteControl } from "./remote_control"
import { useClassroomStore } from "@/stores/classroom"



export class MessageSender {

    duringClass

    constructor() {
        // this.remoteConnected = false
    }

    sendStartClassResult(params) {
        this.duringClass = true
        
        this.sendMessage(ControlOptions.login, LoginOptions.startClass, params)
        remoteControl.sendStudentList()
    }

    sendStopClassResult(params) {
        this.duringClass = false
        this.sendMessage(ControlOptions.login, LoginOptions.stopClass, params)
        webRtcServer.closeRTC()
    }

    

    sendMessageToRemote(params) {
        // if (!this.remoteConnected) {
        //     return
        // }
        LocalRequest.sendMessageToRemote(params).then((res) => {
            if (res.code !== 1) {
                //TODO 提示遥控器断开连接
                this.remoteConnected = false
            }
        })
    }

    sendMessage(control, option, params = {}) {
        this.sendMessageToRemote({
            control,
            option,
            ...params
        })
    }

    sendRTCOffer(params) {
        this.sendMessage(ControlOptions.rtcConnect, RtcOptions.offer, params)
    }

    sendRTCAnswer(params) {
        this.sendMessage(ControlOptions.rtcConnect, RtcOptions.answer, params)
    }


    sendRTCCandidate(params) {
        this.sendMessage(ControlOptions.rtcConnect, RtcOptions.candidate, params)
    }

    sendInteractStateResult(params) {
        this.sendMessage(ControlOptions.interact, InteractOptions.state, params)
    }

    sendStartInteractResult(params) {
        this.sendMessage(ControlOptions.interact, InteractOptions.start, params)
    }

    sendSelectInteractResult(params) {
        this.sendMessage(ControlOptions.interact, InteractOptions.select, params)
    }

    sendRollingInteractResult(params) {
        this.sendMessage(ControlOptions.interact, InteractOptions.rolling, params)
    }

    sendStopInteractResult(params) {
        this.sendMessage(ControlOptions.interact, InteractOptions.stop, params)
    }

    sendAnswerInteractResult(params) {
        this.sendMessage(ControlOptions.interact, InteractOptions.answer, params)
    }

    sendExplainInteractResult(params) {
        this.sendMessage(ControlOptions.interact, InteractOptions.explain, params)
    }
    sendMultiQuestionSelectResult(params) {
        this.sendMessage(ControlOptions.interact, InteractOptions.multiQuestionsSelect, params)
    }
    sendMultiQuestionsSelectAnswerResult(params) {
        this.sendMessage(ControlOptions.interact, InteractOptions.multiQuestionsSelectAnswer, params)
    }
    sendMultiQuestionNumberResult(params) {
        this.sendMessage(ControlOptions.interact, InteractOptions.multiQuestionsNumber, params)
    }

    sendStudentList() {
        const classroomStore = useClassroomStore()
        if(classroomStore.selectedClassroom&&classroomStore.selectedClassroom.studentList){
            let params = { code: 1, message: 'success',list:[]};
            params.list = classroomStore.selectedClassroom.studentList
            this.sendMessage(ControlOptions.interact, InteractOptions.studentList,params )
        }
        
    }

    sendOperating(){
        let params = { code: 1, message: '当前正在进行其他操作'};
        this.sendMessage(ControlOptions.interact, InteractOptions.operate,params )
    }
    
}