<template>
  <div class="math-calc-root">
    <!-- 绘图区 -->
    <div class="plot-area">
      <div ref="plotRef" class="plot-canvas"></div>
    </div>
    <!-- 键盘按钮 -->
    <button class="keyboard-toggle-btn" @click="showKeyboard = true">键盘</button>
    <!-- 半屏弹窗 -->
    <div v-if="showKeyboard" class="keyboard-modal" @click.self="showKeyboard = false">
      <div class="keyboard-modal-content">

        <!-- 自定义功能弹窗 -->
        <transition name="function-modal" appear>
          <div v-if="showFunctionModal" class="function-modal-overlay" @click="closeFunctionModal">
            <div
              ref="functionModalContent"
              class="function-modal-animated"
              :style="functionModalStyle"
              @click.stop
            >
              <div class="function-modal-content">
                <div class="function-modal-header">
                  <h3>功能</h3>
                  <button @click="closeFunctionModal" class="close-btn">×</button>
                </div>
                <div class="function-modal-body">
                  <div class="function-group">
                    <h4>三角函数</h4>
                    <div class="function-buttons">
                      <button @click="insertFunction('sin')" class="function-btn">sin</button>
                      <button @click="insertFunction('cos')" class="function-btn">cos</button>
                      <button @click="insertFunction('tan')" class="function-btn">tan</button>
                      <button @click="insertFunction('csc')" class="function-btn">csc</button>
                      <button @click="insertFunction('sec')" class="function-btn">sec</button>
                      <button @click="insertFunction('cot')" class="function-btn">cot</button>
                    </div>
                  </div>
                  <div class="function-group">
                    <h4>反三角函数</h4>
                    <div class="function-buttons">
                      <button @click="insertFunction('asin')" class="function-btn">sin<sup>-1</sup></button>
                      <button @click="insertFunction('acos')" class="function-btn">cos<sup>-1</sup></button>
                      <button @click="insertFunction('atan')" class="function-btn">tan<sup>-1</sup></button>
                      <button @click="insertFunction('acsc')" class="function-btn">csc<sup>-1</sup></button>
                      <button @click="insertFunction('asec')" class="function-btn">sec<sup>-1</sup></button>
                      <button @click="insertFunction('acot')" class="function-btn">cot<sup>-1</sup></button>
                    </div>
                  </div>
                  <div class="function-group">
                    <h4>统计</h4>
                    <div class="function-buttons">
                      <button @click="insertFunction('sum')" class="function-btn">∑</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </transition>
        <!-- 方程列表 -->
        <div class="equation-list-modal">
          <div class="equation-list-content">
            <div v-for="(eq, idx) in equations" :key="eq.id" class="equation-item">
              <input
                v-model="eq.expr"
                @focus="setActiveInput(idx)"
                placeholder="输入表达式，如 y = x^2"
              />
              <button @click="removeEquation(idx)" class="delete-btn">删除</button>
            </div>
            <button class="add-btn" @click="addEquation">添加方程</button>
          </div>
          <div class="equation-list-footer">
            <button class="display-btn" @click="drawPlot">展示</button>
            <button class="hide-btn" @click="showKeyboard = false">收起</button>
          </div>
        </div>
        <!-- 数学键盘 -->
        <div class="math-keyboard-modal" :class="{'abc-keyboard': keyboardMode === 'abc'}">
          <!-- 动态渲染键盘按钮 -->
          <template v-if="keyboardMode === '123'">
            <!-- 第一行 -->
            <button @click="handleKey('x')" class="key-btn">x</button>
            <button @click="handleKey('y')" class="key-btn">y</button>
            <button @click="handleKey('d²')" class="key-btn">d²</button>
            <button @click="handleKey('aᵇ')" class="key-btn">aᵇ</button>
            <button @click="handleKey('7')" class="key-btn number-key">7</button>
            <button @click="handleKey('8')" class="key-btn number-key">8</button>
            <button @click="handleKey('9')" class="key-btn number-key">9</button>
            <button @click="handleKey('÷')" class="key-btn operator-key">÷</button>

            <!-- 第二行 -->
            <button @click="handleKey('(')" class="key-btn">(</button>
            <button @click="handleKey(')')" class="key-btn">)</button>
            <button @click="handleKey('<')" class="key-btn">&lt;</button>
            <button @click="handleKey('>')" class="key-btn">&gt;</button>
            <button @click="handleKey('4')" class="key-btn number-key">4</button>
            <button @click="handleKey('5')" class="key-btn number-key">5</button>
            <button @click="handleKey('6')" class="key-btn number-key">6</button>
            <button @click="handleKey('×')" class="key-btn operator-key">×</button>

            <!-- 第三行 -->
            <button @click="handleKey('|a|')" class="key-btn">|a|</button>
            <button @click="handleKey('≤')" class="key-btn">≤</button>
            <button @click="handleKey('≥')" class="key-btn">≥</button>
            <!-- 退格键占据第4列，跨2行 -->
            <button @click="handleKey('⌫')" class="key-btn backspace-key backspace-vertical">⌫</button>
            <button @click="handleKey('1')" class="key-btn number-key">1</button>
            <button @click="handleKey('2')" class="key-btn number-key">2</button>
            <button @click="handleKey('3')" class="key-btn number-key">3</button>
            <button @click="handleKey('−')" class="key-btn operator-key">−</button>

            <!-- 第四行 -->
            <button @click="handleKey('abc')" class="key-btn switch-key">abc</button>
            <button @click="handleKey('√')" class="key-btn">√</button>
            <button @click="handleKey('π')" class="key-btn">π</button>
            <!-- 功能按钮 -->
            <button @click="handleKey('func')" class="key-btn function-key" ref="functionButton">功能</button>
            <button @click="handleKey('0')" class="key-btn number-key">0</button>
            <button @click="handleKey('=')" class="key-btn operator-key">=</button>
            <button @click="handleKey('+')" class="key-btn operator-key">+</button>
          </template>
          
          <template v-else>
            <!-- abc键盘布局 -->
            <!-- 第一行 -->
            <button v-for="key in currentKeyboard[0]" :key="key" @click="handleKey(key)" class="key-btn" :class="getKeyClass(key)">{{ getKeyDisplay(key) }}</button>

            <!-- 第二行 -->
            <button v-for="key in currentKeyboard[1]" :key="key" @click="handleKey(key)" class="key-btn" :class="getKeyClass(key)">{{ getKeyDisplay(key) }}</button>

            <!-- 第三行 -->
            <button v-for="key in currentKeyboard[2]" :key="key" @click="handleKey(key)" class="key-btn" :class="[getKeyClass(key), key === '⇧' && shiftActive ? 'shift-active' : '']">{{ getKeyDisplay(key) }}</button>

            <!-- 第四行 -->
            <button v-for="key in currentKeyboard[3]" :key="key" @click="handleKey(key)" class="key-btn" :class="getKeyClass(key)">{{ getKeyDisplay(key) }}</button>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, computed } from 'vue'
import functionPlot from 'function-plot'

const plotRef = ref(null)
const functionButton = ref(null)
const functionModalContent = ref(null)
const activeInputIdx = ref(0)
const showKeyboard = ref(false)
const showFunctionModal = ref(false)
const functionModalPosition = ref({ x: 0, y: 0 })
const equations = reactive([
  { id: Date.now(), expr: 'y = x^2' }
])

// 键盘模式
const keyboardMode = ref('123') // '123' 或 'abc'
// Shift键状态
const shiftActive = ref(false)

// 两套键盘布局 - 根据参考图片优化
const keyboardLayouts = {
  '123': [
    ['x', 'y', 'd²', 'aᵇ', '7', '8', '9', '÷'],
    ['(', ')', '<', '>', '4', '5', '6', '×'],
    ['|a|', '≤', '≥', '⌫_start', '1', '2', '3', '−'],
    ['abc', '√', 'π', '⌫_end', '0', '=', '+']
  ],
  abc: [
    ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
    ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', '\''],
    ['⇧', 'z', 'x', 'c', 'v', 'b', 'n', 'm', 'a_b', '⌫'],
    ['123', '!', '%', '[', ']', '{', '}', '~', ',', 'func']
  ]
}

const currentKeyboard = computed(() => keyboardLayouts[keyboardMode.value])

// 计算功能弹窗的样式
const functionModalStyle = computed(() => {
  return {
    position: 'fixed',
    left: `${functionModalPosition.value.x}px`,
    top: `${functionModalPosition.value.y}px`,
    zIndex: 2001
  }
})

function getKeyDisplay(key) {
  const displayMap = {
    'abc': 'abc',
    '123': '123',
    'aᵇ': 'aᵇ',
    'd²': 'd²',
    '|a|': '|a|',
    '♪': '♪',
    'a_b': 'a_b',
    '⇧': '⇧'
  }
  
  // 如果是字母且Shift键激活，则返回大写字母
  if (shiftActive.value && key.length === 1 && key >= 'a' && key <= 'z') {
    return key.toUpperCase()
  }
  
  return displayMap[key] || key
}

function getKeyClass(key) {
  if (['abc', '123'].includes(key)) return 'switch-key'
  if (['7', '8', '9', '4', '5', '6', '1', '2', '3', '0'].includes(key)) return 'number-key'
  if (['+', '−', '×', '÷', '=', '!', '%', '[', ']', '{', '}', '~', '-', ',', '\''].includes(key)) return 'operator-key'
  if (key === '⌫') return 'backspace-key'
  if (key === '↵') return 'enter-key'
  return ''
}

function handleKey(key) {
  if (key === 'hide' || key === '收起') {
    showKeyboard.value = false
    return
  }
  if (key === '123') {
    keyboardMode.value = '123'
    shiftActive.value = false // 切换到数字键盘时重置Shift状态
    return
  }
  if (key === 'abc') {
    keyboardMode.value = 'abc'
    return
  }
  if (key === '⌫') {
    backspace()
    return
  }
  if (key === '↵') {
    enter()
    return
  }
  if (key === '⇧') {
    // 切换Shift键状态
    shiftActive.value = !shiftActive.value
    return
  }
  if (key === 'func') {
    // 获取功能按钮的位置
    const actualFuncButton = functionButton.value
    if (actualFuncButton) {
      const rect = actualFuncButton.getBoundingClientRect()

      // 计算弹窗位置（按钮上方）
      const modalWidth = 300
      const modalHeight = 300
      const buttonCenterX = rect.left + rect.width / 2
      const buttonTop = rect.top

      // 设置弹窗位置
      functionModalPosition.value = {
        x: buttonCenterX - modalWidth / 2,
        y: buttonTop - modalHeight - 10
      }

      // 显示弹窗
      showFunctionModal.value = true
    }
    return
  }
  // 其它插入逻辑
  insertKey(key)
}

// 关闭功能弹窗
function closeFunctionModal() {
  showFunctionModal.value = false
}

// 插入数学函数
function insertFunction(funcName) {
  let val = funcName + '()';

  const input = document.querySelectorAll('.equation-item input')[activeInputIdx.value]
  if (input) {
    const start = input.selectionStart
    const end = input.selectionEnd
    const oldVal = equations[activeInputIdx.value].expr
    equations[activeInputIdx.value].expr =
      oldVal.slice(0, start) + val + oldVal.slice(end)
    nextTick(() => {
      input.focus()
      // 将光标放在括号内
      input.setSelectionRange(start + val.length - 1, start + val.length - 1)
    })
  }

  // 关闭函数弹窗
  closeFunctionModal()
}

function insertKey(key) {
  let val = key
  // 数字键盘特殊字符映射
  if (key === 'd²') val = '^2'
  if (key === 'aᵇ') val = '^'
  if (key === '÷') val = '/'
  if (key === '×') val = '*'
  if (key === '−') val = '-'
  if (key === '≤') val = '<='
  if (key === '≥') val = '>='
  if (key === '|a|') val = 'abs()'
  if (key === 'π') val = 'pi'
  if (key === '√') val = 'sqrt()'
  
  // 字母键盘特殊字符映射
  if (key === 'a_b') val = '_'
  if (key === '⇧' || key === 'ABC' || key === '♪') val = ''
  if (key === 'θ') val = 'theta'
  
  // 处理字母大小写
  if (key.length === 1 && key >= 'a' && key <= 'z' && shiftActive.value) {
    val = key.toUpperCase()
  }
  
  const input = document.querySelectorAll('.equation-item input')[activeInputIdx.value]
  if (input) {
    const start = input.selectionStart
    const end = input.selectionEnd
    const oldVal = equations[activeInputIdx.value].expr
    equations[activeInputIdx.value].expr =
      oldVal.slice(0, start) + val + oldVal.slice(end)
    nextTick(() => {
      input.focus()
      input.setSelectionRange(start + val.length, start + val.length)
    })
  }
}

function backspace() {
  const input = document.querySelectorAll('.equation-item input')[activeInputIdx.value]
  if (input) {
    const start = input.selectionStart
    const end = input.selectionEnd
    const oldVal = equations[activeInputIdx.value].expr
    if (start > 0) {
      equations[activeInputIdx.value].expr =
        oldVal.slice(0, start - 1) + oldVal.slice(end)
      nextTick(() => {
        input.focus()
        input.setSelectionRange(start - 1, start - 1)
      })
    }
  }
}

function enter() {
  addEquation()
}

function setActiveInput(idx) {
  activeInputIdx.value = idx
}

function addEquation() {
  equations.push({ id: Date.now() + Math.random(), expr: '' })
  nextTick(() => {
    setActiveInput(equations.length - 1)
  })
}

function removeEquation(idx) {
  equations.splice(idx, 1)
  if (activeInputIdx.value >= equations.length) {
    activeInputIdx.value = equations.length - 1
  }
}

function drawPlot() {
  if (!plotRef.value) return;
  plotRef.value.innerHTML = '';
  const validEquations = equations
    .map(e => e.expr.trim())
    .filter(e => e.length > 0)
    .map(expr => expr.replace(/^y\s*=\s*/, ''))
    .filter(expr => expr.length > 0)
  if (validEquations.length === 0) return;
  try {
    functionPlot({
      target: plotRef.value,
      width: plotRef.value.offsetWidth || 800,
      height: plotRef.value.offsetHeight || 600,
      yAxis: { domain: [-50, 50] },
      xAxis: { domain: [-50, 50] },
      grid: true,
      data: validEquations.map(expr => ({
        fn: expr,
        graphType: 'polyline'
      }))
    })
  } catch (e) {
    console.error('绘图失败', e)
  }
}

function waitForPlotReadyAndDraw() {
  if (plotRef.value && plotRef.value.offsetWidth > 0 && plotRef.value.offsetHeight > 0) {
    // 初始加载时不自动绘制，等待用户点击"展示"按钮
    drawPlot()
  } else {
    requestAnimationFrame(waitForPlotReadyAndDraw)
  }
}

onMounted(() => {
  waitForPlotReadyAndDraw()
})
</script>

<style scoped>
.math-calc-root {
  position: relative;
  width: 100%;
  height: 100vh;
  background: #f5f5f5;
  overflow: hidden;
}

.plot-area {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
}

.plot-canvas {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 8px;
  box-sizing: border-box;
}

.keyboard-toggle-btn {
  position: fixed;
  right: 20px;
  bottom: 20px;
  background: #4285f4;
  color: #fff;
  border: none;
  border-radius: 24px;
  padding: 12px 24px;
  font-size: 16px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
  z-index: 10;
  transition: all 0.3s ease;
}

.keyboard-toggle-btn:hover {
  background: #3367d6;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(66, 133, 244, 0.4);
}

.keyboard-modal {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  height: 33.33vh;
  background: rgba(0,0,0,0.2);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.keyboard-modal-content {
  width: 100vw;
  height: 33.33vh;
  background: #fff;
  display: flex;
  flex-direction: row;
  box-shadow: 0 -4px 20px rgba(0,0,0,0.15);
}

.equation-list-modal {
  width: 33.33%;
  background: #fafafa;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  position: relative;
}

.equation-list-content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-y: auto;
  max-height: calc(33.33vh - 60px);
}

.equation-list-footer {
  height: 70px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  background: #f0f0f0;
  border-top: 1px solid #e0e0e0;
}

.equation-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.equation-item input {
  flex: 1;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: #fff;
}

.equation-item input:focus {
  outline: none;
  border-color: #4285f4;
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}

.delete-btn {
  background: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s;
}

.delete-btn:hover {
  background: #d32f2f;
}

.add-btn {
  background: #4285f4;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 10px 0;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s;
}

.add-btn:hover {
  background: #3367d6;
}

.math-keyboard-modal {
  flex: 1;
  background: #f8f9fa;
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  grid-template-rows: repeat(4, 1fr);
  gap: 6px;
}

.math-keyboard-modal.abc-keyboard {
  grid-template-columns: repeat(10, 1fr);
  grid-template-rows: repeat(4, 1fr);
}

.math-keyboard-modal.abc-keyboard button {
  grid-column: auto;
  grid-row: auto;
}

.keyboard-container {
  display: contents;
}

.keyboard-row {
  display: contents;
}

.key-btn {
  font-size: 16px;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  min-height: 40px;
}

.key-btn.backspace-key {
  background: #ffebee;
  color: #c62828;
  font-weight: 600;
}

.backspace-vertical {
  grid-row: 3 / 5;
  grid-column: 4;
}

.key-btn:hover {
  background: #f0f0f0;
  border-color: #bbb;
}

.key-btn:active {
  background: #e0e0e0;
  transform: scale(0.95);
}

.key-btn.number-key {
  background: #e3f2fd;
  color: #1976d2;
  font-weight: 600;
}

.key-btn.number-key:hover {
  background: #bbdefb;
}

.key-btn.operator-key {
  background: #fff3e0;
  color: #f57c00;
  font-weight: 600;
}

.key-btn.operator-key:hover {
  background: #ffe0b2;
}

.key-btn.switch-key {
  background: #f3e5f5;
  color: #7b1fa2;
  font-weight: 600;
}

.key-btn.switch-key:hover {
  background: #e1bee7;
}

.key-btn.enter-key {
  background: #e8f5e9;
  color: #2e7d32;
  font-weight: 600;
}

.key-btn.enter-key:hover {
  background: #c8e6c9;
}

.math-keyboard-modal.abc-keyboard .backspace-vertical {
  grid-row: auto;
  grid-column: auto;
}

.key-btn.shift-active {
  background: #c5cae9;
  color: #3f51b5;
  border-color: #7986cb;
  box-shadow: inset 0 0 5px rgba(63, 81, 181, 0.3);
}

.display-btn {
  background: #4caf50;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 30px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  min-width: 120px;
}

.display-btn:hover {
  background: #43a047;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.hide-btn {
  background: #ff9800;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 30px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  min-width: 120px;
}

.hide-btn:hover {
  background: #f57c00;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 功能按钮样式 */
.key-btn.function-key {
  background: #e8f5e9;
  color: #2e7d32;
  font-weight: 600;
}

.key-btn.function-key:hover {
  background: #c8e6c9;
}

/* 自定义功能弹窗样式 */
.function-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: transparent;
  z-index: 2000;
  pointer-events: auto;
}

.function-modal-animated {
  position: fixed;
  z-index: 2001;
}

/* Vue transition 动画 */
.function-modal-enter-active,
.function-modal-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.function-modal-enter-from {
  transform: translateX(-100px);
  opacity: 0;
}

.function-modal-enter-to {
  transform: translateX(0);
  opacity: 1;
}

.function-modal-leave-from {
  transform: translateX(0);
  opacity: 1;
}

.function-modal-leave-to {
  transform: translateX(-100px);
  opacity: 0;
}

/* 函数弹窗样式 */
.function-modal {
  position: fixed; /* 使用fixed定位，相对于视口 */
  width: auto;
  height: auto;
  background: transparent; /* 透明背景 */
  z-index: 2000;
  display: flex;
  flex-direction: column;
  pointer-events: none; /* 允许点击穿透背景 */
  transform-origin: bottom center; /* 设置变换原点为底部中心 */
}

.function-modal-content {
  pointer-events: auto; /* 内容区域可以点击 */
  width: 300px;
  max-height: 300px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin-bottom: 10px; /* 与按钮保持一定距离 */
}

.function-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.function-modal-header h3 {
  margin: 0;
  font-size: 14px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.close-btn:hover {
  color: #333;
}

.function-modal-body {
  padding: 10px;
  overflow-y: auto;
  max-height: 250px;
}

.function-group {
  margin-bottom: 12px;
}

.function-group h4 {
  margin: 0 0 6px 0;
  font-size: 13px;
  color: #555;
  border-bottom: 1px solid #eee;
  padding-bottom: 4px;
}

.function-buttons {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 6px;
}

.function-btn {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 6px 0;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.function-btn:hover {
  background: #e0e0e0;
  border-color: #bbb;
}

.function-btn:active {
  background: #d5d5d5;
  transform: scale(0.98);
}
</style>