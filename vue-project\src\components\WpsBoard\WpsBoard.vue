<template>
    <div id="wps-display-container" ref="wpsRef" v-show="currentFileItem && !minimized" class="container"></div>
    <Vue3DraggableResizable id="wps-toolbar" v-if="currentFileItem" class="draggable" :init-w="toolW" :init-h="toolH"
        v-model:x="toolX" v-model:y="toolY" v-model:w="toolW" v-model:h="toolH" :draggable="true" :resizable="false"
        v-model:active="active" @drag-end="handleDragEnd">
        <div class="drag-content">
            <label style="color: white; font-size: 12px;">文档</label>
            <div style="width: 12px;"></div>
            <ImageButton @click="setMinimize" :title="stateTitle()" :image="getImage()" />
            <div style="width: 20px;"></div>
            <!-- <ImageButton @click="officeStore.setFullScreen()" :title="getIsFullScreenTitle()" :image="getIsFullScreenImage()" />
            <div style="width: 20px;"></div> -->
            <ImageButton @click="closeFile" title="关闭文档" image="/img/desktop/dekstop_quite.svg" />
            <div style="width: 20px;"></div>
        </div>
    </Vue3DraggableResizable>
    <el-dialog :close-on-click-modal="false" v-model="audioDialogVisible" :title="audioDisplayName" :show-close="false"
        width="60%" modal-class="bottom-dialog">
        <div>
            <audio ref="audioRef" class="cloud-disk-video" controls playsinline autoplay :src="audioFileUrl"></audio>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" @click="audioClose">
                    关闭
                </el-button>
            </div>
        </template>
    </el-dialog>
    <div class="link-container" v-if="showLink">
        <div class="link-body">
            <iframe :src=linkWebUrl width="100%" height="100%" style="border: none"></iframe>
        </div>
        <div class="back-content">
            <div class="back-area" @click="showLink = false">
                <img class="back-icon" src="@/assets/img/quit2.png" alt="">
                <span class="back-title">退出</span>
            </div>
        </div>
    </div>
</template>


<script setup>
import { useClassroomUIStore } from '@/stores/classroom_ui_store';
import { useOfficeFilesStore } from '@/stores/office_files_store';
import { storeToRefs } from 'pinia';
import { nextTick, onBeforeUnmount, ref, watch, onMounted } from 'vue';

import Vue3DraggableResizable from 'vue3-draggable-resizable'
//需引入默认样式
import 'vue3-draggable-resizable/dist/Vue3DraggableResizable.css'
import ImageButton from '../DesktopTool/ImageButton.vue';
import { UIFrames, ViewStatus } from '@/classroom/frame_enums';
import { useDrawBoardStore } from '@/stores/drawboard_store';
import OpenSDK from '@/utils/open-jssdk-v0.0.13.es'
import { useVideoPlayStore } from '@/stores/video_play_store';
import { getZIndex } from '@/components/baseComponents/RBPZIndex.js'
import { remoteControl } from '@/remote_control/remote_control';

const videoPlayStore = useVideoPlayStore()
const { fileUrl, displayName } = storeToRefs(videoPlayStore)

const audioRef = ref(null)

const officeStore = useOfficeFilesStore()
const { currentFileItem, cacheList, minimized, prevPage, nextPage, 
    isFull, count, wpsRef, sdk, isFullScreen, officeType,remoteFlag } = storeToRefs(officeStore)

const classroomUIStore = useClassroomUIStore()
const { mainContentHeight, mainContentTopSpace, showVideoPlayView } = storeToRefs(classroomUIStore)


watch(remoteFlag, (flag) => { 
    if(officeStore.remoteStatus === ViewStatus.closed){
        closeFile()
    }else{
        setMinimize()
    }
})
// const wpsRef = ref(null)

const active = ref(false)

// const toolW = ref(300)
const toolW = ref(230)
const toolH = ref(70)

const toolX = ref(window.innerWidth / 2 - 100)
const toolY = ref(window.innerHeight - UIFrames.tabbarHeight - 70 - 8)

//媒体音频弹窗播放
const audioDialogVisible = ref(false)
const audioDisplayName = ref('')
const audioFileUrl = ref('')

//网页
const showLink = ref(false)
const linkWebUrl = ref('')

onMounted(() => {
    toolX.value = window.innerWidth / 2 - 100
    toolY.value = window.innerHeight - UIFrames.tabbarHeight - 70 - 8
})

watch(count, (newValue, oldValue) => {
    closeFile()
})

watch(nextPage, (newValue, oldValue) => {
    setNextSlide()
})

watch(prevPage, (newValue, oldValue) => {
    setPrevSlide()
})

watch(currentFileItem, (newValue, oldValue) => {
    nextTick(async () => {
        if (sdk.value) {
            sdk.value.destroy()
            sdk.value = null
        }
        if (newValue == null) {
            return
        }
        // 拦截外链跳转函数
        const onHyperLinkOpen = ({
            linkUrl, // 跳转 url
        }) => {
            if (isVideoPath(linkUrl)) {
                fileUrl.value = linkUrl
                displayName.value = linkUrl.split('/').at(-1)
                showVideoPlayView.value = true
            } else if (isAudioPath(linkUrl)) {
                audioFileUrl.value = linkUrl
                audioDisplayName.value = linkUrl.split('/').at(-1)
                audioDialogVisible.value = true
                if (audioRef.value) {
                    audioRef.value.load()
                    //判断是否在播放中，在播放中则停止播放
                    audioRef.value.play().catch((err) => {
                        audioRef.value.play()
                    })
                }
            } else {
                linkWebUrl.value = linkUrl
                showLink.value = true
            }
        };
        const jssdk = OpenSDK.config({
            url: newValue.orignUrl,
            mount: document.querySelector('#wps-display-container'),
            onHyperLinkOpen,
            // commonOptions: {
            //     isShowTopArea: false,
            // },
            // pptOptions: {
            //     isShowRemarkView: false, // 是否显示备注视图
            // }
        })

        currentFileItem.sdk = jssdk

        sdk.value = jssdk

        toolX.value = window.innerWidth / 2 - 100
        toolY.value = window.innerHeight - UIFrames.tabbarHeight - 70 - 8

        addEventListener()
    })
})

function isVideoPath(url) {
    const videoRegex = /\.(mp4|mov|avi|wmv|flv|mkv)$/i;
    return videoRegex.test(url);
}

function isAudioPath(path) {
    const audioRegex = /\.(mp3|wav|flac|aac)$/i;
    return audioRegex.test(path);
}

function onPageChange(data) {

}

async function onFileDidOpen(data) {

    if(data.result === "Fail") {
        return
    }

    if (sdk.value) {
        await sdk.value.ready()
    }
    officeType.value = data.fileInfo.officeType
    if (data.fileInfo.officeType === "p") {
        if (sdk.value) {
            const app = sdk.value.Application
            await app.ActivePresentation.SlideShowWindow.View.GotoSlide(1)

            if (isFull.value) {
                officeStore.enterFullScreen()
            }

            // 监听退出幻灯片全屏播放
            app.Sub.SlideShowEnd = async () => {
                officeStore.exitFullScreen()
            }

            // 监听进入幻灯片全屏播放
            app.Sub.SlideShowBegin = async () => {
                officeStore.enterFullScreen()
            }
        }

    } else {
        if (isFull.value) {
            // 全屏显示
            officeStore.setZIndexOut()
        }
    }
}

function addEventListener() {
    if (sdk.value) {

        sdk.value.ApiEvent.AddApiEventListener("SlideSelectionChanged", onPageChange)
        sdk.value.ApiEvent.AddApiEventListener('fileOpen', onFileDidOpen)
        sdk.value.ApiEvent.AddApiEventListener('fullscreenchange', onFullscreenchange)
    }
}

function removeEventListener() {
    if (sdk.value) {
        sdk.value.ApiEvent.RemoveApiEventListener("SlideSelectionChanged", onPageChange)
        sdk.value.ApiEvent.RemoveApiEventListener('fileOpen', onFileDidOpen)
        sdk.value.ApiEvent.RemoveApiEventListener('fullscreenchange', onFullscreenchange)
    }
}

async function onFullscreenchange(data) {
    if (isFullScreen.value) {
        if (data.status == 1) {
            officeStore.enterFullScreen()
        } else {
            officeStore.exitFullScreen()
        }
    } else {
        isFullScreen.value = true
    }
}

onBeforeUnmount(() => {
    if (sdk.value) {
        removeEventListener()
        sdk.value.destroy()
        sdk.value = null

        currentFileItem.sdk = null
    }
})

function stateTitle() {
    if (minimized.value) {
        return "显示文档"
    }
    return "隐藏文档"
}

function getImage() {
    if (minimized.value) {
        return "/img/office/office_file_hide.png"
    }
    return "/img/office/office_file_show.png"
}

function setMinimize() {
    minimized.value = !minimized.value
    if (!minimized.value) {
        const drawBoardStore = useDrawBoardStore()
        drawBoardStore.minimizePaperPenView()
    }
    if(minimized.value) {
        officeStore.setZIndexIn()
    }else {
        officeStore.setZIndexOut()
    }
    remoteControl.handleInteractState()
}


async function closeFile() {
    const div = document.querySelector('#wps-display-container')
    div.innerHTML = ''
    isFull.value = false
    officeStore.setZIndexIn()
    removeEventListener()
    officeStore.clear()
    officeType.value = ''
    remoteControl.handleInteractState()
}


function handleDragEnd() {
    if (toolX.value < 0) {
        toolX.value = 0; // 如果超出左边界，重置为 0
    } else if (toolX.value + toolW.value > window.innerWidth) {
        toolX.value = window.innerWidth - toolW.value; // 如果超出右边界，重置到屏幕右侧边界内
    }
    if (toolY.value < 0) {
        toolY.value = 0; // 如果超出上边界，重置为 0
    } else if (toolY.value + toolH.value > window.innerHeight) {
        toolY.value = window.innerHeight - toolH.value; // 如果超出下边界，重置到屏幕下侧边界内
    }
}

function getIsFullScreenImage() {
    if (!isFull.value) {
        return "/img/full_screen.png"
    }
    return "/img/cancel_full_screen.png"
}

function getIsFullScreenTitle() {
    if (!isFull.value) {
        return "全屏"
    }
    return "退出全屏"
}

async function setNextSlide() {
    const app = sdk.value.Application
    await app.ActivePresentation.SlideShowWindow.View.GotoNextClick()
}
async function setPrevSlide() {
    const app = sdk.value.Application
    await app.ActivePresentation.SlideShowWindow.View.GotoPreClick()
}
function audioClose() {
    audioRef.value.pause()
    audioDialogVisible.value = false
}
</script>
<style>
.bottom-dialog .el-dialog {
    top: auto;
    bottom: 10vh;
    left: 20vw;
    position: absolute !important;
    margin-bottom: 0px;
    /* 确保水平居中 */
}
</style>

<style scoped lang="scss">
.container {
    position: absolute;
    width: 100%;
    height: v-bind("mainContentHeight + 'px'");
    top: v-bind("mainContentTopSpace + 'px'");
    z-index: 1;
    /* background-color: black; */
}

.draggable {
    position: absolute;
    box-shadow: none;
    z-index: 2001;
    user-select: none;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 70px;
    border: 1px solid #000;
    width: v-bind("toolW + 'px'");
    height: v-bind("toolH + 'px'");
}

.drag-content {
    width: v-bind("toolW + 'px'");
    height: v-bind("toolH + 'px'");
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    align-content: center;
}

.cloud-disk-video {
    width: 100%;
    object-fit: contain;
}

.link-container {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: black;
    z-index: 3000;
    display: flex;
    flex-direction: column;

    .link-body {
        flex: 1;
    }

    .back-content {
        display: flex;
        height: 40px;
        background-color: #000;
        align-items: center;
        justify-content: flex-end;
        padding: 12px 16px;

        .back-area {
            height: 24px;
            padding: 8px 16px;
            background-color: white;
            opacity: 0.6;
            border-radius: 20px;
            display: flex;
            align-items: center;
            cursor: pointer;

            .back-icon {
                height: 16px;
                object-fit: contain;
                margin-right: 8px;

            }

            .back-title {
                font-size: 14px;
                color: #2e4a66;
            }
        }
    }
}
</style>