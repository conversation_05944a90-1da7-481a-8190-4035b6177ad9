import * as THREE from 'three';
import { MeshLineGeometry, MeshLineMaterial } from 'meshline';
import { Line2 } from 'three/examples/jsm/lines/Line2.js';
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial.js';
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry.js';
import { PainterOrder, PenStyle } from '@/drawboard/draw_enums';
import { useClassroomUIStore } from '@/stores/classroom_ui_store';
import { MathGeometry } from '@/drawboard/math_geometry/math_root_view';
import { useDrawBoardStore } from '@/stores/drawboard_store';

export class BlackboardDeserializer {

    deserialize(application, json) {
        //存储子视图line
        this.tempLineCache = {}
        const drawBoardStore = useDrawBoardStore()
        if (drawBoardStore.blackBoardPainter) {
            drawBoardStore.blackBoardPainter.clearAllShape()
            drawBoardStore.blackBoardPainter.clearAllImage()
            drawBoardStore.blackBoardPainter.clearAllTeachPlans()
            drawBoardStore.blackBoardPainter.clearAllGrids()
            drawBoardStore.clearDrawBoard()

        }
        const classroomUIStore = useClassroomUIStore()
        classroomUIStore.realTimeVideo = {
            count:0,
            list:[],
            show:false
        }
        application.lastDrawX = json.lastDrawX??0
        this.deserializeLines(application, json.lineGroupId, json.lines)
        this.deserializeTeachPlans(application, json.teachPlans)
        this.deserializeImages(application, json.imageData)
        this.deserializeUploadImages(application, json.uploadImageData)
        this.deserializeUploadVideo(json.uploadVideoData)
        this.deserializeMathViews(application, json.mathViewData)
        this.deserializeGridViews(application, json.gridViewData)
        application.animate()
    }

    deserializeMathViews(application, mathViewData) {
        let mathView = application.mathRootView

        if (mathViewData && mathViewData.length && mathView) {

            for (let data of mathViewData) {
                let view = mathView.addPlaneViewWithType(data.type, data.position, data.size,data.color)

                if (data.aPoint) {
                    view.aPoint = data.aPoint
                }
                if (data.bPoint) {
                    view.bPoint = data.bPoint
                }
                if (data.cPoint) {
                    view.cPoint = data.cPoint
                }
                if (data.dPoint) {
                    view.dPoint = data.dPoint
                }

                switch (data.type) {
                    case MathGeometry.Line: {
                        view.updateLine()
                        break;
                    }
                    case MathGeometry.Angle:
                    case MathGeometry.Triangle: {
                        view.updateTriangle()
                        break;
                    }
                    case MathGeometry.Circle:
                    case MathGeometry.Oval: {
                        view.updateCircle()
                        break;
                    }
                    case MathGeometry.Square:
                    case MathGeometry.Rectangle:
                    case MathGeometry.Diamond:
                    case MathGeometry.Parallel:
                    case MathGeometry.Trapezoid: {
                        view.updateVertex()
                        break;
                    }
                    case MathGeometry.cone:
                    case MathGeometry.coneWithLight: {
                        break;
                    }
                    case MathGeometry.cube:
                    case MathGeometry.cubeWithLight:
                    case MathGeometry.cuboid:
                    case MathGeometry.cuboidWithLight: {
                        break;
                    }
                    case MathGeometry.cylinder:
                    case MathGeometry.cylinderWithLight: {
                        break;
                    }
                    case MathGeometry.sphere:
                    case MathGeometry.sphereWithLight: {
                        break;
                    }
                    case MathGeometry.light: {
                        view.position.setZ(data.position.z)
                        break;
                    }
                }
                if (view && this.tempLineCache[data.id]) {
                    for (let line of this.tempLineCache[data.id]) {
                        view.add(line)
                    }
                }
                // if(data.rotation&&view.viewGroup){
                //     view.viewGroup.rotation.x = data.rotation.x
                //     view.viewGroup.rotation.y = data.rotation.y
                //     view.animate()
                // }
            }

        }

    }

    deserializeGridViews(application, gridViewData) {
        let writingView = application.writingHelpView
        if (gridViewData && gridViewData.length && writingView) {
            for (let data of gridViewData) {

                let view = writingView.addPlaneViewWithType(data.type, data.position,data.color)
                if (view && this.tempLineCache[data.id]) {
                    for (let line of this.tempLineCache[data.id]) {
                        view.add(line)
                    }
                }
            }
        }
    }

    deserializeTeachPlans(application, teachPlans) {
        if (teachPlans && teachPlans.length && application.addTeachPlan) {
            for (let plan of teachPlans) {
                let view = application.addTeachPlan(plan.plan, plan.presetX, plan.presetY)
                if (view && this.tempLineCache[plan.id]) {
                    for (let line of this.tempLineCache[plan.id]) {
                        view.add(line)
                    }
                }
            }
        }
    }

    deserializeImages(application, imageData) {
        if (imageData && imageData.length && application.addImage) {
            for (let image of imageData) {
                let url = this.dealLocalUrl(image.imageUrl + "",'images')

                let view = application.addImage(url, image.presetX, image.presetY)
                if (view && this.tempLineCache[image.id]) {
                    for (let line of this.tempLineCache[image.id]) {
                        view.add(line)
                    }
                }
            }
        }
    }

    dealLocalUrl(url,split) {
        if (!url.includes('http')) {
            return ('http://' + window.location.host + `/store/${split}/` + url)
        }
        return url
    }

    deserializeUploadImages(application, uploadImageData) {
        if (uploadImageData && uploadImageData.length && application.addUploadPictures) {
            for (let upload of uploadImageData) {
                let urls = upload.urlData.map(e => {
                    let url = this.dealLocalUrl(e.image,'images')
                    e.image = url
                    return url
                })
                if (urls.length) {
                    let view = application.addUploadPictures(upload.taskId, urls, upload.presetX, upload.presetY)


                    for (let child of upload.urlData) {
                        let imageView = view.getOrGreatImageView(child.image)
                        if (child.imageView && imageView) {

                            imageView.setImageViewData(child.imageView.pos, child.imageView.scale)
                        }
                        if (child && this.tempLineCache[child.id] && imageView) {


                            for (let line of this.tempLineCache[child.id]) {
                                imageView.add(line)
                            }
                            imageView.animate()
                        }
                    }

                }






            }
        }
    }

    deserializeUploadVideo(uploadVideoData) {
        if (uploadVideoData && uploadVideoData.length) {
            const classroomUIStore = useClassroomUIStore()
            for (let data of uploadVideoData) {
                data.url = this.dealLocalUrl(data.url,'videos')
            }
            classroomUIStore.realTimeVideo.list = uploadVideoData

        }
    }

    deserializeLines(application, lineGroupId, lines) {
        if (lines && lines.length) {
            let rootView = application.rootView
            let list = []
            for (let lineData of lines) {
                let line = this.deserializeLine(lineData)
                list.push(line)
                let parentId = null
                if (line && line.parentId) {
                    parentId = line.parentId
                }
                
                if (parentId && parentId === lineGroupId) {
                    
                    rootView.totalDrawGroup.add(line)
                }
                else if (parentId) {
                    if (this.tempLineCache[parentId]) {
                        this.tempLineCache[parentId].push(line)
                    } else {
                        this.tempLineCache[parentId] = [line]
                    }
                    // TODO:: 添加到子视图中
                }
            }
            rootView.totalLineCaches = list
        }

    }

    deserializeLine(data) {
        
        
        if (data.penStyle === PenStyle.Line2) {
            const material = new LineMaterial({
                color: data.color,
                linewidth: data.linewidth
            });

            const geometry = new LineGeometry();
            let points = data.points
            let newPoints = []
            for (let i = 0; i < points.length; i++) {
                newPoints.push(points[i].x, points[i].y, points[i].z)
            }
            geometry.setPositions(newPoints);

            let line = new Line2(geometry, material);
            line.penStyle = PenStyle.Line2;
            line.parentId = data.parentId
            line.cachePoints = data.points;
            line.renderOrder = PainterOrder.teacherPoints
            return line;
        }
        else if (data.penStyle === PenStyle.MeshLine) {
            const material = new MeshLineMaterial({
                color: new THREE.Color(data.color),
                lineWidth: data.linewidth,
                side: THREE.DoubleSide,
                sizeAttenuation: false
            });
            
            const geometry = new MeshLineGeometry();
            let line = new THREE.Mesh(geometry, material);
            let newPoints = []
            for (let item of data.points) {                
                newPoints.push(new THREE.Vector3(item.x,item.y,item.z))
            }
            line.geometry.setPoints(newPoints);
            line.penStyle = PenStyle.MeshLine;
            line.parentId = data.parentId;
            line.cachePoints = newPoints;
            line.renderOrder = PainterOrder.teacherPoints
            return line;
        }

        return null;
    }

}

export const blackboardDeserializer = new BlackboardDeserializer();