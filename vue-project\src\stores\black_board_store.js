import { defineStore } from 'pinia'
import { ref } from 'vue'
import { dbH<PERSON><PERSON>, defaultColor } from '@/utils/db_helper'

export const useBlackBoardStore = defineStore('blackBoard', () => {

    const color = ref(defaultColor)
    const predefineColors = ref([
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577',
    ])

    async function setColor(newColor) {
        color.value = newColor
        await dbHelper.updateBlackBoardColorByTeacherId(newColor)
    }

    function setDefaultColor() {
        setColor(defaultColor)
    }

    async function getColor() {
        color.value = await dbHelper.getBlackBoardColorByTeacherId()
        return color.value
    }

    return {
        color,
        predefineColors,
        setColor,
        getColor,
        setDefaultColor
    }
})