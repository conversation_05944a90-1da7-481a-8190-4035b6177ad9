<template>
    <button class="button">
        <img style="width: 20px;height: 20px;" :src="imagePath">
        <div style="height: 2px;"></div>
        <div style="font-size: 10px; color: white;">{{ title }}</div>
    </button>
</template>


<script>
import { defineComponent } from 'vue'

export default defineComponent({
    props: {
        image: String,
        title: String
    },
    computed: {
        imagePath() {
            return new URL(`${this.image}`, import.meta.url).href
        }
    }

})

</script>

<style scoped>
.button {
    width: 50px;
    height: 50px;
    background-color: transparent;
    border: 1px solid #fff;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    align-content: center;
    /* 垂直方向居中 */
    align-items: center;
    justify-content: center;
    text-align: center;
}
</style>