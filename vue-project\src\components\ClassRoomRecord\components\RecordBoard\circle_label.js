import { BoardView } from "@/drawboard/board_view";
import * as THREE from 'three';
import { PainterOrder } from "@/drawboard/draw_enums";
import { BoardLabel } from "@/drawboard/board_label";

export class CircleLabel extends BoardView {

    constructor(application, pos, size, text, textStyle = { fontSize: 0.012, color: 0xFFFFFF, align: 'center' }, background = 0x25B0FA) {
        super(application, pos, size)
        this.text = text
        this.textStyle = textStyle
        this.background = background
        this.setupUI()



    }


    setupUI() {
        const cirlceGeometry = new THREE.CircleGeometry(this.size.width / 2, 32);  // 半径1，分段数32
        const circleMaterial = new THREE.MeshBasicMaterial({ color: this.background });  // 红色
        const circle = new THREE.Mesh(cirlceGeometry, circleMaterial);
        circle.position.set(0, 0, 0)
        this.add(circle)
        // ansView.renderOrder = PainterOrder.customDisplay
        // ansView.rightRotateBtn.visible = false
        // ansView.leftRotateBtn.visible = false
        let nameLabel = new BoardLabel(
            this.application,
            new THREE.Vector3(0, 0, 0),
            this.size,
            this.text,
            this.textStyle
        )
        nameLabel.renderOrder = PainterOrder.studentName
        this.addSubView(nameLabel)

    }




    dispose() {

        super.dispose()
    }
}