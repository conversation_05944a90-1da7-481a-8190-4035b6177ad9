<template>
    <RBPAlert :zIndex="getZIndex('--interact-z-index')" :showBg="false" title="开始听写" width="60%" @close="closeClick" @click.stop>
        <template v-slot:rbpDiv>
            <div class="body-container">
                <div class="process">
                    <CircleProgress :border-width="getBorderWidth()" :fill-color="getProcessColor()" :size="400"
                        :percent="progressValue"></CircleProgress>
                    <div class="questionNum">
                        <div v-if="currentDown > 0">
                            {{ parseInt((currentDown - 1) / 10) + 1 }}
                        </div>
                        <div v-else>第{{ currentIndex + 1 }}题</div>
                    </div>
                    <div class="tips">
                        {{ currentIndex + 1 }}/{{ selectedItems.length }}
                    </div>
                </div>
            </div>
        </template>
        <template #rbpBtns>
            <div class="footer-container">
                <div class="btn" v-for="(item, index) in btns" :key="index" @click="btnClick(item)">
                    <div class="btn-icon">
                        <img :src="item.selected ? item.iconSelected : item.icon">
                    </div>
                    <div class="btn-text">
                        {{ item.again ? item.titleSelectedAgain : item.selected ? item.titleSelected : item.title }}
                    </div>
                </div>
            </div>
        </template>
    </RBPAlert>
    <RBPAlert :zIndex="getZIndex('--interact-z-index')" width="20%" height="30%" top="60%" v-show="showExplain" @close="closeExplainClick" @click.stop>
        <template v-slot:rbpDiv>
            <div class="alert-content">
                <div class="tips-title">
                    释义
                </div>
                <div class="tips-explains">
                    <div class="explain" v-for="(item, index) in getExplains" :key="index">
                        {{ item.pos }} {{ item.explain }}
                    </div>
                </div>
                <div class="tips-audio" @click="readAudio()">
                    <RBPTrumpetAudioAnimation ref="trumpetRef"></RBPTrumpetAudioAnimation>
                </div>
            </div>
        </template>
    </RBPAlert>
    <RBPAlert :zIndex="getZIndex('--interact-z-index')" width="30%" height="30%" top="60%" v-show="showSubmit" @close="closeSubmitClick" @click.stop>
        <template v-slot:rbpDiv>
            <div class="submit-content">
                <RBPButton :btnText="`${autoSubmitDown}秒后自动提交`" :btnSelected="true" btnType="big" @click="submitClick">
                </RBPButton>
                <div :style="{ height: '20px' }"></div>
                <RBPButton btnText="再等等" btnType="big" @click="againClick">
                </RBPButton>
            </div>
        </template>
    </RBPAlert>
</template>
<script setup>
import { defineProps, ref, reactive, computed, watch, toRefs } from 'vue'
import "vue3-circle-progress/dist/circle-progress.css"
import CircleProgress from "vue3-circle-progress"
import roomUpdater from '@/classroom/classroom_updater.js'
import { useInteractStore } from '@/stores/interact_store'
import { storeToRefs } from 'pinia'
import { useDictationStore } from '@/stores/dictation_store'
import { databaseHelper, RecordType } from '@/classroom/class_flow_helper'
import { UIFrames } from '@/classroom/frame_enums'
import { useClassroomStore } from '@/stores/classroom'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { useTimesStore } from '@/stores/times_store'
import RBPAlert from '@/components/baseComponents/RBPAlert.vue'
import { useRbpAudioStore } from '@/stores/rbp_audio_store'
import { RBPColors, getColor } from '@/components/baseComponents/RBPColors.js'
import RBPButton from '@/components/baseComponents/RBPButton.vue'
import { getZIndex } from '@/components/baseComponents/RBPZIndex.js'
import RBPTrumpetAudioAnimation from '@/components/baseComponents/RBPTrumpetAudioAnimation.vue'

const rbpAudioStore = useRbpAudioStore()
const timesStore = useTimesStore()
const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)
const classroomUIStore = useClassroomUIStore()
const { mainContentTopSpace, showRecordResult, showClassRoomRecord } = storeToRefs(classroomUIStore)
const calcTop = computed(() => {
    return `calc(${UIFrames.interactSelectHeight}px + ${UIFrames.tabbarHeight}px - ${mainContentTopSpace.value}px)`
})
const dictationStore = useDictationStore()
const { selectedItems, currentIndex, currentDown, progressValue, playCount, running, finish, autoSubmitDown } = storeToRefs(dictationStore)
const interactStore = useInteractStore()
const { interactStatus, showInteractSelector } = storeToRefs(interactStore)
const showExplain = ref(false)
const showSubmit = ref(false)
const btns = ref([
    {
        title: '提示',
        icon: '/img/svg/icon_dict_tip.svg'
    },
    {
        title: '跳过',
        icon: '/img/svg/icon_dict_skip.svg',
    },
    {
        title: '暂停',
        icon: '/img/svg/icon_dict_pause.svg',
        titleSelected: '继续',
        titleSelectedAgain: '重听',
        iconSelected: '/img/svg/icon_dict_play.svg',
        selected: false,
        again: false
    },
    {
        title: '重复',
        icon: '/img/svg/icon_dict_replay.svg'
    }
])

const trumpetRef = ref(null)

const audioUrl = ref('')
watch(() => playCount.value, () => {
    // 播放音频
    readAudio()
})
watch(() => finish.value, () => {
    if (finish.value) {
        showSubmit.value = true
        btns.value.forEach((item) => {
            if (item.title == '暂停') {
                item.again = true
                item.selected = true
            }
        })
        dictationStore.autoSubmit()
    }
})
async function submitClick() {
    dictationStore.submit()
}
function againClick() {
    showSubmit.value = false
    dictationStore.waitLonger()
}
function readAudio() {
    let chinesePlay = selectedItems.value[currentIndex.value].value?.chinesePlay
    if (chinesePlay?.length > 0) {
        audioUrl.value = chinesePlay
    } else {
        audioUrl.value = selectedItems.value[currentIndex.value].value?.play
    }
    //播放语音
    rbpAudioStore.trumpetRef = trumpetRef.value
    rbpAudioStore.play(audioUrl.value)
}
const getExplains = computed(() => {
    let explains = selectedItems.value[currentIndex.value]?.value?.explains
    if (explains == undefined) {
        explains = selectedItems.value[currentIndex.value]?.explains
    }
    return explains
})
function getBorderWidth() {
    if (progressValue.value < 10 || progressValue.value > 95) {
        return 0
    } else {
        return 15
    }
}
function getProcessColor() {
    return getColor('--primary-color')
}
function btnClick(item) {
    if (item.title === '暂停') {
        item.selected = !item.selected
        if (item.again) {
            item.again = false
            dictationStore.again()
        } else {
            running.value = !item.selected
        }
    } else if (item.title === '提示') {
        btns.value.forEach((item) => {
            if (item.title == '暂停') {
                item.selected = true
            }
        })
        running.value = false
        showExplain.value = true
    } else if (item.title === '跳过') {
        dictationStore.next()
    } else if (item.title === '重复') {
        btns.value.forEach((item) => {
            if (item.title == '暂停') {
                item.selected = true
            }
        })
        running.value = false
        readAudio()
    }
}
function closeClick() {
    roomUpdater.stopAnswer()
    roomUpdater.didStopInteract()
    showInteractSelector.value = true

}
function closeExplainClick() {
    btns.value.forEach((item) => {
        if (item.title == '暂停') {
            item.selected = false
        }
    })
    running.value = true
    showExplain.value = false
}
function closeSubmitClick() {
    showSubmit.value = false
}
</script>
<style lang="scss" scoped>
.submit-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.alert-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .tips-title {
        font-size: 16px;
        margin: 20px 0 0 20px;
        color: var(--text-color);
    }

    .tips-explains {
        flex: 1;
        overflow-y: scroll;
        scrollbar-width: none;

        &::-webkit-scrollbar {
            display: none;
        }

        .explain {
            margin: 20px;
            color: var(--text-color);
        }
    }

    .tips-audio {
        align-self: center;
        cursor: pointer;
        margin: 20px;

        img {
            width: 48px;
            height: 48px;
        }
    }
}

.body-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .process {
        width: 400px;
        height: 400px;
        position: relative;

        .questionNum {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background-color: var(--primary-color);
            width: 300px;
            height: 300px;
            border-radius: 300px;
            line-height: 300px;
            text-align: center;
            color: white;
            font-size: 30px;
        }

        .tips {
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 40px;
            background-color: var(--primary-color);
            color: white;
            line-height: 40px;
            text-align: center;
            border-radius: 40px;
            font-size: 16px;
        }
    }
}

.footer-container {
    width: 100%;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;

    .btn {
        width: 60px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        margin: 0 10px;

        .btn-icon {

            img {
                width: 48px;
                height: 48px;
            }
        }

        .btn-text {
            font-size: 18px;
            color: var(--text-color);
        }
    }
}
</style>