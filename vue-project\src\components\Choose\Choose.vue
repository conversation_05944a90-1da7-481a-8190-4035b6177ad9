<template>
    <RBPQuestionsAlert v-show="minimize === ViewStatus.normal">
        <template v-slot:rbpTitle>
            <div class="interactHead">
                <div class="interactTitle">
                    {{ interactTitle() }}
                </div>
                <div class="interactTime">
                    {{ interactTime }}
                </div>
            </div>
        </template>
        <template v-slot:rbpDiv>
            <div class="interactContent">
                <div class="responder">
                    <Responser v-if="interact == Interact.responder"></Responser>
                    <div v-else-if="isVoteSelected()">
                        <ChooseOption></ChooseOption>
                    </div>
                    <div v-else>
                        <ChooseSubmitAndTime v-if="rightAnswer == ''"></ChooseSubmitAndTime>
                        <ChooseOption  v-else></ChooseOption>
                    </div>
                </div>
                <div class="rightAnswer" v-if="interact != Interact.responder && !isVoteSelected()">
                    <div class="rithtAnswerTitle">
                        请选择正确答案，停止作答
                    </div>
                    <RBPQuestionsAnswer  :interact="interact" :optionCol="optionCol" :chooseOptionList="chooseOptionList"
                        @chooseSelectAnswerClick="chooseSelectAnswerClick">
                    </RBPQuestionsAnswer>
                </div>
                <RBPLine marginTop="20px" width="80%"></RBPLine>
                <div class="score" v-if="interact != Interact.responder && !isVoteSelected()">
                    <div v-if="loginInstance.evaluation">
                        <div class="eva-correct">
                            <RBPAddScoreEva :item="item" v-for="(item, index) in fixedCorrectList" :key="index"
                                @evaluationItemClick="evaluationItemClick(item)"></RBPAddScoreEva>
                        </div>
                        <div class="eva-correct">
                            <RBPAddScoreEva :item="item" v-for="(item, index) in fixedErrorList" :key="index"
                                @evaluationItemClick="evaluationItemClick(item)"></RBPAddScoreEva>
                        </div>
                    </div>
                    <div v-else>
                        <div class="correct">
                            <RBPAddScore score="+1" @click="addScoreClick('+1', 'success')"></RBPAddScore>
                            <RBPAddScore score="+3" @click="addScoreClick('+3', 'success')"></RBPAddScore>
                            <RBPAddScore score="+5" @click="addScoreClick('+5', 'success')"></RBPAddScore>
                        </div>
                        <div class="error">
                            <RBPAddScore score="-1" @click="addScoreClick('-1', 'error')"></RBPAddScore>
                            <RBPAddScore score="-3" @click="addScoreClick('-3', 'error')"></RBPAddScore>
                            <RBPAddScore score="-5" @click="addScoreClick('-5', 'error')"></RBPAddScore>
                        </div>
                    </div>
                </div>
                <div class="back-interact" v-if="showDesktop">桌面返回互动</div>
            </div>
        </template>
        <template v-slot:rbpBtns>
            <div class="interactBtns">
                <RBPButton :btnText="testQuestionInfo?'公布答案':'再次发起'" :btnSelected="true" btnType="big" @click="againStartInteractClick">
                </RBPButton>
                <div :style="{ height: '12px' }"></div>
                <RBPButton btnText="结束互动" :backgroundColor="getColor('--secondary-anti-color')"
                    :color="getColor('--error-color')" btnType="big" @click="chooseStopInteractClick"></RBPButton>
            </div>
        </template>
    </RBPQuestionsAlert>
    <MiniMize></MiniMize>
    <MutilQuestionsExplainAlert></MutilQuestionsExplainAlert>
    <ChooseOptionStatistics></ChooseOptionStatistics>
</template>


<script setup>

import { ref, computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useClassroomStore } from '@/stores/classroom.js'
import Responser from '@/components/Choose/ChooseResponser.vue'
import { useAnswersStore } from '@/stores/answers_store.js'
import roomUpdater from '@/classroom/classroom_updater.js'
import { Interact } from '@/classroom/interact_enums.js'
import { useInteractStore } from '@/stores/interact_store'
import { DisplayDirection, ViewStatus } from '@/classroom/frame_enums'
import MiniMize from '@/components/Minimize/MiniMize.vue'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { useTimesStore } from '@/stores/times_store'
import MutilQuestionsExplainAlert from '@/components/MultiQuestions/MutilQuestionsExplainAlert.vue'
import ChooseSubmitAndTime from './ChooseSubmitAndTime.vue'
import ChooseOption from './ChooseOption.vue'
import ChooseOptionStatistics from './ChooseOptionStatistics.vue'
import { useEvaluationScoreStore } from '@/stores/evaluation_score_store'
import { loginInstance } from '@/login_instance/login_instance'
import { useExplainAlertStore } from '@/stores/explain_alert_store'
import { useScoreAudioStore } from '@/stores/score_audio_store'
import { chooseSelectAnswerClick, chooseStopInteractClick } from '@/remote_control/select_option_common'
import { RBPColors, getColor } from '@/components/baseComponents/RBPColors.js'
import RBPAddScore from '@/components/baseComponents/RBPAddScore.vue'
import RBPButton from '@/components/baseComponents/RBPButton.vue'
import RBPAddScoreEva from '@/components/baseComponents/RBPAddScoreEva.vue'
import RBPQuestionsAlert from '@/components/baseComponents/RBPQuestionsAlert.vue'
import RBPLine from '@/components/baseComponents/RBPLine.vue'
import RBPQuestionsAnswer from '@/components/baseComponents/RBPQuestionsAnswer.vue'
import { useDesktopStore } from '@/stores/desktop_store'
import { ElLoading } from 'element-plus'

const scoreAudioStore = useScoreAudioStore()
const explainAlertStore = useExplainAlertStore()
const evaluationScoreStore = useEvaluationScoreStore()
const { fixedCorrectList, fixedErrorList } = storeToRefs(evaluationScoreStore)
const timesStore = useTimesStore()
const { interactTime } = storeToRefs(timesStore)
const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)
const classroomUIStore = useClassroomUIStore()
const { displayDirection, mainContentHeight, mainContentTopSpace } = storeToRefs(classroomUIStore)
const answerStore = useAnswersStore()
const { responseNumber, studentAnswers, rightAnswer, chooseOptionList } = storeToRefs(answerStore)
const interactStore = useInteractStore()
const { interact, minimize,testQuestionInfo } = storeToRefs(interactStore)

const desktopStore = useDesktopStore()
const { showDesktop } = storeToRefs(desktopStore)


const calcTop = computed(() => {
    // return `calc(${UIFrames.timeHeight}px + ${mainContentTopSpace.value}px)`
    return `calc(20px + ${mainContentTopSpace.value}px)`
})
const contentHeight = computed(() => {
    // return `calc(${mainContentHeight.value}px - ${UIFrames.timeHeight}px)`
    return `calc(${mainContentHeight.value}px - 20px)`
})
function isVoteSelected() {
    if (interact.value == Interact.vote) {
        return true
    } else {
        return false
    }
}

onMounted(() => {
    if (interact.value == Interact.trueFalse) {
        chooseOptionList.value = [
            {
                option: '对',
                selected: false
            },
            {
                option: '错',
                selected: false
            }
        ]
    } else {
        chooseOptionList.value = [
            {
                option: 'A',
                selected: false
            },
            {
                option: 'B',
                selected: false
            },
            {
                option: 'C',
                selected: false
            },
            {
                option: 'D',
                selected: false
            },
            {
                option: 'E',
                selected: false
            },
            {
                option: 'F',
                selected: false
            }
        ]
    }
})
function interactTitle() {
    switch (interact.value) {
        case Interact.multiChoice:
            return "多选题"
        case Interact.singleChoice:
            return "单选题"
        case Interact.trueFalse:
            return "判断题"
        case Interact.responder:
            return "抢答题"
        case Interact.vote:
            return "投票"
    }
    return ""
}

const optionCol = computed(() => {
    if (interact.value == Interact.trueFalse) {
        return 2
    } else {
        return 3
    }
})

const againInteract = ref(Interact.none)
async function againStartInteractClick() {
    if(testQuestionInfo.value){
        let options = testQuestionInfo.value.options
        if(options&&options.length){
            if(interact.value == Interact.multiChoice){
                const answerStore = useAnswersStore()
                answerStore.chooseOptionList.forEach((item) => {
                    item.selected = false
                })
            }
            options.forEach(e => {
                chooseSelectAnswerClick(e)
            });
        }
        
        return
    }
    let loading = ElLoading.service({ background: 'transparent' })
    responseNumber.value = []
    againInteract.value = interact.value
    await roomUpdater.stopInteract(true)
    loading.close()
    await roomUpdater.startInteract(againInteract.value)
}
async function evaluationItemClick(item) {
    if (rightAnswer.value == '') {
        explainAlertStore.show('请选择正确答案', RBPColors.colorC)
        return
    }
    if (rightAnswer.value == '对') {
        rightAnswer.value = 'RIGHT'
    }
    if (rightAnswer.value == '错') {
        rightAnswer.value = 'WRONG'
    }
    let stuList = []
    let scoreNum = parseInt(item.evaluationItemScore)
    if (scoreNum > 0) {
        stuList = selectOptStudentList()
    } else if (scoreNum < 0) {
        stuList = unSelectOptStudentList()
    }

    if (stuList.length == 0) {
        if (parseInt(item.evaluationItemScore) < 0) {
            explainAlertStore.show('当前题目没有作答错误的学生', RBPColors.colorC)
        } else if (parseInt(item.evaluationItemScore) > 0) {
            explainAlertStore.show('当前题目没有作答正确的学生', RBPColors.colorC)
        }
        return
    }

    explainAlertStore.show(item.evaluationItemScore + '分', parseInt(item.evaluationItemScore) > 0 ? RBPColors.colorD : RBPColors.colorC)
    stuList.forEach(stu => {
        stu.selected = true
    })
    let success = roomUpdater.studentsAddScoreTypeTwo(item.evaluationItemId, item.evaluationItemScore)
    if (success) {
        // 播放音频 播放动画
        scoreAudioStore.play(item.evaluationItemScore)
    }
}
async function addScoreClick(score, type) {
    if (rightAnswer.value == '') {
        explainAlertStore.show('请选择正确答案', RBPColors.colorC)
        return
    }
    if (rightAnswer.value == '对') {
        rightAnswer.value = 'RIGHT'
    }
    if (rightAnswer.value == '错') {
        rightAnswer.value = 'WRONG'
    }
    let stuList = []
    let scoreNum = parseInt(score)
    if (scoreNum > 0) {
        stuList = selectOptStudentList()
    } else if (scoreNum < 0) {
        stuList = unSelectOptStudentList()
    }

    if (stuList.length == 0) {
        if (type == 'error') {
            explainAlertStore.show('当前题目没有作答错误的学生', RBPColors.colorC)
        } else if (type == 'success') {
            explainAlertStore.show('当前题目没有作答正确的学生', RBPColors.colorC)
        }
        return
    }

    stuList.forEach(stu => {
        stu.score = String(parseInt(stu.score ?? '0') + parseInt(score))
    })
    // explainAlertStore.show(score + '分', type == 'success' ? 'green' : 'red')

    //上传数据
    let success = await roomUpdater.studentsAddScore(stuList, score)
    if (success) {
        // 播放音频 播放动画
        scoreAudioStore.play(score)
    }
}
function selectOptStudentList() {
    let stuList = []
    if (rightAnswer.value.length > 0) {
        for (let [studentId, answer] of Object.entries(studentAnswers.value)) {
            if (answer[0].sort().join('') == rightAnswer.value) {
                let stu = selectedClassroom.value.idStudentMap.get(parseInt(studentId))
                stuList.push(stu)
            }
        }
    }
    return stuList
}
function unSelectOptStudentList() {
    let stuList = []
    for (let [studentId, answer] of Object.entries(studentAnswers.value)) {
        if (answer[0].sort().join('') != rightAnswer.value) {
            let stu = selectedClassroom.value.idStudentMap.get(parseInt(studentId))
            stuList.push(stu)
        }
    }
    return stuList
}
function getBorderRadius() {
    if (displayDirection.value === DisplayDirection.Left) {
        return '0 36px 36px 0'
    } else {
        return '36px  0px  0px  36px'
    }
}
</script>
<style lang="scss" scoped>
@import '@/assets/scss/mixin.scss';

.interactHead {
    width: 100%;
    height: 100%;
    padding: 0 24px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .interactTitle {
        font-size: 21px;
        font-weight: bold;
        color: var(--text-color);
    }

    .interactTime {
        color: var(--primary-color);
        font-size: 33px;
    }
}

.interactContent {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .responder {
        flex: 1;
        width: 100%;
        position: relative;
    }

    .rightAnswer {
        width: 90%;

        .rithtAnswerTitle {
            text-align: center;
            margin-bottom: 1vh;
            font-size: 1.6vh;
            color: var(--secondary-text-color);
        }
    }

    .score {
        width: 70%;

        .eva-correct {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1vh;
            width: 100%;
        }

        .correct {
            margin-top: 20px;
            @include flex(space-between, center);
        }

        .error {
            margin-top: 20px;
            @include flex(space-between, center);
        }
    }
}

.interactBtns {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
}

.back-interact {
    position: absolute;
    left: v-bind("displayDirection === DisplayDirection.Left ? '0' : null");
    right: v-bind("displayDirection === DisplayDirection.Left ? null : '0'");
    bottom: 28vh;
    width: 8px;
    height: 100px;
    z-index: var(--interact-z-index);
    writing-mode: vertical-lr;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: v-bind("getBorderRadius()");
    cursor: pointer;
    color: var(--secondary-text-color);
    font-size: 15px;
    padding: 20px 12px;
    font-weight: 400;
    background-color: palegoldenrod;
    border: 1px solid var(--border-bar-color);
    // background-color: var(--main-bc-color);
}
</style>