
/// 大的分类
/// 每个大分类中再拆分选项
export const ControlOptions = {
    login: 10000,  // 登录相关操作
    interact: 10001, // 互动
    takePicture: 10005, // 拍照
    takeVideo: 10008, // 拍视频
    rtcConnect: 10006, // 
    pptPage: 10007, // ppt翻页
}


export const LoginOptions = {
    startClass: 1, // 开始上课
    stopClass: 2, // 下课
}


/// 拍照展示 需要携带taskId
export const PictureOptions = {
    addPictures: 1, // 添加一张或者多张图片
    deletePicture: 2, // 删除一张图片
    stopShow: 3,  // 停止展示 
}

/// 录制视频展示 需要携带taskId
export const VideoOptions = {
    addVideo: 1, //添加一个视频
}

export const PptOptions = {
    nextPage: 1, // 下一页
    prevPage: 2, // 上一页
    hide:3,
    close:4,
}

///  webrtc 信息交互
export const RtcOptions = {
    offer: 1,
    answer: 2,
    candidate: 3,
}
 
/// 互动交互
export const InteractOptions = {
    state: 0, // 查询状态
    start: 1, // 开始互动
    select: 2, // 选择
    rolling: 3, // 收卷
    stop: 4, // 结束互动
    multiQuestionsSelect: 5, // 组合题选择题型
    answer: 6, // 组合题开始答题
    explain: 7, // 组合题开始讲解
    multiQuestionsSelectAnswer: 8, // 组合题选择答案
    multiQuestionsNumber: 9, // 组合题 选择题号
    cloudDisk:10 ,//云盘
    operate:11, //操作
    studentList:12, //学生列表
    aiHelp:17, //ai互动
    classRecord:16, //随堂记录
    scores:18, //得分
    teachPlan:19,//导学案
    randomPick:20,//随机点名
    homework:21,//作业讲评
    timer:22,//计时器
    changeScore:23,//修改分数
    screenState:24,//屏幕状态
}

/// 组合题查询状态同步
export const MultiStateSync = {
    none: -1,
    select: 0, // 弹出题目选择框
    start: 1, // 开始答题
    explain: 2, // 开始讲解
    answer: 3, // 选择答案
}