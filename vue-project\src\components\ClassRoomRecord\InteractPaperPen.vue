<template>
	<div class="class-room-record-board">
		<div class="page_wrap">
			<div class="compare">
				<div class="single" v-if="selectedTotal == 1">
					<div class="board">
						<img :src="blackBoard" width="100%" />
					</div>
					<div class="answer" @dblclick="checkTrails(records[currSelectedIndex])">
						<img
							:src="records[currSelectedIndex].pageImgUrl + '?x-oss-process=image/resize,h_' + imgHeight" />
						<div class="stuName">{{ records[currSelectedIndex].deviceAlias }}</div>
					</div>
				</div>
				<div class="selectedList" v-else>
					<div v-for="(item) in records" :key="item.id">
						<div class="selectedStu" :id="item.deviceMac" v-if="item.selected"
							:style="{ width: selectNum == 2 ? '25vw' : selectNum == 4 ? '22vw' : '11vw', margin: selectNum == 2 ? '5vw' : '0.5vw' }"
							:key="item.deviceMac" @dblclick="checkTrails(item)">
							<img :src="item.pageImgUrl + '?x-oss-process=image/resize,h_' + imgHeight"
								:alt="item.deviceAlias" />
							<div class="stuName">{{ item.deviceAlias }}</div>
						</div>
					</div>
				</div>
			</div>

			<div class="options">
				<div class="options_wrapper">
					<div class="selectedNum">{{ '已选' }}{{ selectedTotal }}/{{ records.length }}</div>
					<div class="checkAll">
						<el-checkbox v-model="checkAll" @change="selectAll">{{ '全选' }}</el-checkbox>
					</div>
					<div class="checkmult">
						<el-checkbox v-model="checkMulti" @change="selectMulti">{{ '多选' }}</el-checkbox>
					</div>
					<div class="compNum">
						{{ '单行数量' }}&nbsp;&nbsp;
						<el-select popper-class="class-room-record-board-select-popper" size="mini" v-model="selectNum"
							:placeholder="'请选择'">
							<el-option v-for="item in options" :key="item.value" :label="item.label"
								:value="item.value"></el-option>
						</el-select>
					</div>
				</div>
			</div>

			<div class="thumbs">
				<div class="stuList">
					<div class="student" v-for="(item, index) in records" :key="item.id" @click="selectStu(index)">
						<div class="stuImg">
							<el-checkbox class="radio" v-model="item.selected"></el-checkbox>
							<a :href="'#' + item.deviceMac">
								<img :src="item.pageImgUrl + '?x-oss-process=image/resize,h_' + imgHeight" />
							</a>
						</div>
						<div class="name">{{ item.deviceAlias }}</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { ClassroomRecordRequest } from '@/server_request/classroom_record';
import stopBoard from "./assets/<EMAIL>"
import endBoard from "./assets/<EMAIL>"
import { useInteractStore } from '@/stores/interact_store';
const interactStore = useInteractStore()
const queryData = interactStore.interactResult
const records = ref([])
const blackBoard = ref('')
const currSelectedIndex = ref(0)
const checkAll = ref(false)
const checkMulti = ref(true)
const options = [
	{
		value: 2,
		label: "2",
	},
	{
		value: 4,
		label: "4",
	},
	{
		value: 8,
		label: "8",
	},
]
//选择数量
const selectNum = ref(2)
//定时请求
let timer
//获取黑板和轨迹
function getBlack() {
	let params = {
		classId: queryData.classId,
		classRecordId: queryData.classRecordId,
		interactId: queryData.interactId,
	};
	ClassroomRecordRequest.interactDetail(params).then((res) => {
		// blackBoard.value = res.data.imgUrl;
	}).catch(function (error) {
		console.log(error);
	});
}
function getTrails() {
	let params = {
		taskId: queryData.taskId,
	};
	ClassroomRecordRequest.interactTrails(params).then((res) => {
		res.data.trailsRcords.forEach((item) => {
			item.pageImgUrl = item.pageImageUrl;
		});
		resetData(res.data.trailsRcords, res.data.deviceAmount);
	}).catch(function (error) {
		console.log(error);
	});
}
//选择数量
const selectedTotal = computed(() => {
	let n = 0;
	for (const item of records.value) {
		if (item.selected) {
			n++;
		}
	}
	return n;
})
const recordsTaskIds = computed(() => {
	let arr = [];
	for (const task of records.value) {
		arr.push(task.deviceMac);
	}
	return arr;
})
const imgHeight = computed(() => {
	return parseInt(window.innerHeight * 0.8);
})
//数据重置
function resetData(recordData, count) {
	if (records.value.length > 0) {
		for (let i = 0; i < recordData.length; i++) {
			if (recordsTaskIds.value.indexOf(recordData[i].deviceMac) < 0) {
				recordData[i].selected = false;
				records.value.push(records[i]);
			}
		}
	} else {
		for (let i = 0; i < recordData.length; i++) {
			if (i == currSelectedIndex.value) {
				recordData[i].selected = true;
			} else {
				recordData[i].selected = false;
			}
		}
		records.value = recordData;
	}
	if (count != recordData.length) {
		timer = setTimeout(() => {
			getTrails();
		}, 2000);
	}
}

function checkTrails(item) {
	let currStuInfo = [
		{
			blockKey: item.blockKey,
			noteKey: item.noteKey,
			recogImgUrl: item.pageImgUrl,
			imgUrl: "",
			taskId: item.taskId,
			studentId: item.studentExtId,
			pageId: item.pageId,
		},
	];
	localStorage.setItem("currStuInfo", JSON.stringify(currStuInfo));
	if (window.CusWebEngine) {
		window.CusWebEngine.checkTrailsInteraction(0 + "&" + item.deviceAlias);
	}
}
//选择方法
function selectStu(index) {
	currSelectedIndex.value = index;
	if (checkAll.value) {
		checkAll.value = !checkAll.value;
	}
	if (checkMulti.value) {
		if (selectedTotal == 1 && records.value[index].selected) {

		} else {
			records.value[index].selected = !records.value[index].selected;
			if (!records.value[index].selected) {
				currSelectedIndex.value = records.value.findIndex(
					(item, index, arr) => {
						return item.selected;
					}
				);
			}
		}
	} else {
		for (let i = 0; i < records.value.length; i++) {
			records.value[i].selected = i == index
		}
	}
	checkAll.value = selectedTotal.value == records.value.length

}
function selectAll(checked) {
	if (checked) {
		records.value.forEach(item => {
			item.selected = true
		})
		checkMulti.value = true
	} else {
		records.value.forEach(item => {
			item.selected = false
		})
		records.value[0] && (records.value[0].selected = true)
	}
}
function selectMulti(checked) {
	if (!checked) {
		for (let i = 0; i < records.value.length; i++) {
			records.value[i].selected = i == currSelectedIndex.value

		}
		checkAll.value = false;
	}
}
onMounted(() => {
	getTrails()
	getBlack()
})
onUnmounted(() => {
	interactStore.interactResult = {}
	clearInterval(timer)
})
</script>
<style lang="scss">
@import '../../assets/scss/components.scss';

.class-room-record-board {

	.el-select {
		width: 60px;

		.el-select__wrapper {
			border: solid #414f5d 1px !important;
			border-color: #414f5d !important;
			background-color: #091722 !important;
			box-shadow: none;
		}

		.el-select__wrapper:hover {
			border: solid #414f5d 1px !important;
			border-color: #414f5d !important;
			background-color: #091722 !important;
			box-shadow: none;
		}




	}



	.el-input__inner {
		border: 1px solid #414f5d !important;
	}

	.el-checkbox__label {
		color: #fff;
	}

	.el-checkbox__inner:hover {
		border-color: #414f5d !important;
	}

	.el-select:hover .el-input__inner {
		border: 1px solid #414f5d !important;
	}

	.el-select .el-input__inner:focus {
		border-color: #414f5d !important;
	}

	.el-select:hover .el-input__inner {
		border-color: #414f5d !important;
	}

	.el-select:hover .el-input__inner {
		border: 1px solid #414f5d;
	}

	.el-select .el-input__inner:focus {
		border-color: #414f5d !important;
	}

	.el-checkbox__input.is-checked .el-checkbox__inner {
		background-color: #091722 !important;
		border-color: #414f5d !important;
	}

	.el-checkbox__input.is-checked+.el-checkbox__label {
		color: #fff !important;
	}

	.el-checkbox__input.is-focus .el-checkbox__inner {
		border-color: #556b81 !important;
	}

	.el-select-dropdown {
		border: 1px solid #030a10 !important;
		background-color: #030a10 !important;
	}

	.el-checkbox__inner {
		background-color: #091722 !important;
		border: 1px solid #414f5d !important;
	}

	.el-input__inner {
		background-color: #091722 !important;
		color: #fff !important;
	}


	.radio .el-checkbox__input.is-checked .el-checkbox__inner {
		background-color: #00d97f !important;
		border-color: #00d5a3 !important;
		/* border-radius: 50%!important; */
	}

	.radio .el-checkbox__inner {
		background-color: #f2f5fb !important;
		border: 1px solid #aabbd9 !important;
		border-radius: 50% !important;
	}

	.el-checkbox__label {
		padding-left: 10px !important;
		line-height: 19px !important;
		font-size: 14px !important;
	}

	.el-checkbox__inner {
		width: 14px !important;
		height: 14px !important;
		border-radius: 2px !important;
	}

	.el-checkbox__inner::after {
		border-width: 1px !important;
		height: 7px !important;
		left: 4px !important;
		top: 1px !important;
		width: 3px !important;
	}

	.el-input--mini .el-input__inner {
		height: 28px !important;
		line-height: 28px !important;
	}

	.el-input--mini .el-input__icon {
		line-height: 20px;
	}

	.el-input--suffix .el-input__inner {
		padding-left: 15px !important;
	}

	.el-input__inner {
		border-radius: 4px !important;
		padding: 0 15px !important;
		font-size: 12px !important;
	}


}


.class-room-record-board-select-popper {
	border: none !important;

	.el-popper__arrow {
		display: none;
	}

	.el-select-dropdown {
		background-color: #091722 !important;
		border: none !important;

		.el-select-dropdown__item {
			background-color: transparent;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 0px !important;
			margin: 0px !important;
			// font-size: 16px;
		}

		// padding: 0px;
		// display: flex;
		// align-items: center;
		// justify-content: center;
		.el-select-dropdown__item.is-selected {
			color: #fff;
			font-weight: normal;
		}
	}
}
</style>
<style lang="scss" scoped>
@import '../../assets/scss/components.scss';

.class-room-record-board {
	.page_wrap {
		width: 100vw;
		height: 100vh;
		display: flex;
		flex-direction: column;
		position: relative;

		.compare {
			box-sizing: border-box;
			height: 80vh;
			width: 100%;
			background-color: #2e4a66;
			overflow: auto;

			.single {
				background: #0b423d;
				height: 100%;
				display: flex;
				align-items: center;

				.board {
					// min-height: 694px;
					flex: 1;
					background: #0b423d;
				}

				.answer {
					height: 85%;
					background: #fff;
					position: relative;
					margin-right: 25px;
					// margin-bottom: ;

					.stuName {
						font-size: 16px;
						width: 100%;
						color: #556677;
						position: absolute;
						top: 15px;
						left: 0;
						text-align: center;
					}

					img {
						height: 100%;
						margin: 0 auto;
					}
				}
			}

			.selectedList {
				width: 100%;
				/* padding: 0 0.7rem; */
				box-sizing: border-box;
				text-align: center;
				display: flex;
				flex-direction: row;
				flex-wrap: wrap;
				justify-content: center;

				.selectedStu {
					width: 25%;
					display: inline-block;
					box-sizing: border-box;
					position: relative;
					background: #fff;

					.stuName {
						font-size: 16px;
						width: 100%;
						color: #2e4a66;
						position: absolute;
						top: 3%;
						left: 0;
						text-align: center;
					}

					img {
						width: 100%;
					}
				}
			}
		}

		.options {
			width: 100%;
			position: fixed;
			bottom: 21vh;
			left: 0;
			font-size: 14px;
			color: #fff;
			display: flex;
			justify-content: center;
			align-items: center;

			.options_wrapper {
				padding: 0 20px;
				height: 40px;
				background: #030a10;
				border-radius: 6px;
				width: 60%;
				display: flex;
				justify-content: center;
				align-items: center;

				.selectedNum {}

				.checkAll {
					.el-checkbox {
						height: 100%;
						display: flex;
						align-items: center;
					}
				}

				.checkmult {
					.el-checkbox {
						height: 100%;
						display: flex;
						align-items: center;
					}
				}

				.compNum {}
			}

			.options_wrapper>div {
				margin: 0 20px;
				height: 100%;
				display: flex;
				align-items: center;
			}
		}

		.thumbs {
			position: fixed;
			bottom: 0;
			left: 0;
			width: 100%;
			height: 20vh;
			padding: 0 100px 0 50px;
			background: #9cbeda;
			white-space: nowrap;
			box-sizing: border-box;
			// background-color: yellow;

			.stuList {
				width: 100%;
				height: 100%;
				display: flex;
				gap: 20px;
				// align-items: center;
				overflow: auto;
				background: #9cbeda;
				// background-color: greenyellow;

				.student {
					display: flex;
					flex-direction: column;
					align-items: center;
					width: 100px;
					height: 20vh;
					// margin-right: 20px;
					cursor: pointer;
					// background-color: red;

					.stuImg {
						width: 100px;
						margin-top: 12px;
						height: calc(20vh - 48px);
						// height: 142px;
						background: rgba(255, 255, 255, 1);
						box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.2);
						border-radius: 4px;
						// border: 1px solid #fff;
						position: relative;
						cursor: not-allowed;
						pointer-events: none;

						img {
							width: 100%;
							// max-width: 100%;
							// min-height: 100%;
							height: 100%;
							object-fit: contain;
						}

						.radio {
							position: absolute;
							top: 0px;
							right: 0;
						}

						.el-checkbox {
							margin-right: 5px !important;
						}
					}

					.name {
						font-size: 14px;
						color: #333333;
						line-height: 20px;
						margin-top: 4px;
					}
				}
			}
		}
	}
}
</style>