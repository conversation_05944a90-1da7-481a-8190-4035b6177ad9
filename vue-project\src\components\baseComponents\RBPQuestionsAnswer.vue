<template>
    <div class="rbp-questions-answer" :style="{ gridTemplateColumns: `repeat(${props.optionCol}, 1fr)` }">
        <div class="rightAnswerItem" :class="{ rightAnswerItemActive: item.selected }"
            :style="{ width: getWidthByIsTrueFalse() }" v-for="(item, index) in props.chooseOptionList" :key="index"
            @click="chooseSelectAnswerClick(item)">
            {{ item.option }}
        </div>
    </div>
</template>
<script setup>
import { defineProps } from 'vue'
import { Interact } from '@/classroom/interact_enums.js'
import { RBPColors } from '@/components/baseComponents/RBPColors.js'

const props = defineProps({
    optionCol: Number,
    interact: Number,
    chooseOptionList: Array,
})

function getWidthByIsTrueFalse() {
    if (props.interact == Interact.trueFalse) {
        return '118px'
    } else {
        return '54px'
    }
}

const emits = defineEmits(['chooseSelectAnswerClick'])

function chooseSelectAnswerClick(item) {
    emits('chooseSelectAnswerClick', item)
}

</script>
<style lang="scss" scoped>

.rbp-questions-answer {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    place-items: center;
    grid-gap: 20px;
    width: 100%;
    box-sizing: border-box;
    padding: 0 20px;

    .rightAnswerItem {
        background-color: var(--secondary-color);
        color: var(--text-color);
        font-size: 21px;
        height: 54px;
        width: 54px;
        border-radius: 15px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .rightAnswerItemActive {
        background-color: var(--primary-color);
        color: white;
    }
}
</style>