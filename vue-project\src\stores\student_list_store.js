import { defineStore } from "pinia"
import { ref } from "vue"

export const StudentListType = {
    seat: '座次',
    group: '小组',
    name: '姓氏',
}

export const useStudentListStore = defineStore('studentList', () => {

    const currentSelectedTitle = ref(StudentListType.seat)
    const remoteFlag  = ref(0)
    let nowScore = 0
    const lastnameFlag = ref("全部")
    function cleanData() {
        currentSelectedTitle.value = StudentListType.seat
    }

    return {
        currentSelectedTitle,
        remoteFlag,
        lastnameFlag,
        nowScore,
        cleanData
    }
})