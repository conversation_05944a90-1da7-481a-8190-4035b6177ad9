<?xml version="1.0" encoding="UTF-8"?>
<svg width="30px" height="30px" viewBox="0 0 30 30" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Icon/搜索</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#2A85FB" offset="0%"></stop>
            <stop stop-color="#3AF8DE" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="0切图" transform="translate(-640, -849)">
            <g id="Icon/搜索" transform="translate(640, 849)">
                <rect id="Rectangle-271-Copy-2" stroke="url(#linearGradient-1)" fill="#000000" opacity="0" x="0.5" y="0.5" width="29" height="29"></rect>
                <path d="M14,20 C17.866,20 21,16.866 21,13 C21,9.13401 17.866,6 14,6 C10.134,6 7,9.13401 7,13 C7,16.866 10.134,20 14,20 Z" id="路径" stroke="#565656" stroke-width="2" stroke-linejoin="round"></path>
                <polyline id="路径" stroke="#565656" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" transform="translate(20.9393, 20.9393) rotate(-315) translate(-20.9393, -20.9393)" points="17.4393398 20.9393398 20.9393398 20.9393398 24.4393398 20.9393398"></polyline>
            </g>
        </g>
    </g>
</svg>