<template>
    <div class="chapter-tree-widget">
        <div class="expand"></div>
        <div class="tree-wrap" v-if="cloudTree.length">
            <el-tree :data="cloudTree" node-key="id" ref="treeRef" :highlight-current="!showSearch" :indent="10"
                :accordion="true" :expand-on-click-node="true" :default-expanded-keys="[6]" @current-change="nodeClick">
                <template #default="{ node, data }">
                    <div class="folder-show" :style="{ color: !showSearch && id == data.id ? '#409EFF' : '' }">
                        <el-icon>
                            <Folder />
                        </el-icon>
                        <span class="name">{{ node.label }}</span>
                    </div>
                </template>

            </el-tree>
        </div>
    </div>
</template>

<script setup>
import { CloudDiskRequest } from '@/server_request/cloud_disk';
import { nextTick, onMounted, ref, defineEmits, defineExpose } from 'vue';
const cloudTree = ref([])
const props = defineProps({
    showSearch: Boolean
})
const emits = defineEmits(['setNodeRoute']);
const treeRef = ref()
const id = ref(null)
let folderId = 0;
let nodeRoute = []
//获取分类
async function getChapters() {
    // return
    try {
        let { data } = await CloudDiskRequest.getCatalogList({})
        if(data&&data.length){
            data = resetKey(data)
        }else{
            data = []
        }
        cloudTree.value = [{
            cdId: 0,
            id: 0,
            label: '我的备课资料',
            children: data
        }]
    } catch (e) {
        console.error("--------------------获取云盘分类：",e)
        //TODO handle the exception
    }
    folderId = localStorage.getItem('cloudId')
    if(!folderId){
        folderId = "0"
    }
    id.value = folderId.toString();
    //模拟一次点击 刷新文件列表
    nextTick(() => {
        setCurrentKey({ id: id.value })
    })
}
function resetKey(list) {
    list.forEach(item => {
        item.id = item.cdId
        item.label = item.displayName
        if (item.children && item.children.length) {
            item.children = resetKey(item.children)
        }
    })
    return list
}
//文件折叠展开
function expandNodes(data) {
    let node = treeRef.value.getNode(data)
    // expanded 为true表示展开状态  置为false收起
    node.expanded = false
    for (let i = 0; i < node.childNodes.length; i++) {
        node.childNodes[i].expanded = false;
        if (node.childNodes[i].childNodes.length > 0) {
            //  需要用到递归
            expandNodes(node.childNodes[i]);
        }
    }
}
//设置点击文件树
function setCurrentKey(data) {
    nextTick(() => {
        if(treeRef.value){
            treeRef.value.setCurrentKey(data.id)
        }
    })
}
//点击单个文件树
function nodeClick(data, node) {
    try {
        id.value = data.id
        nodeRoute = []
        getNodeRoute(node)
        localStorage.setItem('cloudId', data.id)
        emits('setNodeRoute', nodeRoute, data.id)
    } catch (e) {
        console.log(e)
        //TODO handle the exception
    }
}
//获取整个路径节点
function getNodeRoute(node) {
    if (!node || !node.parent) {// 若无父节点，则直接返回
        return
    }
    nodeRoute.unshift(node.data)// 将value保存起来
    //调用递归
    getNodeRoute(node.parent)
}
onMounted(() => {
    getChapters()
})
defineExpose({ setCurrentKey, expandNodes,getChapters })
</script>

<style lang="scss" scoped>
.chapter-tree-widget {
    display: flex;
    flex-direction: column;
    align-items: start;
    // justify-content: end;
    height: calc(100% - 32px);
    overflow: auto;

    .expand {
        flex: 1;
    }

    .folder-show {
        display: flex;
        align-items: center;

        .name {
            margin-left: 4px;
        }
    }

    .tree-wrap {
        margin-bottom: 8px;
        width: 100%;
        overflow-x: auto;
    }
}
</style>