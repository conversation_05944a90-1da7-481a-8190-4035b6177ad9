<template>
    <div style="height: 100%; width: 100%;" @click.stop="">
        <InteractPaperPen v-if="nowType == 'paperPen'"></InteractPaperPen>
        <InteractTestPaper v-if="nowType == 'testPaper'" @hideInteract="hideInteract"></InteractTestPaper>
        <InteractSingleChoice v-if="nowType == 'singleChoice'"></InteractSingleChoice>
        <InteractMultiChoice v-if="nowType == 'multiChoice'"></InteractMultiChoice>
        <InteractResponder v-if="nowType == 'responder'"></InteractResponder>
        <InteractChinese v-if="nowType == 'chineseWord'"></InteractChinese>
        <InteractEnglish v-if="nowType == 'englishWord'"></InteractEnglish>
        <div v-if="nowType == 'paperPen'" class="btn_quit" @click="hideInteract">
            <img src="@/assets/img/quit2.png" />退出
        </div>
    </div>
</template>

<script setup>
import InteractSingleChoice from './InteractSingleChoice.vue'
import InteractMultiChoice from './InteractMultiChoice.vue'
import InteractPaperPen from './InteractPaperPen.vue'
import InteractResponder from './InteractResponder.vue'
import InteractTestPaper from './InteractTestPaper.vue'
import InteractChinese from './InteractChinese.vue'
import InteractEnglish from './InteractEnglish.vue'
import { computed, defineEmits, onMounted } from 'vue'
const emits = defineEmits(['hideInteract'])
import { useInteractStore } from '@/stores/interact_store'
const interactStore = useInteractStore()
const nowType = computed(() => {
    switch (interactStore.interactResult.type) {
        case "0":
            return 'paperPen'
        case "1":
        case "6":
            return 'testPaper'
        case "2":
        case "3":
        case "4":
        case "13":
            return 'singleChoice'
        case "5":
            return 'responder'
        case "7": 
            return 'englishWord'
        case "9": 
            return 'chineseWord'
        case "12":
            return 'multiChoice'
        default:
            return 5
    }
})
onMounted(() => {

})

const hideInteract = () => {
    emits('hideInteract')
}
</script>

<style lang="scss" scoped>
    .btn_quit {
        bottom: 20px;
    }
</style>