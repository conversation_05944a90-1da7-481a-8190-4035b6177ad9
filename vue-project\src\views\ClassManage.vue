<template>
    <div class="class-manage">
        <div class="blackborad"></div>
        <div class="manage">
            <div class="left">
                <div class="left-top">
                    <div class="avatar">
                        <img v-if="info.headUrl" :src="info?.headUrl">
                        <img v-else src="/icon/icon_head.png">
                    </div>
                    <div class="name">{{ info.name }}</div>
                </div>
                <RBPSegButton v-if="showClassType" class="types" :options="['行政班', '课程班']"
                    :current-value="currectClassType" @update-current-value="setClassType"></RBPSegButton>

                <div class="expand"></div>
                <div class="left-body">

                    <div class="cls-item" v-for="(item, index) in classList" :key="item.classId">
                        <div class="cls-head" v-if="index == 0 || item.gid !== classList[index - 1]['gid']">
                            <img src="/icon/icon_class.svg" alt="">
                            <div class="text">{{ getGrade(item.gid) }}</div>
                        </div>
                        <div class="cls-wrap" :class="{ active: selectedClassroom?.classId == item.classId }"
                            @click="setClass(item)">
                            {{ item.className }}
                        </div>
                    </div>
                </div>
                <div class="left-bottom rbp-select">
                    <el-select popper-class="rbq-select-body" v-model="subjectId" placeholder="请选择"
                        v-if="subjects.length" @change="changeSubject">
                        <el-option v-for="item in subjects" :key="item.subjectId" :label="item.subjectName"
                            :value="item.subjectId"></el-option>
                    </el-select>
                </div>
            </div>
            <div class="right">
                <div class="right-body">

                    <div class="block">
                        <div class="expand"></div>
                        <StudentInfo class="student-show"></StudentInfo>
                        <div class="start-class-btn-border">
                            <div @click="startClass" class="start-class-btn">
                                <img src="/icon/icon_start_room.svg" alt="">
                                <div class="start-text">开始上课</div>
                            </div>
                        </div>
                    </div>
                    <div class="stu-list">
                        <div class="stu-item" :class="{ active: stuInfo && stuInfo.studentId == item.studentId }"
                            v-for="item in selectedClassroom?.studentList" :key="item.studentId" @click="setStu(item)">
                            <div class="stu-avatar">
                                <img v-if="item.headUrl" :src="item.headUrl">
                                <!-- <img v-else-if="item.sex" src="/img/icon_avatar_m.png"> -->
                                <img v-else src="/icon/icon_head.png">
                            </div>
                            <div class="stu-info">
                                <div class="stu-name">
                                    {{ item.name }}
                                </div>
                                <div class="stu-mac">
                                    设备地址：{{ item.deviceMac }}
                                </div>
                            </div>
                            <img class="stu-click" src="/icon/icon_right.svg" alt="">
                        </div>
                    </div>


                </div>
            </div>
            <SystemSet></SystemSet>
            <Updater></Updater>
        </div>
        <div class="manage-bottom-div">

        </div>
        <ClassRoomRecord v-if="showClassRecord"></ClassRoomRecord>
        <div class="manage-bottom class-manage-bottom-control">
            <div @click="bottomClick(index)" v-for="(item, index) in bottomList" :key="index"
                :class="item.icon ? 'icon-item' : 'expand'">
                <img v-if="item.icon" :src="mainContentTopSpace > 0 && index == 4 ? 'icon/recover_screen.svg' : item.icon" alt="">
                <div v-if="item.name" class="icon-name">{{ mainContentTopSpace && index == 4 ? '还原' : item.name }}</div>
            </div>
        </div>
    </div>
    <CenterSelectAlert :centerDialogVisible="showCenterSelectAlert" :className="selectedClassroom?.className"
        :cancelText="selectedClassroom?.classId == globalUnfinishClassId ? '继续' : '取消'"
        @cancelAndContinue="centerSelectAlertCancelAndContinue" @xinke="centerSelectAlertXinke"></CenterSelectAlert>
</template>
<script setup>
import { ref, computed, onMounted, getCurrentInstance, watch, reactive, onBeforeUnmount } from 'vue'
import RBPSegButton from '@/components/baseComponents/RBPSegButton.vue'
import { useClassroomStore } from '@/stores/classroom'
import CenterSelectAlert from '@/components/CenterSelectAlert.vue'
import { useRouter } from 'vue-router'
import roomUpdater from '@/classroom/classroom_updater'
import { ClassManageRequest } from '@/server_request/classmanage_request'
import { loginInstance } from '@/login_instance/login_instance'
import { arrangeClassroomData, getGrade, arrangeMacAddress } from '@/classroom/classroom_helper'
import { ElLoading, } from 'element-plus'
import { Alert } from '@/utils/alert'
import { databaseHelper, RecordType } from '@/classroom/class_flow_helper'
import SystemSet from '@/components/SystemSet/SystemSet.vue'
import { useDictationStore } from '@/stores/dictation_store'
const dictationStore = useDictationStore()
import { storeToRefs } from 'pinia'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import StudentInfo from '@/components/StudentInfo/StudentInfo.vue'
import { useStudentInfoStore } from '@/stores/student_info_store'
import { useTimesStore } from '@/stores/times_store'
import Updater from '@/components/SystemSet/Updater.vue'
import { dbHelper } from '@/utils/db_helper'
import { usePhetExpStore } from '@/stores/phet_exp_store'
import ClassRoomRecord from '@/components/ClassRoomRecord/ClassRoomRecord.vue'
import { LoginRequest } from '@/server_request/login_request'
const phetExpStore = usePhetExpStore()
const timesStore = useTimesStore()
const studentInfoStore = useStudentInfoStore()
const { stuInfo } = storeToRefs(studentInfoStore)
const classroomUIStore = useClassroomUIStore()
const { showSystemSet, mainContentTopSpace,showClassRecord } = storeToRefs(classroomUIStore)
const router = useRouter()
const { proxy } = getCurrentInstance()
const info = ref({})
const subjects = ref([]) //所有科目
const subjectId = ref(1) //当前选中的科目id
const showClassType = ref(false)
const classType = ref(1)
const attendClassList = ref([]) //课程班级
const physicalClassList = ref([]) //行政班级
const classList = computed(() => {

    if (classType.value == 1) {
        return physicalClassList.value
    } else {
        return attendClassList.value
    }
})
const currectClassType = computed(() => {
    return classType.value == 1 ? '行政班' : '课程班'
})
const bottomList = [
    {
        icon: 'icon/icon_tool_setting.svg',
        name: '设置'
    },
    {
        icon: 'icon/icon_interaction_record.svg',
        name: '随堂记录'
    },
    {},

    {
        icon: 'icon/icon_tool_refresh.svg',
        name: '刷新'
    },
    {
        icon: 'icon/icon_tool_drop_screen.svg',
        name: '下拉屏幕'
    },
    {
        icon: 'icon/icon_quit.svg',
        name: '退出'
    }
]
const showCenterSelectAlert = ref(false)
const globalUnfinishClassId = ref(0)

const classroomStore = useClassroomStore()
const { selectedClassroom, } = storeToRefs(classroomStore)





//点击底部
function bottomClick(index) {
    switch (index) {
        case 0:
            setupClick()
            break;
        case 1:
            recordClick()
            break;
        case 3:
            getData()
            break;
        case 4:
            dropScreenClick()
            break;
        case 5:
            quitLoginClick()
            break;
        default:
            break

    }

}
function dropScreenClick() {

    if (mainContentTopSpace.value !== 0) {
        mainContentTopSpace.value = 0
    }
    else {
        mainContentTopSpace.value = window.innerHeight / 3
    }
}
async function setClassType(type) {
    classType.value = type == '行政班' ? 1 : 2
    await dbHelper.updateClassType(classType.value)

    let clsRoom
    if (classType.value == 1) {
        // 行政班
        let pyClassId = await dbHelper.getGrade()
        clsRoom = physicalClassList.value.find(item => item.classId == pyClassId)

    } else if (classType.value == 2) {
        // 课程班
        let attendClassId = await dbHelper.getAttendGrade(loginInstance.teacher.teacherId, subjectId.value)
        clsRoom = attendClassList.value.find(item => item.classId == attendClassId)
    }

    if (clsRoom) {
        classroomStore.setSelectedClassroom(clsRoom)
    } else {
        if (classType.value == 1) {
            if (physicalClassList.value.length > 0) {
                classroomStore.setSelectedClassroom(physicalClassList.value[0])
            } else {
                classroomStore.setSelectedClassroom(null)
                return
            }
        } else if (classType.value == 2) {
            if (attendClassList.value.length > 0) {
                classroomStore.setSelectedClassroom(attendClassList.value[0])
            } else {
                classroomStore.setSelectedClassroom(null)
                return
            }
        }

    }
    setClass(selectedClassroom.value)
}
async function setClass(item) {
    if (classType.value == 1) {
        // 行政班
        await dbHelper.updateGrade(item.classId)
    } else if (classType.value == 2) {
        // 课程班
        await dbHelper.updateAttendGrade(loginInstance.teacher.teacherId, subjectId.value, item.classId)
    }
    classroomStore.setSelectedClassroom(item)

    stuInfo.value = null
    getStudents()
}
async function changeSubject() {
    let gradeCT = await dbHelper.getClassType()
    classType.value = showClassType.value ? gradeCT : 1
    if (showClassType.value) {
        classType.value = gradeCT ?? 1
    } else {
        if (physicalClassList.value.length == 0 && attendClassList.value.length > 0) {
            classType.value = 2
        } else {
            classType.value = 1
        }
    }
    classroomStore.setSelectedClassroom(null)
    loginInstance.saveSubjectMainId(subjectId.value)
    setSubjectName()
    await dbHelper.updateUserConfig(loginInstance.teacher.teacherId, subjectId.value)
    await getList()

}
function setupClick() {
    if(showClassRecord.value){
        showClassRecord.value = false
    }
    showSystemSet.value = true
    dictationStore.setTimesAndInterval()
}
onMounted(async () => {
    if (window.electron) {
        window.electron.fullScreen()
    }
    await dbHelper.addClassType()  //添加班级类型
    await dbHelper.addGrade()  // 添加行政班年级
    await getData()
})
async function getData() {
    let loadingInstance = ElLoading.service({ background: 'transparent' })
    const res = await ClassManageRequest.getUserInfo()
    loadingInstance.close()
    if (res.code == 1) {
        await dbHelper.addUserConfig(res.data.teacherId)
        loginInstance.saveTeacher(res.data)
        loginInstance.saveEvaluation(res.data?.evaluation)
        info.value.headUrl = res.data?.headUrl ?? ''
        info.value.sex = res.data?.sex ?? true
        info.value.name = res.data?.name ?? ''
        subjects.value = res.data?.subjects
        let dbUser = await dbHelper.getUserConfig(res.data.teacherId)
        if (dbUser?.subjectId == -1) {
            subjectId.value = loginInstance.subjectMainId ?? subjects.value[0]?.subjectId ?? 0
        } else {
            subjectId.value = dbUser?.subjectId ?? subjects.value[0]?.subjectId ?? 0
        }
        await dbHelper.updateUserConfig(res.data.teacherId, subjectId.value)
        loginInstance.saveSubjectMainId(subjectId.value)
        setSubjectName()
        if (loginInstance.subjectMainId == 0) {
            Alert.showErrorMessage('当前无任教学科，将退出登录')
            // back()
            quitLoginClick()
        }
        loginInstance.saveLastSchoolId(res.data.schoolId, res.data.memberId)
        LoginRequest.loginReceive(res.data.schoolId)
        await getList()
    } else {
        Alert.showErrorMessage(res.message)
    }
}
function setSubjectName() {
    loginInstance.subjectName = subjects.value.find(item => item.subjectId == loginInstance.subjectMainId).subjectName ?? '未知'
}
async function getList() {
    let loadingInstance = ElLoading.service({ background: 'transparent' })
    const res = await ClassManageRequest.getClassList()
    loadingInstance.close()
    if (res.code == 1) {
        physicalClassList.value = res.data.filter(item => {
            return item.classType == 1
        }).sort(sortByCharCode).sort((a, b) => { return a.gid - b.gid })
        attendClassList.value = res.data.filter(item => {
            return item.classType == 2
        }).sort(sortByCharCode).sort((a, b) => { return a.gid - b.gid })

        if (attendClassList.value.length > 0) {
            showClassType.value = true
        }

        let classT = await dbHelper.getClassType()

        classType.value = parseInt(classT)
        if (physicalClassList.value.length == 0 && attendClassList.value.length > 0) {
            classType.value = 2
        }
        if (attendClassList.value.length == 0) {
            classType.value = 1
        }
        setClassType(classType.value == 1 ? '行政班' : '课程班')
    } else {
        Alert.showErrorMessage(res.message)
    }
}
function back() {
    proxy.$router.replace('/')
}

function quitLoginClick() {
    LoginRequest.loginLog(false)
    showClassRecord.value = false
    stuInfo.value = null
    loginInstance.cleanData()
    classroomStore.setSelectedClassroom(null)
    router.back()
    if (window.electron) {
        window.electron.quiteFullScreen()
    }
    
}

function closeWindow() {
    if (window.electron) {
        window.electron.closeWindow()
    }
}
// 根据字符UTF-16编码值排序
function sortByCharCode(a, b) {
    const aValue = a.className?.value;
    const bValue = b.className?.value;

    if (typeof aValue !== 'string' || typeof bValue !== 'string') {
        return 0;
    }

    const al = aValue.split('').map(c => c.charCodeAt(0));
    const bl = bValue.split('').map(c => c.charCodeAt(0));
    for (let i = 0; i < Math.min(al.length, bl.length); i++) {
        if (al[i] > bl[i]) {
            return 1;
        } else if (al[i] < bl[i]) {
            return -1;
        }
    }
    return al.length - bl.length;
}
async function getStudents() {
    if (!selectedClassroom.value) {
        return
    }
    let loadingInstance = ElLoading.service({ background: 'transparent' })
    const res = await ClassManageRequest.getStudentList(selectedClassroom.value.classId)
    loadingInstance.close()
    if (res.code == 1) {
        selectedClassroom.value.studentList = res.data
        arrangeClassroomData(selectedClassroom.value, res.data, loginInstance.subjectMainId)

        await getStudentsLevel()
        await getDeviceMac()
    } else {
        Alert.showErrorMessage(res.message)
    }
}

async function getStudentsLevel() {
    let loadingInstance = ElLoading.service({ background: 'transparent' })
    const res = await ClassManageRequest.getStudentListLevel(selectedClassroom.value.classId)
    loadingInstance.close()
    if (res.code == 1) {
        res.data.forEach(item => {
            let student = selectedClassroom.value.idStudentMap.get(item.studentId)
            if (student) {
                student.levelId = item.levelId
            }
        })
    }
}

async function getDeviceMac() {
    let loadingInstance = ElLoading.service({ background: 'transparent' })
    const res = await ClassManageRequest.getDevices(selectedClassroom.value.classId)
    loadingInstance.close()
    if (res.code == 1) {
        res.data.forEach(item => {
            let student = selectedClassroom.value.idStudentMap.get(item.studentId)
            if (student) {
                student.deviceMac = item.device
            }
        })
        arrangeMacAddress(selectedClassroom.value)
    } else {
        Alert.showErrorMessage(res.message)
    }
}
async function startClass() {
    if (!selectedClassroom.value) {
        Alert.showErrorMessage('请选择班级')
        return
    }
    let loadingInstance = ElLoading.service({ background: 'transparent' })
    const res = await ClassManageRequest.getStartClass(selectedClassroom.value.classId, subjectId.value)
    if (res.code == 1) {
        stuInfo.value = null
        selectedClassroom.value.classRecordId = res.data.classRecordId
        selectedClassroom.value.subjectId = subjectId.value
        await sendClassStart()
        timesStore.startClock()
        await databaseHelper.addRecords()
        await databaseHelper.addLines(RecordType.startClass)
        // await phetExpStore.show()
        router.push('/classroom')
    } else if (res.code == -2001) {
        let data = res.data[0]
        let unfinishClassId = data['classId']
        let unfinishRecordId = data['classRecordId']

        await startNewClass(unfinishClassId, unfinishRecordId)
    } else {
        Alert.showErrorMessage('加入失败')
    }
    loadingInstance.close()
}
async function sendClassStart() {
    let token = loginInstance.token
    let teacher = loginInstance.teacher
    roomUpdater.didStartClassroom(token, teacher, selectedClassroom.value)
}
async function startNewClass(unfinishClassId, unfinishedRecordId) {
    if (unfinishClassId) {
        let loadingInstance = ElLoading.service({ background: 'transparent' })
        const res = await ClassManageRequest.classEnd(unfinishClassId, unfinishedRecordId)
        loadingInstance.close()
        if (res.code == 1) {
            startClass()
        } else {
            Alert.showErrorMessage('结束失败')
        }
    } else {
        startClass()
    }
}
function setStu(item) {
    stuInfo.value = item
}
function recordClick() {
    if(showSystemSet.value){
        showSystemSet.value = false
    }
    classroomUIStore.showRecordType = 'interact'
    classroomUIStore.showClassRecord = true
    // proxy.$router.push({ path: '/classRecord' })
}
function centerSelectAlertCancelAndContinue() {
    showCenterSelectAlert.value = false
    if (selectedClassroom.value.classId == globalUnfinishClassId.value) {
        startClassResult(true, null, null)
    }
}
function centerSelectAlertXinke() {
    showCenterSelectAlert.value = false
    startNewClass(unfinish_class_id.value, unfinish_class_record_id.value)
}
</script>
<style lang="scss" scoped>
.class-manage {
    .expand {
        flex: 1;
    }

    .blackborad {
        width: 100vw;
        height: v-bind("mainContentTopSpace + 'px'");
        background-color: var(--main-anti-bc-color);
    }

    .manage {

        position: relative;
        width: 100vw;
        height: calc(100vh - v-bind("mainContentTopSpace + 'px'") - 78px);
        display: flex;
        background-color: var(--classroom-manage-bc-color);

        .left {
            width: 24%;
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 22px 56px 80px 30px;
            box-sizing: border-box;

            .types {
                margin-top: 16px;
                margin-bottom: 8px;
                height: 54px;
                min-height: 54px;
                box-sizing: border-box;
            }

            .left-top {
                display: flex;
                align-items: center;
                height: 54px;
                width: 100%;

                .avatar {
                    width: 54px;
                    height: 54px;
                    border-radius: 50%;
                    overflow: hidden;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }

                .name {
                    margin: 0 18px;
                    font-size: 24px;
                    color: var(--text-color);
                }


            }



            .left-body {
                width: 100%;
                max-height: calc(100% - 135px);
                overflow-y: auto;
                scrollbar-width: none;
                display: flex;
                flex-direction: column;



                .cls-item {
                    width: 100%;
                    font-size: 21px;
                    color: var(--text-color);

                    .cls-head {
                        height: 54px;
                        font-weight: 500;
                        padding: 0 12px;
                        display: flex;
                        align-items: center;

                        .text {
                            margin-left: 12px;
                            flex: 1;
                        }
                    }

                    .cls-wrap {
                        display: flex;
                        align-items: center;
                        font-weight: 400;
                        padding: 0 12px;
                        padding-left: 42px;
                        height: 54px;
                        cursor: pointer;
                    }

                    .active {
                        background-color: var(--primary-color);
                        border-radius: 15px;
                        color: var(--anti-text-color);
                    }
                }
            }

            .left-bottom {
                height: 54px;
                // background-color: yellow;
                margin-top: 27px;
                width: 100%;

                .el-select {
                    z-index: 100;
                }

            }
        }

        .right {
            width: 76%;
            height: calc(100vh - v-bind("mainContentTopSpace + 'px'") - 78px);
            display: flex;



            .right-body::-webkit-scrollbar {
                display: none;
                /* 适用于 Chrome, Safari 和 Opera */
            }

            .right-body {
                flex: 1;
                position: relative;
                display: flex;
                flex-direction: column;
                overflow-y: auto;
                scrollbar-width: none;

                .block::-webkit-scrollbar {
                    display: none;
                    /* 适用于 Chrome, Safari 和 Opera */
                }

                .block {
                    position: absolute;
                    width: 45%;
                    min-width: 250px;
                    left: calc(40% + 108px);
                    top: 0;

                    box-sizing: border-box;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    max-height: 100%;
                    height: 100%;
                    overflow-y: auto;
                    scrollbar-width: 0;

                    .student-show {
                        width: 100%;
                        margin-bottom: 106px;
                        margin-top: 24px;
                    }

                    .start-class-btn-border {
                        border: 6px solid rgba($color: var(--border-light-color-rgba), $alpha: 0.5);
                        margin-bottom: 28px;
                        border-radius: 80px;
                    }

                    .start-class-btn {
                        background: var(--primary-color);
                        border-radius: 80px;

                        width: 147px;
                        height: 147px !important;
                        min-height: 147px;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        border-radius: 50%;

                        .start-text {
                            font-weight: 500;
                            font-size: 21px;
                            color: var(--anti-text-color);
                            line-height: 31px;
                            text-align: center;
                        }
                    }
                }



                .stu-list::-webkit-scrollbar {
                    display: none;
                    /* 适用于 Chrome, Safari 和 Opera */
                }

                .stu-list {
                    // background-color: red;
                    width: 40%;
                    flex: 1;
                    box-sizing: border-box;
                    margin-left: 54px;
                    margin-top: 30px;
                    overflow-y: auto;
                    scrollbar-width: none;

                    .stu-item {
                        width: 100%;
                        height: 84px;
                        background-color: var(--main-bc-color);
                        display: flex;
                        align-items: center;
                        border-radius: 10px;
                        padding-left: 36px;
                        margin-bottom: 12px;
                        cursor: pointer;
                        box-sizing: border-box;

                        .stu-avatar {
                            width: 54px;
                            height: 54px;
                            border-radius: 50%;
                            overflow: hidden;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            margin-right: 30px;

                            img {
                                width: 100%;
                                height: 100%;
                            }
                        }

                        .stu-info {
                            flex: 1;
                            display: flex;
                            flex-direction: column;
                            justify-content: center;

                            .stu-name {
                                font-weight: 400;
                                font-size: 21px;
                                color: var(--text-color);
                            }

                            .stu-mac {
                                font-weight: 400;
                                font-size: 18px;
                                color: var(--secondary-text-color);
                            }
                        }

                        .stu-click {
                            margin-right: 36px;
                            margin-left: 12px;

                        }

                    }

                    .active {
                        background-color: var(--primary-color);

                        .stu-info {

                            .stu-name {
                                color: var(--anti-text-color) !important;
                            }

                            .stu-mac {
                                color: var(--anti-text-color) !important;
                            }
                        }
                    }
                }
            }

            .right-bottom {
                height: 60px;
                // background-color: yellow;
                display: flex;
                justify-content: flex-end;
                align-items: center;
            }
        }
    }
    .manage-bottom-div{
        height: 78px;
    }

    .manage-bottom {
        height: 78px;
        background-color: var(--toolbar-bg-color);
        display: flex;
        padding: 0px 6px;
        align-items: center;
        position: fixed;
        width: 100vw;
        bottom: 0;
        left: 0;
        z-index: var(--toolbar-ex-z-index);

        .icon-item {
            height: 72px;
            width: 72px;
            display: flex;
            flex-direction: column;
            align-items: center;

            .icon-name {
                font-weight: 400;
                font-size: 12px;
                color: var(--secondary-text-color);
                line-height: 20px;
            }
        }
    }
}
</style>

<style lang="scss">
@import url(../components/baseComponents/RBPSelect.scss);
</style>