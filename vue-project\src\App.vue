<template>
  <div id="app">
    <router-view></router-view>
  </div>
</template>
<style lang="scss">
@import './assets/scss/reset.scss';
@import './assets/scss/config.scss';
@import './assets/scss/mixin.scss';
@include overflowScrollbar();
</style>

<style>
/* 全局隐藏滚动条但允许滚动 */
html,
body {
  overflow: auto;
  /* 或 scroll */
  -ms-overflow-style: none;
  /* 适用于 IE 和 Edge */
  scrollbar-width: none;
  /* 适用于 Firefox */
  user-select: none;
  /* 禁用文本选择 */
}

html::-webkit-scrollbar,
body::-webkit-scrollbar {
  display: none;
  /* 适用于 Chrome, Safari 和 Opera */
}
</style>

<script setup>
import { LocalServerEvent } from './local_request/local_server_event';
import fontLoader from './drawboard/board_font';

fontLoader.setup()
LocalServerEvent.listen()

</script>