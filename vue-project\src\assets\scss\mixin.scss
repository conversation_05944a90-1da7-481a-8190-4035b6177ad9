//flex布局复用
@mixin flex($hov: space-between, $col: center) {
    display: flex;
    justify-content: $hov;
    align-items: $col;
}

@mixin position($pos: absolute, $top: 0, $left: 0, $w: 100%, $h: 100%) {
    position: $pos;
    top: $top;
    left: $left;
    width: $w;
    height: $h;
}

@mixin height($h: 0, $lh: $h) {
    height: $h;
    line-height: $lh;
}

@mixin wH($w: 0, $h: 0) {
    width: $w;
    height: $h;
}

@mixin border($bw: 1px, $bc: #d7d7d7, $bs: solid) {
    border: $bw $bs $bc;
}

@mixin bgImg($w: 0, $h: 0, $img: '', $size: contain) {
    display: inline-block;
    width: $w;
    height: $h;
    background: url($img) no-repeat center;
    background-size: $size;
}

// 添加overflow滚动条
@mixin overflowScrollbar() {
    ::-webkit-scrollbar {
        width: 14px;
        /* 垂直滚动条宽度 */
        height: 14px;
        /* 水平滚动条高度 */
    }

    /* 自定义滚动条轨道的样式 */
    ::-webkit-scrollbar-track {
        // background-color: #fff;
        border-radius: 7px;
    }

    /* 自定义滚动条的滑块样式 */
    ::-webkit-scrollbar-thumb {
        background-color: var(--border-bar-color);
        border-radius: 10px;
        border: 3px solid transparent;
        /* 添加透明边框，增加滑块宽度 */
    }

    /* 鼠标悬停时改变滑块的颜色 */
    ::-webkit-scrollbar-thumb:hover {
        background-color: var(--border-bar-hover-color);
    }
}