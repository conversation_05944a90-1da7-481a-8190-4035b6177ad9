<template>
    <div class="software-version" v-if="props.centerDialogVisible" @click="handleBeforeClose">
        <RBPAlert title="版本信息" width="707px" height="399px" @close="handleBeforeClose" :hide-bc="true">
            <template v-slot:rbpDiv>
                <div class="content" @click.stop="">
                    <div class="software-no">{{ version }}</div>
                    <div class="software-copy">Copyright © 2021 robotpen.com. All rights reserved.</div>
                    <div v-if="loginInstance.activeCode?.length > 0" class="software-active">已激活：{{ getActiveCode() }}</div>
                    <RBPButton btn-text="网络测试" btn-type="big" :btn-selected="true" class="software-btn" @click="openNetTestClick"></RBPButton>
                </div>
            </template>
        </RBPAlert>
        
    </div>
</template>
<script setup>
import { AppVersion } from '@/app_verson';
import { defineProps, toRefs, defineEmits, ref } from 'vue'
import { loginInstance } from '@/login_instance/login_instance'
import RBPAlert from './baseComponents/RBPAlert.vue';
import RBPButton from './baseComponents/RBPButton.vue';
const props = defineProps({
    centerDialogVisible: Boolean
})

const version = ref('软件版本: ' + AppVersion)

// const { centerDialogVisible } = toRefs(props)

const emits = defineEmits(['updateCenterDialogVisible', 'networkTestClick'])

function handleBeforeClose(done) {
    emits('updateCenterDialogVisible', false)
}
function openNetTestClick() {
    emits('networkTestClick')
}
function getActiveCode() {
    return loginInstance.activeCode.slice(0, 4) + '****' + loginInstance.activeCode.slice(-4)
}
</script>
<style lang="scss" scoped>
.software-version {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 100;
    background-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    -webkit-app-region: no-drag;
    border-radius: 30px;
    .box-shade{
        box-shadow: 0px 0px 12px rgba($color: var(--boxshaw-main-color-rgb), $alpha: 0.15);
    }

    .content {
        width: 100%;
        height: 100%;
        padding: 30px;
        display: flex;
        flex-direction: column;
        align-items: center;
        box-sizing: border-box;
        position: relative;
        .software-no {
            font-weight: 400;
            font-size: 30px;
            color: var(--text-color);
            line-height: 45px;
            width: 100%;
    
        }

        .software-copy {
            margin-top: 16px;
            font-weight: 400;
            font-size: 24px;
            width: 100%;
            color: var(--secondary-text-color);
            line-height: 36px;
        }

        .software-active {
            margin-top: 16px;
            font-weight: 400;
            font-size: 24px;
            width: 100%;
            color: var(--explanatory-text-color);
            line-height: 36px;
        }

        .software-btn {
            position: absolute;
            bottom: 36px;
            left: 210px;
        }
    }
}
</style>