<template>
    <div class="video-overlay-body" v-if="realTimeVideo.list.length">
        <div class="video-icon-body" @click="clickIcon">
            <img src="/icon/classroom_video_record.svg" alt="">
            <!-- <div v-if="realTimeVideo.count"  class="count-icon">{{ realTimeVideo.count>99?'99+': realTimeVideo.count}}</div> -->
        </div>
    </div>
</template>

<script setup>
import { useClassroomUIStore } from '@/stores/classroom_ui_store';
import { storeToRefs } from 'pinia';

const classroomUIStore = useClassroomUIStore()
const { realTimeVideo, } = storeToRefs(classroomUIStore)

function clickIcon() {
    realTimeVideo.value.show = !realTimeVideo.value.show
    realTimeVideo.value.count = 0
}
</script>
<style lang="scss"></style>

<style lang="scss" scoped>
.video-overlay-body {
    position: absolute;
    z-index: var(--classroom-video-icon-z-index);
    // bottom: 210px;
    bottom: 150px;

    right: 150px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .video-icon-body {
        position: relative;
        box-sizing: border-box;
        width: 48px;
        height: 48px;
        padding: 8px;
        border-radius: 24px;
        background-color: var(--classroom-overflow-icon-bc);

        .count-icon {
            height: 24px;
            min-width: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            color: var(--anti-text-color);
            font-size: 12px;
            line-height: 12px;
            background-color: var(--new-message-count-bc);
            position: absolute;
            top: -4px;
            right: -4px;
        }
    }

}
</style>