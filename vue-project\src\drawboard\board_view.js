import * as THREE from 'three';
import { PainterOrder } from './draw_enums';

export class BoardView extends THREE.Group {
    constructor(application, pos, size) {
        super()
        this.size = size
        this.position.set(pos.x, pos.y, pos.z)
        this.subViews = []
        // 保存父视图  weak引用
        this.superView = null
        // 保存 application实例 weak引用 
        this.application = application

        // 用于调整层级，在renderOrder相同时，用这个来调整层级
        this.flgIndex = 0

        this.renderOrder = PainterOrder.background
    }

    animate() {
        let application = this.application?.deref()
        if (application) {
            application.animate()
        }
    }

    addSubView(subview) {
        if (this.subViews.length > 0 && this.subViews[0].renderOrder === subview.renderOrder) {
            subview.flgIndex = this.subViews[0].flgIndex + 1
        }
        this.subViews.push(subview)
        this.subViews.sort((a, b) => {
            if (b.renderOrder === a.renderOrder) {
                return b.flgIndex - a.flgIndex
            }
            return b.renderOrder - a.renderOrder
        })
        this.add(subview)
        subview.superView = new WeakRef(this)
        this.animate()
    }

    bringSubViewToFront(subview) {
        if (this.subViews[0] === subview) {
            return
        }
        const maxFlgIndex = this.subViews.reduce((max, item) => Math.max(max, item.flgIndex), 0);
        subview.flgIndex = maxFlgIndex + 1
        this.update()
    }

    // 更新子视图层级
    update() {
        this.subViews.sort((a, b) => {
            if (b.renderOrder === a.renderOrder) {
                return b.flgIndex - a.flgIndex
            }
            return b.renderOrder - a.renderOrder
        })
        this.animate()
    }

    setBackgroundColor(color) {
        if (!this.backgroundMesh) {
            let backgroundPlane = new THREE.PlaneGeometry(this.size.width, this.size.height)
            let backgroundMaterial = new THREE.MeshBasicMaterial({
                color: color,
                side: THREE.DoubleSide,
            })
            this.backgroundMesh = new THREE.Mesh(backgroundPlane, backgroundMaterial)
            this.backgroupGroup = new THREE.Group()

            this.backgroupGroup.renderOrder = this.renderOrder - 1
            this.backgroupGroup.add(this.backgroundMesh)
            this.add(this.backgroupGroup)
        }
        else {
            this.backgroundMesh.material.color.set(color)
        }
        this.animate()
    }

    setRenderOrder(renderOrder) {
        this.renderOrder = renderOrder
        if (this.backgroupGroup) {
            this.backgroupGroup.renderOrder = this.renderOrder - 1
        }
        this.update()
    }

    setSize(size) {
        this.size = size
        if (this.backgroundMesh) {
            this.backgroundMesh.geometry.dispose()
            this.backgroundMesh.geometry = new THREE.PlaneGeometry(this.size.width, this.size.height)
            this.animate()
        }
    }

    removeSubView(subview) {
        let index = this.subViews.indexOf(subview)
        if (index > -1) {
            this.subViews.splice(index, 1)
            this.remove(subview)
            subview.superView = null
            this.animate()
        }
    }

    removeFromSuperView() {
        let superView = this.superView?.deref()
        if (superView) {
            superView.removeSubView(this)
        }
    }

    removeAndDisposeSubViews() {
        for (let subview of this.subViews) {
            subview.dispose()
        }
        this.subViews = []
    }


    convertPoint(point, z) {
        if (!BoardView.raycaster) {
            BoardView.raycaster = new THREE.Raycaster();
        }
        let raycaster = BoardView.raycaster;
        raycaster.setFromCamera(point, this.application.deref().camera);

        const rayOrigin = raycaster.ray.origin;
        const rayDirection = raycaster.ray.direction;

        if (Math.abs(rayDirection.z) < 1e-6) {
            console.warn("Ray direction z is too small, may cause division error.");
            return null; // 避免除零错误
        }

        const t = (z - rayOrigin.z) / rayDirection.z;
        if (t < 0) {
            console.warn("Intersection is behind the camera.");
            return null; // 避免无效交点
        }

        return new THREE.Vector3(
            rayOrigin.x + rayDirection.x * t,
            rayOrigin.y + rayDirection.y * t,
            z
        );
    }


    onTouchDown(point) {
        if (!this.onPointInside(point)) {
            return null
        }
        this.onTouch = true
        this.touchDownSubview = null
        for (let i = 0; i < this.subViews.length; i++) {
            let subView = this.subViews[i]
            if (subView.onPointInside(point)) {
                this.touchDownSubview = subView
                return subView.onTouchDown(point)
            }
        }
        return this
    }

    onMultiDown(point) {
        if (!this.onPointInside(point)) {
            return null
        }
        this.onMultiTouch = true
        this.multiTouchDownSubview = null
        for (let i = 0; i < this.subViews.length; i++) {
            let subView = this.subViews[i]
            if (subView.onPointInside(point)) {
                this.multiTouchDownSubview = subView
                return subView.onMultiDown(point)
            }
        }
        return this
    }

    onWhell(point){        
        if (!this.onPointInside(point)) {
            return null
        }
        for (let i = 0; i < this.subViews.length; i++) {
            let subView = this.subViews[i]
            if (subView.onPointInside(point)) {
                return subView.onWhell(point)
            }
        }
        return null
    }

    onTouchMove(point) {
        if (!this.onTouch) {
            return null
        }
        for (let i = 0; i < this.subViews.length; i++) {
            let subView = this.subViews[i]
            if (this.touchDownSubview === subView || subView.onPointInside(point)) {
                return subView.onTouchMove(point)
            }
        }
        return this
    }


    onTouchUp(point) {
        if (!this.onTouch) {
            this.onTouch = false
            return null
        }
        this.onTouch = false
        for (let i = 0; i < this.subViews.length; i++) {
            let subView = this.subViews[i]
            if (subView.onPointInside(point) || this.touchDownSubview === subView) {
                return subView.onTouchUp(point)
            }
        }
        return this
    }

    onTapCancel() {
        //如果按下后移动则取消单击、双击、长按响应
        for (let i = 0; i < this.subViews.length; i++) {
            let subView = this.subViews[i]
            subView.onTapCancel()
        }
    }

    /// 检测点是否在控件中，这里的point是世界坐标
    onPointInside(point) {
        if (!this.visible || this.touchEnable === false) {
            return false
        }
        let vector = new THREE.Vector3()
        this.localToWorld(vector)
        let cvtPoint = this.convertPoint(point, vector.z)
        const pos = new THREE.Vector3()
        this.getWorldPosition(pos)
        let leftX = pos.x - this.size.width / 2
        let rightX = pos.x + this.size.width / 2
        let topY = pos.y + this.size.height / 2
        let bottomY = pos.y - this.size.height / 2
        if (cvtPoint.x > leftX && cvtPoint.x < rightX && cvtPoint.y > bottomY && cvtPoint.y < topY) {
            return true
        }
        return false
    }

    dispose() {
        for (let i = 0; i < this.subViews.length; i++) {
            let subView = this.subViews[i]
            subView.dispose()
        }
        this.subViews = []

        // 获取 Group 中的所有子对象
        const children = this.children;
        // 遍历每个子对象
        for (let i = children.length - 1; i >= 0; i--) {
            const child = children[i];
            // 从 Group 中移除子对象
            this.remove(child);
            // 销毁子对象的几何体和材质
            if (child.geometry) {
                child.geometry.dispose();
            }
            if (child.material) {
                // 如果材质是一个数组（例如多材质对象），需要遍历数组进行销毁
                if (Array.isArray(child.material)) {
                    child.material.forEach(material => material.dispose());
                } else {
                    child.material.dispose();
                }
            }
            //  texture 全局统一回收
            //  // 销毁纹理（如果有）
            //  if (child.material && child.material.map) {
            //      child.material.map.dispose();
            //  }
        }
    }
}