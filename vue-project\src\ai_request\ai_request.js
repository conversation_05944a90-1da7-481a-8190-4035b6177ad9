import axios from "axios";
import { AIUrls } from "./ai_urls";
import { loginInstance } from "@/login_instance/login_instance";

async function encryptAESCBC(plainText,) {
    // 将 key 和 iv 
    const keyBuffer = new Uint8Array([
        0xC7, 0xE0, 0xB5, 0xBA, 0xC2, 0xDE, 0xB2, 0xA9,
        0xCA, 0xFD, 0xC2, 0xEB, 0xBF, 0xC6, 0xBC, 0xBC,
        0xD7, 0xDA, 0xCA, 0xF7, 0xCE, 0xB0, 0xB5, 0xC4,
        0xBF, 0xAA, 0xB7, 0xA2, 0xCF, 0xEE, 0xC4, 0xBF,
    ]);
    const ivBuffer = new Uint8Array([
        0xD4, 0xDA, 0x32, 0x30, 0x32, 0x35, 0xC4, 0xEA,
        0x30, 0x32, 0xD4, 0xC2, 0x32, 0x36, 0xC8, 0xD5,
    ]);

    // 导入 key
    const cryptoKey = await crypto.subtle.importKey(
        'raw',
        keyBuffer,
        { name: 'AES-CBC' },
        false,
        ['encrypt']
    );
    const data = hexToBytes(plainText);

    // 加密
    const encrypted = await crypto.subtle.encrypt(
        {
            name: 'AES-CBC',
            iv: ivBuffer
        },
        cryptoKey,
        data
    );
    // 返回 base64 编码的密文
    return arrayBufferToHex(encrypted);
}

function hexToBytes(hex) {
    const bytes = new Uint8Array(hex.length / 2);
    for (let i = 0; i < hex.length; i += 2) {
        bytes[i / 2] = parseInt(hex.slice(i, i + 2), 16);
    }
    return bytes;
}

function arrayBufferToHex(arrayBuffer) {
    const uint8Array = new Uint8Array(arrayBuffer);  // 创建一个视图来读取 ArrayBuffer    
    let hexString = '';

    // 遍历每个字节，将其转换为十六进制字符串
    uint8Array.forEach(byte => {
        hexString += byte.toString(16).padStart(2, '0');  // 转换为两位十六进制，并补充0
    });

    return hexString;
}
// 转成补位0+id 总共12位
function format_id(userId) {
    if (!userId) {
        userId = ""
    }
    const totalLength = 12;
    const numStr = userId.toString();
    const paddedNum = numStr.padStart(totalLength, '0');
    return paddedNum;
}
//转换时间戳为16味十六进制并将两位为单位翻转，最后加上0202补位
function timeToReversedHexBytes() {
    const timeNow = Math.floor(Date.now() / 1000);
    const hex = timeNow.toString(16).padStart(16, '0');
    const bytes = hex.match(/.{2}/g); // 每两位一组
    return bytes.reverse().join('') + '0202'; // 反转后拼接
}
export class AIRequest {
    // 用户授权信息生成 - 使用AES-CBC加密
    static async getAuthorization() {
        let formatData = format_id(loginInstance.teacher.teacherId) + timeToReversedHexBytes()

        let authorization = await encryptAESCBC(formatData)
        return authorization
    }

    // 默认请求配置
    static async defaultConfig() {
        let authorization = await this.getAuthorization()
        return {
            headers: {
                'Authorization': authorization
            }
        };
    }

    // 开启对话
    static async openChat(userId) {
        try {
            const url = AIUrls.chatOpenUrl();
            const formData = new FormData();
            formData.append('user_id', format_id(userId));
            formData.append('chattor','teacher');
            // chattor
            let config = await this.defaultConfig()
            const response = await axios.post(url, formData, config);
            return response.data;
        } catch (error) {
            console.error('开启对话失败:', error);
            return null;
        }
    }

    // 上传音频文件并实时处理AI响应流（使用回调函数）
    static uploadVoiceStream(userId, file, callbacks = {}, audioOutput = 1, textFormat = 'utf-8') {
        const url = AIUrls.voiceUploadUrl();
        const formData = new FormData();
        formData.append('user_id', format_id(userId));
        formData.append('file', file);
        formData.append('audio_output', audioOutput);
        formData.append('text_format', textFormat);
        formData.append('chattor','teacher');
        return new Promise(async (resolve, reject) => {
            const config = await this.defaultConfig();

            // 发送POST请求获取响应
            axios.post(url, formData, {
                ...config,
                responseType: 'text',
                headers: {
                    ...config.headers,
                    'Accept': 'text/event-stream'
                }
            }).then(response => {
                // 处理文本响应为流
                let buffer = '';
                const messages = [];
                let audioUrl = null;

                // 处理行
                const processLine = (line) => {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.substring(6));

                            if (data.node_type === 'ai-chat-node') {
                                messages.push(data.content);
                                if (callbacks.onText) {
                                    callbacks.onText(data.content);
                                }
                            } else if (data.node_type === 'text-to-speech-node') {
                                audioUrl = data.content;
                                if (callbacks.onAudio) {
                                    callbacks.onAudio(data.content);
                                }
                                // 完成流处理
                                if (callbacks.onComplete) {
                                    callbacks.onComplete({
                                        text: messages.join(''),
                                        messages,
                                        audioUrl
                                    });
                                }
                                resolve({
                                    text: messages.join(''),
                                    messages,
                                    audioUrl
                                });
                            } else if (data.node_type === 'error-node') {
                                const error = new Error(data.content);
                                if (callbacks.onError) {
                                    callbacks.onError(error);
                                }
                                reject(error);
                            }
                        } catch (e) {
                            console.error('解析SSE消息失败:', e, line);
                            if (callbacks.onError) {
                                callbacks.onError(e);
                            }
                        }
                    }
                };

                // 处理响应文本
                const lines = response.data.split('\n');
                for (const line of lines) {
                    if (line.trim()) {
                        processLine(line);
                    }
                }

            }).catch(error => {
                console.error('上传音频失败:', error);
                if (callbacks.onError) {
                    callbacks.onError(error);
                }
                reject(error);
            });
        });
    }

    // 课堂批量批改结果推流接口
    static async sendMarkResultBatch(taskId, markNum, callbacks = {}, textFormat = 'utf-8') {
        const url = AIUrls.markResultBatchUrl();
        const formData = new FormData();
        formData.append('task_id', taskId);
        formData.append('mark_num', markNum);
        if (textFormat) {
            formData.append('text_format', textFormat);
        }

        return new Promise(async (resolve, reject) => {
            try {
                const config = await this.defaultConfig();
                
                // 实现自定义的数据接收器
                const results = [];
                let buffer = '';
                
                // 使用fetch API实现实时处理
                try {
                    const response = await fetch(url, {
                        method: 'POST',
                        headers: {
                            ...config.headers,
                            'Accept': 'text/event-stream',
                        },
                        body: formData,
                    });
                    
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    
                    // 获取响应的可读流
                    const reader = response.body.getReader();
                    
                    // 处理数据流
                    while (true) {
                        const { done, value } = await reader.read();
                        
                        if (done) {
                            if (buffer.trim() && buffer.includes('data: ')) {
                                // 处理缓冲区中的最后一条消息
                                const lines = buffer.split('\n');
                                for (const line of lines) {
                                    if (line.startsWith('data: ')) {
                                        processEventData(line.substring(6));
                                    }
                                }
                            }
                            break;
                        }
                        
                        // 将二进制数据转换为文本
                        const chunk = new TextDecoder().decode(value);
                        buffer += chunk;
                        
                        // 按行拆分并处理
                        let newlineIndex;
                        while ((newlineIndex = buffer.indexOf('\n')) !== -1) {
                            const line = buffer.substring(0, newlineIndex);
                            buffer = buffer.substring(newlineIndex + 1);
                            
                            if (line.startsWith('data: ')) {
                                processEventData(line.substring(6));
                            }
                        }
                    }
                    
                    // 完成处理
                    resolve({ results });
                    
                } catch (error) {
                    console.error('批量批改请求失败:', error);
                    if (callbacks.onError) {
                        callbacks.onError(error);
                    }
                    reject(error);
                }
                
                // 处理事件数据
                function processEventData(dataStr) {
                    try {
                        const data = JSON.parse(dataStr);
                        
                        if (callbacks.onData) {
                            callbacks.onData(data);
                        }
                        
                        // 根据node_type处理不同类型的数据
                        if (data.node_type === 'ai-mark-node') {
                            // 添加到结果集 - 保留原始内容，不修改换行符
                            results.push(data);
                            // 处理中间结果
                            if (callbacks.onProgress) {
                                callbacks.onProgress(data);
                            }
                        } else if (data.node_type === 'ai-mark-node-complete') {
                            // 处理完成事件
                            if (callbacks.onComplete) {
                                callbacks.onComplete(data);
                            }
                        } else if (data.node_type === 'error-node') {
                            // 处理错误
                            const error = new Error(data.content || '未知错误');
                            if (callbacks.onError) {
                                callbacks.onError(error);
                            }
                        }
                    } catch (e) {
                        console.error('解析事件数据失败:', e, dataStr);
                        if (callbacks.onError && e.message !== data?.content) {
                            callbacks.onError(e);
                        }
                    }
                }
                
            } catch (error) {
                console.error('批量批改结果请求失败:', error);
                if (callbacks.onError) {
                    callbacks.onError(error);
                }
                reject(error);
            }
        });
    }

    // 上传音频文件并实时处理AI响应流（使用回调函数）
    static uploadVoiceStreamReal(userId, file, callbacks = {}, audioOutput = 1, textFormat = 'utf-8') {
        const url = AIUrls.voiceUploadUrl();
        const formData = new FormData();
        formData.append('user_id', format_id(userId));
        formData.append('file', file);
        formData.append('audio_output', audioOutput);
        formData.append('text_format', textFormat);
        formData.append('chattor','teacher');
        return new Promise(async (resolve, reject) => {
            try {
                const config = await this.defaultConfig();
                let buffer = '';
                const messages = [];
                let audioUrl = null;
                
                // 使用fetch API实现实时处理
                try {
                    const response = await fetch(url, {
                        method: 'POST',
                        headers: {
                            ...config.headers,
                            'Accept': 'text/event-stream',
                        },
                        body: formData,
                    });
                    
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    
                    // 获取响应的可读流
                    const reader = response.body.getReader();
                    
                    
                    // 处理数据流
                    while (true) {
                        const { done, value } = await reader.read();
                        
                        if (done) {
                            if (buffer.trim() && buffer.includes('data: ')) {
                                // 处理缓冲区中的最后一条消息
                                const lines = buffer.split('\n');
                                for (const line of lines) {
                                    if (line.startsWith('data: ')) {
                                        processMessage(line.substring(6));
                                    }
                                }
                            }
                            break;
                        }
                        
                        // 将二进制数据转换为文本
                        const chunk = new TextDecoder().decode(value);
                        buffer += chunk;
                        
                        // 按行拆分并处理
                        let newlineIndex;
                        while ((newlineIndex = buffer.indexOf('\n')) !== -1) {
                            const line = buffer.substring(0, newlineIndex);
                            buffer = buffer.substring(newlineIndex + 1);
                            
                            if (line.startsWith('data: ')) {
                                processMessage(line.substring(6));
                            }
                        }
                    }
                    
                    // 如果没有收到音频URL，也需要解析处理
                    if (!audioUrl && messages.length > 0) {
                        resolve({
                            text: messages.join(''),
                            messages,
                            audioUrl: null
                        });
                    }
                    
                } catch (error) {
                    console.error('上传音频失败:', error);
                    if (callbacks.onError) {
                        callbacks.onError(error);
                    }
                    reject(error);
                }
                
                // 处理消息
                function processMessage(dataStr) {
                    try {
                        const data = JSON.parse(dataStr);
                        
                        if (data.node_type === 'ai-chat-node') {
                            // 保留换行符
                            messages.push(data.content);
                            if (callbacks.onText) {
                                callbacks.onText(data.content);
                            }
                        } else if (data.node_type === 'text-to-speech-node') {
                            audioUrl = data.content;
                            if (callbacks.onAudio) {
                                callbacks.onAudio(data.content);
                            }
                            // 完成流处理
                            if (callbacks.onComplete) {
                                callbacks.onComplete({
                                    // 保留所有换行符
                                    text: messages.join(''),
                                    messages,
                                    audioUrl
                                });
                            }
                            resolve({
                                text: messages.join(''),
                                messages,
                                audioUrl
                            });
                        } else if (data.node_type === 'error-node') {
                            const error = new Error(data.content);
                            if (callbacks.onError) {
                                callbacks.onError(error);
                            }
                        }
                    } catch (e) {
                        console.error('解析SSE消息失败:', e, dataStr);
                        if (callbacks.onError) {
                            callbacks.onError(e);
                        }
                    }
                }
                
            } catch (error) {
                console.error('上传音频失败:', error);
                if (callbacks.onError) {
                    callbacks.onError(error);
                }
                reject(error);
            }
        });
    }

    static async sendTextMessage(userId, privateKey, message, chatId) {
        // 这个方法需要根据API实际情况进行实现
        // 目前文档中没有提及文本输入的接口，需要与后端确认
        console.warn('sendTextMessage API未实现，请与后端确认API规范');
    }
}

let authorization = ""






