<template>
    <div class="record-test-correct-body" v-if="correctArea && correctArea.popover" @click="correctArea.popover = false">
        <div class="correct-overlay" @click.stop="">
            <div class="correct-title">{{ correctArea.sno }}</div>
            <el-input v-model="correctArea.score"
                v-if="statisticType && correctArea.questionType == 1 && correctArea.isHandle == 1"
                @change="checkValue(correctArea)" style="margin-bottom: 12px;" placeholder="得分" size="medium"
                ref="inputRef">
                <template #prepend> <el-button @click="checkValue(correctArea, 0)">0</el-button></template>
                <template #append> <el-button @click="checkValue(correctArea, correctArea.allScore)">{{
                    correctArea.allScore
                        }}</el-button></template>

            </el-input>
            <el-button-group class="editBtns">
                <el-button type="primary" @click="updateBaseRes(1, correctArea)">
                    <img src="../assets/icon_correct.png" />
                </el-button>
                <el-button type="primary" v-if="correctArea.questionType == 1" @click="updateBaseRes(2, correctArea)">
                    <img src="../assets/icon_half.png" />
                </el-button>
                <el-button type="primary" @click="updateBaseRes(0, correctArea)">
                    <img src="../assets/icon_error.png" />
                </el-button>
            </el-button-group>
        </div>

    </div>
</template>

<script setup>
import { ClassroomRecordRequest } from '@/server_request/classroom_record';
import { useRecordDrawboardStore } from '@/stores/record_drawboard_store';
import { storeToRefs } from 'pinia';
import { watch } from 'vue';
const props = defineProps({
    taskId: String,
    statisticType: Number
})

const emits = defineEmits(['changeAnswer'])

function changeAnswer(){
    
    emits('changeAnswer')
}
const recordDrawboardStore = useRecordDrawboardStore()
const { correctArea } = storeToRefs(recordDrawboardStore)
let oldScore = 0 //原始分
watch(correctArea, () => {
    oldScore = correctArea.score
})
function checkValue(area, score) {
    if (score !== undefined) {
        area.score = score
    }
    if (area.score !== '') {
        if (area.score > area.allScore) {
            area.score = area.allScore
        } else if (area.score < 0) {
            area.score = 0
        }
    } else {
        area.score = oldScore
    }
    area.score = parseInt(area.score)
    area.isHandle = 1
    updateBaseScore(area)
}
// 主观题修改答案
async function updateBaseScore(area) {

    if (correctArea.value) {
        correctArea.value.popover = false
    }

    let params = {
        "taskId": props.taskId,
        "paperId": area.paperId,
        "reDrawImg": false,
        "correctInfo": [{
            "qid": area.qid,
            "studentId": area.studentId,
            "recogScore": area.score,
            "correctResult": area.markPoint,
            "pageId": area.pageId,
        }]
    }
    try {

        let { data } = await ClassroomRecordRequest.answerCorrectManual(params)
        if (area.finish) {
            area.finish(area)
        }

        changeAnswer()
        // this.calcScore(data)
    } catch (e) {
        //TODO handle the exception
    }
}
// 客观题修改答案
async function updateBaseRes(newMarkPoint, area) {
    if (correctArea.value) {
        correctArea.value.popover = false
    }

    if (area.markPoint != newMarkPoint || area.isHandle === 0) {
        let score = newMarkPoint == 0 ? 0 : (newMarkPoint == 1 ? area.allScore : area.allScore / 2)
        try {
            let params = {
                "taskId": props.taskId,
                "paperId": area.paperId,
                "reDrawImg": false,
                "correctInfo": [{
                    "qid": area.qid,
                    "studentId": area.studentId,
                    "recogScore": score,
                    "correctResult": newMarkPoint,
                    "pageId": area.pageId,
                }]
            }
            let { data } = await ClassroomRecordRequest.answerCorrectManual(params)
            area.markPoint = newMarkPoint
            area.score = score
            area.isHandle = 1
            if (area.finish) {
                area.finish(area)
            }

        changeAnswer()
            // this.calcScore(data)
        } catch (e) {
            console.log(e)
            //TODO handle the exception
        }
    }
}
</script>

<style lang="scss" scoped>
.record-test-correct-body {
    z-index: var(--record-test-correct-z-index);
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba($color: var(--boxshaw-main-color-rgb), $alpha: 0.4);

    .correct-overlay {
        position: fixed;
        bottom: 120px;
        display: flex;
        align-items: center;
        flex-direction: column;
        background-color: var(--main-bc-color);
        border-radius: 16px;
        box-sizing: border-box;
        padding: 16px 24px;

        .correct-title {
            font-size: 18px;
            line-height: 18px;
            color: var(--text-color);
            margin-bottom: 12px;
        }

        .editBtns {
            display: flex;

        }

        .editBtns img {
            width: 30px;
            margin: 0 auto;
        }

        .editBtns .type {
            font-size: 15px;
            margin-top: 10px;
        }

        .editBtns .type.right {
            color: var(--correct-color);
        }

        .editBtns .type.half {
            color: var(--unfinished-color);
        }

        .editBtns .type.wrong {
            color: var(--error-color);
        }
    }
}
</style>