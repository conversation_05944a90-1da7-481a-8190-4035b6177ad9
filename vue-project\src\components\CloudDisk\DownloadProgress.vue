<template>

    <div v-show="showProgress" class="full">
        <div class="container">
            <p class="title">下载中..</p>
            <el-progress class="progress" :text-inside="true" :stroke-width="26" :percentage="progress" />
            <img class="close" src="/img/icon_close.png" @click="cancelDownload"></img>
        </div>
    </div>

</template>


<script setup>
import { Alert } from '@/utils/alert';
import { onBeforeUnmount, onMounted, ref } from 'vue';
import { useDesktopStore } from "@/stores/desktop_store"
import { useClassroomUIStore } from '@/stores/classroom_ui_store'

const progress = ref(0)
const showProgress = ref(false)

onMounted(() => {
    if (window.electron) {
        window.electron.on('download-progress', (value) => {
            showProgress.value = true
            progress.value = value
        });

        window.electron.on('download-completed', (filePath) => {
            showProgress.value = false
            Alert.showSuccessMessage("已保存到下载文件夹")
            const desktopStore = useDesktopStore()
            desktopStore.showDesktopView()

            const classroomUIStore = useClassroomUIStore()
            classroomUIStore.showCloudDisk = false
        });

        window.electron.on('download-failed', (state) => {
            showProgress.value = false
            Alert.showErrorMessage("下载出现错误")
        });

        window.electron.on('download-canceled', () => {
            showProgress.value = false
            Alert.showErrorMessage("下载已取消")
        });
    }
})

onBeforeUnmount(() => {
    if (window.electron) {
        window.electron.removeAllListener('download-progress');
        window.electron.removeAllListener('download-completed');
        window.electron.removeAllListener('download-failed');
        window.electron.removeAllListener('download-canceled');
    }
})

function cancelDownload() {
    if (window.electron) {
        window.electron.send('cancel-download');
    }
    showProgress.value = false
}

</script>



<style scoped>
.full {
    background-color: var(--main-anti-bc-alpha-color);
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: var(--toolbar-cloud-disk-download-progress-z-index);
    display: flex;
    justify-content: center;
    align-items: center;
}

.container {
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    /* 垂直居中 */
    height: 100px;
    width: 25%;
    border-radius: 5px;
    background-color: white;
    position: relative;
}

.title {
    margin-top: 8px;
    margin-left: 3px;
}

.progress {
    margin-top: 20px;
}

.close {
    position: absolute;
    width: 20px;
    height: 20px;
    right: 8px;
    top: 8px;
    object-fit: cover;

}
</style>