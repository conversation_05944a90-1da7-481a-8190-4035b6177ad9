<template>
    <Dictation v-if="interact === Interact.dictation"></Dictation>
    <Paperpen v-if="interactStore.showPaperPenView()"></Paperpen>
    <MergedProbability v-if="interactStore.showPaperPenView() && showMergedDisplayView"></MergedProbability>
    <TemplateProbability></TemplateProbability>
    <MultiQuestions v-if="interact === Interact.multiQuestions"></MultiQuestions>
    <Choose v-if="isSingleObjective(interact)"></Choose>
    <div v-if="isFullScreen()" class="interact-practice-body">
        <RecordResult @hideInteract="hideInteract"></RecordResult>
    </div>
    <RBPAlert :zIndex="`${getZIndex('--word-listen-correct-z-index')}`"  width="1357px" height="846px" v-if="showRecordResult&&!isFullScreen()" @close="hideInteract" @click.stop>
        <template v-slot:rbpDiv>
            <RecordResult></RecordResult>
        </template>
    </RBPAlert>
    <PhetExpMenu></PhetExpMenu>
    <PhetExp></PhetExp>
</template>
<script setup>
import { defineProps, toRefs, defineEmits, onMounted, ref } from 'vue'
import { useInteractStore } from '@/stores/interact_store'
import { storeToRefs } from 'pinia'
import { Interact, isSingleObjective } from '@/classroom/interact_enums'
import Paperpen from './PaperPen/Paperpen.vue'
import Choose from './Choose/Choose.vue'
import MultiQuestions from './MultiQuestions/MultiQuestions.vue'
import Dictation from './Dictation/Dictation.vue'
import RecordResult from '@/components/ClassRoomRecord/RecordResult.vue'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { useClassroomStore } from '@/stores/classroom'
import MergedProbability from './PaperPen/MergedProbability.vue'
import { useProbabilityStore } from '@/stores/probability_store'
import TemplateProbability from '@/components/PaperPen/TemplateProbability.vue'
import RBPAlert from '@/components/baseComponents/RBPAlert.vue'
import { getZIndex } from '@/components/baseComponents/RBPZIndex.js'
import PhetExp from '@/components/PhetExp/PhetExp.vue'
import PhetExpMenu from '@/components/PhetExp/PhetExpMenu.vue'

const classroomUIStore = useClassroomUIStore()
const { showRecordResult } = storeToRefs(classroomUIStore)
const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)
const interactStore = useInteractStore()
const { interact } = storeToRefs(interactStore)

function isFullScreen(){
  let type = interactStore.interactResult.type
  
  return showRecordResult.value&&(type == Interact.paperPen ||  type == Interact.classTest ||  type == Interact.homework);
}

const probabilityStore = useProbabilityStore()

const { showMergedDisplayView } = storeToRefs(probabilityStore)

const hideInteract = () => {
    showRecordResult.value = false
    interactStore.interactResult = {}
}
</script>
<style lang="scss" scoped>
.interact-practice-body{
    
    width: 100vw;
    height: 100vh;
    position: fixed;
    z-index: var(--interact-practice-z-index);
}

</style>