import { BoardView } from "./board_view";
import * as THREE from 'three';
import { TextGeometry } from 'three/addons/geometries/TextGeometry.js';
import fontLoader from "./board_font";

export class BoardLabel extends BoardView {
    constructor(application, pos, size, text, style = { fontSize: 0.001, color: 0x000000, align: 'center' }) {
        super(application, pos, size);

        this.text = text
        this.style = style
        this.init()
    }

    init() {
        let textGeometry = new TextGeometry(this.text, {
            font: fontLoader.defaultFont,
            size: this.style.fontSize,
            depth: 0,  // 设置为 0，使文字成为平面
            bevelEnabled: false,  // 禁用斜角效果
        })

        let textMaterial = new THREE.MeshBasicMaterial({ color: this.style.color })
        this.textMesh = new THREE.Mesh(textGeometry, textMaterial)
        this.textMesh.renderOrder = 1
        this.add(this.textMesh)
        this.setTextAlign(this.style.align)
    }

    setFontSize(fontSize) {
        if (this.style.fontSize === fontSize) {
            return
        }
        this.style.fontSize = fontSize
        this.textMesh.geometry.dispose(); // 释放旧的几何体
        this.textMesh.geometry = new TextGeometry(this.text, {
            font: fontLoader.defaultFont,
            size: this.style.fontSize,
            depth: 0,  // 设置为 0，使文字成为平面
            bevelEnabled: false,  // 禁用斜角效果
       })
       this.animate()
    }

    setTextAlign(align) {
        let change = this.style.align !== align
        this.style.align = align
        this.textMesh.geometry.computeBoundingBox();
        const boundingBox = this.textMesh.geometry.boundingBox;
        const textHeight = boundingBox.max.y - boundingBox.min.y;
        const textWidth = boundingBox.max.x - boundingBox.min.x
        if (align === 'center') {
            this.textMesh.position.set(- textWidth / 2, - textHeight / 2, 0);
        }
        else if (align === 'left') {
            this.textMesh.position.set(- this.size.width / 2, - textHeight / 2, 0);
        }
        else if (align === 'right') {
            this.textMesh.position.set(this.size.width / 2 - textWidth, - textHeight / 2, 0);
        }
        if (change) {
            this.animate()
        }
    }

    setText(text) {
        this.text = text
        this.textMesh.geometry.dispose(); // 释放旧的几何体
        this.textMesh.geometry = new TextGeometry(this.text, {
            font: fontLoader.defaultFont,
            size: this.style.fontSize,
            depth: 0,  // 设置为 0，使文字成为平面
            bevelEnabled: false,  // 禁用斜角效果
        })
        this.setTextAlign(this.style.align)
        this.animate()
    }

    setTextColor(color) {
        this.style.color = color
        this.textMesh.material.color.set(color)
        this.animate()
    }

    onPointInside() {
        // label不响应事件
        return false
    }
}