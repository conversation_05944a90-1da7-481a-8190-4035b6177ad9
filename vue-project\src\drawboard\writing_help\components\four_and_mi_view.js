import { FourAndCharacterGridView } from "./four_and_character_view";

// 田字格视图 - 统一版
export class FourAndMiGridView extends FourAndCharacterGridView {

    constructor(application, pos, size, color,isNeedFirst) {
        // 初始化父类
        super(application, pos, size,color,isNeedFirst)
        // 竖线数量（不包括两端的边框线）
        this.verticalLines = 7        
        // 绘制田字格
        this.createMiGrid()
        

        
    }
   
    

    createMiGrid() {
        let app = this.application?.deref()
        this.lineWidth = app.cameraInitSize.width / 100

        // 计算实际内容区域的大小（排除按钮的空间和额外间距）
        let contentWidth = this.size.width 
        let contentHeight = this.size.height
        contentWidth = contentHeight * 4
        
        let offsetX = 0
        let offsetY = 0
        const spacing = contentWidth / (this.verticalLines + 1)
       const dashSize = contentWidth / 240
        
        // 添加对角线 - 现在我们需要为每个正方形添加对角线
        // 确定矩形区域内有8x2=16个正方形
        // 每个正方形的尺寸
        const squareWidth = spacing 
        const squareHeight = spacing
        // 遍历每一个正方形，添加对角线
        // 横向有8*2=16个正方形 (7条竖实线 + 8条竖虚线 = 15条线，分成16个区域)
        // 纵向有4个正方形区域 (1条横实线 + 2条横虚线 = 3条线，分成4个区域)
        
        // 起始X坐标 - 左侧边框之后
        let startX = offsetX - contentWidth/2
        
        // 遍历所有的正方形区域
        for (let col = 0; col < this.verticalLines + 1; col++) {
            // 计算当前正方形的左上角坐标
            let squareLeft = startX + col * spacing
            let squareTop = offsetY 
            
            // 计算右下角坐标
            let squareRight = squareLeft + squareWidth
            let squareBottom = squareTop - squareHeight
            
            // 添加从左上到右下的对角线 (\)
            this.createDashedLine(
                squareLeft,
                squareTop,
                squareRight,
                squareBottom,
                dashSize 
            )
            
            // 添加从右上到左下的对角线 (/)
            this.createDashedLine(
                squareRight,
                squareTop,
                squareLeft,
                squareBottom,
                dashSize 
            )
        }
    }

}
