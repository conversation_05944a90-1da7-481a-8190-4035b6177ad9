<template>
    <div class="record-pages">
        <div class="table-wrap" v-if="!showImgUrl">
            <div class="table" id="canvasWrap" v-if="tableHeight" :style="{ height: tableHeight + 'px' }">
                <div v-if="!selStu.length" class="empty">
                    请选择学生
                </div>
            </div>
            <div v-if="selStu.length" class="stus-paper">
                <RecordReviewStudents :stus="selStu"  ref="stuBoardRef" :taskId="taskId"  :statisticType="statisticType"></RecordReviewStudents>
            </div>
            <div class="stus-wrap">
                <img class="arrow" src="../assets/close.png" v-if="showStatus" @click="showStatus = !showStatus" />
                <img class="arrow" src="../assets/open.png" v-else @click="showStatus = !showStatus" />
                <div class="stus" v-show="showStatus">
                    <div class="students">
                        <div class="student" :class="{ active: isActive(item) }" v-for="(item) in students"
                            @click="setSelStu(item)">
                            {{ item.studentName }}
                        </div>
                    </div>
                    <div class="btns">
                        <div class="btn" @click="toogleMark()" :class="{ active: showMark }">
                            <img class="icon" src="../assets/showAnswer.png">
                            <div class="func">显示批改</div>
                        </div>
                        <div class="btn" @click="toogleAnswer()" :class="{ active: showAnswer }">
                            <img class="icon" src="../assets/showAnswer.png">
                            <div class="func">显示答案</div>
                        </div>
                        <div class="btn" @click="toogleOrder()" :class="{ active: showOrder }">
                            <img class="icon" src="../assets/showOrder.png">
                            <div class="func">作答顺序</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import $ from 'jquery'
import { onMounted, ref, watch, nextTick, onUnmounted } from 'vue';
import { ClassroomRecordRequest } from '@/server_request/classroom_record';
import RecordReviewStudents from './RecordBoard/RecordReviewStudents.vue';
const props = defineProps({
    classId: String,
    interactId: String,
    taskId: String,
    statisticType: Number
})
const emits = defineEmits(['setShowPlayer'])
//列表
const students = ref([])
const tableHeight = ref(0)
//选择的学生
const selStu = ref([])
const stuBoardRef = ref(null)

//展示控制活跃色
const showOrder = ref(false)
const showAnswer = ref(false)
const showMark = ref(true)
//试题高度
const paperHeight = ref(0)
const showImgUrl = ref('')
const paperImgs = ref([])
let playerInfo = null
let index = null
let params = null
const showStatus = ref(false)
const displacement = {
    scale: 1,
    getDistance: (start, stop) => {
        // Math.hypot()计算参数的平方根
        return Math.hypot(stop.x - start.x, stop.y - start.y);
    }
}

function setSelStu(item,isSame) {
    let index = selStu.value.findIndex(it => { return it.studentId == item.studentId })
    if (index > -1) {
        selStu.value.splice(index, 1)
        if(isSame){
            return
        }
    } else {
        if (selStu.value.length > 5) {
            selStu.value.shift()
            selStu.value.push(item)
        } else {
            selStu.value.push(item)
        }
    }
    nextTick(() => {
        if(stuBoardRef.value){
            stuBoardRef.value.getStudentPaper(item).then((value)=>{                
                if(!value){
                    //请求失败
                    setSelStu(item,true)
                }
                setTimeout(()=>{setToogleMark()},500)
            })
        }
        
    })

   
}
function isActive(item) {
    return selStu.value.find(it => { return it.studentId == item.studentId })
}

function getParams() {
    try {
        params = JSON.parse(localStorage.getItem('showImgUrlParams'))
        index = params.index
    } catch (e) {
        console.log(e)
        //TODO handle the exception
    }
}




function showImg(showImgU, playerI, fromChild) {
    if (showImgU) {
        showImgUrl.value = showImgU
    } else {
        showImgUrl.value = ''
        setToogle()
    }
    playerInfo = playerI
    if (fromChild) {
        getParams()
    }
}
async function getPaperImgs() {
    try {
        let { data } = await ClassroomRecordRequest.teacherInteract({ classId: props.classId, interactId: props.interactId })
        paperImgs.value = data.pagesImgs
    } catch (e) {
        //TODO handle the exception
    }
}
async function getTable() {
    try {
        let { data } = await ClassroomRecordRequest.studentInteractDeails({ classId: props.classId, interactId: props.interactId })
        students.value = data
        nextTick(() => {
            paperHeight.value = tableHeight.value = window.innerHeight - document.getElementById('foot').offsetHeight
        })
        data&&data.length&&data[0] && setSelStu(data[0])
    } catch (e) {
        console.log("----------------", e);

        //TODO handle the exception
    }
}
function toogleOrder() {
    showOrder.value = !showOrder.value
    if(stuBoardRef.value){
        stuBoardRef.value.setShowOrder(showOrder.value)
    }
}
function toogleAnswer() {
    showAnswer.value = !showAnswer.value
    if(stuBoardRef.value){
        stuBoardRef.value.setShowAnswer(showAnswer.value)
    }
}
function toogleMark() {
    showMark.value = !showMark.value
    if(stuBoardRef.value){
        stuBoardRef.value.setShowMark(showMark.value)
    }
}
function setToogleMark(){
    if(stuBoardRef.value){
        stuBoardRef.value.setShowMark(showMark.value)
        stuBoardRef.value.setShowAnswer(showAnswer.value)
        stuBoardRef.value.setShowOrder(showOrder.value)
    }
}



onMounted(() => {
    getTable()
    getPaperImgs()
    $('.foot-show-item-full').hide()
})
onUnmounted(()=>{
    $('.foot-show-item-full').show()
})
watch(() => showImgUrl.value, () => {
    if (showImgUrl.value) {
        $('.foot-show-item').hide()
    } else {
        $('.foot-show-item').show()
    }
})
defineExpose({ showImg })
</script>

<style lang="scss" scoped>
.record-pages {
    height: 100%;
    width: 100%;

    .img-mask {
        position: fixed;
        width: 100vw;
        height: calc(100vh - 75px);
        top: 0;
        left: 0;
        background-color: #0B423D;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .el-icon {
        font-size: 100px;
        color: rgba(255, 255, 255, 0.1);
        position: fixed;
        
        // background-color: aqua;
    }

    .paper-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        // background-color: var(--main-bc-color);
    }

    .paper-con {
        display: flex;
        // width: 100%;
        overflow: auto;
    }

    .icon {
        width: 25px;
        height: 25px;
        margin-bottom: 5px;
    }

    .func {
        word-break: keep-all;
    }

    .btn.active {
        background-color: var(--main-bc-color);
    }

    .table-wrap {
        height: 100%;
        display: flex;
        flex-direction: column;
        position: relative;
    }

    .empty {
        font-size: 100px;
        margin-top: 10vh;
    }

    .table {
        flex: 1;
    }

    .stus-wrap {
        position: absolute;
        bottom: 0;
        z-index: 100;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
    .stus-paper {
        position: absolute;
        bottom: top;
        z-index: 80;
        width: 100%;
        height: 100%;
        background-color: #0B423D;
    }

    .arrow {
        vertical-align: middle;
        position: relative;
        top: 1px;
        width: 100px;
    }

    .stus {
        width: 100%;
        padding: 15px;
        background-color: var(--secondary-color);
        text-align: left;
        display: flex;
        padding-bottom: 0;
        box-sizing: border-box;
    }

    .btns {
        display: flex;
        flex-direction: column;
        background-color: var(--toolbar-bg-color);
        height: fit-content;
        border-radius: 8px;
        padding: 15px 10px;
        padding-bottom: 0;
        font-size: 14px;
    }

    .btn {
        padding: 10px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: var(--text-color);
        border-radius: 4px;
        margin-bottom: 5px;
    }

    .students {
        flex: 1;
    }

    .student {
        // width: 75px;
        padding: 0 15px;
        height: 45px;
        display: inline-block;
        text-align: center;
        line-height: 45px;
        background-color: var(--main-bc-color);
        border-radius: 8px;
        margin-bottom: 15px;
        margin-right: 15px;
        font-size: 15px;
        overflow: hidden;
    }

    .student.active {
        background-color: var(--primary-color);
        color: var(--anti-text-color);
    }

    .stu-name {
        // width: 75px;
        padding: 0 15px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--primary-color);
        border-radius: 8px;
        position: absolute;
        left: 10px;
        top: 10px;
        color: var(--anti-text-color);
        font-size: 15px;
        z-index: 10;
    }
}
</style>