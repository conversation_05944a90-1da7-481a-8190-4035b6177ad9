import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig({
  publicPath: '/',
  base: './',
  plugins: [
    vue(),
  ],
  server: {
    // port: 5173,
    // proxy: {
    //   '/phet': {
    //     target: 'https://robotpen.com',
    //     changeOrigin: true,
    //     rewrite: (path) => path.replace(/^\/phet/, '/phet')
    //   }
    // }
  },
  // build: {  //调试关闭混淆 不要提交上去！！
  //   minify: false,
  //   terserOptions: {
  //     compress: false,
  //     mangle: false
  //   }
  // },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  }
})
