<template>
    <div class="container">
        <div class="chart" id="merged-probability">
        </div>
        <div class="close" @click="handleCloseClick">关闭</div>
    </div>
</template>


<script setup>
import { onMounted, ref, watch } from 'vue';
import * as echarts from 'echarts';
import { useProbabilityStore } from '@/stores/probability_store';
import { storeToRefs } from 'pinia';

const probabilityStore = useProbabilityStore()

const { mergedXValues, mergedDataValues, showMergedDisplayView } = storeToRefs(probabilityStore)

const chart = ref(null)

onMounted(() => {
    let div = document.querySelector('#merged-probability');
    // 使用 ECharts 初始化图表
    chart.value = echarts.init(div);
    updateChartsOption()
})

watch(showMergedDisplayView, (newValue) => {
    if (newValue) {
        updateChartsOption()
    }
})

// 更新设置
function updateChartsOption() {
    var option = {
        backgroundColor: '#FFFFFF',  // 设置背景色为白色
        title: {
            text: '合并结果',
            textStyle: {
                fontSize: 20,  // 设置标题字体大小
                fontWeight: 'normal',
                color: '#333',
            },
        },
        tooltip: {},
        xAxis: {
            data: mergedXValues.value,
            axisLabel: {
                fontSize: 26,  // 设置 X 轴字体大小为 16
                color: '#000', // 设置 X 轴字体颜色
            },
        },
        yAxis: {
            min: 0,   // Y 轴的最小值
            max: 1,   // Y 轴的最大值
            interval: 0.25,  // 设置 Y 轴的刻度间隔为 0.25
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['#ccc'],
                    type: 'dashed',  // 设置辅助线为虚线
                },
            },
            axisLabel: {
                fontSize: 26,  // 设置 Y 轴字体大小为 16
                color: '#000', // 设置 Y 轴字体颜色
                formatter: function (value) {
                    return value.toFixed(2); // 保证每个刻度显示两位小数
                },
            },
        },
        series: [{
            name: '概率',
            type: 'line',  // 折线图
            data: mergedDataValues.value, // 0 到 1 的比例数据
            smooth: false,  // 平滑折线
            lineStyle: {
                width: 6,
                color: '#ff6347',  // 设置折线的颜色
            },
            symbolSize: 16,  // 设置折点的大小
            itemStyle: {
                color: '#ff6347',  // 设置折线的点的颜色
            },
            label: {
                show: true, // 显示数据点上的数值
                position: 'top', // 数据标签显示在点的上方
                color: '#000',  // 设置数值的颜色
                fontSize: 26, // 设置数值的字体大小
                formatter: function (params) {
                    return params.value.toFixed(2); // Format to 2 decimal places
                }
            }
        }]
    };
    chart.value.setOption(option);
}

function handleCloseClick() {
    probabilityStore.showMergedDisplayView = false
}

</script>


<style scoped>
.container {
    position: absolute;
    z-index: var(--toolbar-test-z-index);
    width: 60%;
    height: 50%;
    bottom: 120px;
    right: 20%;
    background-color: white;
    border: 3px solid lightblue;
}

.chart {
    width: 100%;
    height: 100%;
}

.close {
    position: absolute;
    z-index: 4;
    top: 2px;
    right: 2px;
    width: 5%;
    /* 可以调整按钮的大小 */
    height: 2.5%width;
    /* 可以调整按钮的大小 */
    color: black;
    background-color: darkcyan;
    color: white;
    /* 设置按钮的字体颜色 */
    text-align: center;
    line-height: 40px;
    /* 让文字垂直居中 */
    cursor: pointer;
    border-radius: 6px;
}
</style>