<template>
    <div class="record-test-table" id="canvasWrap" v-if="tableHeight"  :style="{ height: tableHeight + 'px' }">
        <el-table :data="tableData" stripe style="width: 100%" :height="tableHeight + 'px'" empty-text="暂无数据"
            :default-sort="{ prop: statisticType ? 'score' : 'correctRatio', order: 'descending' }"
            header-row-class-name="elemnt-header" @sort-change="sortChange">
            <el-table-column prop="studentName" :label="'姓名'" sortable :sort-method="sortName"
                :sort-orders="['descending', 'ascending', null]" align="center"></el-table-column>
            <el-table-column prop="groupName" :label="'小组'" sortable :sort-orders="['descending', 'ascending', null]"
                align="center"></el-table-column>
            <el-table-column v-if="statisticType" prop="score" :label="'分数'" sortable sort-by="score"
                :sort-orders="['descending', 'ascending', null]" align="center"></el-table-column>
            <el-table-column v-else prop="correctRatio" :label="'正确率'" sortable
                :sort-orders="['descending', 'ascending', null]" align="center">
                <template #default="scope"> {{ parseInt(scope.row.correctRatio * 100) + '%' }} </template>
            </el-table-column>
            <el-table-column prop="totalTime" :label="'用时'" sortable sort-by="times"
                :sort-orders="['descending', 'ascending', null]" align="center"></el-table-column>
            <el-table-column prop="startTime" :label="'开始时间'" sortable="custom"
                :sort-orders="['descending', 'ascending', null]" align="center">
                <template #default="scope"> {{ formatTime(scope.row, 'startTime') }} </template>
            </el-table-column>
            <el-table-column prop="endTime" :label="'提交时间'" sortable="custom"
                :sort-orders="['descending', 'ascending', null]" align="center">
                <template #default="scope"> {{ formatTime(scope.row, 'endTime') }} </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup>
import $ from 'jquery'
import { ref, nextTick, onMounted, onUnmounted } from 'vue';
import { ClassroomRecordRequest } from '@/server_request/classroom_record';
const props = defineProps({
    classId: String,
    interactId: String,
    taskId: String,
    statisticType: Number
})

//表格数据
const tableData = ref([])
let oldTableData = []//保留原数据 防止因为排序而打乱
const tableHeight = ref(0)
//获取数据
async function getTable() {
    try {
        let { data } = await ClassroomRecordRequest.studentInteractDeails({ classId: props.classId, interactId: props.interactId })
        data.forEach((item) => {
            // 0:未作答 1：作答中 2和3：已作答
            if (item.answerStatus == 0) {
                item.totalTime = "未作答";
                item.times = -1
            } else if (item.answerStatus == 1) {
                item.totalTime = "作答中";
            }
            if (item.times === undefined) {
                item.times = 0
            }
            if (item.totalTime === '') {
                item.totalTime = '--'
            }
            item.times = Number(item.times.toFixed(5))
            item.groupName = item.groupName || '--'
        });
        tableData.value = data
        oldTableData = data
        nextTick(() => {

            tableHeight.value = window.innerHeight - document.getElementById('foot').offsetHeight
        })
    } catch (e) {
        //TODO handle the exception
    }
}
//排序 
function sortChange(column) {
    let tableDataSort = sort(column.prop, column.order)
    tableData.value = tableDataSort
}
function sort(key, order) {
    //生成新的对象
    let oldTableD = JSON.parse(JSON.stringify(oldTableData))
    oldTableD.forEach(item => {
        if (!item[key]) {
            item[key] = 0
        }
    })
    if (order == 'descending') {
        return oldTableD.sort((a, b) => {
            return b[key] - a[key]
        })
    } else if (order == 'ascending') {
        return oldTableD.sort((a, b) => {
            return a[key] - b[key]
        })
    } else {
        return oldTableD
    }
}
function sortName(a, b) {
    a = String(a.studentName);
    b = String(b.studentName);
    // localeCompare()方法返回一个数字，指示引用字符串是在排序顺序之前还是之后，或者与排序顺序中的给定字符串相同，
    // zh-Hans-CN是简体中文的排序规则，sensotivity 是灵敏度，包括 base、accent、case、variant这几种灵敏度
    return a.toString().localeCompare(b, "zh-Hans-CN", { sensitivity: "accent" });
}
function formatTime(item, key) {
    let time = item[key]
    if (time) {
        return `${time.slice(4, 6)}-${time.slice(6, 8)} ${time.slice(8, 10)}:${time.slice(10, 12)}:${time.slice(12)}`
    } else {
        return '--'
    }
}
onMounted(() => {
    getTable()
    $('.foot-draw-item').hide()
})
onUnmounted(()=>{
    $('.foot-draw-item').show()
})
</script>
<style type="text/css" lang="scss">
.record-test-table {
    .el-table__header-wrapper {
        position: absolute;
        bottom: 0;
        z-index: 100;
        height: 48px;
    }

    .el-table__cell {
        background-color: transparent !important; /* 设置 hover 背景色为浅蓝色 */
    }
    .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
        background: var(--list-bc-color) !important;
        color: var(--secondary-text-color);
    }
    .el-scrollbar__wrap {
        display: flex;
        flex-direction: column-reverse;
        // justify-content: end;
    }

    .el-table__body-wrapper {
        padding-bottom: 48px;
        height: 100%;
        /* overflow: scroll !important; */
        display: flex;
        border: none;
    }
    .el-table td {
        font-size: 16px;
        color: var(--text-color);
    }


    .el-table th {
        background-color: var(--secondary-color) !important;
        height: 48px;
        font-weight: 600;
        font-size: 16px;
        color: var(--text-color);
        
    }
    .el-table {
        .ascending .sort-caret.ascending {
            border-bottom-color: var(--primary-color);
        }
        .descending .sort-caret.descending {
            border-top-color: var(--primary-color);
        }
        //descending
    }

    .el-table td,
    .el-table th.is-leaf {
        border: none !important;
    }
}
</style>

<style lang="scss" scoped>
.record-test-table {
    display: block;
}
</style>