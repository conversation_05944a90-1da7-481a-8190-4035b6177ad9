import { createRouter, createWebHashHistory } from 'vue-router'
import LoginView from '../views/LoginView.vue'
// import BluetoothAp from '@/components/BluetoothAp/BluetoothAp.vue'
import SingleBoard from '@/components/ClassRoomRecord/components/SingleBoard.vue'
const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      name: 'login',
      component: LoginView
    },
    {
      path: '/active',
      name: 'active',
      component: () => import('@/views/ActiveView.vue')
    },
    {
      path: '/manage',
      name: 'manage',
      component: () => import('../views/ClassManage.vue')
    },
    {
      path: '/classroom',
      name: 'classroom',
      component: () => import('@/views/ClassRoom.vue')
    },
    // 课程记录
    {
      path: '/classRecord',
      name: 'classRecord',
      component: () => import('@/components/ClassRoomRecord/ClassRoomRecord.vue')
    },
    //云盘全屏播放
    {
      path: '/cloudDisk/video',
      name: 'cloudDiskVideo',
      component: () => import('@/components/CloudDisk/VideoPlayView.vue')
    },
    {
      path: '/single',
      name: 'classrooms',
      component: () => SingleBoard
    },
  ]
})

export default router
