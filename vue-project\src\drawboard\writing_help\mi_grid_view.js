import { CharacterGridView } from "./character_grid_view";
// 田字格视图 - 统一版
export class MiGridView extends CharacterGridView {

    constructor(application, pos, size, color,copyCallBack,type) {
        // 初始化父类
        super(application, pos, size,color,copyCallBack,type)
        // 竖线数量（不包括两端的边框线）
        this.verticalLines = 7        
        // 绘制田字格
        this.createMiGrid()
        

        
    }
   
    

    createMiGrid() {
        let app = this.application?.deref()
        this.lineWidth = app.cameraInitSize.width / 100

        // 计算实际内容区域的大小（排除按钮的空间和额外间距）
        let contentWidth = this.size.width - this.buttonSize * this.spacingFactor
        let contentHeight = this.size.height
        contentWidth = contentHeight * 4
        const dashSize = contentWidth / 240
        let offsetX = -this.buttonSize * this.spacingFactor / 2
        let offsetY = 0
        // 计算横线之间的间距（将高度分为4等份）
        const horizontalSpacing = contentHeight / 4
        // 添加对角线 - 现在我们需要为每个正方形添加对角线
        // 确定矩形区域内有8x2=16个正方形
        // 每个正方形的尺寸
         // 绘制内部的7条均分竖线
         const spacing = contentWidth / (this.verticalLines + 1)
        const squareWidth = spacing 

        const squareHeight = horizontalSpacing * 2
        
        // 遍历每一个正方形，添加对角线
        // 横向有8*2=16个正方形 (7条竖实线 + 8条竖虚线 = 15条线，分成16个区域)
        // 纵向有4个正方形区域 (1条横实线 + 2条横虚线 = 3条线，分成4个区域)
        
        // 起始X坐标 - 左侧边框之后
        let startX = offsetX - contentWidth/2
        
        // 遍历所有的正方形区域
        for (let col = 0; col < this.verticalLines + 1; col++) {
            // 每列有两个区域，每个区域中有1个正方形
            for (let row = 0; row < 2; row++) {
                // 计算当前正方形的左上角坐标
                let squareLeft = startX + col * spacing
                let squareTop = offsetY + contentHeight/2 - row * squareHeight
                
                // 计算右下角坐标
                let squareRight = squareLeft + squareWidth
                let squareBottom = squareTop - squareHeight
                
                // 添加从左上到右下的对角线 (\)
                this.createDashedLine(
                    squareLeft,
                    squareTop,
                    squareRight,
                    squareBottom,
                    dashSize 
                )
                
                // 添加从右上到左下的对角线 (/)
                this.createDashedLine(
                    squareRight,
                    squareTop,
                    squareLeft,
                    squareBottom,
                    dashSize 
                )
            }
        }
    }
}
