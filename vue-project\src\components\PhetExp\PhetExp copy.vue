<template>
    <div class="phet-exp-container" v-if="showPhetExp">
        <div class="phet-exp">
            <iframe :src="url" width="100%" height="100%" frameborder="0"></iframe>
        </div>
        <div class="back-content">
            <span class="file-name">phet实验室</span>
            <div class="back-area" @click="showPhetExp = false">
                <img src="/img/svg/icon_exit.svg">
            </div>
        </div>
    </div>
</template>
<script setup>
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { storeToRefs } from 'pinia'
import { onMounted } from 'vue';
import { usePhetExpStore } from '@/stores/phet_exp_store'

const phetExpStore = usePhetExpStore()
const { url } = storeToRefs(phetExpStore)

const classroomUIStore = useClassroomUIStore()
const { showPhetExp } = storeToRefs(classroomUIStore)


</script>
<style lang="scss" scoped>
.phet-exp-container {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    position: fixed;
    background-color: var(--main-bc-color);
    z-index: var(--toolbar-phet-z-index);
    display: flex;
    flex-direction: column;

    .phet-exp {
        flex: 1;
        width: 100%;
    }

    .back-content {
        display: flex;
        height: 64px;
        background-color: var(--main-anti-bc-color);
        align-items: center;
        justify-content: space-between;
        padding: 0px 16px;

        .file-name {
            font-size: 18px;
            color: var(--anti-text-color);
        }

        .back-area {
            cursor: pointer;

            img {
                width: 48px;
                height: 48px;
            }
        }
    }
}
</style>