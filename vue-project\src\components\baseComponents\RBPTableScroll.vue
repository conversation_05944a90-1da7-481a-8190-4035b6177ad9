<template>
    <div class="rbp-table-scroll">
        <div class="item" v-for="(item, index) in props.list" :key="index" 
        :class="{ active: props.reverseColor ? index % 2 == 0 : index % 2 == 1 }">
            <slot name="rbpDiv" :data="item" :index="index"></slot>
        </div>
    </div>
</template>
<script setup>
import { ref, defineProps } from "vue";
import { RBPColors } from '@/components/baseComponents/RBPColors.js'

const props = defineProps({
    list: {
        type: Array,
        default: [],
    },
    reverseColor: {
        type: Boolean,
        default: false,
    }
})
</script>
<style lang="scss" scoped>
@import '@/assets/scss/mixin.scss';

.rbp-table-scroll {
    width: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;

    .item {
        width: 100%;
        background-color: var(--main-bc-color);
    }

    .active {
        background-color: var(--table-interval-color);
    }
}
</style>