import { BoardTool } from "../board_tool";

import { WritingGridView } from "./writing_grid_view";

// 五线谱视图 - 统一版
export class MusicalStaff extends WritingGridView {

    constructor(application, pos, size, color,copyCallBack,type) {
         // 初始化父类
         super(application, pos,size,color,copyCallBack,type)
         
        
        // 绘制五线谱
        this.createStaff()

       
    }

    createStaff() {
        // 清除之前的线条
        BoardTool.disposeGroup(this.lineGroup)
        this.lineGroup.clear()

        let app = this.application?.deref()
        this.lineWidth = app.cameraInitSize.width / 100

       // 计算实际内容区域的大小（排除按钮的空间和额外间距）
       let contentWidth = this.size.width - this.buttonSize * this.spacingFactor
       let contentHeight = this.size.height * 15 /20 /2
       let gap = this.size.height  - contentHeight * 2 
       contentWidth = contentHeight * 8
       let offsetX = -this.buttonSize * this.spacingFactor/ 2
       let offsetY = 0

       // 计算四条水平线的位置
       const lineSpacing = contentHeight / 4
       
       //上面四条线
       for(let i = 0;i<5;i++){
           let y = offsetY + gap/2 + lineSpacing * i
           // 底线
           this.createLine(
               offsetX - contentWidth/2,y, 
               offsetX + contentWidth/2, y
           )
       }
       //下面四条线
       for(let i = 0;i<5;i++){
           let y = offsetY - gap/2 - lineSpacing * i
           // 底线
           this.createLine(
               offsetX - contentWidth/2,y, 
               offsetX + contentWidth/2, y
           )
       }
       
    }

}
