import { BoardButton } from "../board_button";
import { BoardView } from "../board_view";
import * as THREE from 'three';
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial.js';
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry.js';
import { Line2 } from 'three/examples/jsm/lines/Line2.js';
import { BoardTool } from "../board_tool";
import { LineView } from "./line_view";
import { QuadrilateralType, QuadrilateralView } from "./quadrilateral_view";
import { DrawZ, PainterOrder } from "../draw_enums";
import { MathGeometry } from "./math_root_view";

const MoveTag = {
    none: -1,
    a: 0,
    b: 1,
    c: 2,
    d: 3,
    self: 4 // 移动整个图形
}

export class CircleView extends BoardView {

    constructor(type,application, pos, size, color, circle = true) {

        super(application, pos, size)

        this.renderOrder = PainterOrder.customDisplay

        this.onEdit = false
        this.type = type
        // 是否圆形 限制为正方形就只能是圆，不限制则可以是椭圆
        this.isCircle = circle

        this.centerPoint = { x: 0, y: 0 }

        this.color = color

        this.abRadius = this.size.width / 2
        this.acRadius = this.size.height / 2
        //顶点
        this.aPoint = { x: -0.5 * size.width, y: 0.5 * size.height }

        this.bPoint = { x: 0.5 * size.width, y: 0.5 * size.height }

        this.cPoint = { x: -0.5 * size.width, y: -0.5 * size.height }

        this.dPoint = { x: 0.5 * size.width, y: -0.5 * size.height }

        // 创建材质：线条材质
        const material = new LineMaterial({
            color: color ?? 0x00ff00,        // 边框颜色
            linewidth: 5,           // 线条宽度
            dashed: false,          // 不使用虚线
        });
        let stringApplication = application?.deref()
        this.lineWidth = stringApplication.cameraInitSize.width / 80
        let lineGeometry = new LineGeometry()
        let points = this.getCirclePoints()

        lineGeometry.setPositions(points);
        // 创建 Line2 对象
        const line = new Line2(lineGeometry, material);
        line.computeLineDistances();  // 必须计算线段的距离，以便正确渲染

        let lineGroup = new THREE.Group()
        lineGroup.add(line)
        lineGroup.renderOrder = PainterOrder.customDisplay
        this.lineGroup = lineGroup
        this.add(lineGroup);


        // 创建一个实心圆(圆心)
        const radius = this.lineWidth / 4; // 圆的半径
        const segments = 128; // 圆的细分数，越多越平滑
        const circleGeometry = new THREE.CircleGeometry(radius, segments); // 创建圆的几何体
        const centerMaterial = new THREE.MeshBasicMaterial({ color: color ?? 0x00ff00 }); // 绿色材质
        const circleMesh = new THREE.Mesh(circleGeometry, centerMaterial); // 创建网格

        lineGroup.add(circleMesh);

        this.line = line

        this.setupEditViews()

        this.setupToolViews()
    }

    setupEditViews() {

        let application = this.application?.deref()
        let areaWidth = application.cameraInitSize.width / 40
        let dragAreaSize = { width: areaWidth, height: areaWidth }
        this.dragAreaSize = dragAreaSize

        let aVertexView = new BoardView(
            this.application,
            new THREE.Vector3(this.aPoint.x, this.aPoint.y, 0),
            dragAreaSize)
        aVertexView.renderOrder = 1
        aVertexView.setBackgroundColor(0xff6347)
        aVertexView.visible = false
        aVertexView.setRenderOrder(PainterOrder.customDisplay)
        this.aVertexView = aVertexView
        this.addSubView(aVertexView)

        let bVertexView = new BoardView(
            this.application,
            new THREE.Vector3(this.bPoint.x, this.bPoint.y, 0),
            dragAreaSize)
        bVertexView.renderOrder = 1
        bVertexView.setBackgroundColor(0xff6347)
        bVertexView.visible = false
        bVertexView.setRenderOrder(PainterOrder.customDisplay)
        this.bVertexView = bVertexView
        this.addSubView(bVertexView)

        let cVertexView = new BoardView(
            this.application,
            new THREE.Vector3(this.cPoint.x, this.cPoint.y, 0),
            dragAreaSize)
        cVertexView.renderOrder = 1
        cVertexView.setBackgroundColor(0xff6347)
        cVertexView.visible = false
        cVertexView.setRenderOrder(PainterOrder.customDisplay)
        this.cVertexView = cVertexView
        this.addSubView(cVertexView)

        let dVertexView = new BoardView(
            this.application,
            new THREE.Vector3(this.dPoint.x, this.dPoint.y, 0),
            dragAreaSize)
        dVertexView.renderOrder = 1
        dVertexView.setBackgroundColor(0xff6347)
        dVertexView.visible = false
        dVertexView.setRenderOrder(PainterOrder.customDisplay)
        this.dVertexView = dVertexView
        this.addSubView(dVertexView)

        let viewButton = new BoardButton(
            this.application,
            new THREE.Vector3(0, 0, 0),
            { width: this.size.width + this.dragAreaSize.width * 2, height: this.size.height + this.dragAreaSize.width * 2 })
        viewButton.renderOrder = PainterOrder.customDisplay - 1
        viewButton.onClick(() => {
            const edit = !this.onEdit
            this.setOnEdit(edit)
            if (edit) {
                this.superView.deref().cancelEditWithout(this)
            }
        })
        this.viewButton = viewButton
        this.addSubView(viewButton)
    }

    setupToolViews() {

        let app = this.application?.deref()
        let height = app.cameraInitSize.width / 40
        let itemHeight = height * 0.8
        let itemWidth = itemHeight
        let width = itemHeight * 3.5
        let space = itemHeight * 1.5 / 3

        let startX = - width * 0.5

        this.toolWidth = width
        this.toolHeight = height
        this.toolItemWidth = itemWidth
        let toolView = new BoardView(
            this.application,
            new THREE.Vector3(this.dPoint.x + this.toolItemWidth * 2, this.dPoint.y + height * 2, 0),
            { width, height })
        let deleteButton = new BoardButton(
            this.application,
            new THREE.Vector3(startX + space + itemWidth / 2, 0, 0),
            { width: itemWidth, height: itemHeight }, true)
        deleteButton.setImage('img/math/delete.svg')
        deleteButton.onClick(() => {
            this.removeFromSuperView()
            this.dispose()
        })
        toolView.addSubView(deleteButton)


        let selectorButton = new BoardButton(
            this.application,
            new THREE.Vector3(startX + space * 2 + itemWidth * (2 - 0.5), 0, 0),
            { width: itemWidth, height: itemHeight }, true)
        selectorButton.setImage('img/math/selector.svg')
        selectorButton.onClick(() => {
            this.showOptionsView(!(this.optionsView?.visible ?? false))
        })
        toolView.addSubView(selectorButton)

        // toolView.setBackgroundColor(0x0000ff)
        toolView.setRenderOrder(PainterOrder.customDisplay + 1)

        toolView.visible = false
        this.addSubView(toolView)
        this.toolView = toolView
    }

    showOptionsView(show) {
        if (!this.optionsView) {
            let app = this.application?.deref()
            let width = app.cameraInitSize.width / 20
            let itemHeight = app.cameraInitSize.width / 40
            let height = itemHeight * (this.isCircle ? 4 : 5)

            this.optionsWidth = width
            this.optionsHeight = height
            this.optionsItemHeight = itemHeight
            this.optionsView = new BoardView(
                this.application,
                new THREE.Vector3(
                    this.dVertexView.position.x + this.dVertexView.size.width + width / 2,
                    this.toolView.position.y - this.toolHeight / 2 - this.optionsHeight / 2,
                    0
                ),
                { width, height })
            this.optionsView.setRenderOrder(PainterOrder.customDisplay + 1)
            this.addSubView(this.optionsView)

            let startY = height / 2

            if (this.isCircle) { //圆形
                let radiusButton = new BoardButton(
                    this.application,
                    new THREE.Vector3(0, startY - itemHeight / 2, 0),
                    { width, height: itemHeight })
                radiusButton.setFontSize(0.015)
                radiusButton.setTextAlign('left')
                radiusButton.setTextColor(0xffffff)
                radiusButton.setText("作半径")
                radiusButton.onClick(() => {
                    this.handleDrawRadius()
                    this.optionsView.visible = false
                })
                radiusButton.setBackgroundColor(0xFF6347)
                this.optionsView.addSubView(radiusButton)


                let diameterButton = new BoardButton(
                    this.application,
                    new THREE.Vector3(0, startY - itemHeight / 2 - itemHeight, 0),
                    { width, height: itemHeight })
                diameterButton.setFontSize(0.015)
                diameterButton.setTextAlign('left')
                diameterButton.setTextColor(0xffffff)
                diameterButton.setText("作直径")
                diameterButton.onClick(() => {
                    this.handleDrawDiameter()
                    this.optionsView.visible = false
                })
                diameterButton.setBackgroundColor(0xFF6347)
                this.optionsView.addSubView(diameterButton)


                let cutinsideButton = new BoardButton(
                    this.application,
                    new THREE.Vector3(0, startY - itemHeight / 2 - itemHeight * 2, 0),
                    { width, height: itemHeight })
                cutinsideButton.setFontSize(0.015)
                cutinsideButton.setTextAlign('left')
                cutinsideButton.setTextColor(0xffffff)
                cutinsideButton.setText("内切正方形")
                cutinsideButton.onClick(() => {
                    this.handleDrawCutinside()
                    this.optionsView.visible = false
                })
                cutinsideButton.setBackgroundColor(0xFF6347)
                this.optionsView.addSubView(cutinsideButton)



                let circumscribedButton = new BoardButton(
                    this.application,
                    new THREE.Vector3(0, startY - itemHeight / 2 - itemHeight * 3, 0),
                    { width, height: itemHeight })
                circumscribedButton.setFontSize(0.015)
                circumscribedButton.setTextAlign('left')
                circumscribedButton.setTextColor(0xffffff)
                circumscribedButton.setText("外切正方形")
                circumscribedButton.onClick(() => {
                    this.handleDrawCircumscribed()
                    this.optionsView.visible = false
                })
                circumscribedButton.setBackgroundColor(0xFF6347)
                this.optionsView.addSubView(circumscribedButton)

            }
            else {
                let shortAxisButton = new BoardButton(
                    this.application,
                    new THREE.Vector3(0, startY - itemHeight / 2, 0),
                    { width, height: itemHeight })
                shortAxisButton.setFontSize(0.015)
                shortAxisButton.setTextAlign('left')
                shortAxisButton.setTextColor(0xffffff)
                shortAxisButton.setText("作短轴")
                shortAxisButton.onClick(() => {
                    this.handleDrawAxis(false)
                    this.optionsView.visible = false
                })
                shortAxisButton.setBackgroundColor(0xFF6347)
                this.optionsView.addSubView(shortAxisButton)


                let longAxisButton = new BoardButton(
                    this.application,
                    new THREE.Vector3(0, startY - itemHeight / 2 - itemHeight, 0),
                    { width, height: itemHeight })
                longAxisButton.setFontSize(0.015)
                longAxisButton.setTextAlign('left')
                longAxisButton.setTextColor(0xffffff)
                longAxisButton.setText("作长轴")
                longAxisButton.onClick(() => {
                    this.handleDrawAxis(true)
                    this.optionsView.visible = false
                })
                longAxisButton.setBackgroundColor(0xFF6347)
                this.optionsView.addSubView(longAxisButton)


                let focalLenButton = new BoardButton(
                    this.application,
                    new THREE.Vector3(0, startY - itemHeight / 2 - itemHeight * 2, 0),
                    { width, height: itemHeight })
                focalLenButton.setFontSize(0.015)
                focalLenButton.setTextAlign('left')
                focalLenButton.setTextColor(0xffffff)
                focalLenButton.setText("作焦距")
                focalLenButton.onClick(() => {
                    this.handleDrawAxis(true, true)
                    this.optionsView.visible = false
                })
                focalLenButton.setBackgroundColor(0xFF6347)
                this.optionsView.addSubView(focalLenButton)

                let shortAxisCircleButton = new BoardButton(
                    this.application,
                    new THREE.Vector3(0, startY - itemHeight / 2 - itemHeight * 3, 0),
                    { width, height: itemHeight })
                shortAxisCircleButton.setFontSize(0.015)
                shortAxisCircleButton.setTextAlign('left')
                shortAxisCircleButton.setTextColor(0xffffff)
                shortAxisCircleButton.setText("短轴作圆")
                shortAxisCircleButton.onClick(() => {
                    this.handleDrawCircle(false)
                    this.optionsView.visible = false
                })
                shortAxisCircleButton.setBackgroundColor(0xFF6347)
                this.optionsView.addSubView(shortAxisCircleButton)

                let longAxisCircleButton = new BoardButton(
                    this.application,
                    new THREE.Vector3(0, startY - itemHeight / 2 - itemHeight * 4, 0),
                    { width, height: itemHeight })
                longAxisCircleButton.setFontSize(0.015)
                longAxisCircleButton.setTextAlign('left')
                longAxisCircleButton.setTextColor(0xffffff)
                longAxisCircleButton.setText("长轴作圆")
                longAxisCircleButton.onClick(() => {
                    this.handleDrawCircle(true)
                    this.optionsView.visible = false
                })
                longAxisCircleButton.setBackgroundColor(0xFF6347)
                this.optionsView.addSubView(longAxisCircleButton)
            }
        }

        this.optionsView.visible = show
    }


    handleDrawRadius() {
        let x = this.position.x + this.centerPoint.x
        let y = this.position.y + this.centerPoint.y + this.abRadius / 2
        let cameraInitSize = this.application?.deref()?.cameraInitSize
        let color = this.superView.deref()?.prepareColor ?? this.color

        let lineView = new LineView(
            MathGeometry.Line,
            this.application,
            new THREE.Vector3(x, y, 0),
            { width: this.abRadius, height: cameraInitSize.height * 0.1 }, color)
        lineView.aPoint = { x: 0, y: this.abRadius / 2 }
        lineView.bPoint = { x: 0, y: -this.abRadius / 2 }
        lineView.updateLine()
        this.superView.deref()?.addSubView(lineView)
    }

    handleDrawDiameter() {
        let x = this.position.x + this.centerPoint.x
        let y = this.position.y + this.centerPoint.y
        let cameraInitSize = this.application?.deref()?.cameraInitSize
        let color = this.superView.deref()?.prepareColor ?? this.color

        let lineView = new LineView(
            MathGeometry.Line,
            this.application,
            new THREE.Vector3(x, y, 0),
            { width: this.abRadius * 2, height: cameraInitSize.height * 0.1 }, color)
        this.superView.deref()?.addSubView(lineView)
    }

    handleDrawCutinside() {
        // 内切正方形的边长公式：a = r * √2
        const sideLength = this.abRadius * Math.sqrt(2);
        let x = this.position.x + this.centerPoint.x
        let y = this.position.y + this.centerPoint.y
        let color = this.superView.deref()?.prepareColor ?? this.color

        let squareView = new QuadrilateralView(
            MathGeometry.Square,
            this.application,
            new THREE.Vector3(x, y, 0),
            { width: sideLength, height: sideLength }, QuadrilateralType.square, color)
        this.superView.deref()?.addSubView(squareView)
    }

    handleDrawCircumscribed() {
        let x = this.position.x + this.centerPoint.x
        let y = this.position.y + this.centerPoint.y
        let color = this.superView.deref()?.prepareColor ?? this.color

        let squareView = new QuadrilateralView(
            MathGeometry.Square,
            this.application,
            new THREE.Vector3(x, y, 0),
            { width: this.abRadius * 2, height: this.abRadius * 2 }, QuadrilateralType.square, color)
        this.superView.deref()?.addSubView(squareView)
    }

    // isLong 是否是长轴， focalLen 是否是焦距(焦距在长轴上)
    handleDrawAxis(isLong, focalLen = false) {
        let x = this.position.x + this.centerPoint.x
        let y = this.position.y + this.centerPoint.y
        let cameraInitSize = this.application?.deref()?.cameraInitSize

        let useAb = this.abRadius <= this.acRadius
        if (isLong) {
            useAb = !useAb
        }

        if (useAb) {
            let width = this.abRadius * 2
            if (focalLen) {
                width = Math.sqrt(this.abRadius ** 2 - this.acRadius ** 2) * 2;
            }
            let color = this.superView.deref()?.prepareColor ?? this.color
            let lineView = new LineView(
                MathGeometry.Line,
                this.application,
                new THREE.Vector3(x, y, 0),
                { width: width, height: cameraInitSize.height * 0.1 }, color)
            this.superView.deref()?.addSubView(lineView)
        }
        else {
            let width = this.acRadius * 2
            if (focalLen) {
                width = Math.sqrt(this.acRadius ** 2 - this.abRadius ** 2) * 2;
            }
            let color = this.superView.deref()?.prepareColor ?? this.color

            let lineView = new LineView(
                MathGeometry.Line,
                this.application,
                new THREE.Vector3(x, y, 0),
                { width: width, height: cameraInitSize.height * 0.1 }, color)
            lineView.aPoint.x = 0
            lineView.aPoint.y = width / 2
            lineView.bPoint.x = 0
            lineView.bPoint.y = - width / 2
            lineView.updateLine()
            this.superView.deref()?.addSubView(lineView)
        }
    }

    handleDrawCircle(isLong) {
        let x = this.position.x + this.centerPoint.x
        let y = this.position.y + this.centerPoint.y

        let useAb = this.abRadius <= this.acRadius
        if (isLong) {
            useAb = !useAb
        }
        let width = this.acRadius * 2

        if (useAb) {
            width = this.abRadius * 2
        }
        let color = this.superView.deref()?.prepareColor ?? this.color

        let circleView = new CircleView(
            MathGeometry.Circle,
            this.application,
            new THREE.Vector3(x, y, 0),
            { width: width, height: width }, color)
        this.superView.deref()?.addSubView(circleView)
    }

    setOnEdit(onEdit) {
        this.onEdit = onEdit
        this.aVertexView.visible = this.onEdit
        this.aVertexView.draggable = this.onEdit
        this.bVertexView.visible = this.onEdit
        this.bVertexView.draggable = this.onEdit
        this.cVertexView.visible = this.onEdit
        this.cVertexView.draggable = this.onEdit
        this.dVertexView.visible = this.onEdit
        this.dVertexView.draggable = this.onEdit
        this.viewButton.draggable = this.onEdit
        this.draggable = this.onEdit
        this.toolView.visible = this.onEdit
        if (!this.onEdit && this.optionsView) {
            this.optionsView.visible = false
        }
        if (onEdit) {
            this.superView.deref().bringSubViewToFront(this)
        }
    }


    getCirclePoints() {
        // 生成圆的顶点
        let points = [];
        let center = this.centerPoint
        const segments = 128
        for (let i = 0; i <= segments; i++) {
            const angle = (i / segments) * Math.PI * 2;
            const x = center.x + Math.cos(angle) * this.abRadius;
            const y = center.y + Math.sin(angle) * this.acRadius;
            points.push(x, y, 0);
        }
        return points
    }

    getPointToLineDistance(p1, p2, point) {

        const { x: x1, y: y1 } = p1
        const { x: x2, y: y2 } = p2
        const { x: px, y: py } = point

        // 计算点到线的距离
        const numerator = Math.abs((y2 - y1) * px - (x2 - x1) * py + x2 * y1 - y2 * x1)
        const denominator = Math.sqrt((y2 - y1) ** 2 + (x2 - x1) ** 2)
        const distance = numerator / denominator
        // 判断距离是否在边的宽度范围内
        return distance
    }

    updateCircle() {

        this.aVertexView.position.set(this.aPoint.x, this.aPoint.y, 0)
        this.bVertexView.position.set(this.bPoint.x, this.bPoint.y, 0)
        this.cVertexView.position.set(this.cPoint.x, this.cPoint.y, 0)
        this.dVertexView.position.set(this.dPoint.x, this.dPoint.y, 0)

        this.acRadius = this.getPointToLineDistance(this.aPoint, this.bPoint, this.centerPoint)
        this.abRadius = this.getPointToLineDistance(this.aPoint, this.cPoint, this.centerPoint)

        // 定义直线的两个顶点
        let points = this.getCirclePoints()

        this.line.geometry.dispose()
        let lineGeometry = new LineGeometry();
        // 更新 MeshLineGeometry
        lineGeometry.setPositions(points);

        this.line.geometry = lineGeometry;


        let width = Math.abs(this.bPoint.x - this.aPoint.x)
        let height = Math.abs(this.aPoint.y - this.cPoint.y)
        this.viewButton.setSize({ width: width + this.dragAreaSize.width * 2, height: height + this.dragAreaSize.height * 2 })



        this.toolView.position.set(this.dPoint.x + this.toolItemWidth * 2, this.dPoint.y + this.toolHeight * 2, 0)

        if (this.optionsView) {
            this.optionsView.position.set(
                this.dVertexView.position.x + this.dVertexView.size.width + this.optionsWidth / 2,
                this.toolView.position.y - this.toolHeight / 2 - this.optionsHeight / 2,
                0
            )
        }
    }

    onTouchDown(point) {
        let view = super.onTouchDown(point)
        if (!this.onEdit) {
            return view
        }
        let cvtPoint = this.convertPoint(point, DrawZ.objcZ)
        this.touchDownPoint = cvtPoint
        if (view === this.aVertexView) {
            this.moveTag = MoveTag.a
        }
        else if (view === this.bVertexView) {
            this.moveTag = MoveTag.b
        }
        else if (view === this.cVertexView) {
            this.moveTag = MoveTag.c
        }
        else if (view === this.dVertexView) {
            this.moveTag = MoveTag.d
        }
        else if (view === this.viewButton) {
            this.moveTag = MoveTag.self
        }
        else {
            this.moveTag = MoveTag.none
        }
        return view
    }


    onTouchMove(point) {
        if (!this.onEdit || !this.touchDownPoint) {
            return super.onTouchMove(point)
        }
        let divPoint = point
        let cvtPoint = this.convertPoint(point, DrawZ.objcZ)
        let spaceX = cvtPoint.x - this.touchDownPoint.x
        let spaceY = cvtPoint.y - this.touchDownPoint.y

        this.touchDownPoint = cvtPoint
        if (this.moveTag === MoveTag.a) {
            let x = this.aPoint.x + spaceX;
            let y = this.aPoint.y + spaceY;
            if (x + this.dragAreaSize.width >= 0 || y - this.dragAreaSize.height <= 0) {
                return
            }
            if (this.isCircle) {
                let space = (spaceX <= 0 || spaceY <= 0) ? Math.min(Math.abs(x), Math.abs(y)) : Math.max(Math.abs(x), Math.abs(y))
                this.aPoint = { x: -space, y: space };
                this.bPoint = { x: space, y: space };
                this.cPoint = { x: -space, y: -space };
                this.dPoint = { x: space, y: -space };
            }
            else {
                this.aPoint = { x, y };
                this.bPoint = { x: -x, y };
                this.cPoint = { x, y: -y };
                this.dPoint = { x: -x, y: -y };
            }
            this.updateCircle();

        } else if (this.moveTag === MoveTag.b) {
            let x = this.bPoint.x + spaceX;
            let y = this.bPoint.y + spaceY;
            if (x - this.dragAreaSize.width <= 0 || y - this.dragAreaSize.height <= 0) {
                return
            }
            if (this.isCircle) {
                let space = (spaceX <= 0 || spaceY <= 0) ? Math.min(Math.abs(x), Math.abs(y)) : Math.max(Math.abs(x), Math.abs(y))
                this.aPoint = { x: -space, y: space };
                this.bPoint = { x: space, y: space };
                this.cPoint = { x: -space, y: -space };
                this.dPoint = { x: space, y: -space };
            }
            else {
                this.bPoint = { x, y };
                this.aPoint = { x: -x, y };
                this.cPoint = { x: -x, y: -y };
                this.dPoint = { x, y: -y };
            }
            this.updateCircle();

        } else if (this.moveTag === MoveTag.c) {
            let x = this.cPoint.x + spaceX;
            let y = this.cPoint.y + spaceY;
            if (x + this.dragAreaSize.width >= 0 || y + this.dragAreaSize.height >= 0) {
                return
            }
            let space = (spaceX <= 0 || spaceY <= 0) ? Math.min(Math.abs(x), Math.abs(y)) : Math.max(Math.abs(x), Math.abs(y))
            if (this.isCircle) {
                this.aPoint = { x: -space, y: space };
                this.bPoint = { x: space, y: space };
                this.cPoint = { x: -space, y: -space };
                this.dPoint = { x: space, y: -space };
            }
            else {
                this.aPoint = { x, y: -y };
                this.bPoint = { x: -x, y: -y };
                this.cPoint = { x, y };
                this.dPoint = { x: -x, y };
            }
            this.updateCircle();

        } else if (this.moveTag === MoveTag.d) {
            let x = this.dPoint.x + spaceX;
            let y = this.dPoint.y + spaceY;
            if (x - this.dragAreaSize.width <= 0 || y + this.dragAreaSize.height >= 0) {
                return
            }
            let space = (spaceX <= 0 || spaceY <= 0) ? Math.min(Math.abs(x), Math.abs(y)) : Math.max(Math.abs(x), Math.abs(y))
            if (this.isCircle) {
                this.aPoint = { x: -space, y: space };
                this.bPoint = { x: space, y: space };
                this.cPoint = { x: -space, y: -space };
                this.dPoint = { x: space, y: -space };
            }
            else {
                this.aPoint = { x: -x, y: -y };
                this.bPoint = { x, y: -y };
                this.cPoint = { x: -x, y };
                this.dPoint = { x, y };
            }
            this.updateCircle();

        } else if (this.moveTag === MoveTag.self) {
            let x = this.position.x + spaceX;
            let y = this.position.y + spaceY;
            this.position.set(x, y, 0);
        }

        return super.onTouchMove(divPoint)
    }

    onTouchUp(point) {
        this.moveTag = MoveTag.none
        return super.onTouchUp(point)
    }

    isPointOnLine(touchPoint, lineWidth) {
        const { x: x1, y: y1 } = touchPoint; // 触摸点坐标
        const { x: h, y: k } = this.centerPoint; // 椭圆中心点

        // 获取长半轴和短半轴，确保a是长半轴，b是短半轴
        const a = Math.max(this.abRadius, this.acRadius); // 长半轴
        const b = Math.min(this.abRadius, this.acRadius); // 短半轴

        // 判断长半轴是在哪个方向
        const isYAxisLong = this.abRadius === b; // 如果y轴是长半轴

        // 计算代数距离
        const distance = isYAxisLong
            ? Math.sqrt(Math.pow((x1 - h) / b, 2) + Math.pow((y1 - k) / a, 2)) // y轴是长半轴
            : Math.sqrt(Math.pow((x1 - h) / a, 2) + Math.pow((y1 - k) / b, 2)); // x轴是长半轴

        // 判断是否在椭圆线上（允许一定的误差范围）
        return Math.abs(1 - distance) <= lineWidth;
    }

    onPointInside(point) {
        if (!this.visible) {
            return false
        }
        const pos = new THREE.Vector3()
        this.viewButton.getWorldPosition(pos)
        for (let subView of this.subViews) {
            if (subView !== this.viewButton) {
                if (subView.onPointInside(point)) {
                    return true
                }
            }
            // else if (this.onEdit) {
            //     if (subView.onPointInside(point)) {
            //         return true
            //     }
            // }
        }
        let cvtPoint = this.convertPoint(point, DrawZ.objcZ)
        let localPoint = this.worldToLocal(cvtPoint)
        let match = this.isPointOnLine(localPoint, this.lineWidth)
        return match
    }


    dispose() {
        BoardTool.disposeGroup(this.lineGroup)
        super.dispose()
    }

}