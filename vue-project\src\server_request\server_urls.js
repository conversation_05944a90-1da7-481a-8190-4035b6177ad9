import { UserChannel } from "@/app_verson";
import { DrawQuality } from "@/drawboard/draw_enums";
import { LocalRequest } from "@/local_request/local_request";

export const HttpEnv = {
	test: "test",
	online: "online",
	dev: "dev"
}

export class ServerHost {
	constructor() {
		if (!ServerHost.instance) {
			// 默认online  不要提交！！
			this.env = HttpEnv.online
			this.userChannel = UserChannel.public
			this.canvasResolution = DrawQuality.WQHD
			// 是否开启蓝牙模式
			this.bleApEnable = false
			// 是否开启概率互动
			this.probabilityEnable = false
			this.showActive =  true // 在崂山版本激活的渠道，是否跳过激活，默认不跳过显示激活页面
			ServerHost.instance = this
		}
		return ServerHost.instance
	}


	async updateEnv() {
		const config = await LocalRequest.readConfig()
		//添加config 方便后续保存
		this.appConfig = config
		this.bleApEnable = config.ble_ap_enable
		this.probabilityEnable = config.probability_enable
		this.privateConfig = config.private_config
		this.showActive = config.show_active
		this.aiCorrectOpen = config.ai_correct_open
		if (config.environment) {
			this.env = config.environment
		}
		if (config.user_channel) {
			this.userChannel = config.user_channel
		}
		

		if (config.canvas_resolution === "QHD") {
			this.canvasResolution = DrawQuality.QHD
		}
		else if (config.canvas_resolution === "FHD") {
			this.canvasResolution = DrawQuality.FHD
		}
		else if (config.canvas_resolution === "UHD") {
			this.canvasResolution = DrawQuality.UHD
		}
		else {
			this.canvasResolution = DrawQuality.WQHD
		}
	}

	getChannelString() {
		if (this.userChannel === UserChannel.laoshan) {
			return "LAOSHAN"
		}
		return "RELEASE"
	}
}

export const serverHost = new ServerHost()

export class ServerUrls {

	static getFutureInkHost() {
		if (serverHost.env == HttpEnv.test) {
			return "http://api.test.future.ink"
		} else if (serverHost.env == HttpEnv.dev) {
			return "http://api.dev.future.ink"
		}
		return "https://api.future.ink"
	}

	static getMgboardHost() {
		if (serverHost.privateConfig?.enable ?? false) {
			return this.getPrivateHost()
		}
		if (serverHost.env == HttpEnv.test) {
			return "http://console.test.mgboard.com";
		} else if (serverHost.env == HttpEnv.dev) {
			return "http://console.dev.mgboard.com";
		}
		return "https://console.mgboard.com";
	}

	static getLaoshanHost(){
		return "https://www.laoshanedu.cn"
	}

	static getPrivateHost() {
		if (serverHost.privateConfig.domain !== "") {
			return serverHost.privateConfig.domain
		}
		if (serverHost.env == HttpEnv.test) {
			return "http://mgboard.test.robota.ink"
		} else if (serverHost.env == HttpEnv.dev) {
			return "http://mgboard.dev.robota.ink"
		}
		return "http://mgboard.robota.ink"
	}

	static loginUrl() {
		return this.getMgboardHost() + "/api/login/user"
	}

	static getUserInfoUrl() {
		return this.getMgboardHost() + '/api/user/info'
	}

	static getClassListUrl() {
		return this.getMgboardHost() + '/api/teacher/class/list'
	}

	static getStudentListUrl() {
		return this.getMgboardHost() + '/api/teacher/student/list'
	}

	static getStudentListLevelUrl() {
		return this.getMgboardHost() + '/api/teacher/level/class/student'
	}

	static getDevicesUrl() {
		return this.getMgboardHost() + '/api/teacher/student/virtual/devices'
	}

	static getStartClassUrl() {
		return this.getMgboardHost() + '/api/teacher/class/start'
	}

	static getStopClassUrl() {
		return this.getMgboardHost() + '/api/teacher/class/end'
	}
	static historyUrl() {
		return this.getMgboardHost() + '/api/teacher/class/history'
	}

	static homeworkHistoryUrl() {
		// return this.getMgboardHost() + '/api/teacher/interact/history'
		return this.getMgboardHost() + '/api/teacher/interact/history/v2'
	}

	static getDayRecordsUrl() {
		return this.getMgboardHost() + '/api/teacher/class/record/day'
	}

	static changeStudentMacAddressUrl() {
		return this.getMgboardHost() + '/api/teacher/student/change/virtual/device'
	}

	static feedbackUrl() {
		return 'https://v2.api.robotpen.cn/feedback/add'
	}

	static interactStartUrl() {
		return this.getMgboardHost() + '/api/teacher/interact/start'
	}

	static stopInteractMultiRequest() {
		return this.getMgboardHost() + '/api/teacher/interact/end'
	}

	static setAnswerUrl() {
		return this.getMgboardHost() + '/api/teacher/interact/set/answer'
	}

	static getTextbookTreeUrl() {
		return this.getMgboardHost() + '/api/teacher/personal/space/textbooks/tree'
	}

	//网盘列表请求
	static cloudDiskListUrl() {
		return this.getMgboardHost() + '/api/teacher/cloud/disk/page/files'
	}
	//网盘文件夹请求 
	static cloudDiskCatalogUrl() {
		return this.getMgboardHost() + '/api/teacher/cloud/disk/catalog'
	}
	//网盘存储空间请求 
	static cloudDiskSpaceUrl() {
		return this.getMgboardHost() + '/api/teacher/cloud/disk/used'
	}
	//wps预览
	static cloudDiskWpsPreviewUrl() {
		return this.getMgboardHost() + '/api/teacher/office/wps/opt/preview'
	}
	//wps编辑
	static cloudDiskWpsEditUrl() {
		return this.getMgboardHost() + '/api/teacher/office/wps/opt/edit'
	}

	static searchMutilWordsUrl() {
		return this.getMgboardHost() + '/api/zuul/dictation/subjects/words'
	}

	static searchWordsUrl() {
		return this.getMgboardHost() + '/api/zuul/dictation/en_words/search'
	}

	static loadExplainUrl() {
		return this.getMgboardHost() + '/api/zuul/dictation/en_words/explain'
	}

	//教师随堂记录删除  
	static classRoomRecordDelete() {
		return this.getMgboardHost() + '/api/teacher/micro/course/delete'
	}
	//教师随堂记录获取微课程列表 
	static classRoomRecordMicroCourse() {
		return this.getMgboardHost() + '/api/teacher/micro/course/list'
	}
	//教师随堂记录获取互动记录列表 
	static classRoomRecordInteraction() {
		return this.getMgboardHost() + '/api/teacher/interact/record/class'
	}
	//教师作业讲评列表 
	static classRoomRecordHomework() {
		return this.getMgboardHost() + '/api/teacher/interact/correct/list/v2'
	}
	//随堂记录互动任务进度 
	static classRoomRecordInteractionTaskProgress() {
		return this.getMgboardHost() + '/api/zuul/question/answer/correct/progress'
	}
	//随堂记录学生统计情况 
	static classRoomRecordQuestionStatistics() {
		return this.getMgboardHost() + '/api/zuul/dictation/questions/item_statistics'
	}

	static loadAccuracyUrl() {
		return this.getMgboardHost() + '/api/zuul/dictation/questions/item'
	}

	static getMineTeachPlanUrl() {
		return this.getMgboardHost() + '/api/zuul/question/paper/manage/list'
	}

	static getSchoolTeachPlanUrl() {
		return this.getMgboardHost() + '/api/zuul/school/papers'
	}

	static loadStageUrl() {
		return this.getMgboardHost() + '/api/teacher/stage/by/paperId';
	}

	static loadQuestionsUrl() {
		return this.getMgboardHost() + '/api/zuul/question/paper/area/stage/question';
	}

	static loadClassRecordUrl() {
		return this.getMgboardHost() + '/api/teacher/class/record';
	}

	static updateScoreUrl() {
		return this.getMgboardHost() + '/api/teacher/ranks/set/score';
	}

	static getAuthCodeUrl() {
		return this.getMgboardHost() + '/api/login/image';
	}

	static activeCodeUrl() {
		return this.getMgboardHost() + '/api/client/activate/activate'
	}
	// 登录log
	static loginLogUrl(){
		return this.getMgboardHost() + '/api/teacher/client/logs/add/v1'
	}
	// 登录l统计
	static loginReceiveUrl(){
		return this.getLaoshanHost() + '/api/sub/api/v1/app/addAppUseCount'
	}

	static loginReceiveAuth(){
		return this.getLaoshanHost() + '/api/auth/api/v1/oauth/accessToken'
	}

	//获取中文练字批改评分
	static getCalligraphyResultUrl() {
		return this.getMgboardHost() + '/api/zuul/score/words/records/batch/detail'
	}
	//随堂记录学生互动统计情况 
	static classRoomRecordInteractionStatistics() {
		return this.getMgboardHost() + '/api/teacher/interact/option/statistic'
	}
	//随堂记录获取互动答案 
	static classRoomRecordInteractionAnswer() {
		return this.getMgboardHost() + "/api/teacher/interact/set/answer"
	}
	// 组合题统计情况
	static classRoomRecordCroupStatistics() {
		return this.getMgboardHost() + "/api/teacher/interact/combo/statistic"
	}
	//随堂记录互动详情 
	static classRoomRecordInteractDetail() {
		return this.getMgboardHost() + "/api/teacher/interact/detail"
	}
	//随堂记录互动轨迹 
	static classRoomRecordInteractTrails() {
		return this.getMgboardHost() + "/api/zuul/question/answer/correct/tails/image"
	}

	//随堂记录互动学生详情 
	static classRoomRecordInteractDetails() {
		return this.getMgboardHost() + "/api/teacher/interact/student/detail"
	}
	//随堂记录随堂测试批题 
	static classRoomRecordAnswerCorrect() {
		return this.getMgboardHost() + '/api/zuul/question/answer/correct/manual'
	}
	//随堂记录随堂测试 教师互动详情
	static classRoomRecordTeacherInteract() {
		return this.getMgboardHost() + '/api/teacher/interact/detail'
	}

	//随堂记录随堂测试 问题批改 
	static classRoomRecordTaskQuestionsCorrect() {
		return this.getMgboardHost() + '/api/zuul/question/answer/correct/task/questions'
	}

	//随堂记录随堂测试 问题回答统计 
	static classRoomRecordQuestionsAnswerStatistic() {
		return this.getMgboardHost() + '/api/zuul/question/statistic/answer'
	}
	//
	//随堂记录随堂测试 学生问题回答统计 
	static classRoomRecordStudentQuestionsAnswerStatistic() {
		return this.getMgboardHost() + '/api/zuul/question/statistic/student/answer'
	}
	//随堂记录 随堂测试问题回答轨迹页面  
	static classRoomRecordQuestionAnswerTrails() {
		return this.getMgboardHost() + '/api/zuul/question/answer/trails/page'
	}
	//随堂记录 随堂测试优化问题 
	static classRoomRecordPentrailsOptimizeQuestion() {
		return this.getMgboardHost() + '/api/zuul/pentrails/mqtt/optimize/question'
	}
	//随堂记录 随堂测试 学生回答试题 
	static classRoomRecordStudentAnswerPaper() {
		return this.getMgboardHost() + '/api/zuul/question/answer/correct/student/paper'
	}
	//随堂记录 中文练字数据 
	static classRoomRecordInteractStudentWords() {
		return this.getMgboardHost() + '/api/teacher/interact/student/words'
	}
	//随堂记录 中文练字得分情况 
	static classRoomRecordWordsRecordScore() {
		return this.getMgboardHost() + '/api/zuul/score/words/records/batch/detail'
	}
	//随堂记录 中文练字轨迹 
	static classRoomRecordWordsScoreTrails() {
		return this.getMgboardHost() + '/api/zuul/score/trails/record'
	}
	//英语单词听写获取听写人数
	static getEnglishUsersUrl() {
		return this.getMgboardHost() + '/api/zuul/dictation/questions/item_users'
	}
	//英语单词听写结果统计
	static getEnglishStatisticsUrl() {
		return this.getMgboardHost() + '/api/zuul/dictation/questions/item_statistics'
	}

	// 检查更新
	static checkAppUpdateUrl() {
		return this.getFutureInkHost() + '/user/open/activate/version/upgrade'
	}

	// 教师点评项列表
	static getEvaluationItemListUrl() {
		return this.getMgboardHost() + '/api/evaluation/item/list'
	}

	// 给学生打分
	static setStudentsScoreUrl() {
		return this.getMgboardHost() + '/api/evaluation/item/set/score'
	}

	// 学生或者小组打分(新)
	static setScoreNewUrl() {
		return this.getMgboardHost() + '/api/teacher/ranks/set/score/new'
	}

	// 得分统计
	static scoreStatisticsUrl() {
		return this.getMgboardHost() + '/api/evaluation/item/score/statistics'
	}

	// 得分统计明细
	static scoreStatisticsDetailUrl() {
		return this.getMgboardHost() + '/api/evaluation/item/score/statistics/detail'
	}

	// 查询班级科目点名概率
	static levelClassCallUrl() {
		return this.getMgboardHost() + '/api/teacher/level/class/call'
	}

	// 识别优化笔迹API
	static trailsRecogUrl() {
		return this.getFutureInkHost() + '/trails/trails/recog'
	}

	static getPhetLabDataUrl() {
		// return '/phet/phet.json'
		// return '/phet/phetSubject.json'
		// return 'https://robotpen.com/phet/phetSubject.json'
		return this.getMgboardHost() + '/phet/phetSubject.json'
	}
    //随堂测试 获取推荐
	static getRecommandQuestionUrl() {
		// return '/phet/phet.json'
		// return '/phet/phetSubject.json'
		// return 'https://robotpen.com/phet/phetSubject.json'
		return this.getMgboardHost() + '/api/teacher/interact/accurate'
		///api/teacher/interact/demo/accurate
	}
    //随堂测试获取报告
	static getPaperAnalysisUrl() {
		// return '/phet/phet.json'
		// return '/phet/phetSubject.json'
		// return 'https://robotpen.com/phet/phetSubject.json'
		return this.getMgboardHost() + '/api/teacher/interact/analysis'
		///api/teacher/interact/demo/analysis
	}   
}