import * as THREE from 'three';

const TextureLoadState = {
    loading: 'loading',
    success: 'success',
    fail: 'fail'
}

export class TextureCache {

    constructor() {
        this.paperCacheMap = new Map() // 存储已加载的纹理
        this.loadingStateMap = new Map() // 存储每个纹理的加载状态
        this.subscribers = new Map() // 用于存储需要刷新的回调函数
    }

    // 加载页面纹理，支持多个对象同时请求，支持多个callback，支持重试
    loadImageTexture(textureId, imageUrl, callback, retries = 3) {
        // 检查纹理是否已经加载
        if (this.loadingStateMap.get(textureId) === TextureLoadState.success) {
            // console.log('纹理已加载，无需再次加载')
            return callback(this.paperCacheMap.get(textureId))
        }
        this.subscribe(textureId, callback)

        if (this.loadingStateMap.get(textureId) === TextureLoadState.loading) {
            return
        }

        this.loadingStateMap.set(textureId, TextureLoadState.loading)
        this.startTextureLoader(textureId, imageUrl, retries, (success, texture) => {
            if (success) {
                this.paperCacheMap.set(textureId, texture)
                this.loadingStateMap.set(textureId, TextureLoadState.success) // 设置为加载成功
                this.notifySubscribers(textureId, texture) // 通知所有订阅者
            } else {
                this.loadingStateMap.set(textureId, TextureLoadState.fail)
                console.error('纹理加载失败')
            }
        })
    }

    startTextureLoader(textureId, imageUrl, retries = 3, callback) {
        const textureLoader = new THREE.TextureLoader()
        textureLoader.crossOrigin = 'anonymous';
        textureLoader.load(
            imageUrl,
            (texture) => {
                texture.minFilter = THREE.LinearFilter;
                texture.magFilter = THREE.LinearFilter;
                texture.colorSpace = THREE.SRGBColorSpace;
                callback(true, texture)
            },
            undefined,
            (err) => {
                if (retries > 0) {
                    console.warn(`纹理加载失败，正在重试...剩余重试次数：${retries}`);
                    setTimeout(() => {
                        // 重试加载
                        this.startTextureLoader(textureId, imageUrl, retries - 1, callback)
                    }, 2000);
                } else {
                    callback(false, null)
                }
            }
        )
    }

    // 添加订阅者回调函数
    subscribe(textureId, callback) {
        if (!this.subscribers.has(textureId)) {
            this.subscribers.set(textureId, [])
        }
        this.subscribers.get(textureId).push(callback)
    }

    // 通知订阅者更新
    notifySubscribers(textureId, texture) {
        if (this.subscribers.has(textureId)) {
            this.subscribers.get(textureId).forEach(callback => {
                callback(texture) // 调用回调并传递纹理
            })
        }
    }

    // 清理所有资源
    clearAll() {
        // 清理所有缓存的纹理
        this.paperCacheMap.forEach((texture, textureId) => {
            texture.dispose() // 释放纹理资源
        })
        this.paperCacheMap.clear()

        // 清理所有订阅者
        this.subscribers.clear()

        // 清理所有加载状态
        this.loadingStateMap.clear()
    }
}