<template>
    <div class="class-room-record-test-footer">
        <div class="foot" id="foot">
            <div class="left">
                <div v-for="(item, index) in leftList" class="item foot-show-item"
                    :class="{ 'item-choose': status == (1 + index),'foot-show-item-recommand':index==4 }" @click="clickLeft(index + 1)">
                    <img class="icon" :src="item.url">
                    <div>{{ item.text }}</div>
                </div>

            </div>
            <div class="middle">
                <ToolbarColorItem class="foot-draw-item" :is-big="true" :item="TabBarItem.colorSelect"
                    @itemClick="colorSelectClick"></ToolbarColorItem>
                <div @dblclick="dbMiddleClick(index + 1)" v-for="(item, index) in middleList"
                    class="item foot-draw-item"
                    :class="{ 'item-choose': canvasStatus == (index + 1), 'foot-show-item-full': index == 5 }"
                    @click="clickMiddle(index + 1)">
                    <img class="icon"
                        :src="isFullScreen && item.text == '全屏' ? 'icon/record_tool_exit_full.svg' : item.url">
                    <div>{{ isFullScreen && item.text == '全屏' ? '退出全屏' : item.text }}</div>
                </div>
            </div>
            <div class="right">
                <div v-for="(item, index) in rightList" class="item " @click="clickRight(item.type)" :key="index">
                    <img class="icon" :src="item.url">
                    <div>{{ item.text }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import ToolbarColorItem from '@/components/TabBar/ToolbarColorItem.vue';
import { TabBarItem } from '@/components/TabBar/tabbar_enums';
import { ref, nextTick, defineEmits, onMounted, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { useDrawBoardStore } from '@/stores/drawboard_store'
import { useRecordDrawboardStore } from '@/stores/record_drawboard_store';
import { DrawMode, EditMode } from '@/drawboard/draw_enums';
const drawBoardStore = useDrawBoardStore()
const { showColorSelect, editMode, drawMode, showEraserSelect, } = storeToRefs(drawBoardStore)

const recordDrawboardStore = useRecordDrawboardStore()
const { isFullScreen, recordDrawBoardPainter } = storeToRefs(recordDrawboardStore)




function isHomework() {

    return classroomUIStore.showRecordType == 'homework';
}
function colorSelectClick() {
    //颜色
    showColorSelect.value = true
}

function eraserSelectClick() {
    showEraserSelect.value = true
}

const classroomUIStore = useClassroomUIStore()
const { showClassRoomRecord, hideClassRoomRecord, showClearScreenAlert, showClassRecord } = storeToRefs(classroomUIStore)



const leftList = ref([
    {
        "text": "题目讲评",
        "url": 'icon/record_tool_question_review.svg'
    },
    {
        "text": "学生讲评",
        "url": 'icon/record_tool_student_review.svg'
    },
    {
        "text": "学生统计",
        "url": 'icon/record_tool_student_statistics.svg'
    },


])

onMounted(() => {

    //只有课堂才展示最小化
    if (showClassRoomRecord.value) {
        rightList.value.unshift({
            "text": "最小化",
            "type": 0,
            "url": 'icon/record_tool_min.svg'
        },)
    }
    if (!isHomework()) {
        leftList.value.push(
            {
                "text": "班级报告",
                "url": 'icon/record_tool_question_report.svg'
            },
        )
        if (!showClassRecord.value) {
            leftList.value.push(
                {
                    "text": "推题",
                    "url": 'icon/record_tool_question_recommand.svg'
                }
            )
        }
    }

    canvasStatus.value = editMode.value == EditMode.Dragging ? 1 : drawMode.value == DrawMode.pen ? 2 : 3

})

const middleList = [
    {
        "text": "选择",
        "url": '/img/svg/icon_tab_canvasOptionArrow.svg'
    },
    {
        "text": "画笔",
        "url": '/img/svg/icon_tab_canvasOptionPen.svg'
    },
    {
        "text": '橡皮擦',
        "url": '/img/svg/icon_tab_canvasOptionEraser.svg'
    },
    {
        "text": '撤销画笔',
        "url": '/img/svg/icon_tab_undoBrush.svg'
    },
    {
        "text": "清屏",
        "url": '/img/svg/icon_tab_cleanScreen.svg'
    },
    {
        "text": "全屏",
        "url": 'icon/record_tool_full.svg'
    },
];

const rightList = ref(
    [

        {
            "text": "刷新",
            "type": 1,
            "url": 'icon/icon_tool_refresh.svg'
        },
        {
            "text": "退出讲评",
            "type": 2,
            "url": 'icon/icon_quit.svg'
        }
    ]
)
//点击右侧
function clickRight(index) {
    switch (index) {
        case 0: //最小化
            hideClassRoomRecord.value = true
            break;
        case 1: //刷新
            reload()
            break;
        case 2: //退出
            close()
            break;
    }
}

watch(hideClassRoomRecord, () => {
    canvasStatus.value = editMode.value == EditMode.Dragging ? 1 : drawMode.value == DrawMode.pen ? 2 : 3
})
//点击中部
function clickMiddle(index) {
    switch (index) {
        case 1: //移动
            canvasStatus.value = index
            setTuozhuaiCanvas()
            break;
        case 2: //画笔
            canvasStatus.value = index
            drawBoardStore.setEditMode(EditMode.Drawing)
            drawBoardStore.setDrawMode(DrawMode.pen)
            break;
        case 3: //橡皮擦
            canvasStatus.value = index
            drawBoardStore.setEditMode(EditMode.Drawing)
            drawBoardStore.setDrawMode(DrawMode.eraser)
            break;
        case 4: //撤销画笔
            recordDrawBoardPainter.value.cancelALine()
            break;
        case 5: //清屏
            clearCanvas()
            break;
        case 6: //全屏
            fullCanvas()
            break;
    }

}
//双击中部
function dbMiddleClick(index) {
    if (index == 3) {
        eraserSelectClick()
    }
}

//左侧控制view状态
const status = ref(1)
//画布状态
const canvasStatus = ref(1)

const emits = defineEmits(['showReport', 'showRecommand', 'setQues', 'closeQues', 'exitShowImgUrl', 'setStatus', 'hideInteract', 'footerReload'])
//重新加载
function reload() {
    if (isFullScreen.value) {
        isFullScreen.value = false
    }
    if (localStorage.getItem('showImgUrl')) {
        emits('exitShowImgUrl')
        localStorage.removeItem('showImgUrl')
    }
    emits('footerReload')
}

//点击左侧
function clickLeft(index) {
    switch (index) {

        case 4: //显示报告
            emits('showReport')
            break;
        case 5: //显示推题
            emits('showRecommand')
            break;
        default:

            setStatus(index)
            break;

    }
}


//关闭
function close() {
    // if (isFullScreen.value) {
    //     isFullScreen.value = false

    //     if(recordDrawBoardPainter.value){
    //         recordDrawBoardPainter.value.changeFull(false)
    //     }
    //     return
    // }
    if (localStorage.getItem('showQues')) {
        // emits('closeQues')
    } else if (localStorage.getItem('showImgUrl')) {
        emits('exitShowImgUrl')
        localStorage.removeItem('showImgUrl')
    } else {
        emits('hideInteract')
    }
}


//设置状态
function setStatus(statusD) {
    if (isFullScreen.value) {
        isFullScreen.value = false
    }
    status.value = statusD
    emits('setStatus', statusD)
}


function setTuozhuaiCanvas() {
    drawBoardStore.setEditMode(EditMode.Dragging)
}

function clearCanvas() {
    showClearScreenAlert.value = true
}
function fullCanvas() {
    if (recordDrawBoardPainter.value) {
        isFullScreen.value = !isFullScreen.value
        recordDrawBoardPainter.value.changeFull(isFullScreen.value)
    }


    // if(!isFullScreen.value){
    //     isFullScreen.value = true
    //     if(recordDrawBoardPainter.value){
    //         recordDrawBoardPainter.value.changeFull(true)
    //     }
    // }
}


</script>

<style lang="scss" scoped>
.class-room-record-test-footer {
    height: 100%;
    background-color: var(--toolbar-bg-color);
    padding: 0px 6px;

    .clear {
        height: 30px;
        line-height: 30px;
        padding: 0 5px;
        text-align: center;
        font-size: 14px;
        color: #2e4a66;
        border-radius: 3px;
        background: #B2D9F9;
        position: fixed;
        right: 10px;
        z-index: 100;
    }

    .foot {
        height: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        padding-top: 3px;
        padding-bottom: 7px;
    }

    .item {
        display: flex;
        flex-direction: column;
        width: 75px;
        height: 68px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-weight: 400;
        font-size: 12px;
        color: var(--secondary-text-color);
        line-height: 20px;
    }

    .item-choose {
        background-color: var(--main-bc-color);
        border-radius: 10px;
        color: var(--text-color);
    }

    .icon {
        width: 48px;
        height: 48px;
    }

    .line-width {
        max-width: 25px;
        max-height: 25px;
        background-color: #F74E59;
        border-radius: 50%;
    }

    .active {
        background-color: #1A1E28;
        color: #8BADCE;
    }

    .dot {
        width: 22px;
        height: 22px;
        border-radius: 11px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .el-icon-check {
        font-weight: bold;
        font-size: 14px;
    }

    .left {
        display: flex;
        width: 420px;
        height: 100%;
        align-items: center;
    }

    .middle {
        display: flex;
        align-items: center;
    }

    .right {
        display: flex;
        justify-content: flex-end;
        width: 270px;
    }
}
</style>