<?xml version="1.0" encoding="UTF-8"?>
<svg width="30.7682243px" height="31px" viewBox="0 0 30.7682243 31" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Icon/展开箭头</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#2A85FB" offset="0%"></stop>
            <stop stop-color="#3AF8DE" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="0切图" transform="translate(-369.1159, -308.8841)">
            <g id="Icon/下拉箭头" transform="translate(369.1159, 308.8841)">
                <polyline id="路径" stroke="#565656" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" points="23.588972 11.3666667 15.3841121 19.6333333 7.17925234 11.3666667"></polyline>
                <rect id="Rectangle-271-Copy-2" stroke="url(#linearGradient-1)" fill="#000000" opacity="0" x="0.5" y="0.5" width="29.7682243" height="30"></rect>
            </g>
        </g>
    </g>
</svg>