<template>
    <div v-show="minimize == ViewStatus.normal" class="mini-btn" @click="miniClick">
        <img :src="getMinimizeImg()">
    </div>
    <div id="questionBackBox" v-show="minimize == ViewStatus.minimize" class="mini-reset"
        @click="miniClick">
        {{ interactTitle() }}
    </div>
</template>
<script setup>
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { ViewStatus } from "@/classroom/frame_enums"
import { DisplayDirection, UIFrames } from '@/classroom/frame_enums'
import { storeToRefs } from 'pinia'
import { useInteractStore } from '@/stores/interact_store'
import { Interact } from '@/classroom/interact_enums.js'

const classroomUIStore = useClassroomUIStore()
const { displayDirection } = storeToRefs(classroomUIStore)
const interactStore = useInteractStore()
const { interact, minimize } = storeToRefs(interactStore)

function interactTitle() {
    switch (interact.value) {
        case Interact.multiQuestions:
            return "组 合 题"
        case Interact.multiChoice:
            return "多 选 题"
        case Interact.singleChoice:
            return "单 选 题"
        case Interact.trueFalse:
            return "判 断 题"
        case Interact.responder:
            return "抢 答 题"
        case Interact.vote:
            return "投票"
    }
    return ""
}
function getMinimizeImg() {
    if (displayDirection.value == DisplayDirection.Right) {
        if (minimize.value == ViewStatus.normal) {
            return '/img/icon_interaction_in.png'
        } else {
            return '/img/icon_interaction_ex.png'
        }
    } else {
        if (minimize.value == ViewStatus.normal) {
            return '/img/icon_interaction_ex.png'
        } else {
            return '/img/icon_interaction_in.png'
        }
    }
}
function miniClick() {
    if (minimize.value == ViewStatus.normal) {
        minimize.value = ViewStatus.minimize
    } else {
        minimize.value = ViewStatus.normal
    }
}
function getBottom() {
    if (minimize.value == ViewStatus.normal) {
        return (UIFrames.tabbarHeight + UIFrames.pptTabHeight + 1) + 'px'
    } else {
        return (UIFrames.tabbarHeight + UIFrames.pptTabMiniHeight + 1) + 'px'
    }
}
function getBorderRadius() {
    if(displayDirection.value === DisplayDirection.Left) {
        return '0 20px 20px 0'
    }else {
        return '20px 0 0 20px'
    }
}
</script>
<style lang="scss" scoped>
.mini-btn {
    position: absolute;
    left: v-bind("displayDirection === DisplayDirection.Left ? '1px' : null");
    right: v-bind("displayDirection === DisplayDirection.Left ? null : '1px'");
    width: 40px;
    height: 40px;
    z-index: 1000;
    cursor: pointer;
    border-radius: 6px;
    z-index: var(--interact-z-index);
    bottom: 140px;
}

.mini-reset {
    position: absolute;
    left: v-bind("displayDirection === DisplayDirection.Left ? '0' : null");
    right: v-bind("displayDirection === DisplayDirection.Left ? null : '0'");
    bottom: 140px;
    z-index: var(--interact-z-index);
    writing-mode: vertical-lr;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: v-bind("getBorderRadius()");
    cursor: pointer;
    color: var(--secondary-text-color);
    font-size: 18px;
    padding: 20px 12px;
    font-weight: 600;
    border: 1px solid var(--border-bar-color);
    background-color: var(--main-bc-color);
}
</style>