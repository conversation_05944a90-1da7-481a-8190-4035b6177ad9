<template>
    <div class="rbp-line"></div>
</template>
<script setup>
import { defineProps } from 'vue'
import { RBPColors } from '@/components/baseComponents/RBPColors.js'

const props = defineProps({
    marginTop: { type: String, default: '0px' },
    marginBottom: { type: String, default: '0px' },
    width: { type: String, default: '100%' },
    height: { type: String, default: '1px' },
})
</script>
<style lang="scss" scoped>
.rbp-line {
    width: v-bind("props.width");
    height: v-bind("props.height");
    background-color: var(--border-bar-color);
    margin-top: v-bind("props.marginTop");
    margin-bottom: v-bind("props.marginBottom");
}
</style>