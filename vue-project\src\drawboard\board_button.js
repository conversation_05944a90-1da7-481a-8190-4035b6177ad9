import { Board<PERSON>abel } from "./board_label";
import { BoardTool } from "./board_tool";
import { BoardView } from "./board_view";
import * as THREE from 'three';
import { PainterOrder } from "./draw_enums";

export class BoardButton extends BoardView {
    constructor(application, pos, size, imageTransparent = false) {
        super(application, pos, size);
        this.isPressed = false;
        this.clickTimeout = null;
        this.doubleClickTimeout = null;
        this.longPressTimeout = null;
        this.clickCount = 0;
        this.imageTransparent = imageTransparent
        this.touchStartPoint = { x: 0, y: 0 }
        this.renderOrder = PainterOrder.customDisplay + 2
    }

    static buttonWithImageUrl(application, pos, size, imageUrl, transparent) {
        let button = new BoardButton(application, pos, size, transparent);
        button.setImage(imageUrl);
        return button
    }



    setImage(imageUrl) {
        this.imageUrl = imageUrl
        let textureId = btoa(imageUrl)
        let application = this.application?.deref()

        application?.textureCache.loadImageTexture(textureId, imageUrl, (texture) => {
            this.setImageTexture(texture)
        })
    }

    setImageTexture(texture) {
        if (!this.imageMesh) {
            let imageGeometry = new THREE.PlaneGeometry(this.size.width, this.size.height)
            const imageMeterial = new THREE.MeshBasicMaterial({
                map: texture, precision: 'highp', transparent: this.imageTransparent
            })
            this.imageMesh = new THREE.Mesh(imageGeometry, imageMeterial)
            let imageGroup = new THREE.Group()
            imageGroup.renderOrder = this.renderOrder - 1
            imageGroup.add(this.imageMesh)
            this.add(imageGroup)
            this.imageGroup = imageGroup
        }
        else {
            this.imageMesh.material.map = texture
        }
        this.animate()
    }

    setRenderOrder(renderOrder) {
        if (this.imageGroup) {
            this.imageGroup.renderOrder = renderOrder - 1
        }
        if (this.textLabel) {
            this.textLabel.renderOrder = renderOrder
        }
        super.setRenderOrder(renderOrder)
    }

    getTextLabel() {
        if (!this.textLabel) {
            this.textLabel = new BoardLabel(this.application, new THREE.Vector3(0, 0, 0), this.size, "")
            this.textLabel.renderOrder = this.renderOrder
            this.addSubView(this.textLabel)
        }
        return this.textLabel
    }

    setFontSize(fontSize) {
        this.getTextLabel().setFontSize(fontSize)
    }

    setTextAlign(align) {
        this.getTextLabel().setTextAlign(align)
    }

    setTextColor(color) {
        this.getTextLabel().setTextColor(color)
    }

    setText(text) {
        this.getTextLabel().setText(text)
    }

    onClick(callback) {
        this.onClickCallback = callback
    }

    onDoubleClick(callback) {
        this.onDoubleClickCallback = callback
    }

    onLongPress(callback) {
        this.onLongPressCallback = callback
    }

    onTouchDown(point) {
        let result = super.onTouchDown(point)
        if (result !== this) {
            this.isPressed = false
            this.clickCount = 0
            return result
        }
        this.touchStartPoint = point
        this.isPressed = true;
        this.clickCount++;

        // 处理长按事件
        if (this.onLongPressCallback && !this.longPressTimeout) {
            this.longPressTimeout = setTimeout(() => {
                if (this.longPressTimeout) {
                    this.longPressTimeout = null
                }
                if (this.isPressed) {
                    if (this.onLongPressCallback) {
                        this.clickPoint = point
                        this.onLongPressCallback()
                        this.animate()
                    }
                    this.isPressed = false
                }
                this.clickCount = 0; // 长按后重置点击计数
            }, 600); // 长按时间阈值 500ms
        }
        return result
    }

    onTouchMove(point) {
        let result = super.onTouchMove(point)
        if (result !== this) {
            if (this.longPressTimeout) {
                clearTimeout(this.longPressTimeout);
                this.longPressTimeout = null;
            }
            this.isPressed = false
        }
        return result
    }

    onTapCancel() {
        super.onTapCancel()
        if (this.longPressTimeout) {
            clearTimeout(this.longPressTimeout);
            this.longPressTimeout = null;
        }
        if (this.clickTimeout) {
            clearTimeout(this.clickTimeout);
            this.clickTimeout = null
        }
        this.clickCount = 0
        this.isPressed = false
    }

    onTouchUp(point) {
        let result = super.onTouchUp(point)
        if (this.longPressTimeout) {
            clearTimeout(this.longPressTimeout);
            this.longPressTimeout = null;
        }
        if (!this.isPressed) {
            return result
        }
        this.isPressed = false
        
        if (this.onClickCallback && this.clickCount === 1) {
            this.clickTimeout = setTimeout(() => {
                if (this.clickCount === 1) {
                    if (this.longPressTimeout) {
                        return result
                    }
                    this.clickTimeout = null
                    if (this.onClickCallback) {
                        this.clickPoint = point
                        this.onClickCallback()
                        this.animate()
                    }
                }
                this.clickCount = 0;
            }, 200); // 双击时间阈值 250ms
        }
        else if (this.onDoubleClickCallback && this.clickCount === 2) {
            if (this.clickTimeout) {
                clearTimeout(this.clickTimeout);
                this.clickTimeout = null
            }
            this.clickCount = 0;
            if (this.onDoubleClickCallback) {
                this.clickPoint = point
                this.onDoubleClickCallback()
                this.animate()
            }
        }
        return result
    }

    dispose() {
        BoardTool.disposeGroup(this.imageGroup)
        super.dispose()
    }

}