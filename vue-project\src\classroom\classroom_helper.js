import { pinyin } from 'pinyin-pro'
import { STUDENT_INDEX_MAX } from '@/stores/answers_store'
import { StuStatus } from '@/classroom/interact_enums.js'
function arrayToIdMap(arr) {
    let map = new Map()
    arr.forEach(item => {
        map.set(item.studentId, item)
    })
    return map
}

function arrayToMacMap(arr) {
    let map = new Map()
    arr.forEach(item => {
        map.set(item.deviceMac, item)
    })
    return map
}

function arrayToSeatMap(arr) {
    // 取出最大列，最大行
    let maxCol = arr.reduce((max, item) => {
        return Math.max(max, item.locationCol ?? 0)
    }, 0)
    let maxRow = arr.reduce((max, item) => {
        return Math.max(max, item.locationRow ?? 0)
    }, 0)
    let hasLocation = true
    if (maxCol == 0 && maxRow == 0) {
        hasLocation = false
    }

    // 构造二维数组(如果学生有座位则添加)
    let seatStudentArray = []

    if (hasLocation) {
        // 规定最大为10x10
        maxCol = 10;
        maxRow = 10;

        for (let i = 1; i <= maxRow; i++) {
            let row = []
            for (let j = 1; j <= maxCol; j++) {
                let item = arr.find(item => item.locationRow === i && item.locationCol === j)
                if (item) {
                    row.push(item)
                } else {
                    row.push({})
                }
            }
            seatStudentArray.push(row)
        }

        //放入没有座位的学生{}
        let notSeatStudentArray = []
        arr.filter(stu => {
            if ((stu?.locationRow ?? 0) === 0 && (stu?.locationCol ?? 0) === 0) {
                notSeatStudentArray.push(stu)
            }
        })

        //把没有座位的学生插入座位数组中
        if (notSeatStudentArray.length > 0) {
            let i = -1;
            seatStudentArray = seatStudentArray.map(row => {
                return row.map((stu) => {
                    if (Object.keys(stu).length === 0 && stu.constructor === Object) {
                        i = i + 1;
                        if (i < notSeatStudentArray.length) {
                            return notSeatStudentArray[i]
                        } else {
                            return {}
                        }
                    } else {
                        return stu
                    }
                })
            })
        }

        // 删除空余的列和行
        //去除行
        seatStudentArray = seatStudentArray.filter(row => {
            return row.some(item => Object.keys(item).length > 0)
        })

        // 去除空列
        let columnCount = seatStudentArray[0].length;
        // 遍历每一列，检查该列所有子数组对应位置的对象是否为空
        for (let col = 0; col < columnCount; col++) {
            const allEmpty = seatStudentArray.every(subArr => Object.keys(subArr[col]).length === 0);
            // 如果该列所有的子对象都是空对象，则删除该列的所有对象
            if (allEmpty) {
                seatStudentArray.forEach(subArr => {
                    subArr.splice(col, 1); // 删除每个子数组中对应列的元素
                });
                col--; // 调整列的索引，因为删除元素后列数减少
                columnCount--; // 总列数减少
            }
        }
    }

    let map = {
        hasLocation: hasLocation,
        seatStudentArray: seatStudentArray.reverse()
    }
    return map
}

/// 按照座位排序
function arrayToLocationMap(arr) {
    //取出最大列
    let maxCol = arr.reduce((max, item) => {
        return Math.max(max, item.locationCol ?? 0)
    }, 0)

    //取出最大行
    let maxRow = arr.reduce((max, item) => {
        return Math.max(max, item.locationRow ?? 0)
    }, 0)

    let hasLocation = true
    if (maxCol == 0 && maxRow == 0) {
        hasLocation = false
    }

    if (maxCol == 0) {
        if (arr.length < 10) {
            maxCol = arr.length
        } else {
            maxCol = 10
        }
    }

    if (maxRow == 0) {
        maxRow = Math.ceil(arr.length / maxCol)
    }

    //构造数组，按照localationRow, locationCol排序
    let locationStudentArray = []
    for (let i = 1; i <= maxRow; i++) {
        let row = []
        for (let j = 1; j <= maxCol; j++) {
            let item = arr.find(item => (maxRow - item.locationRow + 1) === i && item.locationCol === j)
            if (item) {
                row.push(item)
            } else {
                row.push({})
            }
        }
        locationStudentArray.push(...row)
    }

    //locationRow为空的情况要添加
    arr.forEach(item => {
        if (!item.locationRow) {
            let indexItem = getItem(maxRow, maxCol, locationStudentArray)
            let index = indexItem.index

            if (index === -1) {
                maxRow += 1
                let rowList = [item]
                for (let i = 2; i <= maxCol; i++) {
                    rowList.push({})
                }
                locationStudentArray = [...rowList, ...locationStudentArray]
            } else {
                item.locationRow = indexItem.row
                item.locationCol = indexItem.col
                locationStudentArray[index] = item
            }
        }
    })

    // 前面的列为空的座位删除

    // 如果这一列都是空的都删除
    let delAllS = delAllSpace(maxRow, maxCol, locationStudentArray)
    maxCol = delAllS.maxCol
    locationStudentArray = delAllS.locationStudentArray

    let map = {
        hasLocation: hasLocation,
        maxCol: maxCol,
        maxRow: maxRow,
        locationStudentArray: locationStudentArray
    }

    return map
}

function delAllSpace(maxRow, maxCol, locationStudentArray) {
    let allSpace = true
    for (let row = 0; row < maxRow; row++) {
        let index = row * maxCol
        let item = locationStudentArray[index]
        if (Object.keys(item).length > 0) {
            allSpace = false
        }
    }
    if (allSpace && locationStudentArray.length > 0) {
        // 第一列全部删除 列-1
        let tempArr = [...locationStudentArray]
        for (let row = 0; row < maxRow; row++) {
            let index = row * maxCol
            let item = tempArr[index]
            item.delete = true
        }
        const newArr = tempArr.filter(item => item.delete !== true)

        maxCol -= 1
        return delAllSpace(maxRow, maxCol, newArr)
    } else {
        return { maxCol, locationStudentArray }
    }
}

function getItem(maxRow, maxCol, locationStudentArray) {
    for (let row = maxRow; row >= 1; row--) {
        for (let col = 1; col <= maxCol; col++) {
            let item1 = locationStudentArray.find(item2 => item2.locationRow === row && item2.locationCol === col)
            if (!item1) {
                let index = ((row - 1)) * maxCol + col - 1
                return {
                    index,
                    row,
                    col
                }
            }
        }
    }
    return -1
}

function arrayToGroupArr(arr, subjectMainId) {
    let groupStudentMap = {}
    arr.forEach(item => {
        let rank = item.ranks.filter(rank => rank.subjectMainId == subjectMainId)[0]
        if (rank?.name) {
            if (!groupStudentMap[rank.name]) {
                groupStudentMap[rank.name] = []
            }
            groupStudentMap[rank.name].push(item)
        } else {
            groupStudentMap['未分组'] = groupStudentMap['未分组'] || []
            groupStudentMap['未分组'].push(item)
        }
    })

    Object.values(groupStudentMap).forEach(item => {
        //对数组item进行排序
        item.sort((a, b) => a.name.localeCompare(b.name))
    })

    let keys = Object.keys(groupStudentMap)
    let values = Object.values(groupStudentMap)
    let groupArray = []
    for (let i = 0; i < keys.length; i++) {
        groupArray.push({
            'groupName': keys[i],
            'groupStudent': values[i]
        })
    }

    groupArray.forEach(item => {
        let stu = item.groupStudent[0]
        let rank = stu.ranks.find(obj => obj.name === item.groupName)
        if (rank) {
            item['rankId'] = rank.rankId
        } else {
            item['rankId'] = 0
        }
    })

    groupArray.sort((a, b) => a.rankId - b.rankId)
    // groupArray.sort((a, b) => a.groupName.localeCompare(b.groupName))

    // 给学生添加分组
    groupArray.forEach(item => {
        item.groupStudent.forEach(stu => {
            stu.rankName = item.groupName
        })
    })

    // 初始化分组选中状态
    groupArray.forEach(item => {
        item.selected = false
    })
    return groupArray
}

function arrayToLastnameMap(arr) {
    let lastnameStudentMap = {
        '全部': arr
    }

    arr.forEach(item => {
        const lastname = pinyin(item.name, { pattern: 'first', toneType: 'none' })
        const upperFirst = lastname[0].toUpperCase()
        if (!lastnameStudentMap[upperFirst]) {
            lastnameStudentMap[upperFirst] = []
        }
        lastnameStudentMap[upperFirst].push(item)
    })
    Object.values(lastnameStudentMap).forEach(item => {
        //对数组item进行排序
        item.sort((a, b) => a.name.localeCompare(b.name))
    })
    return lastnameStudentMap
}

/// 将一些数据变成字典方便查询
export function arrangeClassroomData(classroom, studentList, subjectMainId) {

    studentList.sort((a, b) => a.name.localeCompare(b.name))

    //变量初始化
    studentList.forEach(stu => {
        stu.answerSubmitIndex = STUDENT_INDEX_MAX
        stu.selected = false
        stu.score = '0'
    })

    classroom.idStudentMap = arrayToIdMap(studentList)
    classroom.macStudentMap = arrayToMacMap(studentList)
    // classroom.locationStudentMap = arrayToLocationMap(studentList)
    classroom.groupStudentArray = arrayToGroupArr(studentList, subjectMainId)
    classroom.lastnameStudentMap = arrayToLastnameMap(studentList)
    classroom.seatStudentMap = arrayToSeatMap(studentList)

    // 是否有座位
    classroom.hasLocation = classroom.locationStudentMap?.hasLocation ?? false
    // 是否有小组
    if (classroom.groupStudentArray.length > 1) {
        classroom.hasGroup = true
    } else {
        if (classroom.groupStudentArray.length > 0) {
            let groupName = classroom.groupStudentArray[0].groupName
            if (groupName == '未分组') {
                classroom.hasGroup = false
            } else {
                classroom.hasGroup = true
            }
        } else {
            classroom.hasGroup = false
        }
    }
}

export function arrangeMacAddress(classroom) {
    classroom.macStudentMap = arrayToMacMap(classroom.studentList)
}

export function getGrade(gid) {
    let grades = {
        1: '一年级',
        2: '二年级',
        3: '三年级',
        4: '四年级',
        5: '五年级',
        6: '六年级',
        7: '七年级',
        8: '八年级',
        9: '九年级',
        10: '高一',
        11: '高二',
        12: '高三',
    }
    return grades[gid]
}


export function getSubjectName(subjectId) {
    let subjects = {
        1: '语文',
        2: '数学',
        3: '英语',
        4: '物理',
        5: '化学',
        6: '生物',
        7: '地理',
        8: '历史',
        9: '政治',
    }
    let name = subjects[subjectId]
    if (name) {
        return name
    }
    return "未知"
}

export function utf8Phonetic(str) {
    return str
        .replace(/u0([0-9a-fA-F]{3})/g, (match, p1) => String.fromCharCode(parseInt(p1, 16)))
        .replace(/u0([0-9a-fA-F]{2})/g, (match, p1) => String.fromCharCode(parseInt(p1, 16)))
        .replace(/u0([0-9a-fA-F])/g, (match, p1) => String.fromCharCode(parseInt(p1, 16)));
}