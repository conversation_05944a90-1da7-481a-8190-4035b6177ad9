import { defineStore } from "pinia";
import { ref } from "vue";
import { useDrawBoardStore } from "./drawboard_store";
import { DrawMode, EditMode } from "@/drawboard/draw_enums";
import { useOfficeFilesStore } from "./office_files_store";


export const useDesktopStore = defineStore('desktop_store', () => {
    /// 桌面模式
    const showDesktop = ref(false)

    const drawMode = ref(false)

    const showDesktopButton = ref(window.electron !== undefined)

    const hideToolBar = ref(false)

    const resetNumber = ref(0)

    const showScreenShot = ref(false)

    function showDesktopView() {
        // if(showDesktop.value) {
        //     resetNumber.value ++
        //     return
        // }
        showDesktop.value = true
        drawMode.value = false
        hideToolBar.value = true
        const drawBoardStore = useDrawBoardStore()
        //画笔
        drawBoardStore.setEditMode(EditMode.Drawing)
        drawBoardStore.setDrawMode(DrawMode.pen)
        drawBoardStore.minimizePaperPenView()

        const officeStore = useOfficeFilesStore()
        officeStore.minimizeOfficeView()

        if (window.electron) {
            window.electron.setTransparent()
        }
        resetNumber.value ++
    }

    function showDrawBoard() {
        drawMode.value = true
        // 打开画板先显示工具栏
        // hideToolBar.value = false

        const officeStore = useOfficeFilesStore()
        officeStore.minimizeOfficeView()
        
        const drawBoardStore = useDrawBoardStore()
        drawBoardStore.minimizePaperPenView()
    }

    function quitDesktop() {
        if(!showDesktop.value) {
            return
        }
        drawMode.value = false
        showDesktop.value = false
        hideToolBar.value = false
        showScreenShot.value = false
        
        if (window.electron) {
            window.electron.cancelTransparent()
        }

        const drawBoardStore = useDrawBoardStore()
        drawBoardStore.normalPaperPenView()
    }

    return {
        showDesktopButton, hideToolBar,
        showDesktop, drawMode,resetNumber,showScreenShot,
        showDesktopView,
        showDrawBoard,
        quitDesktop
    }
})