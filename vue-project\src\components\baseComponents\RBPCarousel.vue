<template>
    <div class="rbp-carousel" :style="{ backgroundColor: bgColor }">
        <div class="time-selector">
            <div class="time-part" @wheel="onWheel('hours')" @touchstart="onTouchStart('hours', $event)"
                @touchmove="onTouchMove('hours', $event)" @touchend="onTouchEnd" @mousedown="onMouseDown"
                @mousemove="onMouseMove('hours', $event)" @mouseup="onMouseUp">
                <div class="time-value" v-for="(item, index) in hourValues" :key="'hour-' + index"
                    :class="{ 'time-value-selected': index == 1 }">{{ item }}</div>
            </div>
            <div class="maohao">
                <span>:</span>
            </div>
            <div class="time-part" @wheel="onWheel('minutes')" @touchstart="onTouchStart('minutes', $event)"
                @touchmove="onTouchMove('minutes', $event)" @touchend="onTouchEnd" @mousedown="onMouseDown"
                @mousemove="onMouseMove('minutes', $event)" @mouseup="onMouseUp">
                <div class="time-value" v-for="(item, index) in minuteValues" :key="'minute-' + index"
                    :class="{ 'time-value-selected': index == 1 }">{{ item }}</div>
            </div>
            <div class="maohao">
                <span>:</span>
            </div>
            <div class="time-part" @wheel="onWheel('seconds')" @touchstart="onTouchStart('seconds', $event)"
                @touchmove="onTouchMove('seconds', $event)" @touchend="onTouchEnd" @mousedown="onMouseDown"
                @mousemove="onMouseMove('seconds', $event)" @mouseup="onMouseUp">
                <div class="time-value" v-for="(item, index) in secondValues" :key="'second-' + index"
                    :class="{ 'time-value-selected': index == 1 }">{{ item }}</div>
            </div>
        </div>
        <div class="whell-bg"></div>
    </div>
</template>
<script setup>
import { onMounted, ref, computed, defineEmits } from "vue";
import { useTimeKeeperStore } from '@/stores/time_keeper.js'
import { storeToRefs } from 'pinia'

const emits = defineEmits(['updateTime'])

const timeKeeperStore = useTimeKeeperStore()
const { hours, minutes, seconds } = storeToRefs(timeKeeperStore)
const startTouch = ref(null);

const getAdjacentValues = (currentValue, maxValue) => {
    const prev = (currentValue - 1 + maxValue + 1) % (maxValue + 1);
    const next = (currentValue + 1) % (maxValue + 1);
    return [prev, currentValue, next];
};

const hourValues = computed(() => getAdjacentValues(hours.value, 23));
const minuteValues = computed(() => getAdjacentValues(minutes.value, 59));
const secondValues = computed(() => getAdjacentValues(seconds.value, 59));


const onWheel = (part) => {
    const adjustValue = (event) => {
        if (part === 'hours') {
            hours.value = (hours.value + (event.deltaY > 0 ? 1 : -1) + 24) % 24;
        } else if (part === 'minutes') {
            minutes.value = (minutes.value + (event.deltaY > 0 ? 1 : -1) + 60) % 60;
        } else if (part === 'seconds') {
            seconds.value = (seconds.value + (event.deltaY > 0 ? 1 : -1) + 60) % 60;
        }

        emits('updateTime');
    };
    document.addEventListener('wheel', adjustValue, { once: true });
};


const onTouchStart = (part, event) => {
    event.preventDefault();
    startTouch.value = event.touches[0].clientY;
};

const onTouchMove = (part, event) => {
    if (!startTouch.value) return;
    const diff = event.touches[0].clientY - startTouch.value;
    const threshold = 20; // 设置滑动距离的阈值
    if (Math.abs(diff) < threshold) {
        return;
    }
    
    const step = diff < 0 ? 1 : -1;
    if (part === 'hours') {
        hours.value = (hours.value + step + 24) % 24;
    } else if (part === 'minutes') {
        minutes.value = (minutes.value + step + 60) % 60;
    } else if (part === 'seconds') {
        seconds.value = (seconds.value + step + 60) % 60;
    }
    emits('updateTime');
    startTouch.value = event.touches[0].clientY;
};

const onTouchEnd = () => {
    startTouch.value = null;
};

const onMouseDown = (event) => {
    event.preventDefault();
    startTouch.value = event.clientY;
};

const onMouseMove = (part, event) => {
    if (!startTouch.value) return;
    const diff = event.clientY - startTouch.value;
    const threshold = 20; // 设置滑动距离的阈值
    if (Math.abs(diff) < threshold) {
        return;
    }

    const step = diff < 0 ? 1 : -1;
    if (part === 'hours') {
        hours.value = (hours.value + step + 24) % 24;
    } else if (part === 'minutes') {
        minutes.value = (minutes.value + step + 60) % 60;
    } else if (part === 'seconds') {
        seconds.value = (seconds.value + step + 60) % 60;
    }
    emits('updateTime');
    startTouch.value = event.clientY;
};

const onMouseUp = () => {
    startTouch.value = null;
};
</script>
<style lang="scss" scoped>
.rbp-carousel {
    width: 100%;
    height: 354px;
    position: relative;

    .time-selector {
        position: absolute;
        z-index: 2;
        width: 100%;
        height: 100%;
        display: flex;
        cursor: pointer;
        justify-content: space-between;
        align-items: center;

        .time-part {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;


            .time-value {
                height: 122px;
                color: var(--timer-time-color);
                font-size: 60px;
                opacity: 0.3;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .time-value-selected {
                opacity: 1;
                font-size: 85px;
            }
        }

        .maohao {
            color: var(--timer-time-color);
            font-size: 85px;
            display: flex;
            justify-content: center;
            align-items: center;

            span {
                margin-bottom: 18px;
            }
        }
    }

    .whell-bg {
        width: 100%;
        height: calc(354px / 3);
        background-color: var(--main-bc-color);
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
        opacity: 0.6;
        border-radius: calc(354px / 3);
    }
}
</style>