html[data-theme="light"] {
    /* 主色 */
    --primary-color: #25B0FA;

    /* -----------------------------------------------------文字*/
    /* 正文字 */
    --text-color: #000000;
    --anti-text-color: #FFFFFF;

    /* 辅助文字 */
    --secondary-text-color: #565656;

    /* 说明性文字 */
    --explanatory-text-color: #A9A9A9;

    /* -----------------------------------------------------背景*/
    /* 进度条底色 */
    --progress-bar-bg-color: #F6F9F9;

    /* 主背景色*/
    --main-bc-color: #FFFFFF;

    /* 辅助色 */
    --list-bc-color: #F6F9F9;

    /* 辅助色 */
    --secondary-color: #DCF1F8;
    --secondary-anti-color: #FFE4D2;

    /* 主背景相对色*/
    --main-anti-bc-color: #000000;
    --main-anti-bc-color-rgb: 0, 0, 0;

    /* 弹出框背景色 带透明度 */
    --main-anti-bc-alpha-color: #0000003A;


    /* -----------------------------------------------------边框*/
    /* 边框/底色/滚动条 */
    --border-bar-color: #EAEAEC;
    --border-bar-hover-color: #C9C9C9;
    /* 边框light主题颜色 */
    --border-light-color: #fff;
    --border-light-color-rgba:255,255,255;

    /* -----------------------------------------------------阴影*/
    /* 阴影主色 */
    --boxshaw-main-color: #000;
    --boxshaw-main-color-rgb: 0,0,0;

    /* -----------------------------------------------------单独定义*/

    /* 未完成 */
    --unfinished-color: #F7B500;

    /* 错误 */
    --error-color: #FF8533;
    --error-bg-color: #FFE4D2;

    /* 正确 */
    --correct-color: #6DD400;
    --correct-bg-color: #DEF5D5;

    /* 登录页扫码登录标题 */
    --login-scan-title: #0040AA;


    /* 辅助底色 */
    --toolbar-bg-color: #DDEBE7;
    --random-roll-top-bg-color: #FFF2DF;
    --toolbar-selected-color: #D0F4FF;
    --toolbar-color-border-color: #000000;

    /* 计时器头部背景色 */
    --timer-header-bg-color: #D0F4FF;

    /* 学生卡片背景色 */
    --student-card-bg-online-color: #EBF9F9;
    --student-card-bg-offline-color: #EAEAEC;

    /* 计时器时间颜色 */
    --timer-time-color: #1876A7;

    /* 进度条说明性文字 */
    --progress-bar-explanatory-text-color: #96A4B2;

    /* 组合题 */
    --combination-question-selector-item-bg-color: #FAFAFC;
    --combination-question-selector-item-head-bg-color: #F4F4F4;

    /* 表格间隔色 */
    --table-interval-color: #F6F9F9;
    /*课程管理背景色 */
    --classroom-manage-bc-color:#EFF4ED;
    /*随堂练习日历顶部 */
    --course-record-calendar-header-bc-color:#ECD9CB;
    /*随堂练习日历悬浮顶部 */
    --course-record-calendar-header-bc-top-color:#FAF5F2;
    /* 练习题目选中颜色 */
    --record-question-bg-color: #D0F4FF;
    /* 课堂悬浮icon底色 */
    --classroom-overflow-icon-bc: #5F9A9E;
    /* 新消息计数 */
    --new-message-count-bc: #F11616;
}