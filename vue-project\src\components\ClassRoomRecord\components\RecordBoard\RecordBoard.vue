<template>
    <div class="single-board-body">
        <div class="board-body">
            <RecordDrawBoard class="drawboard" v-if="recordDrawBoardPainter" :painter="recordDrawBoardPainter">
            </RecordDrawBoard>
        </div>
        <ClearScreenAlert v-if="!showClassRoomRecord"></ClearScreenAlert>
    </div>
</template>

<script setup>
import {onMounted, onUnmounted, watch, } from 'vue'
import { storeToRefs } from 'pinia'
import ClearScreenAlert from '@/components/TabBar/ClearScreenAlert.vue'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { useRecordDrawboardStore } from '@/stores/record_drawboard_store'
import RecordDrawBoard from './RecordDrawBoard.vue'
const classroomUIStore = useClassroomUIStore()
const { showClassRoomRecord, } = storeToRefs(classroomUIStore)

const recordDrawBoardStore = useRecordDrawboardStore()
const { recordDrawBoardPainter,isFullScreen } = storeToRefs(recordDrawBoardStore)
const props = defineProps({
    image: {
        default: ''
    },
    id: {
        default: ''
    },
    isReview: {
        default: false // 是否是讲评单个学生界面
    },
    students:{
        default:[]
    },
    showWidth: {
        default: 0
    },
    showHeight: {
        default: 0
    },
})


watch(props, (newV, oldV) => {
    if (props.id || props.image) {
        initBoard()
    }

})






onMounted(() => {
    if(props.showHeight){
        recordDrawBoardStore.setShowSize({showWidth:props.showWidth * window.devicePixelRatio,showHeight:props.showHeight * window.devicePixelRatio})
    }
    if (props.image||props.students.length) {
               
        
        initBoard()
    }
})
//初始化
function initBoard() {
    if(!props.isReview){
        if (props.image) {
            recordDrawBoardStore.clearDrawBoard()
            recordDrawBoardStore.addImageToBoard(props.image, false, true)
        }
    }else{
        
        recordDrawBoardPainter.value.drawStudents(props.students)
    }
}

onUnmounted(() => {
    isFullScreen.value = false
    recordDrawBoardStore.clearDrawBoard()
    if(recordDrawBoardPainter.value){
        recordDrawBoardPainter.value.clearAll()
    }
})

defineExpose({ initBoard });
</script>

<style lang="scss" scoped>
.single-board-body {
    width: 100%;
    height: 100%;
    position: relative;
    .board-body {
        height: 100%;
        width: 100%;
        background-color: #0B423D;
        position: absolute;
        right: 0;
        top: 0;
    }

}
</style>