import axios from "axios"
import { HttpEnv, serverHost, ServerUrls } from "./server_urls"
import { App<PERSON><PERSON><PERSON>, UserChannel } from "@/app_verson"
import { loginInstance } from "@/login_instance/login_instance"

export class LoginRequest {
    static async login(account, password, authCode, uniqueId) {
        let params = {
            "username": account.trim(),
            "password": window.btoa(password),
            "mode": 0,
            "authCode": authCode.trim(),
            "uniqueId": uniqueId.trim(),
            "tel": "",
            "code": "",
        }
        const url = ServerUrls.loginUrl()
        try {
            const response = await axios.post(url, params)
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }

    //获取验证码图片
    static async getAuthCode() {
        let url = ServerUrls.getAuthCodeUrl()
        try {
            const response = await axios.post(url)
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }

    //激活码
    static async doActive(code, deviceId, install) {
        let data = {
            "cateType": "INTERACTION_CLASS",
            "activeType": install ? 1 : 2,
            "activiationCode": code,
            "mac": deviceId,
        }
        let url = ServerUrls.activeCodeUrl()
        try {
            const response = await axios.post(url, data)
            return response.data
        } catch (e) {
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }

    static defaultConfig() {
        return {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': loginInstance.token ?? localStorage.getItem('token'),
                "Ink-Mgboard-Subject-Id": loginInstance.subjectMainId,
                "Robot-Selected-Role-Codes": "MGBOARD_TEACHER"
            }
        }
    }

    static defaultLaoShanConfig(token) {
        return {
            headers: {
                'Content-Type': 'application/json',
                'token': token,
            }
        }
    }

    //添加log
    static async loginLog(isLogin){
        
        
        if(!loginInstance.deviceId){
            return
        }
        let data = {
            "channelSource": serverHost.appConfig.user_channel == 'public'?0:1,
            "clientTag": loginInstance.deviceId,
            "cateType": "INTERACTION_CLASS",
            "channelSource": 1,
            "clientVersion": AppVersion,
            "eventType": !isLogin ? 0 : 1,
            "osName": navigator.userAgent,
        }
        let url = ServerUrls.loginLogUrl()
        try {
            const response = await axios.post(url, data,this.defaultConfig())
            return response.data
        } catch (e) {
            console.error("添加登录log失败",e);
            
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }

    static async loginReceive(schoolId){
        
        
        if (serverHost.env != HttpEnv.online || serverHost.userChannel != UserChannel.laoshan){
            return
        }
        

        let authUrl = ServerUrls.loginReceiveAuth()

        let url = ServerUrls.loginReceiveUrl()
        try {
            
            let authData = {
                "appId":appId,
                "appSecret":appSecret,
            }
            const authRes = await axios.post(authUrl,authData,this.defaultLaoShanConfig())
            
            if(authRes.data&&authRes.data.result&&authRes.data.result.token){
                let data = {
            "appId": appId,
            "tenantId": schoolId,
        }
                const response = await axios.post(url, data,this.defaultLaoShanConfig(authRes.data.result.token))
            }
        } catch (e) {
            console.error("添加登录log失败",e);
            
            return {
                code: -1,
                message: '网络错误'
            }
        }
    }
}

const appId = "777775035052982603"

const appSecret= "cW9PeXdpa2ZQdDAyQ3V6QXdBZE10dTRWTEJyY1ZTQ1M="