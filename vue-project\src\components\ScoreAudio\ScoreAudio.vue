<template>
    <div class="score-audio-container" v-if="showScoreAudio">
        <div class="content">
            <img :src="gifUrl">
        </div>
    </div>
    <audio ref="scoreAudioRef" :src="scoreAudioUrl" controls preload="auto" autoplay
        :style="{ display: 'none' }"></audio>
</template>
<script setup>
import { ref, watch, onMounted } from 'vue'
import { useScoreAudioStore } from '@/stores/score_audio_store'
import { storeToRefs } from 'pinia';

const scoreAudioStore = useScoreAudioStore()
const { scoreAudioRef, scoreAudioUrl, showScoreAudio, gifUrl } = storeToRefs(scoreAudioStore)

onMounted(() => {
    scoreAudioStore.setListener()
})

</script>
<style lang="scss" scoped>
.score-audio-container {
    position: absolute;
    height: 100%;
    width: 100%;
    background-color: rgba($color: #000000, $alpha: 0.2);
    opacity: 1;
    z-index: var(--toolbar-random-roll-high-z-index);

    .content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 300px;
        height: 300px;
        // background-color: white;
        border-radius: 10px;
        display: flex;
        flex-direction: column;

        img {
            width: 100%;
            height: 100%;
        }
    }
}
</style>