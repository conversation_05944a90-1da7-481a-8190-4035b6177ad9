<template>
    <div class="updater" v-if="showUpdater">
        <div class="content box-shade">
            <div class="title">
                最新版本：{{ versionName }}
            </div>
            <div class="con">
                <div class="item" v-for="(item, index) in updateList" :key="index">
                    {{ item }}
                </div>
                <!-- {{ updateDescription }} -->
            </div>
            <div class="btns">
                <div v-if="updateStatusName == UpdateType.MANDATORY" class="btn1">
                    <div class="forcedUpgrade" @click="confirm">升级</div>
                </div>
                <div v-else class="btn2">
                    <div class="cancel" @click="cancel">取消</div>
                    <div class="confirm" @click="confirm">升级</div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { useUpdaterStore } from '@/stores/updater_store'
import { UpdateType } from '@/server_request/app_updater'
import { LocalRequest } from "@/local_request/local_request"
import { ElLoading } from 'element-plus'

const updaterStore = useUpdaterStore()
const { updateDescription, updateStatusName, versionName, updateList, downloadUrl } = storeToRefs(updaterStore)
const classroomUIStore = useClassroomUIStore()
const { showUpdater } = storeToRefs(classroomUIStore)
function cancel() {
    showUpdater.value = false
}

async function confirm() {
    // 点击升级调用rust下载
    let loading = ElLoading.service({ background: 'transparent' })
    await LocalRequest.updateApp(downloadUrl.value)
    loading.close()
}
</script>
<style lang="scss" scoped>
.updater {
    position: absolute;
    height: 100%;
    width: 100%;
    background-color: rgba($color: #000000, $alpha: 0);
    opacity: 1;
    z-index: 100;
    display: flex;
    justify-content: center;
    align-items: center;
    .box-shade{
        box-shadow: 0px 0px 12px rgba($color: var(--boxshaw-main-color-rgb), $alpha: 0.15);
    }

    .content {
        width: 30%;
        height: 60%;
        background-color: #fff;
        border-radius: 10px;
        padding: 20px;
        display: flex;
        flex-direction: column;

        .title {
            font-size: 22px;
            align-self: center;
            height: 60px;
            line-height: 60px;
        }

        .con {
            flex: 1;
            width: 100%;
            font-size: 18px;
            padding: 20px;
            box-sizing: border-box;
            overflow-y: scroll;
            scrollbar-width: none;
            // background-color: yellow;

            &::-webkit-scrollbar {
                display: none;
            }

            .item {
                margin-bottom: 10px;
            }
        }

        .btns {
            height: 60px;
            width: 100%;
            padding: 0 20px;
            box-sizing: border-box;
            align-self: center;
            font-size: 18px;
            -webkit-app-region: no-drag;

            .btn1 {
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;

                .forcedUpgrade {
                    width: 60%;
                    height: 40px;
                    line-height: 40px;
                    text-align: center;
                    border-radius: 6px;
                    background-color: #007DFF;
                }
            }

            .btn2 {
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;

                .cancel {
                    width: 30%;
                    height: 40px;
                    line-height: 40px;
                    text-align: center;
                    background-color: grey;
                    border-radius: 6px;
                    color: white;
                    cursor: pointer;
                    margin-right: 20px;
                }

                .confirm {
                    width: 30%;
                    height: 40px;
                    line-height: 40px;
                    text-align: center;
                    background-color: #007DFF;
                    border-radius: 6px;
                    color: white;
                    cursor: pointer;
                    margin-left: 20px;
                }
            }
        }
    }
}
</style>