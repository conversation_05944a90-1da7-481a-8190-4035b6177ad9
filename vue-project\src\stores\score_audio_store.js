import { defineStore } from "pinia"
import { ref, computed } from "vue"


export const useScoreAudioStore = defineStore('score_audio', () => {

    const scoreAudioRef = ref(null)
    const scoreAudioUrl = ref('')
    const showScoreAudio = ref(false)

    const gifUrl = ref('')

    function setListener() {
        scoreAudioRef.value.addEventListener('canplaythrough', () => {
            // 缓冲完成，可以播放
            scoreAudioRef.value.play();
        });
    }

    function play(score) {
        if(parseInt(score) > 0) {
            gifUrl.value = '/img/icon_score_audio.gif'
        } else if(parseInt(score) < 0) {
            gifUrl.value = '/img/icon_score_audio_minus2.gif'
        }

        let url = ''
        if (parseInt(score) < -10) {
            url = '/static/sound/score/-多多努力.mp3'
        } else if (parseInt(score) > 10) {
            url = '/static/sound/score/+继续加油.mp3'
        } else {
            score = score.toString()
            if (!score.includes('+') && !score.includes('-')) {
                score = '+' + score
            }
            url = '/static/sound/score/' + score + '.mp3'
        }
        if (!scoreAudioRef.value.pause) {
            scoreAudioRef.value.pause()
        }
        scoreAudioUrl.value = url
        scoreAudioRef.value.load()
        showScoreAudio.value = true
        setTimeout(function () {
            cleanData()
        }, 2000)
    }

    function cleanData() {
        gifUrl.value = ''
        scoreAudioUrl.value = ''
        showScoreAudio.value = false
    }

    return {
        gifUrl,
        scoreAudioRef,
        scoreAudioUrl,
        showScoreAudio,
        setListener,
        play,
    }
})