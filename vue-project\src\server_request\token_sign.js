import CryptoJS from "crypto-js"

class TokenSign {

    generateSignature(method, url, params, key) {
        // 将参数按照字典序排序
        const sortedParams = Object.keys(params).sort().map(k => `${k}=${params[k]}`).join('&');
        // 将请求方法、URL、URL 编码后的参数用 & 连接起来
        const message = method.toUpperCase() + "&" + encodeURIComponent(url) + "&" + encodeURIComponent(sortedParams).replace(/%2F/g, "%252F");
        // message = message.replace("%27", "")
        key = key + "&";
        // 使用 HMAC 算法计算签名
        const hmac = CryptoJS.HmacSHA1(message, key);
        const sign = CryptoJS.enc.Base64.stringify(hmac).replace(/\+/g, '-').replace(/\//g, '_');
        return sign;
    }


    _getSecretKey(password) {
        // Hash the password once using SHA1
        let hashedPassword = CryptoJS.SHA1(CryptoJS.enc.Utf8.parse(password)).toString(CryptoJS.enc.Hex);
        // Hash the result again using SHA1
        hashedPassword = CryptoJS.SHA1(CryptoJS.enc.Hex.parse(hashedPassword)).toString(CryptoJS.enc.Hex);
        // Convert the hashed password to a WordArray and take the first 16 bytes
        const secretKey = CryptoJS.enc.Hex.parse(hashedPassword.substring(0, 32));
        return secretKey;
    }

    _padContent(content) {
        const paddingLength = 16 - (content.length % 16);
        const padding = String.fromCharCode(paddingLength).repeat(paddingLength);
        return content + padding;
    }

    _unpadContent(content) {
        const paddingLength = content.charCodeAt(content.length - 1);
        return content.slice(0, -paddingLength);
    }

    aesEncrypt(content, password) {
        const key = this._getSecretKey(password);
        const contentPadded = this._padContent(content);
        const encrypted = CryptoJS.AES.encrypt(contentPadded, CryptoJS.enc.Utf8.parse(key), {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.NoPadding
        });
        return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);
    }

    _unpadContent(content) {
        // Remove padding manually, assuming PKCS7 padding
        const paddingChar = content.charCodeAt(content.length - 1);
        const padLength = paddingChar < 16 ? paddingChar : 0;
        return content.slice(0, content.length - padLength);
    }
    
    aesDecrypt(content, password) {
        const key = this._getSecretKey(password);
        const decrypted = CryptoJS.AES.decrypt(content, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7
        });
    
        // Decrypt to a WordArray and then convert to a Utf8 string
        const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);
    
        return this._unpadContent(decryptedText);
    }
}


const tokenSign = new TokenSign()


export {
    tokenSign
}
