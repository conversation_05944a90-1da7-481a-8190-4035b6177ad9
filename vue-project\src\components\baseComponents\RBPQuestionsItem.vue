<template>
    <div class="rbp-questions-item">
        <div class="item-icon">
            <img :src="props.img">
        </div>
        <div class="item-title">
            {{ props.title }}
        </div>
        <div class="item-submit">
            {{ props.content }}
        </div>
    </div>
</template>
<script setup>
import { defineProps } from 'vue'
import { RBPColors } from '@/components/baseComponents/RBPColors.js'

const props = defineProps({
    img: String,
    title: String,
    content: String,
})
</script>
<style lang="scss" scoped>
.rbp-questions-item {
    width: 375px;
    height: 78px;
    background-color: var(--progress-bar-bg-color);
    border-radius: 15px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .item-icon {
        margin-left: 12px;
        width: 48px;
        height: 48px;
        
        img {
            width: 100%;
            height: 100%;
        }
    }

    .item-title {
        color: var(--secondary-text-color);
        font-size: 21px;
    }

    .item-submit {
        margin-right: 13px;
        color: var(--secondary-text-color);
        font-size: 21px;
    }
}
</style>