<template>
    <div v-show="showDesktop === false || (showDesktop && (desktopDrawMode || paperViewState === ViewStatus.normal))"
        id="tab-bar" class="black-toolbar" ref="toolbarRef">
        <div class="black-toolbar-center">
            <ToolbarItem :item="TabBarItem.preview" v-if="!isPaperpen()" :selected="previewMode"
                @itemClick="sceneReduct"></ToolbarItem>
            <ToolbarItem :item="TabBarItem.restoreView" v-if="isPaperpen()" @itemClick="restoreViewClick"></ToolbarItem>
            <ToolbarColorItem :item="TabBarItem.colorSelect" @itemClick="colorSelectClick"></ToolbarColorItem>
            <ToolbarItem :item="TabBarItem.canvasOptionArrow" :active="editMode === EditMode.Dragging"
                @itemClick="canvasOptionArrowClick"></ToolbarItem>
            <ToolbarItem :item="TabBarItem.canvasOptionPen"
                :active="editMode === EditMode.Drawing && drawMode === DrawMode.pen" @itemClick="canvasOptionPenClick">
            </ToolbarItem>
            <ToolbarItem :item="TabBarItem.canvasOptionEraser"
                :active="editMode === EditMode.Drawing && drawMode === DrawMode.eraser"
                @itemClick="canvasOptionEraserClick" @canvasOptionEraserDoubleClick="canvasOptionEraserDoubleClick">
            </ToolbarItem>
            <ToolbarItem :item="TabBarItem.cleanScreen" @itemClick="cleanScreenClick"></ToolbarItem>
            <ToolbarItem :item="TabBarItem.undoBrush" @itemClick="canceLine"></ToolbarItem>
            <ToolbarItem :item="TabBarItem.plane2D" @itemClick="showPlane2D" v-if="isPaperpen() === false"></ToolbarItem>
            <ToolbarItem :item="TabBarItem.writingGrids" @itemClick="showWrintingGrids" v-if="isPaperpen() === false">
            </ToolbarItem>
            <ToolbarItem :item="TabBarItem.mathCalculator" @itemClick="showMathCalculator"></ToolbarItem>
            <!-- <ToolbarItem :item="TabBarItem.undoBrush" @itemClick="saveBlackBoard"></ToolbarItem>
            <ToolbarItem :item="TabBarItem.undoBrush" @itemClick="deserializeBlackBoard"></ToolbarItem> -->
        </div>
    </div>
</template>
<script setup>
import { TabBarItem } from '@/components/TabBar/tabbar_enums'
import ToolbarItem from '@/components/TabBar/ToolbarItem.vue'
import ToolbarColorItem from '@/components/TabBar/ToolbarColorItem.vue'
import { storeToRefs } from 'pinia'
import { useDrawBoardStore } from '@/stores/drawboard_store'
import { useClassroomStore } from '@/stores/classroom'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { useTeachPlanStore } from '@/stores/teach_plan'
import { DrawMode, EditMode } from '../../drawboard/draw_enums'
import { databaseHelper, RecordType } from '@/classroom/class_flow_helper'
import { useDesktopStore } from '@/stores/desktop_store'
import { tdRecord } from '@/utils/talkingdata_tool'
import { useRandomRollCallStore } from '@/stores/random_roll_call_store'
import { ViewStatus } from '@/classroom/frame_enums'
import { useInteractStore } from '@/stores/interact_store'
import { InteractMode } from '@/classroom/interact_enums'

const interactStore = useInteractStore()
const { interact, interactMode } = storeToRefs(interactStore)

const randomRollCallStore = useRandomRollCallStore()

const drawBoardStore = useDrawBoardStore()
const { showColorSelect, editMode, drawMode, showEraserSelect,
    previewMode, blackBoardPainter, paperPenPainter, paperViewState } = storeToRefs(drawBoardStore)

const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)

const classroomUIStore = useClassroomUIStore()
const { showScoreStatistics, showStudentList, showSystem, showRandomRollCall,
    showTimeKeeper, mainContentTopSpace, showClassRoomRecord, showCloudDisk,
    showRecordType, showClearScreenAlert, toolbarRef,
    hideClassRoomRecord, showBluetoothAp } = storeToRefs(classroomUIStore)

const teachPlanStore = useTeachPlanStore()
const { showTeachPlan, treeList } = storeToRefs(teachPlanStore)


const desktopStore = useDesktopStore()
const { showDesktop, drawMode: desktopDrawMode } = storeToRefs(desktopStore)

function systemSettingClick(item) {
    //系统
    showSystem.value = true
}

//判断是否是最小化
function judgeMin(type) {
    if (showClassRoomRecord.value && hideClassRoomRecord.value) {
        if (type != showRecordType.value) {
            showClassRoomRecord.value = false
        }
        hideClassRoomRecord.value = false
    }
}

async function classRecordsClick(item) {
    //随堂记录
    await databaseHelper.addLines(RecordType.classRecord)
    judgeMin('interact')
    showRecordType.value = 'interact'
    setTimeout(() => {
        showClassRoomRecord.value = true
    }, 100)
}

async function beikeSourceClick(item) {
    //我的云盘
    await databaseHelper.addLines(RecordType.beikeSource)
    showCloudDisk.value = true
}

async function bluetoothClick(item) {
    showBluetoothAp.value = true
}

async function homeworkCalendarClick(item) {
    //作业讲评
    await databaseHelper.addLines(RecordType.homeworkCalendar)
    judgeMin('homework')
    showRecordType.value = 'homework'
    setTimeout(() => {
        showClassRoomRecord.value = true
    }, 100)
}

function teachPlanClick(item) {
    //导学案
    if (treeList.value.length == 0) {
        teachPlanStore.show(selectedClassroom.value)
    } else {
        showTeachPlan.value = true
    }
}

async function studentsScoreRateClick(item) {
    //得分统计
    showScoreStatistics.value = true
    await databaseHelper.addLines(RecordType.scoreStatistics)
}

function sceneReduct(item) {
    //预览视图
    if (previewMode.value) {
        if (blackBoardPainter.value) {
            blackBoardPainter.value.resetInstallFrame()
        }
    }
    else {
        if (blackBoardPainter.value) {
            blackBoardPainter.value.resetMaxFrame()
        }
    }

    previewMode.value = !previewMode.value
    drawBoardStore.updateEraserWidth(drawBoardStore.pxEraserWidth)
    // if(previewMode.value){
    //     drawBoardStore.pxEraserWidth = PxEraserWidthShow.default
    // }else{
    //     drawBoardStore.pxEraserWidthBorad = PxEraserWidthShow.boardInit
    // }



    // drawBoardStore.updateEraserWidth(previewMode.value?drawBoardStore.pxEraserWidth:drawBoardStore.pxEraserWidthBorad)


}

function restoreViewClick(item) {
    if (paperPenPainter.value) {
        paperPenPainter.value.updateDisplayBoards()
    }
}

function colorSelectClick(item) {
    //颜色
    showColorSelect.value = true
}

function canvasOptionArrowClick(item) {
    //选择
    drawBoardStore.setEditMode(EditMode.Dragging)
}

function canvasOptionPenClick(item) {
    //画笔
    drawBoardStore.setEditMode(EditMode.Drawing)
    drawBoardStore.setDrawMode(DrawMode.pen)
}

function canvasOptionEraserClick(item) {
    //橡皮
    drawBoardStore.setEditMode(EditMode.Drawing)
    drawBoardStore.setDrawMode(DrawMode.eraser)
}

function canvasOptionEraserDoubleClick() {
    // 调整橡皮粗细
    showEraserSelect.value = true
}

function cleanScreenClick(item) {
    //先判断是不是在别的画布上
    if (drawBoardStore.paperViewState == ViewStatus.normal
        || classroomUIStore.showRecordResult
        || (classroomUIStore.showClassRoomRecord && !classroomUIStore.hideClassRoomRecord)
        || classroomUIStore.showClassRecord
    ) {
        //清屏
        showClearScreenAlert.value = true
    } else {
        drawBoardStore.showClearSelect = true
    }

    // drawBoardStore.clearDrawBoard()
}

function isPaperpen() {
    if (interactStore.showPaperPenView() && paperViewState.value === ViewStatus.normal) {
        return true;
    } else {
        return false;
    }
}
function canceLine() {

    if (isPaperpen()) {
        if (paperPenPainter.value) {
            paperPenPainter.value.cancelALine()
        }
    } else {
        //撤销画笔
        if (blackBoardPainter.value) {
            blackBoardPainter.value.cancelALine()
        }
    }
}

function saveBlackBoard() {
    drawBoardStore.saveBlackBoard()
}


function deserializeBlackBoard() {
    drawBoardStore.deserializeBlackBoard()
}

async function randomRollClick(item) {
    //随机点名
    randomRollCallStore.setup(selectedClassroom.value.studentList)
    showRandomRollCall.value = true
    await databaseHelper.addLines(RecordType.randomRollCall)
}

function studentListClick(item) {
    //展开学生
    showStudentList.value = !showStudentList.value
}

function timeKeepingClick(item) {
    //计时
    showTimeKeeper.value = true
    tdRecord('开启计时')
}

function halfscreenClick(item) {
    //下拉屏幕
    if (mainContentTopSpace.value !== 0) {
        mainContentTopSpace.value = 0
    }
    else {
        mainContentTopSpace.value = window.innerHeight / 3
    }
}

function desktopClick(item) {
    desktopStore.showDesktopView()
}

function sidebarClick(item) {
    //侧边栏
    if (interactMode.value === InteractMode.none) {
        interactMode.value = InteractMode.small
    } else if (interactMode.value === InteractMode.small) {
        interactMode.value = InteractMode.big
    } else if (interactMode.value === InteractMode.big) {
        interactMode.value = InteractMode.none
    }
}

function showPlane2D() {
    drawBoardStore.plane2DSelector.showSelector = true
}

function showWrintingGrids() {
    drawBoardStore.writingGridsSelector.showSelector = true
}

function showMathCalculator() {
    
}

</script>
<style lang="scss" scoped>
.black-toolbar {
    // width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    overflow: hidden;
    position: relative;
    // justify-content: center;
    background-color: v-bind("(showDesktop && desktopDrawMode) ? 'white' : 'transparent'");
    border-radius: 10px;

    .black-toolbar-center {
        margin-left: 10px;
        margin-right: 10px;
        display: flex;
    }
}
</style>