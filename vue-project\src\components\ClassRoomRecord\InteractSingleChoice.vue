<template>
	<div class="class-room-record-detail">
		<div class="boardImg" @click="openBoard(false)" v-show="showBoard">
			<el-image :src="boardUrl" fit="contain"></el-image>
		</div>
		
		<div class="top">
			<div class="optionList" v-if="currMode == '学生统计'">
				<el-radio-group v-model="radio">
					<div class="optionItem" v-for="item in updatedList" :key="item.option"
						:class="radio === item.option ? 'right' : 'error'">
						<div class="optionTitle">
							<div class="optionName" :class="radio === item.option ? 'right' : ''">
								<el-radio :label="item.option" :value="item.option" @change="setRadio" :disabled="queryData.type == '13'">
									{{ item.option }}：{{ item.optionStuPOs.length }}人
								</el-radio>
							</div>
						</div>
						<div class="userList">
							<div class="userItem" v-for="(stu, index) in item.optionStuPOs" :key="index">
								<div class="avatar"><img :src="stu.imgUrl" /></div>
								<div class="name" :class="stu.stuName.length > 3 ? 'ellipsis' : ''">{{
									stu.stuName }}
								</div>
								<div class="name" v-if="stu.groupName">分组：{{ stu.groupName }}</div>
								<div class="name" v-else>&nbsp;</div>
							</div>
						</div>
					</div>
					<div class="optionItem" v-for="item in answerInfo" :key="item.option"
						:style="{ 'display': item.calculate ? 'none' : 'block' }">
						<div class="optionTitle">
							<div class="optionName">
								<el-radio :label="item.option" :value="item.option" @change="setRadio" disabled>
									{{ item.option }}：{{ item.optionStuPOs.length }}人
								</el-radio>
							</div>
						</div>
						<div class="userList">
							<div class="userItem" v-for="(stu, index) in item.optionStuPOs" :key="index">
								<div class="avatar"><img :src="stu.imgUrl" /></div>
								<div class="name" :class="stu.stuName.length > 3 ? 'ellipsis' : ''">{{
									stu.stuName }}
								</div>
								<div class="group" v-if="stu.groupName">分组：{{ stu.groupName }}</div>
								<div class="name" v-else>&nbsp;</div>
							</div>
						</div>
					</div>
				</el-radio-group>
			</div>
			<div class="optionCharts" v-show="currMode == '选项统计'">
				<div class="charts">
					<BaseQuestion :answer-student="answerInfo" :ques-type="quesType" :answer="answerTip"
						:options="updatedList"></BaseQuestion>
				</div>
			</div>
		</div>
		<div class="footer-title">
			<div class="ratio-button">
				<RBPSegButton :options="['选项统计', '学生统计']" :currentValue="currMode" @updateCurrentValue="changeMode">
				</RBPSegButton>
			</div>
			<RBPButton :btn-selected="true" @click="openBoard(true)" btn-type="big" v-if="boardUrl || question"
				:btn-text="question ? '查看题目' : '查看黑板图'">

			</RBPButton>
		</div>
	</div>
	<el-dialog title="题目" :visible.sync="dialogVisible" width="70%">
		<div class="ques-body" v-if="question" v-html="question.quesBody"></div>
		<img v-if="boardUrl" :src="boardUrl" />
		<span slot="footer" class="dialog-footer">
		</span>
	</el-dialog>
</template>

<script setup>
import { ClassroomRecordRequest } from '@/server_request/classroom_record';
import { ElMessage } from 'element-plus';
import { ref, nextTick, computed, onMounted, onUnmounted } from 'vue';
import BaseQuestion from './components/BaseQuestion.vue';
import RBPButton from '../baseComponents/RBPButton.vue';

import { useInteractStore } from '@/stores/interact_store';
import RBPSegButton from '../baseComponents/RBPSegButton.vue';
const interactStore = useInteractStore()




//黑板绘图
const boardUrl = ref('')
//底部选中
const currMode = ref('选项统计')
//query参数
const queryData = interactStore.interactResult
const dialogVisible = ref(false)

//问题信息
const question = ref()
//正确答案
const answerTip = ref('')
//tab子项内容信息
let oldData = null
let multipleData = null
//加载获取互动信息
async function loadRecordInfo() {
	let params = {
		interactId: queryData.interactId,
		classId: queryData.classId,
	}
	try {
		let { data } = await ClassroomRecordRequest.interactionStatistics(params)
		boardUrl.value = data.imgUrl
		question.value = data.question
		answerTip.value = data.answer
		if (quesType.value == 2 && answerTip.value) {
			answerTip.value = answerTip.value == "True" ? '对' : "错"
		}
		if (queryData.type == '4') { // 如果是多选题，重新组织答案选项
			let tempdata = JSON.parse(JSON.stringify(data))
			tempdata.answerStudents = resetType(tempdata.answerStudents, tempdata.answer)
			multipleData = getResponse(tempdata)
		}
		oldData = getResponse(data)
		setRes(oldData)
	} catch (e) {
		console.log(e)
	}
}
const answerInfo = ref([])
const updatedList = ref([])
const radio = ref()
const checked = ref([])
const quesType = ref(queryData.type)


//是否打开黑板
const showBoard = ref(false)
//打开黑板
function openBoard(data) {
	if (question.value) {
		dialogVisible.value = data
	} else {
		showBoard.value = data;
	}
}
//数据自处理
function getResponse(data) {
	if (data.answerStudents && data.answerStudents[0] && data.answerStudents[0].option == "错") {
		data.answerStudents.reverse();
	}
	let response = {
		optionList: [
			...(data.answerStudents ?? []),
			{
				isCorrOption: "999",
				calculate: 0,
				option: ("未作答"),
				optionStuPOs: data.unAnswerStudents || [],
			},
		],
		blackImg: data.imgUrl,
		question: data.question
	};
	if (data && data.answerStudents) {
		response.optionList.forEach((item) => {
			if (item.calculate === undefined) {
				item.calculate = 1;
				item.isCorrOption = item.isRight;
			}
			item.optionStuPOs.forEach((stu) => {
				stu.stuId = stu.studentId;
				stu.stuName = stu.studentName;
				stu.imgUrl = stu.headUrl || "http://res.mgboard.com/avatar/online.png";
				stu.op = stu.answer;
			});
		});
	}
	return response
}
function resetType(students, answer) {
	let result = []
	students.forEach(item => {
		item.optionStuPOs.forEach(ite => {
			let index = result.findIndex(it => { return it.option == ite.answer })
			if (index < 0) {
				result.push({
					option: ite.answer,
					isRight: ite.answer == answer ? "1" : "0",
					optionStuPOs: [ite]
				})
			} else {
				let idx = result[index].optionStuPOs.findIndex(it => { return it.studentId == ite.studentId })
				if (idx < 0) {
					result[index].optionStuPOs.push(ite)
				}
			}
		})
	})
	// 如果没有正确答案，则塞一个正确答案
	let idx = result.findIndex(it => { return it.option == answer })
	if (idx < 0) {
		result.push({
			option: answer,
			isRight: "1",
			isCorrOption: "1",
			calculate: 1,
			optionStuPOs: []
		})
	}
	return result
}
function setRes(res) {
	answerInfo.value = res.optionList;
	updatedList.value = answerInfo.value.filter(item => item.calculate === 1)
	if (quesType.value != 4) {
		initRadio();
	}

}

function selectedOption() {
	let arr = [];
	for (let option of updatedList.value) {
		if (option.isCorrOption === "1") {
			arr.push(option.option);
		}
	}
	return arr.join("");
}


function initRadio() {
	for (let option of updatedList.value) {
		if (option.isCorrOption === "1") {
			radio.value = option.option;
		}
	}
}
//重新新设选中相
function setRadio(val) {
	if(queryData.type == '13') {
		// console.log('投票 不能点击')
		return;
	}
	radio.value = val;
	let list = updatedList.value;
	for (let i = 0; i < list.length; i++) {
		if (list[i].option === val) {
			updatedList.value[i].isCorrOption = "1";
		} else {
			updatedList.value[i].isCorrOption = "0";
		}
	}

	postAnswer();
}
function selectedAns(index) {
	if (question.value) {
		ElMessage.error('课堂练习题目不允许设置答案')
		return
	}
	selectedAnswer(index)
}
//选择答案
function selectedAnswer(index) {
	if (index < 0) {
		return
	}
	if (quesType.value === "4") {
		if (updatedList.value[index].isCorrOption === "1") {
			updatedList.value[index].isCorrOption = "0";
		} else {
			updatedList.value[index].isCorrOption = "1";
		}
	} else {
		let list = updatedList.value;
		for (let i = 0; i < list.length; i++) {
			if (list[i].option === updatedList.value[index].option) {
				updatedList.value[i].isCorrOption = "1";
			} else {
				updatedList.value[i].isCorrOption = "0";
			}
		}
	}

	postAnswer();
}
//设置答案
async function postAnswer() {
	if (dialogVisible.value) {
		return
	}
	let right = selectedOption();
	let params = {
		answer:
			right == "错"
				? "False"
				: right == "对"
					? "True"
					: right,
		interactId: queryData.interactId,
	};
	ClassroomRecordRequest
		.interactionSetAnswer(params)
		.then((res) => {
			answerTip.value = right
		})
		.catch(function (error) {
			console.log(error);
		});
}
function updateBar(val, index) {
	updatedList.value[index].isCorrOption = val ? "1" : "0";

	postAnswer();
}
function changeMode(e) {
	currMode.value = e;
	if (queryData.type == '4') {
		if (currMode.value == '学生统计') {
			setRes(multipleData)
			quesType.value = 3
		} else {
			setRes(oldData)
			quesType.value = 4
		}
	}
	if (quesType.value != 4) {
		initRadio();
	} else {
		let checkes = [];
		updatedList.value.forEach((item, index) => {
			if (item.isCorrOption === "1") {
				checkes.push(item.option);
			}
		});
		checked.value = checkes;
	}
}

onMounted(() => {

	loadRecordInfo()

})

</script>
<style lang="scss">
.class-room-record-detail {
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;

	.top {
		.el-radio-group {
			width: 100% !important;
		}

		.el-radio__label {
			font-size: var(--el-radio-font-size);
			padding-left: 8px;
			font-weight: 500;
			font-size: 21px;
			line-height: 36px;
		}

		.el-radio__input.is-checked .el-radio__inner {
			border-color: var(--correct-color) !important;
			background: var(--correct-color) !important;
		}
		.el-radio__input.is-disabled+span.el-radio__label {
			color: var(--unfinished-color);
		}

		.el-radio__inner {
			width: 18px;
			height: 18px;
		}

		.el-radio__inner:hover {
			border-color: var(--correct-color) !important;
		}



		.optionItem.right {
			// border: 2px solid var(--correct-color);

			.el-radio__label {
				color: var(--correct-color) !important;
			}
		}

		.optionItem.error {
			// border: 2px solid var(--error-color);

			.el-radio__label {
				color: var(--error-color) !important;
			}
		}

	}
}
</style>
<style lang="scss" scoped>
.class-room-record-detail {


	.charts {
		width: 788px;
		height: 600px;
		// min-width: 560px;
	}

	.top {
		height: calc(100% - 132px);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: end;
	}


	.boardImg {
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		background: rgba(0, 0, 0, 0.5);
		z-index: 99;
		display: flex;
		justify-content: center;
		box-sizing: border-box;

		.btn_quit_img {
			width: 100px;
			display: flex;
			font-size: 16px;
			align-items: center;
			justify-content: end;
			position: absolute;
			right: 24px;
			vertical-align: middle;
			cursor: pointer;
			bottom: 20px;
			color: white;

			img {
				width: 24px;
				height: 24px;
				margin-right: 8px;
			}
		}
	}

	.boardImg .imgWrap {
		display: flex;
		align-items: center;
	}

	.boardImg img {
		display: block;
		// width: 100%;
		width: 90%;
		object-fit: contain;
	}

	.optionList {
		box-sizing: border-box;
		height: 100%;
		padding: 30px 30px 0 30px;
		overflow: auto;
	}

	.selectArea {
		box-sizing: border-box;
		padding-top: 6%;
		padding-right: 9%;
		padding-left: 10%;
		position: absolute;
		background-color: #00d97f;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 10001;
		display: flex;
		justify-content: space-around;
	}

	.selectArea .area {
		background: rgba(73, 175, 79, .15);
		margin-right: 2px;
		flex: 1;
		color: #000;
	}

	.answerTip {
		color: #2e4a66;
		font-size: 20px;
		padding-top: 20px;
	}

	.checkBoard {
		width: 48px;
		height: 24px;
		background: #9dbeda;
		color: #fff;
		font-size: 15px;
		border-radius: 0.03rem;
		line-height: 0.42rem;
		margin: 0.2rem auto;
	}

	.optionItem {
		border-radius: 17px;
		margin-bottom: 15px;
		padding: 12px 25px;
		border: 2px solid var(--border-bar-color);
		width: calc(100% - 6px);
		background-color: var(--list-bc-color);
	}





	.optionName.right {
		color: #2e4a66;
	}

	.userList {
		text-align: left;
		min-height: 40px;
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		gap: 12px 0px;
		box-sizing: border-box;
		padding-top: 16px;
	}

	.userList.right {
		background: rgba(73, 175, 79, 0.15);
	}

	.userList .userItem {
		width: 10%;
		height: 120px;
		// margin-top: 16px;
		// margin-right: 16px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		text-align: center;
		
	}

	.userList .userItem .avatar img {
		width: 48px;
		height: 48px;
		border-radius: 50%;
	}

	.userList .userItem .name {
		width: 100%;
		text-align: center;
		font-weight: 500;
		font-size: 18px;
		color: var(--text-color);
		margin-top: 10px;
	}

	.userList .userItem .group {
		width: 100%;
		text-align: center;
		font-weight: 500;
		font-size: 16px;
		color: var(--secondary-text-color);
		margin-top: 2px;
	}

	.userList .userItem .name.ellipsis {
		white-space: nowrap;
		text-overflow: ellipsis;
	}


	.footer-title {
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		box-sizing: border-box;
		height: 54px;
		margin-bottom: 36px;
		margin-top: 42px;
		width: 100%;

		.ratio-button {
			position: absolute;
			left: 40px;
			display: flex;
		}
	}
}
</style>