<template>
    <div class="rbp-content">
        <div class="rbp-title" v-show="props.title != undefined">
            <span>{{ props.title }}</span>
        </div>
        <div class="rbp-div" :class="{ 'rbp-div-border-radius': props.haveBorderRadius }">
            <slot name="rbpDiv"></slot>
        </div>
        <div class="rbp-btns">
            <slot name="rbpBtns"></slot>
        </div>
        <div class="rbp-bottom-right">
            <div class="minimize" v-show="props.showMini" @click.stop="miniClick">
                <img src="/img/svg/icon_small.svg">
            </div>
            <div class="exit" @click.stop="closeClick" v-if="!hideClose">
                <img src="/img/svg/icon_close.svg">
            </div>
        </div>
    </div>
</template>
<script setup>
import { computed, defineEmits, defineProps, onMounted } from 'vue'

const emits = defineEmits(['close', 'mini'])

const props = defineProps({
    title: String,
    showMini: Boolean,
    haveBorderRadius: {
        type: Boolean,
        default: true
    },
    hideClose:{
        default:false
    }
})

function miniClick() {
    emits('mini')
}

function closeClick() {
    emits('close')
}
</script>
<style lang="scss" scoped>
@import "@/assets/scss/mixin.scss";

.rbp-content {
    background-color: var(--main-bc-color);
    width: 100%;
    height: 100%;
    border-radius: 26px;
    display: flex;
    flex-direction: column;

    .rbp-title {
        height: 60px;
        border-radius: 26px 26px 0px 0px;
        background-color: var(--secondary-color);
        font-size: 30px;
        color: var(--text-color);
        @include flex(start, center);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        span {
            margin-left: 30px;
        }
    }

    .rbp-div {
        flex: 1;
        border-radius: 26px 26px 0px 0px;

        overflow-y: auto;
        scrollbar-width: none;
        
        &::-webkit-scrollbar {
            display: none;
            /* 适用于 Chrome, Safari 和 Opera */
        }
    }

    .rbp-div-border-radius {
        border-radius: 0;
    }

    .rbp-btns {
        width: 100%;
    }

    .rbp-bottom-right {
        position: absolute;
        right: 0;
        bottom: 0;
        height: 32px;
        display: flex;
        justify-content: flex-end;
        margin-right: 20px;
        margin-bottom: 20px;

        .minimize {
            width: 32px;
            height: 32px;
            margin-right: 28px;
            cursor: pointer;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .exit {
            width: 32px;
            height: 32px;
            cursor: pointer;

            img {
                width: 100%;
                height: 100%;
            }
        }
    }
}
</style>