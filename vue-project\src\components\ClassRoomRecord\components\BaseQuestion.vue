<template>
    <div class="base-question-body">
        <div :class="size == 'big'? 'question-content':'question-content-small'">
            <div :class="size == 'big'? 'top':'top-small'">
                <div v-if="props.qIndex !== -1">{{ `第${props.qIndex}题  `  }}&nbsp</div>
                <div class="name">{{ getTypeName({type:props.quesType})  }}</div>
                <div v-if="props.quesType != Interact.vote" class="right-answer" :style="answer?'':{color:'var(--explanatory-text-color)'}">{{ answer?`正确答案:${answer}`:'请选择正确答案'}}</div>
                <div v-if="props.ratio" class="ratio-text">正确率：{{ ratio }}</div>
            </div>
            <div class="bar-chart">
                <div class="bar-item" v-for="(item,index) in props.options" :key="index">
                    <div>{{ !item.optionStuPOs?'0人':`${item.optionStuPOs.length}人` }}</div>
                    <div class="bar-area">
                        <div class="bar" :style="{height:`${dealPercent(item.optionStuPOs)}%`,background: item.isRight == 1?'var(--correct-color)':'var(--error-color)'}"></div>
                    </div>
                    <div class="bottom-options">{{ item.option }}</div>
                </div>
                <div class="bottom-line"></div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch ,onMounted} from 'vue';
import { getTypeName } from '../js/utils';
import { Interact } from '@/classroom/interact_enums';


const props = defineProps(
    {
        size:{
            default:"big",
        } ,// small big
        answer:{
            default:'',
        },
        options:{
            default:[]
        },
        quesType:{
            default:0
        },
        answerStudent:{
            default:[],
        },
        qIndex:{
            default:-1
        },
        ratio:{
            default : ""
        },
        isMulti:{
            default: false
        }
    }
)

watch(props,()=>{
    dealData()
})
onMounted(()=>{
    dealData()
})

//处理百分比
function dealPercent(data){
    let percent = 0;
    
    
    if(data&&data.length&&allCount.value){
        percent = Math.round(data.length / allCount.value * 100)
    }
    return percent
}
const allCount = ref(0)

//获取总人数
function dealData(){
    
    if(props.answerStudent&&props.answerStudent.length){
        let sId = []
        props.answerStudent.forEach((e)=>{
            if(e.optionStuPOs&&e.optionStuPOs.length){
                e.optionStuPOs.forEach((v)=>{
                    if(v.studentId&&!sId.includes(v.studentId)){
                        sId.push(v.studentId)
                    }
                })
            }
            if(e.stus&&e.stus.length){
                e.stus.forEach((v)=>{
                    if(v.studentId&&!sId.includes(v.studentId)){
                        sId.push(v.studentId)
                    }
                })
            }
        })
        allCount.value = sId.length
    }else{
        allCount.value = 0
    }
    
}
</script>

<style lang="scss" scoped>
.base-question-body{
    width: 100%;
    height: 100%;
    .small-border{
        border-radius: 30px;
        border: 1px solid var(--border-bar-color);
    }
    .question-content{
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border-radius: 60px;
        border: 2px solid var(--border-bar-color);

        .bar-chart{
            flex: 1;
            margin-top: 42px;
            padding: 0px 24px;
            position: relative;
            display: flex;
            .bar-item{
                flex: 1;
                display: flex;
                align-items: center;
                flex-direction: column;
                font-weight: 500;
                font-size: 30px;
                color: var(--explanatory-text-color);
                line-height: 35px;
                text-align: center;
                .bar-area{
                    flex: 1;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: end;
                    background-color: var(--progress-bar-bg-color);
                    border-radius: 30px 30px 0px 0px;
                    width: 36px;
                    box-sizing: border-box;
                    margin-top: 12px;
                    .bar{
                        border-radius: 30px 30px 0px 0px;
                        width: 36px;
                    }
                }
                .bottom-options{
                    margin-top: 7px;
                    margin-bottom: 6px;
                }
            }
            .bottom-line{
            position: absolute;
            box-sizing: border-box;
            width: calc(100% - 48px);
            height: 2px;
            bottom: 46px;
            left: 24px;
            background-color: var(--border-bar-color);
        }
        }
        
    }
    .top{
        display: flex;
        padding-left: 42px;
        padding-top: 12px;
        font-size: 30px;
        color: var(--text-color);
        line-height: 72px;
        .name{
            font-weight: 500;
            margin-right: 68px;
        }
        .right-answer{
            color: var(--correct-color);
        }
        

    }
    .question-content-small{
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border-radius: 30px;
        border: 1px solid var(--border-bar-color);

        .bar-chart{
            flex: 1;
            margin-top: 20px;
            padding: 0px 12px;
            position: relative;
            display: flex;
            .bar-item{
                flex: 1;
                display: flex;
                align-items: center;
                flex-direction: column;
                font-weight: 500;
                font-size: 15px;
                color: var(--explanatory-text-color);
                line-height: 18px;
                text-align: center;
                .bar-area{
                    flex: 1;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: end;
                    background-color: var(--progress-bar-bg-color);
                    border-radius: 15px 15px 0px 0px;
                    width: 18px;
                    box-sizing: border-box;
                    margin-top: 6px;
                    .bar{
                        border-radius: 15px 15px 0px 0px;
                        width: 18px;
                    }
                }
                .bottom-options{
                    margin-top: 4px;
                    margin-bottom: 2px;
                }
            }
            .bottom-line{
            position: absolute;
            box-sizing: border-box;
            width: calc(100% - 24px);
            height: 1px;
            bottom: 24px;
            left: 12px;
            background-color: var(--border-bar-color);
        }
        }
        
    }
    .top-small{
        display: flex;
        padding:0px 21px;
        padding-top: 6px;
        font-size: 15px;
        color: var(--text-color);
        line-height: 36px;
        font-weight: 500;
        .name{
            margin-right: 34px;
        }
        .right-answer{
            color: var(--correct-color);
            margin-right: 4px;
        }
        .ratio-text{
            flex: 1;
            text-align: end;
            color: var(--correct-color);
        }
    }
    

}
</style>