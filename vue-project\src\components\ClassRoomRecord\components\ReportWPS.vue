<template>
    <div class="record-report-wps-body" v-if="props.url">
        <iframe style="width: 100%; height: 100%; background-color: aqua;" :src="`${props.url}#toolbar=0&navpanes=0&scrollbar=0`"></iframe>
        <div class="top-title" :style="{transform: `scale(${scale})`}" >
            <RBPButton  :btn-selected="true" :btn-text="props.title" ></RBPButton>
        </div>
    </div>
</template>

<script setup>
import RBPButton from '@/components/baseComponents/RBPButton.vue';
const props = defineProps(
    {
        url:{
            default:""
        },
        title:{
            default:''
        },
        scale:{
            default:1
        }
    }
)

function getBound(){
    return `${props.scale * 24}px`
}
</script>

<style lang="scss" scoped>
.record-report-wps-body{
    width: 100%;
    height: 100%;
    color: #fff;
    position: relative;
    .top-title{
        transform-origin: top left;
        position: absolute;
        left: v-bind('getBound()');
        top: v-bind('getBound()');
        

    }
}
</style>