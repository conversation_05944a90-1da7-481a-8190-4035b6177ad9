<template>
    <div class="rbp-seg-button" :class="{ disable: props.disable }">
        <div class="btn"
            :class="{ left: (index === 0), right: (index === props.options.length - 1), selected: (currentValue === item) }"
            v-for="(item, index) in props.options" :key="index" @click="segClick(item)">
            {{ item }}
        </div>
    </div>
</template>
<script setup>
import { computed, defineProps, ref, defineEmits } from 'vue'

const props = defineProps({
    currentValue: Number,
    options: Array,
    disable: <PERSON><PERSON><PERSON>,
})
const width = computed(() => {
    return (1 / props.options.length * 100) + '%'
})
const emits = defineEmits(['updateCurrentValue'])
function segClick(item) {
    emits('updateCurrentValue', item)
}
</script>
<style lang="scss" scoped>
.rbp-seg-button {
    width: 246px;
    height: 54px;
    border: 1px solid var(--border-bar-color);
    border-radius: 10px;
    box-sizing: border-box;
    display: flex;

    .btn {
        width: v-bind(width);
        height: 100%;
        background-color: var(--main-bc-color);
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 21px;
        color: var(--text-color);
        cursor: pointer;
    }

    .selected {
        background-color: var(--primary-color);
        color: var(--anti-text-color);
    }

    .left {
        border-radius: 10px 0px 0px 10px;
    }

    .right {
        border-radius: 0px 10px 10px 0px;
    }
}
.disable {
    pointer-events: none;
    opacity: 0.5;
}
</style>