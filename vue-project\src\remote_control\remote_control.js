import { LocalUrls } from "@/local_request/local_urls"
import { loginInstance } from '@/login_instance/login_instance'
import { useClassroomStore } from '@/stores/classroom'
import { ControlOptions, LoginOptions, PictureOptions, RtcOptions, PptOptions, InteractOptions, MultiStateSync, VideoOptions } from "./control_options"
import { useDrawBoardStore } from "@/stores/drawboard_store"
import roomUpdater from "@/classroom/classroom_updater"
import { MessageSender } from "./message_sender"
import { webRtcServer } from "./webrtc_server"
import { useDesktopStore } from "@/stores/desktop_store"
import { useOfficeFilesStore } from "@/stores/office_files_store"
import { useInteractStore } from '@/stores/interact_store'
import { Interact, InteractStatus, MultiQusLecture } from '@/classroom/interact_enums.js'
import { chooseSelectAnswerClick, chooseStopInteractClick } from '@/remote_control/select_option_common'
import { useAnswersStore } from '@/stores/answers_store.js'
import { ElLoading, ElMessage } from 'element-plus'
import { ClassRoomRequest } from '@/server_request/classroom_request'
import { Alert } from '@/utils/alert'
import { useClassroomUIStore } from "@/stores/classroom_ui_store"
import { getCurrentDateTime } from "@/utils/date_time"
import { useVideoPlayStore } from "@/stores/video_play_store"
import utils from "@/components/CloudDisk/js/utils"
import { useTimeKeeperStore } from "@/stores/time_keeper"
import { useTeachPlanStore } from "@/stores/teach_plan"
import { ViewStatus } from "@/classroom/frame_enums"
import { useRandomRollCallStore } from "@/stores/random_roll_call_store"
import { databaseHelper, RecordType } from "@/classroom/class_flow_helper"
import { StudentListType, useStudentListStore } from "@/stores/student_list_store"
import { useEvaluationScoreStore } from "@/stores/evaluation_score_store"
import { dbHelper } from "@/utils/db_helper"
import { useScoreStatisticsStore } from "@/stores/score_statistics_store"
import { LocalRequest } from "@/local_request/local_request"

class RemoteControl extends MessageSender {
    constructor() {
        super()
        if (!RemoteControl.instance) {
            RemoteControl.instance = this
        }
        return RemoteControl.instance
    }

    setup() {
        this.controlMethods = {}

        let loginHandles = {}
        loginHandles[LoginOptions.startClass] = this.handleStartClass
        loginHandles[LoginOptions.stopClass] = this.handleStopClass
        this.controlMethods[ControlOptions.login] = loginHandles


        let pictureHandles = {}
        pictureHandles[PictureOptions.addPictures] = this.handleAddPictures
        pictureHandles[PictureOptions.deletePicture] = this.handleDeletePicture
        pictureHandles[PictureOptions.stopShow] = this.handlePictureStopShow
        this.controlMethods[ControlOptions.takePicture] = pictureHandles

        let videoHandles = {}
        videoHandles[VideoOptions.addVideo] = this.handleAddVideos
        this.controlMethods[ControlOptions.takeVideo] = videoHandles

        let rtcHandles = {}
        rtcHandles[RtcOptions.offer] = this.handleReceiveRTCOffer
        rtcHandles[RtcOptions.answer] = this.handleReceiveRTCAnswer
        rtcHandles[RtcOptions.candidate] = this.handleReceiveRTCCandidate
        this.controlMethods[ControlOptions.rtcConnect] = rtcHandles

        let pptHandles = {}
        pptHandles[PptOptions.nextPage] = this.handlePptNextPage
        pptHandles[PptOptions.prevPage] = this.handlePptPrevPage
        pptHandles[PptOptions.close] = this.handlePptClose
        pptHandles[PptOptions.hide] = this.handlePptHide
        this.controlMethods[ControlOptions.pptPage] = pptHandles

        let interactHandles = {}
        interactHandles[InteractOptions.state] = this.handleInteractState
        interactHandles[InteractOptions.start] = this.handleStartInteract
        interactHandles[InteractOptions.select] = this.handleSelectInteract
        interactHandles[InteractOptions.rolling] = this.handleRollingInteract
        interactHandles[InteractOptions.stop] = this.handleStopInteract
        interactHandles[InteractOptions.answer] = this.handleAnswerInteract
        interactHandles[InteractOptions.explain] = this.handleExplainInteract
        interactHandles[InteractOptions.multiQuestionsSelect] = this.handleMultiQuestionsSelectInteract
        interactHandles[InteractOptions.multiQuestionsSelectAnswer] = this.handleMultiQuestionsSelectAnswerInteract
        interactHandles[InteractOptions.multiQuestionsNumber] = this.handleMultiQuestionNumber
        interactHandles[InteractOptions.cloudDisk] = this.handleCloudDiskInfo
        interactHandles[InteractOptions.homework] = this.handleClassRecord
        interactHandles[InteractOptions.classRecord] = this.handleClassRecord
        interactHandles[InteractOptions.scores] = this.handleScores
        interactHandles[InteractOptions.timer] = this.handleTimer
        interactHandles[InteractOptions.randomPick] = this.handleRandomPick
        interactHandles[InteractOptions.aiHelp] = this.handleAIHelp
        interactHandles[InteractOptions.teachPlan] = this.handleTeachPlan
        interactHandles[InteractOptions.changeScore] = this.handleChangeScore
        interactHandles[InteractOptions.studentList] = this.handleStudentList
        interactHandles[InteractOptions.screenState] = this.handleScreentState
        this.controlMethods[ControlOptions.interact] = interactHandles
        this.startSse()
    }

    startSse() {
        const url = LocalUrls.remoteSseEventUrl()
        var sse = new EventSource(url)
        sse.retry = 2000
        sse.addEventListener("message", (e) => {
            const event = JSON.parse(e.data)
            this.handleMessage(event)
        })
    }


    handleMessage(event) {
        let control = event.control
        let option = event.option
        if (remoteControl.checkState(option)) {
            remoteControl.sendOperating()
            return
        }
        if (this.controlMethods[control]) {
            let handle = this.controlMethods[control][option]
            if (handle) {
                handle(event)
            }
        }
    }


    async handleStartClass(params) {
        if (roomUpdater.currentClassroom) {
            //课堂内
            //判断是不是一样的classId 。。。，不一样重新整合数据
            if (params.teacherId==loginInstance.teacher.teacherId&&params.classId == roomUpdater.currentClassroom.classId && params.subjectId == roomUpdater.currentClassroom.subjectId) {
                await roomUpdater.handleStartClass(params, true, true)
            } else {
                await roomUpdater.handleStartClass(params, true)
            }
        } else {
            //课堂外直接整合数据
            await roomUpdater.handleStartClass(params, false)
        }
    }

    checkState(option) {

        if ([InteractOptions.aiHelp,
        InteractOptions.homework,
        InteractOptions.classRecord,
        InteractOptions.scores,
        InteractOptions.timer,
        InteractOptions.randomPick,
        InteractOptions.changeScore,
        InteractOptions.studentList].includes(option)) {
            const classroomUIStore = useClassroomUIStore()
            if (classroomUIStore.showAIHelp
                || classroomUIStore.showClassRoomRecord
                || classroomUIStore.showScoreStatistics && option != InteractOptions.scores
                || classroomUIStore.showStudentList && option != InteractOptions.studentList
                || classroomUIStore.showTimeKeeper && option != InteractOptions.timer
                || classroomUIStore.showRandomRollCall && option != InteractOptions.randomPick
                || classroomUIStore.showRecordResult
            ) {
                return true
            } else {
                return false
            }
        }


        else {
            return false
        }
    }

    handleStopClass(params) {
        if (roomUpdater.currentClassroom) {
            roomUpdater.stopClass()
        }

    }

    /// 添加照片
    handleAddPictures(params) {
        let taskId = params.taskId
        let urls = params.urls
        const drawBoardStore = useDrawBoardStore()
        if (drawBoardStore.blackBoardPainter) {
            drawBoardStore.blackBoardPainter.addUploadPictures(taskId, urls)
            drawBoardStore.previewMode = false
            drawBoardStore.minimizePaperPenView()

            const desktopStore = useDesktopStore()
            if (window.electron) {
                window.electron.unignoreMosue()
            }
            desktopStore.drawMode = true

            const officeStore = useOfficeFilesStore()
            officeStore.minimizeOfficeView()
        }
    }

    handleDeletePicture(params) {
        let taskId = params.taskId
        let urls = params.urls
        const drawBoardStore = useDrawBoardStore()
        if (drawBoardStore.blackBoardPainter) {
            drawBoardStore.blackBoardPainter.deleteUploadPicture(taskId, urls)
        }
    }
    /// 添加照片
    handleAddVideos(params) {
        let taskId = params.taskId
        let url = params.urls[0]
        const classroomUIStore = useClassroomUIStore()
        // if(!classroomUIStore.show){
        //     classroomUIStore.realTimeVideo.count++
        //     classroomUIStore.realTimeVideo.show = false
        // }
        const videoPlayStore = useVideoPlayStore()
        classroomUIStore.realTimeVideo.list.push(
            {
                url,
                taskId,
                "time": getCurrentDateTime()
            }
        )
        videoPlayStore.fileUrl = url;
        videoPlayStore.displayName = "视频" + classroomUIStore.realTimeVideo.list.length
        classroomUIStore.showVideoPlayView = true
    }

    handlePictureStopShow(params) {

    }


    handleReceiveRTCOffer(params) {
        if (params.code === 1) {
            webRtcServer.setupRTC(params.offer)
        }
    }

    handleReceiveRTCAnswer(params) {
        if (params.code === 1) {
            webRtcServer.receiveAnswer(params.answer)
        }
    }

    handleReceiveRTCCandidate(params) {
        if (params.code === 1) {
            webRtcServer.addCandidate(params.candidate)
        }
    }

    handlePptNextPage() {
        const officeStore = useOfficeFilesStore()
        officeStore.nextPage = officeStore.nextPage + 1
    }

    handlePptPrevPage() {
        const officeStore = useOfficeFilesStore()
        officeStore.prevPage = officeStore.prevPage - 1
    }
    handlePptHide() {
        const officeStore = useOfficeFilesStore()
        officeStore.remoteFlag++;
        officeStore.remoteStatus = ViewStatus.minimize
    }
    handlePptClose() {
        const officeStore = useOfficeFilesStore()
        officeStore.remoteFlag++;
        officeStore.remoteStatus = ViewStatus.closed
    }

    handleInteractState(params) {
        // console.trace('调用栈如下：');
        const interactStore = useInteractStore()
        // 组合题
        const answerStore = useAnswersStore()
        const classroomUIStore = useClassroomUIStore()
        let args = {}
        if (interactStore.interact === Interact.multiQuestions) {
            if (answerStore.mutilQuestionSelectorShow) {
                //选择题目页面
                args.multiState = MultiStateSync.select
                args.numList = answerStore.numList.flat()
            } else {
                if (interactStore.interactStatus == InteractStatus.undelivered) {
                    //开始答题
                    args.multiState = MultiStateSync.start
                    args.startQuestions = answerStore.startQuestions
                } else {
                    if (interactStore.interactStatus == InteractStatus.underway) {
                        if (answerStore.lecture == MultiQusLecture.question) {
                            //开始讲解
                            args.multiState = MultiStateSync.explain
                            args.multiRightAnswers = answerStore.multiRightAnswers
                            args.currentQuestionIndex = answerStore.currentQuestionIndex
                            args.startQuestions = answerStore.startQuestions
                        }
                    }
                }
            }
        } else {
            // 单选题 多选题 判断题
            args.rightAnswer = answerStore.rightAnswer
        }
        // 添加其他状态
        const classroomStore = useClassroomStore()
        const studentListStore = useStudentListStore()
        let groupNames = []
        if (classroomStore.selectedClassroom.groupStudentArray) {
            classroomStore.selectedClassroom.groupStudentArray.forEach(group => {
                if (group.selected) {
                    groupNames.push(group.groupName)
                }

            })
        }
        const evaluationScoreStore = useEvaluationScoreStore()

        args.studentList = {
            "list": classroomStore.selectedClassroom.studentList,
            "show": classroomUIStore.showStudentList,
            "tab": studentListStore.currentSelectedTitle == StudentListType.name
                ? 0 : 1,
            "groups": groupNames,
            "name": studentListStore.lastnameFlag,
            "scoreType": evaluationScoreStore.alone ? 1 : 2,
            "review":classroomUIStore.showStudentCategoryScore,
        }

        args.electron = window.electron != null
        args.reviews = {
            "evaluationAddList":evaluationScoreStore.evaluationAddList,
            "evaluationMinusList":evaluationScoreStore.evaluationMinusList,
            "evaluationList":evaluationScoreStore.evaluationList,
            "fixedCorrectList":evaluationScoreStore.fixedCorrectList,
            "fixedErrorList":evaluationScoreStore.fixedErrorList
        }




        const officeStore = useOfficeFilesStore()
        if (officeStore.currentFileItem != null) {
            args.wps = {
                url: officeStore.currentFileItem.url,
                orignUrl: officeStore.currentFileItem.orignUrl,
                fileName: officeStore.currentFileItem.fileName,
                isMin: officeStore.minimized
            }
        }




        const scoreStatisticsStore = useScoreStatisticsStore()
        args.score = {
            "sort": scoreStatisticsStore.sort,
            "type": scoreStatisticsStore.type,
            "show": classroomUIStore.showScoreStatistics,
            "showDetail":classroomUIStore.showScoreStatisticsDetail,
            "title":scoreStatisticsStore.title,
            "detailId":scoreStatisticsStore.statisticId
        }

        const timerStore = useTimeKeeperStore()
        args.timer = {
            "second": timerStore.totalSeconds,
            "current": timerStore.currentSeconds,
            "countType": timerStore.timerValue,
            "isOpen": classroomUIStore.showTimeKeeper,
            "isCounting": timerStore.isCounting,
        }
        const randomStore = useRandomRollCallStore()
        args.random = {
            "show": classroomUIStore.showRandomRollCall,
            "isMin": randomStore.isMinimize,
            "stu": randomStore.stu,
        }
        args.evaluation = loginInstance.evaluation



        remoteControl.sendInteractStateResult({
            interact: interactStore.interact,
            interactStatus: interactStore.interactStatus,
            params: args
        })
    }

    handleStartInteract(params) {
        roomUpdater.startInteract(params.interact)

    }

    handleSelectInteract(params) {
        const interactStore = useInteractStore()
        if (interactStore.interact == Interact.singleChoice
            || interactStore.interact == Interact.multiChoice
            || interactStore.interact == Interact.trueFalse) {
            chooseSelectAnswerClick({ option: params.select, selected: false })
        }

    }

    handleRollingInteract(params) {
        roomUpdater.stopInteract()

    }

    handleStopInteract(params) {
        const interactStore = useInteractStore()
        if (interactStore.interact == Interact.singleChoice
            || interactStore.interact == Interact.multiChoice
            || interactStore.interact == Interact.trueFalse
            || interactStore.interact == Interact.responder
            || interactStore.interact == Interact.vote) {
            chooseStopInteractClick()
        } else if (interactStore.interact == Interact.paperPen
            || interactStore.interact == Interact.classTest
            || interactStore.interact == Interact.chineseWriting
            || interactStore.interact == Interact.examMode
            || interactStore.interact == Interact.multiQuestions) {
            roomUpdater.stopInteract()
        }

    }

    async handleMultiQuestionsSelectInteract(params) {
        //组合题 选择题目
        const answerStore = useAnswersStore()
        let list = answerStore.numList.flat()
        let index = params.index
        let type = params.type
        let item = list[index]
        if (type == "单选题") {
            item.single = !item.single
            item.multi = false
            item.just = false
        } else if (type == "多选题") {
            item.multi = !item.multi
            item.single = false
            item.just = false
        } else if (type == "判断题") {
            item.just = !item.just
            item.single = false
            item.multi = false
        }
        remoteControl.sendMultiQuestionSelectResult(params)
    }

    async handleAnswerInteract(params) {
        //组合题 开始答题
        const answerStore = useAnswersStore()
        answerStore.startQuestions = [...params.startQuestions]

        // interactStatus.value = InteractStatus.undelivered
        answerStore.mutilQuestionSelectorShow = false

        let loading = ElLoading.service({ background: 'transparent' })
        const res = await ClassRoomRequest.interactStart(Interact.multiQuestions)
        if (res.code == 1) {
            roomUpdater.didStartInteract(Interact.multiQuestions, res.data.interactId, res.data.taskId, answerStore.startQuestions)
        } else {
            Alert.showErrorMessage(res.message)
        }
        loading.close()


    }

    // 组合题 开始讲解
    async handleExplainInteract(params) {
        const answerStore = useAnswersStore()
        const interactStore = useInteractStore()
        interactStore.interactStatus = InteractStatus.underway
        answerStore.lecture = MultiQusLecture.question
        roomUpdater.stopAnswer()

        remoteControl.sendExplainInteractResult({})
    }

    // 组合题 选择答案
    async handleMultiQuestionsSelectAnswerInteract(params) {
        let index = params.index
        const answerStore = useAnswersStore()
        answerStore.currentQuestionIndex = index
        let item = answerStore.comOptionList.find(item => item.option == params.item.option)
        answerStore.selectComOption(item)
    }

    // 组合题 选择题号
    async handleMultiQuestionNumber(params) {
        let index = params.index
        const answerStore = useAnswersStore()
        answerStore.currentQuestionIndex = index
        remoteControl.sendMultiQuestionNumberResult({ index })
    }
    // 接收云盘信息
    async handleCloudDiskInfo(param) {
        if (param.file) {
            utils.cloudFilePreview(param.file)
        } else if (param.url && window.electron) {
            window.electron.downloadWithUrl(param.url)
        }
    }


    async handleClassRecord(param) {
        var item = param.item
        if (!item) {
            return
        }
        let classId = item.classId
        let classRecordId = item.classRecordId
        let interactId = item.interactId
        let type = item.type
        let taskId = item.taskId

        const interactStore = useInteractStore()
        interactStore.interactResult = {
            classId,
            classRecordId,
            interactId,
            type,
            taskId,
            remote: true
        }
        let classroomUIStore = useClassroomUIStore()
        if (type == Interact.classTest || type == Interact.homework) {
            classroomUIStore.showRecordResult = true

        } else {
            classroomUIStore.showClassRoomRecord = true
            classroomUIStore.showRecordType = "interact"
        }


    }
    async handleAIHelp(param) {

    }
    async handleScores(param) {

        
        const classroomUIStore = useClassroomUIStore()
        const scoreStatisticsStore = useScoreStatisticsStore()
        if (param.show) {
            classroomUIStore.showScoreStatistics = true
            scoreStatisticsStore.currentValue = "学生明细"
            scoreStatisticsStore.type = 2
        } else if (param.show === false) {
            classroomUIStore.showScoreStatistics = false
        } else if (param.sort !== undefined) {
            scoreStatisticsStore.remoteFlag++;
            scoreStatisticsStore.sort = param.sort
        } else if (param.tab) {
            scoreStatisticsStore.currentValue = param.tab
            scoreStatisticsStore.type = param.tab == "学生明细" ? 2 : 1
        }else if(param.id !=null){
            scoreStatisticsStore.title = param.title
            scoreStatisticsStore.statisticId = param.id
            classroomUIStore.showScoreStatisticsDetail = param.open
        }

    }
    async handleTeachPlan(param) {
        if (param.item) {
            const teachPlanStore = useTeachPlanStore()

            teachPlanStore.showTeachPlan = false
            const drawBoardStore = useDrawBoardStore()
            drawBoardStore.addTeachPlan(param.item)
            drawBoardStore.previewMode = false
            if (param.item.stages) {
                teachPlanStore.curerntStage = param.item.stages[0]
            }
        }
    }
    async handleRandomPick(param) {
        const randomStore = useRandomRollCallStore()
        const classroomUIStore = useClassroomUIStore()
        const classroomStore = useClassroomStore()
        if (param.status == ViewStatus.normal) {

            if (param.student) {
                let stu = param.student
                classroomStore.selectedClassroom.studentList.forEach((e) => {
                    if (e.studentId == stu.studentId) {
                        stu = e
                    }
                })
                classroomUIStore.showRandomRollCall = true
                randomStore.setup(classroomStore.selectedClassroom.studentList, null, stu)
                randomStore.stu = stu
                databaseHelper.addLines(RecordType.randomRollCall)

            }
        } else if (param.status == ViewStatus.minimize) {
            randomStore.isMinimize = true
            classroomUIStore.showRandomRollCall = false
        } else if (param.status == ViewStatus.closed) {
            randomStore.isMinimize = false
            classroomUIStore.showRandomRollCall = false
        } else{
            randomStore.countFlag++;
            randomStore.nowScore = param.score!= undefined?param.score:param.id
        }
    }
    async handleTimer(param) {
        const classroomUIStore = useClassroomUIStore()
        const timerStore = useTimeKeeperStore()
        timerStore.timerValue = param.countType == 2 ? '倒计时' : '正计时'

        if (param.status == ViewStatus.normal) {
            classroomUIStore.showTimeKeeper = true
            if (!param.second) {
                return
            }
            if (param.isStart) {
                if (param.second == timerStore.totalSeconds && timerStore.currentSeconds != 0) {
                    timerStore.isCounting = true
                } else {
                    ///重置计时器
                    timerStore.cleanData()
                    timerStore.timeList.forEach((item) => {
                        item.selected = item.value == param.second
                    })
                    timerStore.totalSeconds = param.second
                    if (timerStore.timerValue == '正计时') {
                        timerStore.currentSeconds = 0
                    } else {
                        timerStore.currentSeconds = timerStore.totalSeconds
                    }
                    timerStore.currentSecondsToHourMinuteSecond()
                    timerStore.isCounting = true


                }
                if (!timerStore.timer) {
                    timerStore.createTimer()
                }

            } else {
                timerStore.isCounting = false
            }
            remoteControl.handleInteractState()
        } else if (param.status == ViewStatus.minimize) {
            classroomUIStore.showTimeKeeper = false
        } else if (param.status == ViewStatus.closed) {
            classroomUIStore.showTimeKeeper = false
            timerStore.cleanData()
            timerStore.timeList.forEach((item) => {
                item.selected = false
            })
        }

    }
    async handleChangeScore(param) {

    }
    async handleStudentList(param) {
        
        const classroomUIStore = useClassroomUIStore()
        const classroomStore = useClassroomStore()
        const studentListStore = useStudentListStore()
        if (param.show) {
            classroomUIStore.showStudentList = true
        } else if (param.show === false) {
            classroomUIStore.showStudentList = false
        } else if (param.tab !== undefined) {

            studentListStore.currentSelectedTitle =
                param.tab == 0 ? StudentListType.name
                    : param.tab == 1 ? StudentListType.group
                        : StudentListType.seat

        } else if (param.scoreType) {
            const evaluationScoreStore = useEvaluationScoreStore()
            evaluationScoreStore.alone = param.scoreType == 1
            dbHelper.updateAddScoreTypeByTeacherId(param.scoreType == 1 ? '1' : "2")
        } else if (param.stu) {
            classroomStore.selectedClassroom.groupStudentArray.forEach(group => {
                group.selected = false
                group.groupStudent.forEach(s => {
                    if (s.studentId === param.stu.studentId) {
                        s.selected = param.stu.selected
                    }

                })
            })
        } else if (param.groupName) {
            classroomStore.selectedClassroom.groupStudentArray.forEach(group => {
                if (group.groupName == param.groupName) {
                    group.selected = !group.selected
                }
                group.groupStudent.forEach(s => {
                    s.selected = false
                })
            })
        } else if (param.dataKey) {
            studentListStore.lastnameFlag = param.dataKey
        } else if (param.score) {
            studentListStore.remoteFlag++
            studentListStore.nowScore = param.score
        } else if(param.id){
            const  evaluationScoreStore = useEvaluationScoreStore()
            evaluationScoreStore.remoteFlag++
            evaluationScoreStore.score = param.id
        }else if(param.review !== undefined){
            classroomUIStore.showStudentCategoryScore = param.review
        }
    }

    async handleScreentState(param) {
        if (!window.electron) {
            return
        }

        if (param.action) {
            if (window.electron.turnOnBlock) {
                window.electron.turnOnBlock()
            }
        } else {
            if (window.electron) {
                let x = Math.floor(Math.random() *1920)
                let y = Math.floor(Math.random() * 1080)
                LocalRequest.moveMouse(x,y)
            }
        }
    }
}

export const remoteControl = new RemoteControl()