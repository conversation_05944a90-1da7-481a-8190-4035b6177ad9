import { remoteControl } from "./remote_control"


class WebRTCServer {

    constructor() {

        if (!WebRTCServer.instance) {
            WebRTCServer.instance = this
        }
        return WebRTCServer.instance
    }

    async setupRTC(offer) {

        this.closeRTC()
        if (window.electron === undefined) {
            return {
                code: -1,
                message: "无法创建连接"
            }
        }
        this.rtc = new RTCPeerConnection()
        this.rtc.onconnectionstatechange = () => {
        }

        let source = await window.electron.getMainScreenId()
        const stream = await navigator.mediaDevices.getDisplayMedia({
            audio: false,
            video: {
                width: { ideal: 1920, max: 1920 },  // 理想且最大宽度
                height: { ideal: 1080, max: 1080 },  // 理想且最大高度
                frameRate: { ideal: 24, max: 30 },  // 理想帧率
            }
        });
        stream.getTracks().forEach(track => this.rtc.addTrack(track, stream));
        this.stream = stream

        this.rtc.ondatachannel = (event) => {
            // console.log("on data channel", event)
        }

        // onnegotiationneeded
        this.rtc.onnegotiationneeded = () => {
            // console.log(`onnegotiationneeded`);
            this.rtc.createOffer().then((offer) => {
                this.rtc.setLocalDescription(offer)
                remoteControl.sendRTCOffer({
                    code: 1,
                    offer,
                })
            })
        }

        this.rtc.onicecandidate = ({ candidate }) => {
            if (candidate) {
                remoteControl.sendRTCCandidate({
                    code: 1,
                    candidate: candidate
                })
            }
        }
        await this.rtc.setRemoteDescription(offer)

        // 创建并设置 answer
        const answer = await this.rtc.createAnswer();
        await this.rtc.setLocalDescription(answer);
        let display = source.currentDisplay
        remoteControl.sendRTCAnswer({
            code: 1,
            answer: answer,
            width: display.bounds.width * display.scaleFactor,
            height: display.bounds.height * display.scaleFactor,
            orgin: {
                x: display.bounds.x,
                y: display.bounds.y
            }
        })
    }

    addCandidate(candidate) {
        if (this.rtc) {
            // 收到 ICE 候选者
            try {
                this.rtc.addIceCandidate(candidate);
            }
            catch(e) {
                console.log("add ice candidate", e)
            }
        }
    }

    receiveAnswer(answer) {
        if (this.rtc) {
            this.rtc.setRemoteDescription(answer)
        }
    }

    closeRTC() {
        if (this.rtc) {
            this.rtc.close()
            this.rtc = null
        }

        if (this.stream) {
            const tracks = this.stream.getTracks();
            tracks.forEach(track => track.stop()); // 停止每个轨道
            this.stream = null
        }
    }
}

export const webRtcServer = new WebRTCServer()