<template>
    <div  class="system-toolbar" id="sys-tool-bar" ref="systemToolbarRef" v-if="hideToolBar === false">
        <SystemToolbarItem :image="tabbarImage(item.item, item.selected)" :title="tabbarName(item.item, item.selected)"
            :rowReverse="item.rowReverse" @click="item.click" v-for="(item, index) in systemToolbarList.slice(0, -1)"
            :key="index">
        </SystemToolbarItem>
    </div>
    <!-- <div class="system-toolbar2" ref="systemToolbarHalfScreenRef" v-if="hideToolBar === false">
        <SystemToolbarItem :image="tabbarImage(item.item, item.selected)" :title="tabbarName(item.item, item.selected)"
            :rowReverse="item.rowReverse" @click="item.click" v-for="(item, index) in systemToolbarList.slice(-1)"
            :key="index">
        </SystemToolbarItem>
    </div> -->
</template>
<script setup>
import { TabBarItem, tabbarName, tabbarImage } from '@/components/TabBar/tabbar_enums'
import SystemToolbarItem from '@/components/TabBar/SystemToolbarItem.vue'
import { storeToRefs } from 'pinia'
import { useDrawBoardStore } from '@/stores/drawboard_store'
import { useClassroomStore } from '@/stores/classroom'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { useTeachPlanStore } from '@/stores/teach_plan'
import { DrawMode, EditMode } from '../../drawboard/draw_enums'
import { databaseHelper, RecordType } from '@/classroom/class_flow_helper'
import { useDesktopStore } from '@/stores/desktop_store'
import { onBeforeUnmount, onMounted, onBeforeMount, computed } from 'vue'
import { tdRecord } from '@/utils/talkingdata_tool'
import { useRandomRollCallStore } from '@/stores/random_roll_call_store'
import { DisplayDirection } from '@/classroom/frame_enums'
import { useInteractStore } from '@/stores/interact_store'
import { InteractMode } from '@/classroom/interact_enums'
import { usePhetExpStore } from '@/stores/phet_exp_store'

const phetExpStore = usePhetExpStore()

const interactStore = useInteractStore()
const { interactMode } = storeToRefs(interactStore)

const randomRollCallStore = useRandomRollCallStore()

const drawBoardStore = useDrawBoardStore()

const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)

const classroomUIStore = useClassroomUIStore()
const { showScoreStatistics, showStudentList, showSystem, showRandomRollCall,
    showTimeKeeper, mainContentTopSpace, showClassRoomRecord, showCloudDisk,
    showRecordType, showClearScreenAlert, displayDirection, toolbarRef,
    hideClassRoomRecord, systemToolbarRef, systemToolbarHalfScreenRef,showAIHelp } = storeToRefs(classroomUIStore)

const teachPlanStore = useTeachPlanStore()
const { showTeachPlan, treeList } = storeToRefs(teachPlanStore)


const desktopStore = useDesktopStore()
const { showDesktopButton, hideToolBar } = storeToRefs(desktopStore)

const rowReverse = computed(() => {
    return displayDirection.value === DisplayDirection.Left
})

const systemToolbarList = computed(() => {
    let list = [
        {
            item: TabBarItem.studentsScoreRate,
            selected: false,
            rowReverse: rowReverse.value,
            click: studentsScoreRateClick,
        },
        {
            item: TabBarItem.beikeSource,
            selected: false,
            rowReverse: rowReverse.value,
            click: beikeSourceClick,
        },
        {
            item: TabBarItem.homeworkCalendar,
            selected: false,
            rowReverse: rowReverse.value,
            click: homeworkCalendarClick,
        },
        {
            item: TabBarItem.teachPlan,
            selected: false,
            rowReverse: rowReverse.value,
            click: teachPlanClick,
        },
        {
            item: TabBarItem.classRecords,
            selected: false,
            rowReverse: rowReverse.value,
            click: classRecordsClick,
        },
        {
            item: TabBarItem.randomRoll,
            selected: false,
            rowReverse: rowReverse.value,
            click: randomRollClick,
        },
        {
            item: TabBarItem.studentList,
            selected: showStudentList.value,
            rowReverse: rowReverse.value,
            click: studentListClick,
        },
        {
            item: TabBarItem.timeKeeping,
            selected: false,
            rowReverse: rowReverse.value,
            click: timeKeepingClick,
        },
        // {
        //     item: TabBarItem.aiHelp,
        //     selected: false,
        //     rowReverse: rowReverse.value,
        //     click: startAIHelp,
        // },
        // {
        //     item: TabBarItem.halfscreen,
        //     selected: mainContentTopSpace.value !== 0,
        //     rowReverse: rowReverse.value,
        //     click: halfscreenClick,
        // },
        // {
        //     item: TabBarItem.phetExp,
        //     selected: false,
        //     rowReverse: rowReverse.value,
        //     click: phetClick,
        // }
    ]
    if (showDesktopButton.value) {
        list.push({
            item: TabBarItem.desktop,
            selected: false,
            rowReverse: rowReverse.value,
            click: desktopClick,
        })
    }

    list.push({
        item: TabBarItem.systemSetting,
        selected: false,
        rowReverse: rowReverse.value,
        click: systemSettingClick,
    },)

    list.push({
        item: TabBarItem.halfscreen,
        selected: mainContentTopSpace.value !== 0,
        rowReverse: rowReverse.value,
        click: halfscreenClick,
    })
    return list
})

onMounted(() => {
    const tabBar = document.querySelector("#sys-tool-bar")
    if (window.electron) {
        tabBar.addEventListener('mouseenter', onMouseEnter)
        tabBar.addEventListener('pointerenter', onMouseEnter)
    }
})

function onMouseEnter() {
    window.electron.unignoreMosue()

}

onBeforeUnmount(() => {
    const tabBar = document.querySelector("#sys-tool-bar")
    if (window.electron) {
        tabBar.removeEventListener('mouseenter', onMouseEnter)
        tabBar.removeEventListener('pointerenter', onMouseEnter)
    }
})

function systemSettingClick() {
    //系统
    showSystem.value = true
}

//判断是否是最小化
function judgeMin(type) {
    if (showClassRoomRecord.value && hideClassRoomRecord.value) {
        if (type != showRecordType.value) {
            showClassRoomRecord.value = false

        }
        hideClassRoomRecord.value = false
    }
}

async function classRecordsClick() {
    //随堂记录
    await databaseHelper.addLines(RecordType.classRecord)
    judgeMin('interact')
    showRecordType.value = 'interact'
    setTimeout(()=>{
        showClassRoomRecord.value = true
    },200)
    
}

async function beikeSourceClick() {
    //我的云盘
    await databaseHelper.addLines(RecordType.beikeSource)
    showCloudDisk.value = true
}

async function homeworkCalendarClick() {
    //作业讲评
    await databaseHelper.addLines(RecordType.homeworkCalendar)
    judgeMin('homework')
    showRecordType.value = 'homework'
    setTimeout(()=>{
        showClassRoomRecord.value = true
    },200)
}

function teachPlanClick() {
    //导学案
    if (treeList.value.length == 0) {
        teachPlanStore.show(selectedClassroom.value)
    } else {
        showTeachPlan.value = true
    }
}

async function studentsScoreRateClick() {
    //得分统计
    showScoreStatistics.value = true
    await databaseHelper.addLines(RecordType.scoreStatistics)
}

async function randomRollClick() {
    //随机点名
    showRandomRollCall.value = true
    randomRollCallStore.setup(selectedClassroom.value.studentList)
    
    await databaseHelper.addLines(RecordType.randomRollCall)
}

function studentListClick() {
    //展开学生
    showStudentList.value = !showStudentList.value
}

function timeKeepingClick() {
    //计时
    showTimeKeeper.value = true
    tdRecord('开启计时')
}

function startAIHelp() {
    showAIHelp.value = true
}

function halfscreenClick() {
    //下拉屏幕
    if (mainContentTopSpace.value !== 0) {
        mainContentTopSpace.value = 0
    }
    else {
        mainContentTopSpace.value = window.innerHeight / 3
    }
}

function phetClick() {
    phetExpStore.show()
}

function desktopClick() {
    desktopStore.showDesktopView()
}

function sidebarClick(item) {
    //侧边栏
    if (interactMode.value === InteractMode.none) {
        interactMode.value = InteractMode.small
    } else if (interactMode.value === InteractMode.small) {
        interactMode.value = InteractMode.big
    } else if (interactMode.value === InteractMode.big) {
        interactMode.value = InteractMode.none
    }
}

</script>
<style lang="scss" scoped>
.system-toolbar {
    position: absolute;
    z-index: var(--toolbar-z-index);
    // bottom: 210px;
    bottom: 150px;
    display: grid;
    left: v-bind("displayDirection === DisplayDirection.Left ? null : '0'");
    right: v-bind("displayDirection === DisplayDirection.Left ? '0' : null");
    padding: 10px;
    transform: scale(1.2);   /* 放大 1.2 倍 */
    transform-origin: v-bind("displayDirection === DisplayDirection.Left ? 'right bottom' : 'left bottom'"); /* 放大时从左上角对齐，不偏移位置 */
}

.system-toolbar2 {
    position: absolute;
    z-index: var(--toolbar-ex-z-index);
    bottom: 150px;
    display: grid;
    left: v-bind("displayDirection === DisplayDirection.Left ? null : '0'");
    right: v-bind("displayDirection === DisplayDirection.Left ? '0' : null");
    padding: 10px;
    
}
</style>