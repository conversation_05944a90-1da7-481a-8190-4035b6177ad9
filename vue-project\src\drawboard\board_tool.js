

export class BoardTool {

    /// 销毁 mesh
    static disposeMesh(mesh) {
        if (!mesh) {
            return
        }
        // 1. 从父对象中移除
        if (mesh.parent) {
            mesh.parent.remove(mesh);
        }
        // 2. 释放几何体资源
        if (mesh.geometry) {
            mesh.geometry.dispose();
        }
        // 3. 释放材质资源
        if (mesh.material) {
            // 如果材质是一个数组
            if (Array.isArray(mesh.material)) {
                mesh.material.forEach(material => {
                    if (material) material.dispose();
                });
            } else {
                // 单个材质
                mesh.material.dispose();
            }
        }
        // 4. 将 Mesh 对象设置为 null（可选）
        mesh = null;
    }


    static disposeGroup(group) {
        if (!group) {
            return
        }
        // 获取 Group 中的所有子对象
        const children = group.children;
        // 遍历每个子对象
        for (let i = children.length - 1; i >= 0; i--) {
            const child = children[i];
            // 从 Group 中移除子对象
            group.remove(child);
            // 销毁子对象的几何体和材质
            if (child.geometry) {
                child.geometry.dispose();
            }
            if (child.material) {
                // 如果材质是一个数组（例如多材质对象），需要遍历数组进行销毁
                if (Array.isArray(child.material)) {
                    child.material.forEach(material => material.dispose());
                } else {
                    child.material.dispose();
                }
            }
            // 不在这里销毁，统一在texture_cache管理
            // if (child.material && child.material.map) {
            //     child.material.map.dispose();
            // }
        }
    }

}