
import * as THREE from 'three';
import { MeshLineGeometry, MeshLineMaterial } from 'meshline';
import { DrawZ, LineMode } from '@/drawboard/draw_enums';

export class PageSnapShoot {
    constructor(application) {
        this.application = application
        let app = application.deref()
        //render 设置了devicePixelRatio，分辨率要除去
        let width = 210 * 6 
        let height = 297 * 6
        if (width > window.innerWidth) {
            width = window.innerWidth
            height = Math.floor(width * 297 / 210)
        }
        this.width = width
        this.height = height
        this.renderWidth = width / app.pixelRatio
        this.renderHeight = height / app.pixelRatio

        this.scene = new THREE.Scene()
        const aspect = width / height
        this.camera = new THREE.PerspectiveCamera(90, aspect, 0.01, 1000)
        this.camera.position.z = DrawZ.objcZ + 0.1
        this.camera.updateProjectionMatrix()

        const size = this.getScreenSizeAtDepth(this.camera, DrawZ.objcZ)

        this.depthSize = size

        this.lineMaterial = new MeshLineMaterial({
            color: new THREE.Color(LineMode.A4LineColor),
            lineWidth: LineMode.A4LineWidth, // 线宽度
            sizeAttenuation: false,
        })
        this.lineMaterial.depthTest = false
        this.lineMaterial.depthWrite = false
        let texture = this.lineMaterial.map;
        if (texture) {
            texture.minFilter = THREE.NearestFilter;  // 使用 NearestFilter 可以避免 mipmapping
            texture.magFilter = THREE.NearestFilter;  // 使用 NearestFilter 防止模糊
            texture.generateMipmaps = false;  // 禁用 mipmap 生成
        }

        this.planeGeometry = new THREE.PlaneGeometry(size.width, size.height)
    }

    getScreenSizeAtDepth(camera, depth) {
        var fov = camera.fov * (Math.PI / 180); // 将fov从度数转换为弧度
        var height = 2 * Math.tan(fov / 2) * (camera.position.z - depth);
        var width = height * camera.aspect;
        return { width, height };
    }

    snapShootMesh(mesh, lastTexture, renderScale) {
        this.scene.clear()
        mesh.position.set(0, 0, DrawZ.objcZ)
        mesh.scale.set(renderScale, renderScale, 1.0)
        mesh.material = this.lineMaterial
        mesh.renderOrder = 1
        this.scene.add(mesh)

        if (lastTexture) {
            let cacheMaterial = new THREE.MeshBasicMaterial({
                map: lastTexture,
                transparent: true,
                blending: THREE.NormalBlending,
                depthTest: false,
                depthWrite: false,
                precision: 'highp'
            })
            let cacheMesh = new THREE.Mesh(this.planeGeometry, cacheMaterial)
            cacheMesh.position.set(0, 0, DrawZ.objcZ)
            cacheMesh.renderOrder = 0
            this.scene.add(cacheMesh)
        }

        let app = this.application.deref()
        let texture = new THREE.FramebufferTexture(this.width, this.height)
        texture.minFilter = THREE.NearestFilter;
        texture.magFilter = THREE.NearestFilter;
        texture.generateMipmaps = false;  // 禁用 mipmap 生成
        app.renderer.setSize(this.renderWidth, this.renderHeight, false)
        app.renderer.render(this.scene, this.camera)
        app.renderer.copyFramebufferToTexture(texture)

        app.renderer.setSize(app.width, app.height, false)
        return texture
    }

    dispose() {
        this.scene.traverse((o) => {
            if (o.geometry) {
                o.geometry.dispose()
            }
            if (o.material) {
                if (o.material.length) {
                    for (let i = 0; i < o.material.length; ++i) {
                        o.material[i].dispose()
                    }
                }
                else {
                    o.material.dispose()
                }
            }
        })
        this.scene.clear()
        this.planeGeometry.dispose()
        this.planeGeometry = null
        this.scene = null
        this.camera = null
        this.lineMaterial = null
    }

}