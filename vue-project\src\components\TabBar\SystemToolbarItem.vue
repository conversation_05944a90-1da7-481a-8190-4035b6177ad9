<template>
    <div class="item" :style="{ flexDirection: props.rowReverse ? 'row-reverse' : 'row' }">
        <div class="icon">
            <img :src="props.image">
        </div>
        <!-- <div :style="{ width: '10px' }"></div>
        <div class="title">
            {{ props.title }}
        </div> -->
    </div>
</template>
<script setup>
import { defineProps } from 'vue'

const props = defineProps({
    rowReverse: {
        type: Boolean,
        default: false
    },
    image: {
        type: String,
        default: ''
    },
    title: {
        type: String,
        default: ''
    }
})
</script>

<style lang="scss" scoped>
.item {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 10px;
    // background-color: yellow;

    .icon {
        width: 48px;
        height: 48px;

        img {
            width: 100%;
            height: 100%;
        }
    }

    .title {
        font-size: 15px;
        color: var(--explanatory-text-color);
    }
}
</style>