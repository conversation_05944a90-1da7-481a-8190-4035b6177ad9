

import { BoardButton } from "../board_button";
import { BoardView } from "../board_view";
import * as THREE from 'three';
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial.js';
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry.js';
import { Line2 } from 'three/examples/jsm/lines/Line2.js';
import { BoardTool } from "../board_tool";
import { LineView } from "./line_view";
import { DrawZ, PainterOrder } from "../draw_enums";
import { MathGeometry } from "./math_root_view";


const MoveTag = {
    none: -1,
    a: 0,
    b: 1,
    c: 2,
    d: 3,
    self: 4 // 移动整个图形
}

export const QuadrilateralType = {
    rectangle: 0,  //矩形
    square: 1,    // 正方形
    diamond: 2,  // 菱形
    parallel: 3, //平行四边形
    trapezoid: 4, // 梯形
}

/// 四边形
export class QuadrilateralView extends BoardView {

    constructor(type,application, pos, size, displayType, color) {

        super(application, pos, size)

        this.renderOrder = PainterOrder.customDisplay
        this.type = type
        this.onEdit = false

        this.color = color

        this.setDisplayType(displayType)

        // 创建材质：线条材质
        const material = new LineMaterial({
            color: color ?? 0x00ff00,        // 边框颜色
            linewidth: 5,           // 线条宽度
            dashed: false,          // 不使用虚线
        });
        let stringApplication = application?.deref()
        this.lineWidth = stringApplication.cameraInitSize.width / 80
        let lineGeometry = new LineGeometry()
        let points = this.getVertexPoints()

        lineGeometry.setPositions(points);
        // 创建 Line2 对象
        const line = new Line2(lineGeometry, material);
        line.computeLineDistances();  // 必须计算线段的距离，以便正确渲染

        let lineGroup = new THREE.Group()
        lineGroup.add(line)
        lineGroup.renderOrder = PainterOrder.customDisplay
        this.lineGroup = lineGroup
        this.add(lineGroup);

        this.line = line

        this.setupEditViews()

        this.setupToolViews()

        if (this.displayType === QuadrilateralType.diamond) {
            this.updateVertexViewPos()
        }
    }


    setDisplayType(type) {

        this.displayType = type
        if (type === QuadrilateralType.rectangle) {
            //顶点
            this.aPoint = { x: -0.5 * this.size.width, y: 0.5 * this.size.height }
            this.bPoint = { x: 0.5 * this.size.width, y: 0.5 * this.size.height }
            this.cPoint = { x: -0.5 * this.size.width, y: -0.5 * this.size.height }
            this.dPoint = { x: 0.5 * this.size.width, y: -0.5 * this.size.height }
        }
        else if (type === QuadrilateralType.square) {
            let width = Math.max(this.size.width, this.size.height)
            //顶点
            this.aPoint = { x: -0.5 * width, y: 0.5 * width }
            this.bPoint = { x: 0.5 * width, y: 0.5 * width }
            this.cPoint = { x: -0.5 * width, y: -0.5 * width }
            this.dPoint = { x: 0.5 * width, y: -0.5 * width }
        }
        else if (type === QuadrilateralType.diamond) { //菱形
            //顶点
            this.aPoint = { x: -0.5 * this.size.width, y: 0 }
            this.bPoint = { x: 0, y: 0.5 * this.size.height }
            this.cPoint = { x: 0, y: -0.5 * this.size.height }
            this.dPoint = { x: 0.5 * this.size.width, y: 0 }
        }
        else if (type === QuadrilateralType.parallel) {
            //顶点
            this.aPoint = { x: 0, y: 0.5 * this.size.height }
            this.bPoint = { x: 0.5 * this.size.width, y: 0.5 * this.size.height }
            this.cPoint = { x: -0.5 * this.size.width, y: -0.5 * this.size.height }
            this.dPoint = { x: 0, y: -0.5 * this.size.height }
        }
        else if (type === QuadrilateralType.trapezoid) {
            //顶点
            this.aPoint = { x: - 0.3 * this.size.width, y: 0.5 * this.size.height }
            this.bPoint = { x: 0.3 * this.size.width, y: 0.5 * this.size.height }
            this.cPoint = { x: -0.5 * this.size.width, y: -0.5 * this.size.height }
            this.dPoint = { x: 0.5 * this.size.width, y: -0.5 * this.size.height }
        }
    }

    setupEditViews() {

        let application = this.application?.deref()
        let areaWidth = application.cameraInitSize.width / 40
        let dragAreaSize = { width: areaWidth, height: areaWidth }
        this.dragAreaSize = dragAreaSize

        let aVertexView = new BoardView(
            this.application,
            new THREE.Vector3(this.aPoint.x - areaWidth, this.aPoint.y + areaWidth, 0),
            dragAreaSize)
        aVertexView.renderOrder = 1
        aVertexView.setBackgroundColor(0xff6347)
        aVertexView.visible = false
        aVertexView.setRenderOrder(PainterOrder.customDisplay)
        this.aVertexView = aVertexView
        this.addSubView(aVertexView)

        let bVertexView = new BoardView(
            this.application,
            new THREE.Vector3(this.bPoint.x + areaWidth, this.bPoint.y + areaWidth, 0),
            dragAreaSize)
        bVertexView.renderOrder = 1
        bVertexView.setBackgroundColor(0xff6347)
        bVertexView.visible = false
        bVertexView.setRenderOrder(PainterOrder.customDisplay)
        this.bVertexView = bVertexView
        this.addSubView(bVertexView)

        let cVertexView = new BoardView(
            this.application,
            new THREE.Vector3(this.cPoint.x - areaWidth, this.cPoint.y - areaWidth, 0),
            dragAreaSize)
        cVertexView.renderOrder = 1
        cVertexView.setBackgroundColor(0xff6347)
        cVertexView.visible = false
        cVertexView.setRenderOrder(PainterOrder.customDisplay)
        this.cVertexView = cVertexView
        this.addSubView(cVertexView)

        let dVertexView = new BoardView(
            this.application,
            new THREE.Vector3(this.dPoint.x + areaWidth, this.dPoint.y - areaWidth, 0),
            dragAreaSize)
        dVertexView.renderOrder = 1
        dVertexView.setBackgroundColor(0xff6347)
        dVertexView.visible = false
        dVertexView.setRenderOrder(PainterOrder.customDisplay)
        this.dVertexView = dVertexView
        this.addSubView(dVertexView)

        let viewButton = new BoardButton(
            this.application,
            new THREE.Vector3(0, 0, 0),
            { width: this.size.width + areaWidth, height: this.size.height + areaWidth })
        viewButton.renderOrder = PainterOrder.customDisplay - 1
        viewButton.onClick(() => {
            const edit = !this.onEdit
            this.setOnEdit(edit)
            if (edit) {
                this.superView.deref().cancelEditWithout(this)
            }
        })
        this.viewButton = viewButton
        this.addSubView(viewButton)

    }

    setupToolViews() {

        let app = this.application?.deref()
        let height = app.cameraInitSize.width / 40
        let itemHeight = height * 0.8
        let itemWidth = itemHeight
        let width = itemHeight * 3.5
        let space = itemHeight * 1.5 / 3

        let startX = - width * 0.5

        this.toolWidth = width
        this.toolHeight = height
        this.toolItemWidth = itemWidth
        let toolView = new BoardView(
            this.application,
            new THREE.Vector3(this.dPoint.x + this.toolItemWidth * 2, this.dPoint.y + height * 2, 0),
            { width, height })
        let deleteButton = new BoardButton(
            this.application,
            new THREE.Vector3(startX + space + itemWidth / 2, 0, 0),
            { width: itemWidth, height: itemHeight }, true)
        deleteButton.setImage('img/math/delete.svg')
        deleteButton.onClick(() => {
            this.removeFromSuperView()
            this.dispose()
        })
        toolView.addSubView(deleteButton)


        let selectorButton = new BoardButton(
            this.application,
            new THREE.Vector3(startX + space * 2 + itemWidth * (2 - 0.5), 0, 0),
            { width: itemWidth, height: itemHeight }, true)
        selectorButton.setImage('img/math/selector.svg')
        selectorButton.onClick(() => {
            this.showOptionsView(!(this.optionsView?.visible ?? false))
        })
        toolView.addSubView(selectorButton)
        toolView.setRenderOrder(PainterOrder.customDisplay + 1)

        // toolView.setBackgroundColor(0x0000ff)
        toolView.visible = false
        this.addSubView(toolView)
        this.toolView = toolView
    }


    showOptionsView(show) {
        if (!this.optionsView) {
            let app = this.application?.deref()
            let width = app.cameraInitSize.width / 20
            let itemHeight = app.cameraInitSize.width / 40

            let count = 2
            if (this.displayType === QuadrilateralType.rectangle || this.displayType === QuadrilateralType.square) {
                count = 1
            }
            let height = itemHeight * count

            this.optionsWidth = width
            this.optionsHeight = height
            this.optionsItemHeight = itemHeight
            this.optionsView = new BoardView(
                this.application,
                new THREE.Vector3(
                    this.dVertexView.position.x + this.dVertexView.size.width + width / 2,
                    this.toolView.position.y - this.toolHeight / 2 - this.optionsHeight / 2,
                    0
                ),
                { width, height })
            this.optionsView.setRenderOrder(PainterOrder.customDisplay + 1)
            this.addSubView(this.optionsView)

            let startY = height / 2

            let diagonalButton = new BoardButton(
                this.application,
                new THREE.Vector3(0, startY - itemHeight / 2, 0),
                { width, height: itemHeight })
            diagonalButton.setFontSize(0.015)
            diagonalButton.setTextAlign('left')
            diagonalButton.setTextColor(0xffffff)
            diagonalButton.setText("作对角线")
            diagonalButton.onClick(() => {
                this.handleDrawDiagonal()
                this.optionsView.visible = false
            })
            diagonalButton.setBackgroundColor(0xFF6347)
            this.optionsView.addSubView(diagonalButton)

            if (count > 1) {
                let heightButton = new BoardButton(
                    this.application,
                    new THREE.Vector3(0, startY - itemHeight / 2 - itemHeight, 0),
                    { width, height: itemHeight })
                heightButton.setFontSize(0.015)
                heightButton.setTextAlign('left')
                heightButton.setTextColor(0xffffff)
                heightButton.setText("作高")
                heightButton.onClick(() => {
                    this.handleDrawHeight()
                    this.optionsView.visible = false
                })
                heightButton.setBackgroundColor(0xFF6347)
                this.optionsView.addSubView(heightButton)
            }
        }

        this.optionsView.visible = show
    }

    // 计算两点之间的距离
    calculateDistance(x1, y1, x2, y2) {
        return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
    }

    handleDrawDiagonal() {
        let cameraInitSize = this.application?.deref()?.cameraInitSize
        let distanceAD = this.calculateDistance(this.aPoint.x, this.aPoint.y, this.dPoint.x, this.dPoint.y)
        let color = this.superView.deref()?.prepareColor ?? this.color

        let lineA = new LineView(
            MathGeometry.Line,
            this.application,
            new THREE.Vector3(this.position.x, this.position.y, 0),
            { width: distanceAD, height: cameraInitSize.height * 0.1 }, color)
        lineA.aPoint = this.aPoint
        lineA.bPoint = this.dPoint
        lineA.updateLine()
        this.superView.deref()?.addSubView(lineA)

        let distanceBC = this.calculateDistance(this.bPoint.x, this.bPoint.y, this.cPoint.x, this.cPoint.y)

        let lineB = new LineView(
            MathGeometry.Line,
            this.application,
            new THREE.Vector3(this.position.x, this.position.y, 0),
            { width: distanceBC, height: cameraInitSize.height * 0.1 }, color)
        lineB.aPoint = this.bPoint
        lineB.bPoint = this.cPoint
        lineB.updateLine()
        this.superView.deref()?.addSubView(lineB)
    }

    findPerpendicularIntersection(x, y, x1, y1, x2, y2) {
        // 计算直线的斜率
        const m = (y2 - y1) / (x2 - x1);

        // 如果直线是垂直线（x2 === x1），则交点直接为 (x1, y)
        if (x2 === x1) {
            return { x: x1, y: y };
        }

        // 如果直线是水平线（y2 === y1），则交点直接为 (x, y1)
        if (y2 === y1) {
            return { x: x, y: y1 };
        }

        // 计算垂直线的斜率
        const mPerpendicular = -1 / m;

        // 计算交点的 x 坐标
        const xi = (m * x1 - mPerpendicular * x + y - y1) / (m - mPerpendicular);

        // 计算交点的 y 坐标
        const yi = m * (xi - x1) + y1;

        return { x: xi, y: yi };
    }

    findMidpoint(x1, y1, x2, y2) {
        // 计算中心点的 x 和 y 坐标
        const midX = (x1 + x2) / 2;
        const midY = (y1 + y2) / 2;

        return { x: midX, y: midY };
    }

    handleDrawHeight() {

        let cameraInitSize = this.application?.deref()?.cameraInitSize

        let a = this.bPoint
        let b = null

        // A -> B -> D -> C

        if (this.aPoint.y === this.dPoint.y && this.bPoint.x === this.cPoint.x) {
            let ad = this.calculateDistance(this.aPoint.x, this.aPoint.y, this.dPoint.x, this.dPoint.y)
            let bc = this.calculateDistance(this.bPoint.x, this.bPoint.y, this.cPoint.x, this.cPoint.y)
            // console.log("space ", Math.abs(ad - bc))
            if (Math.abs(ad - bc) <= 0.05) {// 正方形
                a = this.findMidpoint(this.aPoint.x, this.aPoint.y, this.bPoint.x, this.bPoint.y)
                b = this.findPerpendicularIntersection(a.x, a.y, this.cPoint.x, this.cPoint.y, this.dPoint.x, this.dPoint.y)
            }
            else if (ad < bc) {
                a = this.aPoint
                b = this.findPerpendicularIntersection(a.x, a.y, this.bPoint.x, this.bPoint.y, this.dPoint.x, this.dPoint.y)
            }
            else if (ad > bc) {
                a = this.bPoint
                b = this.findPerpendicularIntersection(a.x, a.y, this.aPoint.x, this.aPoint.y, this.cPoint.x, this.cPoint.y)
            }
        }
        else if (this.aPoint.x >= this.cPoint.x) {
            a = this.aPoint
            b = this.findPerpendicularIntersection(a.x, a.y, this.cPoint.x, this.cPoint.y, this.dPoint.x, this.dPoint.y)
        }
        else if (this.bPoint.x >= this.dPoint.x) {
            a = this.cPoint
            b = this.findPerpendicularIntersection(a.x, a.y, this.aPoint.x, this.aPoint.y, this.bPoint.x, this.bPoint.y)
        }
        else {
            b = this.findPerpendicularIntersection(a.x, a.y, this.cPoint.x, this.cPoint.y, this.dPoint.x, this.dPoint.y)
        }

        let distance = this.calculateDistance(a.x, a.y, b.x, b.y)
        let color = this.superView.deref()?.prepareColor ?? this.color

        let lineA = new LineView(
            MathGeometry.Line,
            this.application,
            new THREE.Vector3(this.position.x, this.position.y, 0),
            { width: distance, height: cameraInitSize.height * 0.1 }, color)
        lineA.aPoint = a
        lineA.bPoint = b
        lineA.updateLine()
        this.superView.deref()?.addSubView(lineA)
    }


    setOnEdit(onEdit) {
        this.onEdit = onEdit
        this.aVertexView.visible = this.onEdit
        this.aVertexView.draggable = this.onEdit
        this.bVertexView.visible = this.onEdit
        this.bVertexView.draggable = this.onEdit
        this.cVertexView.visible = this.onEdit
        this.cVertexView.draggable = this.onEdit
        this.dVertexView.visible = this.onEdit
        this.dVertexView.draggable = this.onEdit
        this.viewButton.draggable = this.onEdit
        this.draggable = this.onEdit
        this.toolView.visible = this.onEdit
        if (!this.onEdit && this.optionsView) {
            this.optionsView.visible = false
        }
        if (onEdit) {
            this.superView.deref().bringSubViewToFront(this)
        }
    }

    getVertexPoints() {
        const { x: x1, y: y1 } = this.aPoint
        const { x: x2, y: y2 } = this.bPoint
        const { x: x3, y: y3 } = this.cPoint
        const { x: x4, y: y4 } = this.dPoint

        // 顶点数组 A -> B -> D -> C -> A
        let points = [x1, y1, 0, x2, y2, 0, x4, y4, 0, x3, y3, 0, x1, y1, 0];

        return points
    }

    getPointToLineDistance(p1, p2, point) {

        const { x: x1, y: y1 } = p1
        const { x: x2, y: y2 } = p2
        const { x: px, y: py } = point

        // 计算点到线的距离
        const numerator = Math.abs((y2 - y1) * px - (x2 - x1) * py + x2 * y1 - y2 * x1)
        const denominator = Math.sqrt((y2 - y1) ** 2 + (x2 - x1) ** 2)
        const distance = numerator / denominator
        // 判断距离是否在边的宽度范围内
        return distance
    }


    updateVertexViewPos() {
        let areaWidth = this.dragAreaSize.width
        if (this.displayType === QuadrilateralType.diamond) {
            this.aVertexView.position.set(this.aPoint.x - areaWidth, this.aPoint.y, 0)
            this.bVertexView.position.set(this.bPoint.x, this.bPoint.y + areaWidth, 0)
            this.cVertexView.position.set(this.cPoint.x, this.cPoint.y - areaWidth, 0)
            this.dVertexView.position.set(this.dPoint.x + areaWidth, this.dPoint.y, 0)
        }
        else {
            this.aVertexView.position.set(this.aPoint.x - areaWidth, this.aPoint.y + areaWidth, 0)
            this.bVertexView.position.set(this.bPoint.x + areaWidth, this.bPoint.y + areaWidth, 0)
            this.cVertexView.position.set(this.cPoint.x - areaWidth, this.cPoint.y - areaWidth, 0)
            this.dVertexView.position.set(this.dPoint.x + areaWidth, this.dPoint.y - areaWidth, 0)
        }

        let leftX = Math.min(this.aPoint.x, this.bPoint.x, this.cPoint.x, this.dPoint.x)
        let rightX = Math.max(this.aPoint.x, this.bPoint.x, this.cPoint.x, this.dPoint.x)
        let topY = Math.max(this.aPoint.y, this.bPoint.y, this.cPoint.y, this.dPoint.y)
        let bottomY = Math.min(this.aPoint.y, this.bPoint.y, this.cPoint.y, this.dPoint.y)
        let width = rightX - leftX
        let height = topY - bottomY
        this.viewButton.setSize({ width: width + areaWidth, height: height + areaWidth })
        this.viewButton.position.set(leftX + width / 2, bottomY + height / 2, 0)


        this.toolView.position.set(this.dPoint.x + this.toolItemWidth * 2, this.dPoint.y + this.toolHeight * 2, 0)

        if (this.optionsView) {
            this.optionsView.position.set(
                this.dVertexView.position.x + this.dVertexView.size.width + this.optionsWidth / 2,
                this.toolView.position.y - this.toolHeight / 2 - this.optionsHeight / 2,
                0
            )
        }
    }

    updateVertex() {

        this.updateVertexViewPos()

        // 定义直线的两个顶点
        let points = this.getVertexPoints()

        this.line.geometry.dispose()
        let lineGeometry = new LineGeometry();
        // 更新 MeshLineGeometry
        lineGeometry.setPositions(points);

        this.line.geometry = lineGeometry;
    }

    onTouchDown(point) {
        let view = super.onTouchDown(point)
        if (!this.onEdit) {
            return view
        }
       let cvtPoint =  this.convertPoint(point, DrawZ.objcZ)
        this.touchDownPoint = cvtPoint
        if (view === this.aVertexView) {
            this.moveTag = MoveTag.a
        }
        else if (view === this.bVertexView) {
            this.moveTag = MoveTag.b
        }
        else if (view === this.cVertexView) {
            this.moveTag = MoveTag.c
        }
        else if (view === this.dVertexView) {
            this.moveTag = MoveTag.d
        }
        else if (view === this.viewButton) {
            this.moveTag = MoveTag.self
        }
        else {
            this.moveTag = MoveTag.none
        }
        return view
    }


    onTouchMove(point) {
        if (!this.onEdit || !this.touchDownPoint) {
            return super.onTouchMove(point)
        }
        let divPoint = point
        let cvtPoint =  this.convertPoint(point, DrawZ.objcZ)
        let spaceX = cvtPoint.x - this.touchDownPoint.x
        let spaceY = cvtPoint.y - this.touchDownPoint.y

        let areaWidth = this.dragAreaSize.width

        this.touchDownPoint = cvtPoint
        if (this.moveTag === MoveTag.a) {
            let x = this.aPoint.x + spaceX;
            let y = this.aPoint.y + spaceY;
            if (x + areaWidth >= this.bPoint.x) {
                x = this.bPoint.x - areaWidth;
                // A不能超过B
            }
            else if (y - areaWidth <= this.cPoint.y) {
                y = this.cPoint.y + areaWidth;
            }
            if (this.displayType === QuadrilateralType.square) {
                let space = (spaceX <= 0 || spaceY <= 0) ? Math.min(Math.abs(x), Math.abs(y)) : Math.max(Math.abs(x), Math.abs(y))
                this.aPoint = { x: -space, y: space };
                this.bPoint = { x: space, y: space };
                this.cPoint = { x: -space, y: -space };
                this.dPoint = { x: space, y: -space };
            }
            else if (this.displayType === QuadrilateralType.diamond) {
                this.aPoint = { x, y: 0 };
                this.dPoint = { x: -x, y: 0 }
            }
            else if (this.displayType === QuadrilateralType.rectangle) {
                this.aPoint = { x, y };
                this.bPoint.y = y
                this.cPoint.x = x
            }
            else if (this.displayType === QuadrilateralType.parallel) {
                this.aPoint = { x, y };
                this.dPoint = { x: -x, y: -y };
            }
            else if (this.displayType === QuadrilateralType.trapezoid) {
                this.aPoint = { x, y };
                this.bPoint.y = y
            }
            this.updateVertex();

        } else if (this.moveTag === MoveTag.b) {
            let x = this.bPoint.x + spaceX;
            let y = this.bPoint.y + spaceY;
            if (x - areaWidth <= this.aPoint.x) {
                x = this.aPoint.x + areaWidth;
                // A不能超过B
            }
            else if (y - areaWidth <= this.dPoint.y) {
                y = this.dPoint.y + areaWidth;
            }

            if (this.displayType === QuadrilateralType.square) {
                let space = (spaceX <= 0 || spaceY <= 0) ? Math.min(Math.abs(x), Math.abs(y)) : Math.max(Math.abs(x), Math.abs(y))
                this.aPoint = { x: -space, y: space };
                this.bPoint = { x: space, y: space };
                this.cPoint = { x: -space, y: -space };
                this.dPoint = { x: space, y: -space };
            }
            else if (this.displayType === QuadrilateralType.diamond) {
                this.bPoint = { x: 0, y };
                this.cPoint = { x: 0, y: -y }
            }
            else if (this.displayType === QuadrilateralType.rectangle) {
                this.bPoint = { x, y };
                this.aPoint.y = y
                this.dPoint.x = x
            }
            else if (this.displayType === QuadrilateralType.parallel) {
                this.bPoint = { x, y };
                this.cPoint = { x: -x, y: -y };
            }
            else if (this.displayType === QuadrilateralType.trapezoid) {
                this.bPoint = { x, y };
                this.aPoint.y = y
            }
            this.updateVertex();

        } else if (this.moveTag === MoveTag.c) {
            let x = this.cPoint.x + spaceX;
            let y = this.cPoint.y + spaceY;

            if (x + areaWidth >= this.dPoint.x) {
                x = this.dPoint.x - areaWidth;
            }
            else if (y + areaWidth >= this.aPoint.y) {
                y = this.aPoint.y - areaWidth;
            }

            if (this.displayType === QuadrilateralType.square) {
                let space = (spaceX <= 0 || spaceY <= 0) ? Math.min(Math.abs(x), Math.abs(y)) : Math.max(Math.abs(x), Math.abs(y))
                this.aPoint = { x: -space, y: space };
                this.bPoint = { x: space, y: space };
                this.cPoint = { x: -space, y: -space };
                this.dPoint = { x: space, y: -space };
            }
            else if (this.displayType === QuadrilateralType.diamond) {
                this.cPoint = { x: 0, y };
                this.bPoint = { x: 0, y: -y }
            }
            else if (this.displayType === QuadrilateralType.rectangle) {
                this.cPoint = { x, y };
                this.aPoint.x = x
                this.dPoint.y = y
            }
            else if (this.displayType === QuadrilateralType.parallel) {
                this.cPoint = { x, y };
                this.bPoint = { x: -x, y: -y };
            }
            else if (this.displayType === QuadrilateralType.trapezoid) {
                this.cPoint = { x, y };
                this.dPoint.y = y
            }
            this.updateVertex();

        } else if (this.moveTag === MoveTag.d) {
            let x = this.dPoint.x + spaceX;
            let y = this.dPoint.y + spaceY;
            if (x - areaWidth <= this.cPoint.x) {
                x = this.cPoint.x + areaWidth;
                // A不能超过B
            }
            else if (y + areaWidth >= this.bPoint.y) {
                y = this.bPoint.y - areaWidth;
            }
            if (this.displayType === QuadrilateralType.square) {
                let space = (spaceX <= 0 || spaceY <= 0) ? Math.min(Math.abs(x), Math.abs(y)) : Math.max(Math.abs(x), Math.abs(y))
                this.aPoint = { x: -space, y: space };
                this.bPoint = { x: space, y: space };
                this.cPoint = { x: -space, y: -space };
                this.dPoint = { x: space, y: -space };
            }
            else if (this.displayType === QuadrilateralType.diamond) {
                this.dPoint = { x, y: 0 };
                this.aPoint = { x: -x, y: 0 }
            }
            else if (this.displayType === QuadrilateralType.rectangle) {
                this.dPoint = { x, y };
                this.bPoint.x = x
                this.cPoint.y = y
            }
            else if (this.displayType === QuadrilateralType.parallel) {
                this.dPoint = { x, y };
                this.aPoint = { x: -x, y: -y };
            }
            else if (this.displayType === QuadrilateralType.trapezoid) {
                this.dPoint = { x, y };
                this.cPoint.y = y
            }
            this.updateVertex();

        } else if (this.moveTag === MoveTag.self) {
            let x = this.position.x + spaceX;
            let y = this.position.y + spaceY;
            this.position.set(x, y, 0);
        }

        return super.onTouchMove(divPoint)
    }

    onTouchUp(point) {
        this.moveTag = MoveTag.none
        return super.onTouchUp(point)
    }

    isPointOnLine(p1, p2, touchPoint, lineWidth) {
        const { x: x1, y: y1 } = p1;
        const { x: x2, y: y2 } = p2;
        const { x: touchX, y: touchY } = touchPoint;

        // 计算点到线的距离
        const numerator = Math.abs((y2 - y1) * touchX - (x2 - x1) * touchY + x2 * y1 - y2 * x1);
        const denominator = Math.sqrt((y2 - y1) ** 2 + (x2 - x1) ** 2);
        const distance = numerator / denominator;

        // 判断点是否在线段的投影范围内
        const dotProduct = (touchX - x1) * (x2 - x1) + (touchY - y1) * (y2 - y1);
        const lengthSquared = (x2 - x1) ** 2 + (y2 - y1) ** 2;
        const isWithinSegment = dotProduct >= 0 && dotProduct <= lengthSquared;

        // 判断距离是否在边的宽度范围内，并且点在线段的投影范围内
        return distance <= lineWidth && isWithinSegment;
    }

    isTouchOnQuadrilateralEdges(touchPoint, lineWidth) {
        const aPoint = this.aPoint
        const bPoint = this.bPoint
        const cPoint = this.cPoint
        const dPoint = this.dPoint

        // 判断是否在边AB上
        const onAB = this.isPointOnLine(aPoint, bPoint, touchPoint, lineWidth);

        // 判断是否在边AC上
        const onAC = this.isPointOnLine(aPoint, cPoint, touchPoint, lineWidth);

        // 判断是否在边CD上
        const onCD = this.isPointOnLine(cPoint, dPoint, touchPoint, lineWidth);

        // 判断是否在边CA上
        const onBD = this.isPointOnLine(bPoint, dPoint, touchPoint, lineWidth);

        // 返回是否命中任意一条边
        return onAB || onAC || onCD || onBD;
    }

    onPointInside(point) {
        if (!this.visible) {
            return false
        }
        const pos = new THREE.Vector3()
        this.viewButton.getWorldPosition(pos)
        for (let subView of this.subViews) {
            if (subView !== this.viewButton) {
                if (subView.onPointInside(point)) {
                    return true
                }
            }
            // else if (this.onEdit) {
            //     if (subView.onPointInside(point)) {
            //         return true
            //     }
            // }
        }
        let cvtPoint = this.convertPoint(point, DrawZ.objcZ)
        let localPoint = this.worldToLocal(cvtPoint)
        let match = this.isTouchOnQuadrilateralEdges(localPoint, this.lineWidth)
        return match
    }


    dispose() {
        BoardTool.disposeGroup(this.lineGroup)
        super.dispose()
    }

}