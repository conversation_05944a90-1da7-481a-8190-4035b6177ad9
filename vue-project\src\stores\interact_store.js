import { Interact, InteractStatus, isSingleObjective, InteractMode } from "@/classroom/interact_enums"
import { defineStore } from "pinia"
import { ref, computed, reactive } from "vue"
import { useAnswersStore, STUDENT_INDEX_MAX } from '@/stores/answers_store'
import { ViewStatus } from "@/classroom/frame_enums"
import { useDictationStore } from "@/stores/dictation_store"
import { useProbabilityStore } from "@/stores/probability_store"

export const useInteractStore = defineStore('interaction', () => {

    const answersStore = useAnswersStore()
    const dictationStore = useDictationStore()
    const probabilityStore = useProbabilityStore()

    // 互动结果页面所需参数
    const interactResult = reactive({})

    /// 提交排名
    const currentSubmitIndex = ref(0)
    /// 当前被选择的学生排序
    const currentSelectedIndex = ref(0)
    ///当前组在线人数
    const onlineNum = ref(0)

    const showInteractSelector = ref(true)

    const interact = ref(Interact.none)

    const interactStatus = ref(InteractStatus.none)

    const minimize = ref(ViewStatus.normal)

    /// 互动启动时间
    const interactStartTime = ref(0)

    /// 考试模式
    const examMode = ref(false)

    // 是否是概率，默认纸笔互动，和纸笔互动相同
    const isProbability = ref(false);

    /// 单选题选项统计弹出框
    const showChooseOptionList = ref(false)
    /// 单选题 多选题 判断题 老师选择答案 结束作答
    const teacherStopAnswer = ref(false)

    /// 开启互动之前的屏幕截屏
    const captureScreenUrl = ref('')

    
    /// 互动条隐藏、大、小
    const interactMode = ref(InteractMode.small)

    ///是否是随堂练习推荐题
    const testQuestionInfo = ref(null)
    function didSelectInteract(mode, status) {
        if (mode == Interact.dictation) {
            status = InteractStatus.underway
        }
        interact.value = mode
        interactStatus.value = status
        interactStartTime.value = Date.now()
    }

    function cleanData() {
        answersStore.cleanData()
        dictationStore.cleanData()
        probabilityStore.cleanData()
    }

    function endInteract() {
        isProbability.value = false
        teacherStopAnswer.value = false
        showChooseOptionList.value = false
        examMode.value = false
        interact.value = Interact.none
        interactStatus.value = InteractStatus.none
        currentSubmitIndex.value = 0
        currentSelectedIndex.value = 0
        onlineNum.value = 0
        
        interactStartTime.value = 0
        minimize.value = ViewStatus.normal
        captureScreenUrl.value = ''

        answersStore.endInteract()
        dictationStore.endInteract()
        probabilityStore.endInteract()
        //简单延迟防止后续下课时太快 报异常
        setTimeout(()=>{
            showInteractSelector.value = true
        },200)
    }

    function studentSubmit(stu, answer = null) {
        //提交顺序
        if (stu.answerSubmitIndex === STUDENT_INDEX_MAX) {
            currentSubmitIndex.value++
            stu.answerSubmitIndex = currentSubmitIndex.value
        }
        //如何是客观题或者组合题到下一层处理
        if (isSingleObjective(interact.value) || interact.value == Interact.multiQuestions) {
            answersStore.studentSubmit(stu, answer, interact.value, interactStartTime.value)
        } else if (interact.value == Interact.responder) {

        }
    }

    function showPaperPenView() {
        if (interact.value === Interact.paperPen || interact.value === Interact.classTest || interact.value === Interact.chineseWriting || interact.value === Interact.dictation) {
            return true
        }
        return false
    }

    return {
        interactResult,
        showInteractSelector,
        interact,
        interactStatus,
        interactStartTime,
        currentSubmitIndex,
        currentSelectedIndex,
        onlineNum,
        minimize,
        examMode,
        showChooseOptionList,
        teacherStopAnswer,
        captureScreenUrl,
        isProbability,
        interactMode,
        testQuestionInfo,
        didSelectInteract,
        studentSubmit,
        endInteract,
        cleanData,
        showPaperPenView,
    }
})