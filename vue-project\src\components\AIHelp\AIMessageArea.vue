<template>
  <div class="ai-help-dialog-message">
    <div class="dialog-content" ref="messagesContainer" id="aiChatMessageBox">
      <div style="height: 60%;"></div>
      <div class="messages">
        <div v-for="(message, index) in messages" :key="index" class="message-wrapper" :class="message.type">
          <!-- 用户消息 -->
          <div v-if="message.type === 'user'" class="message">
            <div class="voice-controls" v-if="!message.error">
              <div class="play-button" :class="{ 'playing': message.isPlaying }" @click="togglePlay(message)">
                <img :src="!message.isPlaying ? 'icon/icon_play.svg' : 'icon/icon_pause.svg'" alt="">
              </div>
              <div class="progress-ring">
                <svg class="progress-ring__circle" width="40" height="40">
                  <circle class="progress-ring__circle-bg" stroke="#e0e0e0" stroke-width="2" fill="transparent" r="18"
                    cx="20" cy="20" />
                  <circle class="progress-ring__circle-progress" stroke="#007AFF" stroke-width="2" fill="transparent"
                    r="18" cx="20" cy="20" :style="{
                      strokeDasharray: `${2 * Math.PI * 18}`,
                      strokeDashoffset: `${2 * Math.PI * 18 * (1 - (message.progress || 0))}`
                    }" />
                </svg>
              </div>
            </div>
            <div v-else class="voice-controls">
              <img src="/icon/icon_message_error.svg" alt="" @click="reSend(message)">
            </div>
            <div class="user-message">
              <div class="voice-duration">{{ message.content || message.duration }}</div>
            </div>
          </div>

          <!-- AI消息 -->
          <div v-else class="ai-message-container">
            <div class="message ai-message">
              <div>{{ message.content }}</div>
            </div>

            <!-- 音频进度条控制器 -->
            <div class="audio-player-controls" v-if="message.audioUrl">
              <div class="audio-play-button" @click="togglePlay(message)">
                <img :src="!message.isPlaying ? 'icon/icon_play.svg' : 'icon/icon_pause.svg'" alt="" class="play-icon">
              </div>
              <div v-show="message.isPlaying" class="audio-time">
                {{ formatTime(message.currentTime || 0) }}
              </div>
              <div v-show="message.isPlaying" class="audio-progress-container" :id="`progress-container-${index}`"
                @click="handleProgressClick($event, message)" @mousedown="startDragging($event, message, index)"
                @touchstart="startDragging($event, message, index, true)">
                <div class="audio-progress-bar">
                  <div class="audio-progress-current" :style="{ width: `${(message.progress || 0) * 100}%` }"></div>
                  <div class="audio-progress-thumb" :style="{ left: `${(message.progress || 0) * 100}%` }"></div>
                </div>

              </div>
              <div v-show="message.isPlaying" class="audio-time">
                {{ formatTime(message.duration || 0) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useClassroomUIStore } from '@/stores/classroom_ui_store';
import { storeToRefs } from 'pinia';
import { ref, onUnmounted, watch, } from 'vue';
const emits = defineEmits('reSend')
const classroomUIStore = useClassroomUIStore()
const {showAIHelp} = storeToRefs(classroomUIStore)
 const props = defineProps({
  messages: {
    default: []
  }
})

const messagesContainer = ref(null);
const currentAudio = ref(null);
const isDragging = ref(false);
const dragTarget = ref(null);
const dragElementId = ref(null);
const tempProgress = ref(0); // 临时存储拖动时的进度

// 格式化时间显示
const formatTime = (seconds) => {
  if (!seconds) return '00:00';
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

watch(showAIHelp,()=>{
  if(!showAIHelp.value){
    if(nowMessage&&nowMessage.isPlaying){
      togglePlay(nowMessage)
    }
  }
})
let nowMessage = null
// 播放/暂停音频
const togglePlay = (message) => {
  if(!message){
    if(nowMessage&&nowMessage.isPlaying){
      togglePlay(nowMessage)
    }
    return
  }
  if (message.isPlaying) {
    currentAudio.value?.pause();
    message.isPlaying = false;
    // 保存当前播放进度
    if (currentAudio.value) {
      message.currentTime = currentAudio.value.currentTime;
    }
    return;
  }

  // 停止其他正在播放的音频
  props.messages.forEach(msg => {
    if (msg.isPlaying) {
      msg.isPlaying = false;
      // 保存其他消息的当前进度
      if (currentAudio.value) {
        msg.currentTime = currentAudio.value.currentTime;
      }
      currentAudio.value?.pause();
    }
  });

  if (message.audioUrl) {
    if (!currentAudio.value || currentAudio.value.src !== message.audioUrl) {
      nowMessage = message
      if (currentAudio.value) {
        disposeAudio()
      }
      currentAudio.value = new Audio(message.audioUrl);

      // 设置音频元数据加载完成事件
      currentAudio.value.addEventListener('loadedmetadata', listenLoad);

      currentAudio.value.addEventListener('timeupdate', listenTime);

      currentAudio.value.addEventListener('ended', listenEnd);
      // ▶️ 播放事件
currentAudio.value.addEventListener('play', () => {
  console.log('播放了');
  // 你的播放逻辑
});

// ⏸️ 暂停事件
currentAudio.value.addEventListener('pause', () => {
  console.log('暂停了');
  // 你的暂停逻辑
});
    } else {
      // 如果是同一个音频，从记忆的位置继续播放
      if (message.currentTime) {
        currentAudio.value.currentTime = message.currentTime;
      }
    }

    currentAudio.value.play();
    message.isPlaying = true;
  }
};

function disposeAudio() {
  if (currentAudio.value) {
    currentAudio.value.removeEventListener('loadedmetadata', listenLoad);

    currentAudio.value.removeEventListener('timeupdate', listenTime);

    currentAudio.value.removeEventListener('ended', listenEnd);
  }
}

const listenLoad = () => {
  // 保存总时长
  nowMessage.duration = currentAudio.value.duration;

  // 如果有记忆的播放进度，则设置到该位置
  if (nowMessage.currentTime) {
    currentAudio.value.currentTime = nowMessage.currentTime;
  }
}

const listenTime = () => {
  // 只在非拖动状态下更新进度
  if (!isDragging.value || dragTarget.value !== nowMessage) {
    nowMessage.progress = currentAudio.value.currentTime / currentAudio.value.duration;
    nowMessage.currentTime = currentAudio.value.currentTime;
  }
}
const listenEnd = () => {
  nowMessage.isPlaying = false;
  nowMessage.progress = 0;
  nowMessage.currentTime = 0;
}

//重新发送
function reSend(message) {
  if (message && message.blob) {
    message.error = false
    emits('reSend', message.blob, message.key)
  }
}
function scrollBottom() {
  const element = document.getElementById('aiChatMessageBox');
  element.scrollTo({
    top: element.scrollHeight,
    behavior: 'smooth'
  });
}
// 处理进度条点击
const handleProgressClick = (event, message) => {
  if (isDragging.value) return;

  const rect = event.currentTarget.getBoundingClientRect();
  const offsetX = event.clientX - rect.left;
  const width = rect.width;
  const progress = Math.max(0, Math.min(1, offsetX / width));

  // 更新播放进度
  updateAudioProgress(message, progress);
};

// 开始拖动进度条
const startDragging = (event, message, index, isTouch = false) => {
  event.preventDefault();
  isDragging.value = true;
  dragTarget.value = message;
  dragElementId.value = `progress-container-${index}`;

  // 保存开始拖动时的原始进度
  tempProgress.value = message.progress;

  const moveHandler = isTouch ? handleTouchMove : handleMouseMove;
  const endHandler = isTouch ? handleTouchEnd : handleMouseUp;

  document.addEventListener(isTouch ? 'touchmove' : 'mousemove', moveHandler);
  document.addEventListener(isTouch ? 'touchend' : 'mouseup', endHandler);

  function handleMouseMove(e) {
    if (!isDragging.value) return;
    updateDraggingPosition(e.clientX);
  }

  function handleTouchMove(e) {
    if (!isDragging.value) return;
    updateDraggingPosition(e.touches[0].clientX);
  }

  function updateDraggingPosition(clientX) {
    const progressElement = document.getElementById(dragElementId.value);
    if (!progressElement) return;

    const rect = progressElement.getBoundingClientRect();
    const offsetX = clientX - rect.left;
    const width = rect.width;
    const progress = Math.max(0, Math.min(1, offsetX / width));

    // 仅更新视觉位置，不影响实际播放
    dragTarget.value.progress = progress;

    // 计算但不设置当前时间，用于显示
    const previewTime = progress * (dragTarget.value.duration || 0);
    // 仅在UI上显示拖动的预览时间
    const timeElement = progressElement.querySelector('.audio-time-current');
    if (timeElement) {
      timeElement.textContent = formatTime(previewTime);
    }
  }

  function handleMouseUp() {
    if (!isDragging.value) return;

    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);

    finalizeProgressChange();
  }

  function handleTouchEnd() {
    if (!isDragging.value) return;

    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', handleTouchEnd);

    finalizeProgressChange();
  }

  function finalizeProgressChange() {
    if (dragTarget.value && currentAudio.value) {
      const finalProgress = dragTarget.value.progress;
      const newTime = finalProgress * (dragTarget.value.duration || 0);

      // 直接设置音频时间
      currentAudio.value.currentTime = newTime;
      dragTarget.value.currentTime = newTime;
      dragTarget.value.progress = finalProgress;
    }

    isDragging.value = false;
    dragTarget.value = null;
    dragElementId.value = null;
  }
};

// 统一更新音频播放进度的函数
const updateAudioProgress = (message, progress) => {
  if (!currentAudio.value) return;

  const newTime = progress * (message.duration || 0);
  message.progress = progress;
  message.currentTime = newTime;
  currentAudio.value.currentTime = newTime;
};

// 组件卸载时清理资源
onUnmounted(() => {
  clear()
});

function clear(){
  if (currentAudio.value) {
    currentAudio.value.pause();
    currentAudio.value = null;
    disposeAudio()
  }
}
defineExpose({ scrollBottom,togglePlay })
</script>

<style scoped lang="scss">
.ai-help-dialog-message {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--list-bc-color);

  .dialog-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
  }

  .messages {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .message-wrapper {
    display: flex;
    max-width: 80%;
  }

  .message-wrapper.user {
    align-self: flex-end;
  }

  .message-wrapper.ai {
    align-self: flex-start;
  }

  .ai-message-container {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .message {
    display: flex;
    margin-top: 24px;
    align-items: center;
  }

  .user-message {
    flex: 1;
    padding: 12px 16px;
    background-color: var(--primary-color);
    color: var(--anti-text-color);
    border-radius: 16px 4px 16px 16px;
    white-space: pre-line;
  }

  .ai-message {
    padding: 12px 16px;
    background-color: var(--main-bc-color);
    color: var(--text-color);
    font-size: 32px;
    border-radius: 4px 16px 16px 16px;
    margin-right: 8px;
    white-space: pre-line;
  }

  .voice-duration {
    font-size: 32px;
  }

  .voice-controls {
    position: relative;
    width: 40px;
    height: 40px;
    margin-right: 8px;
  }

  .play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: none;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    cursor: pointer;

    img {
      width: 36px !important;
      height: 36px !important;
    }
  }

  .play-button.playing {
    background-color: rgba(255, 255, 255, 0.7);
  }

  .progress-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .progress-ring__circle {
    transform: rotate(-90deg);
  }

  .progress-ring__circle-bg,
  .progress-ring__circle-progress {
    transition: stroke-dashoffset 0.1s ease;
  }

  // 音频播放控制器样式
  .audio-player-controls {
    display: flex;
    align-items: center;

    max-width: 360px;
    margin-left: 6px;
    height: 42px;

    .audio-play-button {
      min-width: 42px;
      height: 42px;
      border-radius: 50%;
      background-color: #fff;
      cursor: pointer;
      margin-right: 24px;

      .play-icon {
        width: 100%;
        height: 100%;
      }
    }

    .audio-progress-container {
      flex: 1;
      cursor: pointer;
      display: flex;
      align-items: center;

      .audio-progress-bar {
        flex: 1;
        height: 9px;
        margin: 0px 12px;
        background-color: var(--main-bc-color);
        border-radius: 9px;
        position: relative;

        .audio-progress-current {
          position: absolute;
          top: 0;
          left: 0;
          height: 100%;
          background-color: var(--secondary-color);
          border-radius: 9px;
        }

        .audio-progress-thumb {
          position: absolute;
          top: 50%;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background-color: var(--secondary-color);
          transform: translate(-50%, -50%);
        }
      }


    }

    .audio-time {
      font-size: 15px;
      color: var(--secondary-text-color);
      line-height: 1;
    }
  }
}
</style>