<template>
    <div class="home" ref="canvasHomeContainer">
        <div ref="canvasContainer" class="canvas-container">
            <!-- 用户绘制的内容会显示在这个容器里 -->
        </div>
        <!-- <div class="start" @click="startDraging">开启拖拽</div>
        <div class="end" @click="endDraging">关闭拖拽</div>
        <div class="draw-area" @click="zoomClick">缩放</div>
        <div class="clear" @click="zoomYuanClick">还原</div> -->
    </div>
</template>

<script setup>
import { ref, onMounted, watch, defineProps } from 'vue';
import { useCanvasImageStore } from '@/stores/canvas_image_store'
import { storeToRefs } from 'pinia';

const canvasImageStore = useCanvasImageStore()
const { canvasContainer, canvasHomeContainer } = storeToRefs(canvasImageStore)

onMounted(() => {
    canvasImageStore.setup(window.innerWidth - 260, window.innerHeight - 150 - 215)
})
</script>

<style lang="scss" scoped>
.home {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: #0B423D;

    .canvas-container {
        // background-color: orange;
        width: 100%;
        height: 100%;
    }

    .start {
        background-color: #000;
        color: #fff;
        width: 200px;
        height: 50px;
        line-height: 50px;
        cursor: pointer;
    }

    .end {
        background-color: #000;
        color: #fff;
        width: 200px;
        height: 50px;
        line-height: 50px;
        margin-top: 10px;
        cursor: pointer;
    }

    .draw-area {
        background-color: #000;
        color: #fff;
        width: 200px;
        height: 50px;
        line-height: 50px;
        margin-top: 10px;
        cursor: pointer;
    }

    .clear {
        background-color: #000;
        color: #fff;
        width: 200px;
        height: 50px;
        line-height: 50px;
        margin-top: 10px;
        cursor: pointer;
    }
}
</style>