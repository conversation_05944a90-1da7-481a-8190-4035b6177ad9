<template>
  <div class="classroom-record-test-report-dialog" @click="emits('close')">
    <div class="content" @click.stop="" :style="{ background: getStatus().background }" v-loading="loading">
      <div v-if="nowStatus !== 1&&nowStatus!==null" class="content-header">
        <div class="head-img-content">
          <img :src="getStatus().url" alt="" class="header-image">
          <div class="img-text" :style="{ color: getStatus().color }">{{ getStatus().content }}</div>
        </div>
        <div class="header-percent" :style="{ color: getStatus().color }">{{ averageAccuracy }}%</div>
        <div class="question-category">
          <div class="question-category-item" v-for="(category, index) in questionCategoryList" :key="index">
            <div class="title-content">{{ `${category.name}${category.num}（${changePercent(category.percent)}%）` }}
            </div>
            <div style="display: flex;">
              <div v-if="category.accuracyRate > 0" class="percent-content"
                :style="{ width: changePercent(category.accuracyRate) + '%' }">
                <div class="percent-content-line"></div>
                <div class="percent-content-text">{{ changePercent(category.accuracyRate) + '%' }}</div>
                <div></div>
              </div>
              <div v-if="category.accuracyRate < 1" class="percent-content percent-content-error"
                :style="{ width: (100 - changePercent(category.accuracyRate)) + '%' }">
                <div class="percent-content-line"></div>
                <div class="percent-content-text">{{ (100 - changePercent(category.accuracyRate)) + '%' }}</div>
              </div>
            </div>
          </div>

        </div>
      </div>
      <div v-if="nowStatus !== 1&&nowStatus!==null" class="chart-content">

        <div class="pie-show-content">
          <ReportStatisticsPie :data="analysisKnowNumList"></ReportStatisticsPie>
        </div>
        <div class="line-show-content">
          <ReportStatisticsLine :data="analysisAccuracyList"></ReportStatisticsLine>
        </div>
      </div>

      <div v-if="nowStatus !== 1&&nowStatus!==null" class="bottom-content">
        <div v-if="adviseData.importantFocus.length" class="advise-content advise-content-focus">
          <div class="title-content">重点关注</div>
          <div class="advise-content-show">
            <div class="advise-content-show-item" v-for="(item, index) in adviseData.importantFocus" :key="index">
              <div>{{ `${item.knowledgePoint}（${changePercent(item.accuracy)}%）` }}</div>
            </div>
          </div>
        </div>
        <div v-if="adviseData.weakImprove.length" class="advise-content">
          <div class="title-content">薄弱提升：</div>
          <div class="advise-content-show">
            <div class="advise-content-show-item" v-for="(item, index) in adviseData.weakImprove" :key="index">
              <div>{{ `${item.knowledgePoint}（${changePercent(item.accuracy)}%）` }}</div>
            </div>
          </div>
        </div>
        <div v-if="adviseData.needPractive.length" class="advise-content">
          <div class="title-content">适当练习：</div>
          <div class="advise-content-show">
            <div class="advise-content-show-item" v-for="(item, index) in adviseData.needPractive" :key="index">
              <div>{{ `${item.knowledgePoint}（${changePercent(item.accuracy)}%）` }}</div>
            </div>
          </div>
        </div>
        <div v-if="adviseData.goodStudent.length" class="advise-content ">
          <div class="title-content">优秀同学：</div>
          <div class="advise-content-show">
            <div class="advise-content-show-item" v-for="(item, index) in adviseData.goodStudent" :key="index">
              <div>{{ item }}</div>
            </div>
          </div>
        </div>

        <div class="ai-content">
          <div class="title-content">AI建议：</div>
          <span class="ai-content-text">{{ adviseData.aiSuggestion }}</span>
        </div>
      </div>
      <div v-if="nowStatus === 1&&nowStatus!==null" class="perfect-content">
        <img :src="getStatus().url" alt="">
        <div class="perfect-text">{{ getStatus().content }}</div>
        <div class="perfect-percent">{{ averageAccuracy }}%</div>
      </div>
      <div class="close-btn" @click.stop="emits('close')">
        <img src="/img/svg/icon_close.svg">
      </div>
      <div class="join-content">
        参与人数：{{ adviseData.joinText }}
      </div>

    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import ReportStatisticsLine from './ReportStatisticsLine.vue';
import ReportStatisticsPie from './ReportStatisticsPie.vue';
import gradePerfect from '../assets/grade_perfect.gif'
import gradeGood from '../assets/grade_good.svg'
import gradeNormal from '../assets/grade_normal.svg'
import gradeBad from '../assets/grade_bad.svg'
import { ClassroomRecordRequest } from '@/server_request/classroom_record';
import { ElMessage } from 'element-plus';
const loading = ref(false)

function changePercent(percent) {
  return Math.round(percent * 100)
}

const emits = defineEmits('close')

const nowStatus = ref(null) // 1 完美 2 有待提高 3 继续努力
const props = defineProps({
  taskId: {
    default: ''
  },
  interactId: {
    default: ''
  }
})
const adviseData = ref({
  'importantFocus': [

  ],
  'needPractive': [

  ],
  'goodStudent': [

  ],
  weakImprove: [],
  joinText: '',
  aiSuggestion: '',

})
const questionCategoryList = ref([

])
function getStatus(status) {
  let thisStatus = status
  if (!thisStatus) {
    thisStatus = nowStatus.value
  }
  if (thisStatus == 1) {
    return {
      "url": gradePerfect,
      "content": "非常棒",
      "color": 'var(--correct-color)',
      "background": 'linear-gradient( 90deg, #FFF3E6 0%, #E0FFFB 100%)'
    }
  } else if (thisStatus == 2) {
    return {
      "url": gradeGood,
      "content": "很好",
      "color": 'var(--correct-color)',
      "background": 'linear-gradient( 90deg, #FFF3E6 0%, #E0FFFB 100%)'
    }
  } else if (thisStatus == 3) {
    return {
      "url": gradeNormal,
      "content": "一般",
      "color": 'var(--primary-color)',
      "background": 'linear-gradient( 90deg, #F2FCEE 0%, #B9F1FF 100%)'
    }
  } else {
    return {
      "url": gradeBad,
      "content": "不理想",
      "color": 'var(--error-color)',
      "background": 'linear-gradient( 90deg, #F4EFFF 0%, #B7E7FF 100%)'
    }
  }
}

const averageAccuracy = ref(0)
const analysisAccuracyList = ref([])
const analysisKnowNumList = ref([])

onMounted(() => {
  getPaperReport()
})

async function getPaperReport() {
  try {
    let params = {
      interactId: props.interactId,
      taskId: props.taskId,
    }
    loading.value = true
    let res = await ClassroomRecordRequest.getPaperAnalysis(params)
    loading.value = false
    if (res && res.data) {
      let tempData = {
        'importantFocus': [

        ],
        'needPractive': [

        ],
        'goodStudent': [

        ],
        weakImprove: [],
        joinText: '',
        aiSuggestion: '',

      }
      questionCategoryList.value = res.data.analysisFacilityNumList ?? []
      tempData.goodStudent = res.data.excellentStudents ?? []
      tempData.joinText = `${res.data.submitNumber}/${res.data.totalNumber}`
      tempData.aiSuggestion = res.data.aiSuggestion ?? ''
      analysisKnowNumList.value = res.data.analysisKnowNumList ?? []
      averageAccuracy.value = Math.round(res.data.correctAccuracy * 100)
      analysisAccuracyList.value = res.data.analysisAccuracyList ?? []
      for (var e of analysisAccuracyList.value) {
        if (e.accuracy !== undefined) {
          if (e.accuracy < 0.5) {
            tempData.importantFocus.push(e)
          } else if (e.accuracy < 0.75) {
            tempData.weakImprove.push(e)
          } else if (e.accuracy < 1) {
            tempData.needPractive.push(e)
          }
        }
      }
      adviseData.value = tempData
      let avarage = averageAccuracy.value
      if (avarage < 60) {
        nowStatus.value = 4
      } else if (avarage < 85) {
        nowStatus.value = 3
      } else if (avarage < 100) {
        nowStatus.value = 2
      }else{
        nowStatus.value = 1
      }
    }
  } catch (e) {
    ElMessage.error('网络异常')
  }
}
</script>

<style lang="scss" scoped>
.classroom-record-test-report-dialog {
  width: 100vw;
  height: 100vh;
  position: absolute;
  top: 0;
  z-index: 200;
  background-color: var(--main-anti-bc-alpha-color);
  display: flex;
  align-items: center;
  justify-content: center;

  .content {
    display: flex;
    flex-direction: column;
    border-radius: 26px;
    width: 1665px;
    height: 921px;
    overflow: hidden;
    position: relative;
    background: var(--main-bc-color);

    
    .perfect-content {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      padding-bottom: 160px;
      color: var(--correct-color);

      .perfect-text {
        margin-top: 36px;
        font-weight: 500;
        font-size: 42px;
        line-height: 69px;
        margin-bottom: 25px;
      }

      .perfect-percent {
        font-weight: 500;
        font-size: 80px;
        line-height: 69px;
      }
    }

    .content-header {
      height: 170px;
      padding-left: 85px;
      padding-right: 60px;
      display: flex;
      align-items: center;

      .head-img-content {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        .header-image {
          width: 170px;
          object-fit: contain;
        }

        .img-text {
          position: absolute;
          font-size: 33px;
        }
      }

      .header-percent {
        font-weight: normal;
        font-size: 80px;
        color: var(--primary-color);
        margin-left: 42px;
        margin-right: 133px;

      }

      .question-category {
        display: flex;
        align-items: center;
        flex: 1;

        .gap-content {
          width: 8px;
          height: 1px;
        }

        .question-category-item {
          margin-top: 32px;
          flex: 1;
          display: flex;
          flex-direction: column;
          height: 90px;
          margin: 0px 4px;

          .title-content {
            height: 48px;
            width: 100%;
            background-color: var(--main-bc-color);
            border-radius: 4px 4px 0px 0px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 21px;
            color: var(--secondary-text-color);
            line-height: 31px;
            font-style: normal;
          }

          .percent-content {
            height: 42px;

            display: flex;
            flex-direction: column;
            width: 100%;
            align-items: center;
            font-size: 21px;
            color: var(--correct-color);

            .percent-content-text {
              height: 30px;

              display: flex;
              align-items: center;
            }

            .percent-content-line {
              height: 12px;
              background-color: var(--correct-color);
              width: 100%;


            }

          }

          .percent-content-error {
            color: var(--error-color);

            .percent-content-line {
              background-color: var(--error-color);



            }

          }
        }
      }

    }

    .close-btn {
      position: absolute;
      right: 16px;
      bottom: 16px;
      width: 32px;
      height: 32px;
      cursor: pointer;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .join-content {
      position: absolute;
      left: 60px;
      bottom: 16px;
      font-size: 18px;
      color: var(--secondary-text-color);
    }

    .bottom-content {
      width: 100%;
      flex: 1;
      margin-bottom: 42px;
      overflow-y: auto;
    }


    .chart-content {
      padding: 0px 60px;
      width: calc(100% - 120px);
      display: flex;
      justify-content: space-between;
      align-items: end;
      height: 296px;
      margin-bottom: 16px;

      .line-show-content {
        width: 758px;
        height: 296px;
      }

      .pie-show-content {
        width: 758px;
        height: 296px;
      }

    }

    .advise-content {
      margin-top: 16px;
      padding: 0px 60px;
      width: calc(100% - 120px);
      display: flex;
      align-items: start;

      .title-content {
        width: 120px;
        height: 44px;
        align-items: center;
        display: flex;
        font-weight: bold;
        font-size: 24px;
        color: var(--text-color);
      }

      .advise-content-show {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        flex: 1;

        .advise-content-show-item {
          height: 44px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0px 12px;
          background-color: var(--main-bc-color);
          font-weight: 500;
          font-size: 21px;
          border: 1px solid var(--border-bar-color);
          border-radius: 8px;
        }

      }
    }

    .advise-content-focus {
      color: var(--primary-color) !important;
    }

    .ai-content {

      margin: 0px 60px;
      margin-top: 16px;
      width: calc(100% - 120px);
      border-radius: 21px;
      border: 1px solid var(--border-bar-color);
      min-height: 156px;
      padding: 14px 16px;
      display: flex;
      line-height: 36px;
      font-size: 24px;
      font-weight: bold;
      display: flex;
      align-items: start;
      box-sizing: border-box;
      background-color: var(--main-bc-color);

      .title-content {
        width: 100px;
      }

      .ai-content-text {
        flex: 1;
        color: var(--secondary-text-color);
      }
    }


    .focus-section {
      border-radius: 16px;
      border: 1px solid var(--border-bar-color);
      height: 315px;
      overflow-y: scroll;
      padding: 0px 32px;
      display: flex;
      flex-direction: column;

      .title {
        margin-top: 12px;
      }

      .focus-list {
        flex: 1;
        overflow-y: scroll;

        .focus-item {
          display: flex;
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .student-label {
            width: 80px;
            font-size: 21px;
            color: var(--text-color);
            font-weight: 500;
          }

          .knowledge-points {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            .knowledge-point {
              font-size: 21px;
              font-weight: 400;
              color: var(--secondary-text-color);
            }
          }
        }
      }
    }
  }


}
</style>
