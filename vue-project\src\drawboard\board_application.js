import { useDrawBoardStore } from "@/stores/drawboard_store";
import { BoardRootView } from "./board_root_view";
import { DrawQuality, EditMode, DrawZ } from "./draw_enums";
import { TextureCache } from "./texture_cache";
import * as THREE from 'three';

export class BoardApplication {

    /// width、height 单位为像素点
    /// 内部处理分辨率倍数
    constructor(width, height, quality = DrawQuality.WQHD, fov = 75) {

        this.scene = new THREE.Scene()

        // 创建透视相机
        const aspect = width / height;
        this.camera = new THREE.PerspectiveCamera(fov, aspect, 0.01, 1000)
        this.camera.position.z = DrawZ.initZ

        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({ 
            antialias: true, 
            alpha: true, 
            precision: 'highp', 
            preserveDrawingBuffer: true, 
            powerPreference: "high-performance",
             
            
        })
        this.renderer.localClippingEnabled = true
        
        this.renderer.setClearColor(0x000000, 0.0)

        this.textureCache = new TextureCache()

        this.setRenderSize(width, height, quality)

        /// 添加到div中
        this.div = null

        /// 上次animate 时间
        this.lastRefreshTime = 0

        this.cameraInitSize = this.getScreenSizeAtDepth(DrawZ.objcZ)

        this.currentCameraSize = this.cameraInitSize

        // 默认模式是拖拽模式
        this.editMode = EditMode.Dragging

        // 记录所有元素所需在的最大的x点
        // 用于往后空白处添加内容
        this.lastDrawX = -this.cameraInitSize.width / 2

        // 主视图
        this.rootView = new BoardRootView(
            new WeakRef(this),
            new THREE.Vector3(0, 0, DrawZ.objcZ),
            { width: Number.MAX_SAFE_INTEGER, height: Number.MAX_SAFE_INTEGER })

        this.scene.add(this.rootView)
    }

    setLastDrawX(x) {
        if (x > this.lastDrawX) {
            this.lastDrawX = x
        }
    }

    getScreenSizeAtDepth(depth) {
        var fov = this.camera.fov * (Math.PI / 180); // 将fov从度数转换为弧度
        var height = 2 * Math.tan(fov / 2) * (this.camera.position.z - depth);
        var width = height * this.camera.aspect;
        return { width, height };
    }

    setRenderSize(width, height, quality = null) {

        if (this.width === width && this.height === height) {
            if (!quality) {
                return
            }
            if (this.quality === quality) {
                return
            }
        }
        if (quality) {
            this.quality = quality
        }
        else {
            // 后面要使用 quality
            quality = this.quality
        }

        this.width = width
        this.height = height

        const aspect = width / height;
        this.camera.aspect = aspect

        if (width > quality) {
            const yXratio = height / width
            let ratio = (window.screen.width * window.devicePixelRatio) / quality
            let pixelRatio = parseInt(ratio * window.devicePixelRatio * 100) / 100
            this.pixelRatio = pixelRatio

            let newWidth = quality / pixelRatio
            const newHeight = newWidth * yXratio;
            this.renderer.setSize(newWidth, newHeight)
            this.renderWidth = newWidth
            this.renderHeight = newHeight

            this.renderer.setPixelRatio(pixelRatio)

        }
        else {
            this.renderer.setSize(width / window.devicePixelRatio, height / window.devicePixelRatio)
            this.renderWidth = width / window.devicePixelRatio
            this.renderHeight = height / window.devicePixelRatio

            this.renderer.setPixelRatio(window.devicePixelRatio)
            this.pixelRatio = window.devicePixelRatio
        }

        this.camera.updateProjectionMatrix()

        this.updateCanvasStyle()

        this.animate()
    }

    painterSnapShoot() {
        return new Promise((resole) => {
            this.renderer.domElement.toBlob((blob) => {
                resole(blob)
            })
        })
    }


    updateCanvasStyle() {
        this.renderer.domElement.style.width = this.width / window.devicePixelRatio + 'px';
        this.renderer.domElement.style.height = this.height / window.devicePixelRatio + 'px';
        this.renderer.domElement.style.position = 'absolute';
        this.renderer.domElement.style.left = this.left / window.devicePixelRatio + 'px'
        this.renderer.domElement.style.top = this.top / window.devicePixelRatio + 'px'
    }

    /// 显示添加div
    /// 是否要开启鼠标和触摸
    insetToDom(div) {
        this.div = div
        div.appendChild(this.renderer.domElement);
        this.updateCanvasStyle()
        this.animate()
    }

    removeDom() {
        if (this.div) {
            this.div.replaceChildren()
        }
        this.div = null
    }

    /// 编辑还是移动画布 EditMode
    setEditMode(mode) {
        this.editMode = mode
        this.rootView.setEditMode(mode)
    }

    /// 设置 笔或者橡皮模式
    setDrawMode(mode) {
        this.rootView.setDrawMode(mode)
    }


    updateEraserWidth(width) {
        this.rootView.updateEraserWidth(width)
    }

    setLineStyle(width, color) {
        this.rootView.setLineStyle(width, color)
    }

    setPenStyle(style) {
        this.rootView.setPenStyle(style)
    }

    // 撤销一笔画
    cancelALine() {
        this.rootView.cancelALine()
    }

    resetMaxFrame() {
        this.camera.position.z = DrawZ.maxsZ
        this.animate()
        this.currentCameraSize = this.getScreenSizeAtDepth(DrawZ.objcZ)
    }

    // 切换到初始化距离
    resetInstallFrame() {
        this.camera.position.z = DrawZ.initZ
        this.animate()
        this.currentCameraSize = this.getScreenSizeAtDepth(DrawZ.objcZ)
    }


    getCurrentCameraSize() {
        this.currentCameraSize = this.getScreenSizeAtDepth(DrawZ.objcZ)
        return this.currentCameraSize
    }

    onMouseDown(event) {
        if (event.button === 0) {
            this.handleDownEvent(event, true)
        }
    }

    onMouseMove(event) {
        if (event.button === 0) {
            this.handleMoveEvent(event, true)
        }
    }

    onMouseUp(event) {
        if (event.button === 0) {
            this.handleUpEvent(event, true)
        }
    }

    onMouseOut(event) {
        this.onMouseUp(event)
    }

    onTouchStart(event) {
        this.handleDownEvent(event, false)
    }

    onTouchMove(event) {
        this.handleMoveEvent(event, false)
    }

    onTouchEnd(event) {
        this.handleUpEvent(event, false)
    }

    convertPoint(event) {

        let offsetX = event.clientX * window.devicePixelRatio
        let offsetY = event.clientY * window.devicePixelRatio
        if (this.div) {
            const rect = this.div.getBoundingClientRect();
            offsetX = offsetX - rect.left * window.devicePixelRatio
            offsetY = offsetY - rect.top * window.devicePixelRatio
        }
        const x = (offsetX / this.width) * 2 - 1;
        const y = -(offsetY / this.height) * 2 + 1;

        return {x, y}
    }

    getIsPanning(event, isMouse) {
        if (isMouse && this.editMode === EditMode.Dragging) {
            return true
        }
        if (!isMouse) {
            let drag = (this.editMode === EditMode.Dragging && (event.touches.length > 2 || event.touches.length <= 1))
            let dragInDraw = (this.editMode === EditMode.Drawing && event.touches.length > 2)
            return drag || dragInDraw
        }
    }

    getIsScale(event, isMouse) {
        if (!isMouse && event.touches.length === 2) {
            return true
        }
        return false
    }


    getTouchDistance(event) {
        const dx = event.touches[0].clientX - event.touches[1].clientX;
        const dy = event.touches[0].clientY - event.touches[1].clientY;
        return Math.sqrt(dx * dx + dy * dy);
    }

    handleDownEvent(event, isMouse) {
        this.touchDidDown = true
        let singleTouch = isMouse || (!isMouse && event.touches.length === 1)
        let e = isMouse ? event : event.touches[0];
        let point = this.convertPoint(e)
        if (singleTouch&&!this.rootView.subviewScaling) {
            this.touchStart = e
            this.rootView.onTouchDown(point)
        }else{
            let view = this.rootView.onMultiTouchDown(event,point)
            
            if (this.getIsScale(event, isMouse)&&view) {
                this.panInitDistance = this.getTouchDistance(event);
                this.panInitialScale = view.nowScale;
            }
        }
        
        if (this.rootView.subviewDragging||this.rootView.subviewScaling) {
            this.animate()
            return
        }

        if (this.getIsPanning(event, isMouse)) {
            this.panStart = { x: e.clientX, y: e.clientY }
        }
        else if (this.getIsScale(event, isMouse)) {
            this.panInitDistance = this.getTouchDistance(event);
            this.panInitialScale = this.camera.position.z;
        }

        this.animate()
    }

    handleMoveEvent(event, isMouse) {
        if (!this.touchDidDown) {
            return
        }
        let singleTouch = isMouse || (!isMouse && event.touches.length === 1)
        let e = isMouse ? event : event.touches[0];
        if (singleTouch) {
            let point = this.convertPoint(e)
            this.rootView.onTouchMove(point)
            let space = Math.sqrt(Math.pow(e.clientX - this.touchStart.clientX, 2) + Math.pow(e.clientY - this.touchStart.clientY, 2));
            // 有些屏幕不准确会晃动， 大于3才算移动取消点击
            if (space > 3) {
                this.rootView.onTapCancel()
            }
        }else if(this.rootView.subviewScaling&&this.getIsScale(event, isMouse)){
            const newDistance = this.getTouchDistance(event);
            // 减缓缩放速度
            const scaleFactor =(newDistance - this.panInitDistance) / this.panInitDistance*0.3;
            const newScale = this.panInitialScale+ scaleFactor;
            
            this.rootView.onSubViewScale(newScale)
        }

        if (this.rootView.subviewDragging||this.rootView.subviewScaling) {
            this.animate()
            return
        }

        if (this.getIsPanning(event, isMouse) && this.panStart) {
            let spaceX = e.clientX - this.panStart.x
            let spaceY = e.clientY - this.panStart.y
            this.camera.position.x -= spaceX * this.currentCameraSize.width * (this.camera.position.z / DrawZ.initZ) / (this.width / window.devicePixelRatio)
            this.camera.position.y += spaceY * this.currentCameraSize.height * (this.camera.position.z / DrawZ.initZ) / (this.height / window.devicePixelRatio)
            this.panStart = { x: e.clientX, y: e.clientY }
            if (!isMouse) {
                this.rootView.onTapCancel()
            }
        }
        else if (this.getIsScale(event, isMouse)) {
            const newDistance = this.getTouchDistance(event);
            // 减缓缩放速度
            const scaleFactor = 1 + (newDistance - this.panInitDistance) / this.panInitDistance * (0.06 + 0.06 * (this.camera.position.z - DrawZ.objcZ));
            const newScale = this.panInitialScale / scaleFactor;
            
            this.setCameraZScale(newScale)
            if (!isMouse) {
                this.rootView.onTapCancel()
            }
        }
        this.animate()
    }


    handleUpEvent(event, isMouse) {
        this.panStart = null
        this.panInitDistance = null
        this.panInitialScale = null
        if (!this.touchDidDown) {
            return
        }
        this.touchDidDown = false
        let singleTouch = isMouse || (!isMouse && event.touches.length <= 1)
        let e = isMouse ? event : (event.touches.length > 0 ? event.touches[0] : event.changedTouches[0])
        let point = this.convertPoint(e)
        if (singleTouch) {
            
            this.rootView.onTouchUp(point)
        }else{
            this.rootView.onMultiTouchUp(event,point)
        }
        this.animate()
    }

    // 绘制线条在相机z = DrawZ.objcZ的位置上，固定这个值
    // 调整这个值线条会在3D空间z轴位置变化
    // 调整相机 z轴（远近）即可调整放大缩小
    setCameraZScale(z) {
        // 相机设置 低于0.1就看不见了
        if (z < DrawZ.objcZ + 0.2) {
            z = DrawZ.objcZ + 0.2
        }
        else if (z > DrawZ.maxsZ) { // 相机z默认位置5
            z = DrawZ.maxsZ
        }
        this.camera.position.z = z
        this.animate()
        this.currentCameraSize = this.getScreenSizeAtDepth(DrawZ.objcZ)
    }

    onWheel(event) {
        
        let point = this.convertPoint(event)
        let view = this.rootView.onRootWhell(event,point)
        const drawBoardStore = useDrawBoardStore()
        
        if(view&&view.changeScale&&view.nowScale){
            let newScale = view.nowScale - event.deltaY * 0.001
            view.changeScale(newScale)
            return
        }
        if(drawBoardStore.imageOnEdit){
            return
        }
        /// 控制缩放速度
        const v = this.camera.position.z + event.deltaY * 0.0005;
        this.setCameraZScale(v)
    }

    animate() {
        if (this.div) {
            if (Date.now() - this.lastRefreshTime > 15) {
                if (!this.renderer) {
                    return
                }
                if (this.timeout) {
                    clearTimeout(this.timeout)
                }
                requestAnimationFrame(() => {
                    if (this.renderer) {
                        this.renderer.render(this.scene, this.camera);
                    }
                });
                this.lastRefreshTime = Date.now()
            }
            else {
                if (this.timeout) {
                    clearTimeout(this.timeout)
                }
                this.timeout = setTimeout(() => {
                    if (!this.renderer) {
                        return
                    }
                    requestAnimationFrame(() => {
                        if (this.renderer) {
                            this.renderer.render(this.scene, this.camera);
                        }
                    });
                    this.lastRefreshTime = Date.now()
                }, 15)
            }
        }
    }


    dispose() {
        this.rootView.dispose()
        if (this.timeout) {
            clearTimeout(this.timeout)
            this.timeout = null
        }
        this.scene.traverse((o) => {
            if (o.geometry) {
                o.geometry.dispose()
            }
            if (o.material) {
                if (o.material.length) {
                    for (let i = 0; i < o.material.length; ++i) {
                        o.material[i].dispose()
                    }
                }
                else {
                    o.material.dispose()
                }
            }
        })
        this.scene.clear()
        this.scene = null
        this.camera = null
        if (this.renderer) {
            this.renderer.renderLists.dispose()
        }
        this.renderer = null

        this.textureCache.clearAll()
    }
}