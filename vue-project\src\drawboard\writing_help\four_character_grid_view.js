import { FourAndCharacterGridView } from "./components/four_and_character_view";
import * as THREE from 'three';
import { WritingGridView } from "./writing_grid_view";

// 田字格视图 - 统一版
export class FourCharacterGridView extends WritingGridView {

    constructor(application, pos, size, color, copyCallBack, type) {
        // 初始化父类
        super(application, pos, size, color, copyCallBack, type)
        // 竖线数量（不包括两端的边框线）
        this.verticalLines = 7
        // 绘制田字格
        this.createGrid()

    }



    createGrid() {
        let app = this.application?.deref()
        this.lineWidth = app.cameraInitSize.width / 100

        // 计算实际内容区域的大小（排除按钮的空间和额外间距）
        let contentWidth = this.size.width - this.buttonSize * this.spacingFactor
        let contentHeight = this.size.height
        contentWidth = contentHeight * 2
        let offsetX = -this.buttonSize * this.spacingFactor / 2
        let offsetY = 0
        for (let i = 0; i < 2; i++) {
            let position = new THREE.Vector3(offsetX, offsetY + contentHeight / 4 - i * contentHeight / 2, 0)
            let view = new FourAndCharacterGridView(
                this.application,
                position,
                { width: contentWidth, height: contentHeight / 2 },
                this.color,
                i==0,
            )
            this.addSubView(view)
        }

    }
}
