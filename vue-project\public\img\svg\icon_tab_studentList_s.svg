<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="48px" viewBox="0 0 48 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>工具栏icon/展开学生</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="0切图" transform="translate(-1639, -1011)">
            <g id="工具栏icon/展开学生" transform="translate(1639, 1011)">
                <rect id="矩形" fill="#5F9A9E" opacity="0.551176525" x="0" y="0" width="48" height="48" rx="24"></rect>
                <rect id="矩形" stroke="#979797" fill="#D8D8D8" opacity="0" x="0.5" y="0.5" width="47" height="47"></rect>
                <path d="M47.5,0.5 L47.5,47.5 L0.5,47.5 L0.5,0.5 L47.5,0.5 Z" id="矩形备份" stroke="#979797" fill="#D8D8D8" opacity="0"></path>
                <g id="编组" transform="translate(13, 5)" stroke="#FFFFFF" stroke-linecap="round" stroke-linejoin="round" stroke-width="2">
                    <circle id="椭圆形" cx="5.5" cy="13.75" r="2.75"></circle>
                    <circle id="椭圆形" cx="16.5" cy="13.75" r="2.75"></circle>
                    <circle id="椭圆形" cx="11" cy="2.75" r="2.75"></circle>
                    <path d="M11,22 C11,18.96246 8.53754,16.5 5.5,16.5 C2.4624325,16.5 0,18.96246 0,22" id="路径"></path>
                    <path d="M22,22 C22,18.96246 19.53754,16.5 16.5,16.5 C13.46246,16.5 11,18.96246 11,22" id="路径"></path>
                    <path d="M16.5,11 C16.5,7.96246 14.03754,5.5 11,5.5 C7.96246,5.5 5.5,7.96246 5.5,11" id="路径"></path>
                </g>
                <text id="展开学生" font-family="NotoSansCJKsc-Medium, Noto Sans CJK SC" font-size="9" font-weight="400" fill="#FFFFFF">
                    <tspan x="6" y="40">展开学生</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>