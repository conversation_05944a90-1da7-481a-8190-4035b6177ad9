<template>
  <div v-loading="loading&&!showCalendar" class="records" :style="isHomework()?{height:'calc(100% - 136px)'}:''">
    <div class="expand"></div>
    <div class="list" v-if="records.length">
      <div class="item" v-for="(item, index) in records" :key="index" @click="checkTask(item)" :style="index % 2 == 0?{backgroundColor:'var(--list-bc-color)'}:''">
        <div class="time" v-if="isHomework() && (index == 0 || item.time != records[index - 1].time)">
          {{ item.time }}
        </div>
        <div class="info">
          <div class="info_left">
            <div class="type">
              <span>【{{getTypeName(item)}}】</span>
              <span v-if="item.type == 1 && item.ext">{{ decodePaperName(item.ext) }}</span>
              <span v-if="item.type == 6 && item.title">{{item.title}}</span>
            </div>
            <div class="user" v-if="isHomework()">
              <!-- <span>{{ item.studentNum }}人参与</span> -->
              <span>{{ item.submitedNum }}人提交</span>，
              <span>已批改{{ item.correctedNum }}人</span>，
              <span>{{item.status == 0 ? "已结束" : "进行中"}}</span>
            </div>
            <div class="user" v-else>
              <!-- {{ item.studentNum }}人参与 -->
              <span >{{item.studentNum ||(item.user_complete ? item.user_complete : 0) }}人提交</span>，
              <template v-if="item.endTime">结束于{{ formatDate(item.endTime) }}，用时{{item.withTime}}</template>
            </div>
          </div>
          <RBPButton :btn-have-border="true" v-if="!((item.type == 1 || item.type == 6) && item.studentNum == 0)" color="#000" background-color="#fff" style="margin-right: 0px;" @click.stop="checkTask(item)" btn-text="查看"  btn-type="small"></RBPButton>
        </div>
      </div>
    </div>
    <RBPEmpty v-else-if="!loading" des="暂无记录"></RBPEmpty>

  </div>
</template>

<script setup>
import RBPButton from "../baseComponents/RBPButton.vue"
import RBPEmpty from "../baseComponents/RBPEmpty.vue"
import { ref, defineExpose, defineEmits, onBeforeUnmount } from "vue"
import { formatRecordDate, calcDiffTime, getTypeName } from './js/utils.js'
import {  ElMessage } from 'element-plus'
import { ClassroomRecordRequest } from '@/server_request/classroom_record.js'
import { loginInstance } from "@/login_instance/login_instance"
import { storeToRefs } from "pinia";
import { useClassroomUIStore } from "@/stores/classroom_ui_store.js"
const classroomUIStore = useClassroomUIStore()
const { showRecordType } = storeToRefs(classroomUIStore)
import { useInteractStore } from "@/stores/interact_store.js"
const interactStore = useInteractStore()
const emits = defineEmits(['handleShowInteract'])
const props = defineProps({
  showCalendar:{
    default:false
  }
})
//互动列表
const records = ref([])
//互动任务id列表
let taskIds = []

//获取是否是家庭作业
function isHomework(){
  return showRecordType.value == 'homework'
}

const loading = ref(false)
//获取互动记录
const  getRecords = async (params) => {
  records.value = []
  if (isHomework()) {
    await getHomework(params.classId, params.subjectId, params.date)
    return records.value
  } else {
    getInteracts(params.classId, params.classRecordId)
    return []
  }
}

const getInteracts = async (classId, classRecordId) => {
  try {
    
    loading.value = true
    let { data } = await ClassroomRecordRequest.getInteraction({ classId, classRecordId })
    loading.value = false
    if (data) {
      data = data.filter(item => {return item.type != 11})
      data.forEach((item, index) => {
        if (item.endTime) {
          let diffTime = new Date(item.endTime).getTime() - new Date(item.startTime).getTime()
          item.withTime = calcDiffTime(diffTime)
        } else {
          item.endTime = '--:--'
          item.withTime = '--'
        }
        if (item.type == 0 && item.rate != 1 && item.studentNum) {
          taskIds.push(item.taskId)
        }
        if (item.type == 1 && item.rate != 1 && item.studentNum && item.paperId) {
          taskIds.push(item.taskId)
        }
        if (item.type == 7) {
          item.listIndex = index
          qids.push(item)
        }
      })
      records.value = data
      if (taskIds.length > 0) {
        getTaskData()
        taskTimer = setInterval(() => {
          getTaskData()
        }, 5000);
      }
      if (qids.length > 0) {
        startAtTimer()
      }
    }
  } catch (error) {
    console.log(error)
  }
}

const getHomework = async (classId, subjectId, date) => {
  try {
    loading.value = true
    let params = {
      "order": "desc",
      "pageNo": 1,
      "pageSize": 20, // 默认取最近20条
      "sort": "task_start_date",
      "classId": classId,
      "subjectId": subjectId,
    }
    if (date) {
      params.date = date
      // params.date = '20240802'
      params.sort = "start_time"
    }
    let { data } = await ClassroomRecordRequest.getHomework(params)
    loading.value = false
    let list = data.list.reverse()
    list.forEach((item) => {
      item.time =
        item.startTime.slice(0, 4) +
        "-" +
        item.startTime.slice(4, 6) +
        "-" +
        item.startTime.slice(6, 8);
    })
    records.value = list
  } catch (error) {
    console.log(error)
    loading.value = false
  }
}

//时间处理 
function formatDate(time) {
  if (time) {
    let date = new Date(time.replace(/-/g, '/'));
    return formatRecordDate(date, 'hh:mm');
  } else {
    return '--:--'
  }
}
function decodePaperName(str) {
  return Base64.decode(str.split(",")[0].split(":")[1]);
}

//轮询获取任务进度
let taskTimer
let atTimer //爱听写计时器
//爱听写id列表
const qids = []
function startAtTimer() {
  getAitingxieItem()
  atTimer = setInterval(() => {
    if (qids.length == 0) {
      clearInterval(this.atTimer)
    }
    getAitingxieItem()
  }, 10000)
}
function getAitingxieItem() {
  qids.forEach((item, index) => {
    getItemStatics(item, index)
  })
}
async function getItemStatics(item, index) {
  try {
    let params = {
      qid: item.taskId,
      user_id: loginInstance.teacher.teacherId, //路由传入
      ignore_my: 1, // 忽略自己
    }
    let { data } = await ClassroomRecordRequest.questionStatistics(params)
    records.value[item.listIndex]['user_complete'] = data.user_complete
    if (data.user_complete == data.user_count) {
      qids.splice(index)
    }
  } catch (e) {
    console.log(e)
  }
}

//获取记录任务数据
async function getTaskData() {
  if (taskIds.length > 0) {
    try {
      let { data } = await ClassroomRecordRequest.interactionTaskProgress({taskIds})
      let tasks = data
      for (let task of tasks) {
        setPercentNew(task.taskId, task.rate)
      }
    } catch (e) {
      console.log(e)
    }
  }
}
//设置记录的任务rate
function setPercentNew(taskId, rate) {
  let recordL = records.value
  for (let i = 0; i < recordL.length; i++) {
    if (recordL.taskId == taskId) {
      recordL['rate'] = rate;
    }
  }
  //如果进度为100%,则不再请求该task
  if (rate == 1) {
    let index = taskIds.indexOf(taskId)
    taskIds.splice(index, 1)
    if (taskIds.length == 0) {
      clearInterval(taskTimer);
    }
  }
}

//查看条目
function  checkTask(item) {
  let classId = item.classId
  let classRecordId = item.classRecordId
  let interactId = item.interactId
  let type = item.type
  let taskId = item.taskId
  if ((type == 1 || type == 9) && !item.paperId) {
    ElMessage.error('该次互动未检测到试卷插入')
    return
  }
  interactStore.interactResult = {
    classId,
    classRecordId,
    interactId,
    type,
    taskId
  }
  emits('handleShowInteract')
}

onBeforeUnmount(() => {
  //清空计时器
  clearInterval(taskTimer)
  clearInterval(atTimer)
})
defineExpose({ getRecords })
</script>

<style lang="scss" scoped>
.records {
  width: 100%;
  height: calc(100% - 66px);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  .expand {
    flex: 1;
  }
  .list {
    .item {
      box-sizing: border-box;
      border-bottom: 1px solid var(--border-bar-color);
      padding: 12px 20px 12px 30px;
      .time {
        color: var(--explanatory-text-color);
        font-size: 18px;
        margin-bottom: 6px;
        text-align: left;
      }
      .info {
        display: flex;
        flex-direction: row;
        align-items: center;
        .info_left {
          flex: 1;
          text-align: left;
          .type {
            font-weight: 400;
            font-size: 21px;
            color: var(--text-color);
            line-height: 31px;
            margin-bottom: 2px;
            margin-left: -10px;
          }
          .user {
            font-weight: 400;
            font-size: 18px;
            color: var(--secondary-text-color);
            line-height: 27px;
          }  
        }
        .check {
          margin-right: 0px;
        }
      }
    }
    .item:first-child{
      margin-top: 16px;
    }
    // .item:last-of-type {
    //   border-bottom: none;
    // }
  }
}

.progress {
  height: 1px;
  background: #9DBEDA;
  border-radius: 1px;
  position: relative;
  top: 11px;
  .percent {
    height: 1px;
    background: #00D97F;
    border-radius: 1px;
    transition: 0.3s all linear;
  }
}
</style>