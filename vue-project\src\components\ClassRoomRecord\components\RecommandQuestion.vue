<template>
    <div class="classroom-record-recommand-question-dialog" :style="getStyle()">
        <RBPAlert :show :hide-close="testQuestionInfo" :hide-bc="true" :is-support-close="!testQuestionInfo"
            title="推荐题目" width="1500px" height="800px" @close="emits('close')">
            <template v-slot:rbpDiv>
                <div class="content" v-loading="loading">
                    <div class="paper-component" ref="paperComponent" id="paperComponentBox">
                        <div style="height:480px"></div>
                        <div v-for="(item, index) in papers" :key="index" class="paper-wrap">
                            <RBPButton class="launch-btn" background-color="#25B0FA1A" font-size="16px"
                                border-radius="4px" width="100px" height="32px" btn-text="发起互动"
                                @click="clickStart(item)"></RBPButton>
                            <div class="paper" v-html="item.quesBody"></div>
                            <!-- <div class="area" :style="{ width: '100%', height: '32px', right: '24px', top: '16px' }">
                                
                            </div> -->
                        </div>
                        <div style="height:480px"></div>
                    </div>

                    <div class="refresh-btn" @click="getPaper">
                        <RBPButton width="240px" :btn-selected="true" btn-text="继续推荐"></RBPButton>
                    </div>
                </div>
            </template>
        </RBPAlert>
    </div>
</template>

<script setup>
import RBPButton from '@/components/baseComponents/RBPButton.vue'
import { ClassroomRecordRequest } from '@/server_request/classroom_record'
import { onMounted, onUnmounted, ref, watch } from 'vue'
import RBPAlert from '@/components/baseComponents/RBPAlert.vue'
import { ElMessage } from 'element-plus'
import roomUpdater from '@/classroom/classroom_updater'
import { Interact } from '@/classroom/interact_enums'
import $ from "jquery"
import { useInteractStore } from '@/stores/interact_store'
import { storeToRefs } from 'pinia'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { DisplayDirection } from '@/classroom/frame_enums'
const questions = ['#questionSelectBox', '#paperpenBox', '#questionBackBox', '#showPaperpenBox']
const emits = defineEmits('close')
const papers = ref([])
const interactStore = useInteractStore()
const { testQuestionInfo } = storeToRefs(interactStore)
const loading = ref(false)
const classroomUIStore = useClassroomUIStore()
const { displayDirection } = storeToRefs(classroomUIStore)
const props = defineProps({
    taskId: {
        default: ''
    },
    interactId: {
        default: ''
    }
})


function initPaper() {
    if (papers.value.length) {
        return
    }
    getPaper()
}



function scrollToBottom() {
    const element = document.getElementById('paperComponentBox');
    element.scrollTo({
        top: 480,
        behavior: 'smooth'
    });
}
async function getPaper() {
    loading.value = true
    try {
        let params = {
            interactId: props.interactId,
            taskId: props.taskId,
        }
        let res = await ClassroomRecordRequest.getRecommandQuestion(params)

        if (res && res.data && res.data.length) {
            papers.value = []
            let tempList = []
            for (let item of res.data) {
                let quesData = JSON.parse(item.quesBody)
                
                tempList.push(quesData)
            }
            if (window.getZyGroups) {
                let data = window.getZyGroups(tempList,)

                if (data.length) {
                    for (let item of res.data) {
                        for (let group of data) {
                            let fit = false
                            for (let groupItem of group.questions) {

                                if (groupItem.quesId == item.quesId) {
                                    fit = true
                                    const raw = groupItem.quesBody ?? '';

                                    const converted = raw.replace(/\$\$(.+?)\$\$/gs, (_, expr) => `$${expr}$`);
                                    item.quesBody = converted.replaceAll('\\\parallel', '//')
                                     .replaceAll('\\parallel', '//')
                                     .replaceAll('\\boldsymbol','\\mathbf')


                                    papers.value.push(item)
                                    break

                                }
                                if (fit) {
                                    brreak
                                }
                            }
                        }

                        // const raw = item.quesBody;
                        // const converted = raw.replace(/\$\$(.+?)\$\$/gs, (_, expr) => `$${expr}$`);
                        // item.quesBody = converted


                    }
                }
            }
            if (!papers.value.length) {
                ElMessage.error('获取题目失败')

            }

            setTimeout(() => {
                scrollToBottom()
                renderMath()
            }, 200)
        } else {
            ElMessage.error('获取题目失败')
        }
    } catch (e) {
        ElMessage.error('网络异常')
        //TODO handle the exception
    }
    loading.value = false
}

function getMode(type) {
    switch (type) {
        case 2:
            return Interact.singleChoice
        case 3:
            return Interact.trueFalse
        case 5:
            return Interact.multiChoice
        default:
            return Interact.paperPen
    }
}
const paperComponent = ref(null)

function getStyle() {
    const styleMap = {}
    if (testQuestionInfo.value && getMode(testQuestionInfo.value.quesType) != Interact.paperPen) {
        if (displayDirection.value == DisplayDirection.Left) {
            styleMap.right = "-200px"
        } else {
            styleMap.left = "-200px"
        }

    }
    return styleMap

}

// 触发 MathJax 渲染
const renderMath = () => {
    if (window.MathJax?.typesetPromise && paperComponent.value) {
        window.MathJax.typesetPromise([paperComponent.value])
    }
}

async function clickStart(item) {
    if (testQuestionInfo.value) {
        ElMessage.warning("请结束当前互动")
        return
    }
    await roomUpdater.startInteract(getMode(item.quesType))
    const str = item.quesAnswer + "";
    testQuestionInfo.value = item
    if (item.quesAnswer && getMode(item.quesType) != Interact.paperPen) {
        if (getMode(item.quesType) == Interact.trueFalse) {
            let option = '错'
            if (str == 'RIGHT' || str == 1) {
                option = '对'
            }
            testQuestionInfo.value.options = [{ option }]
        } else {
            const result = str.split('').map(char => ({ option: char }));

            testQuestionInfo.value.options = result
        }


    }
    questions.forEach((e) => {
        $(e).css('z-index', 'var(--interact-test-z-index)');
    })
}
onUnmounted(() => {
    testQuestionInfo.value = null
})
defineExpose({
    initPaper
})
</script>
<style lang="scss">
.classroom-record-recommand-question-dialog {
    .el-loading-mask {
        background-color: transparent !important;
    }

    .paper {
        font-size: 24px;

        .qml-og{
            td{
                display: flex;
                align-items: center;
            }
        }

        img {
            min-height: 28px;
            vertical-align: middle;
        }

        p {
            width: 100% !important;
            // font-size: 3em;
            color: var(--text-color);

        }

        i {
            font-style: normal;
        }
        em {
            font-style: normal;
        }
        mjx-math {
            
            
            font-style: normal !important;
        }
        mjx-container[jax="CHTML"] {
        display: inline-block !important;
        overflow: visible !important;
        line-height: 1.2 !important;
        font-style: normal !important;
        margin: 1px 2px;
        overflow: visible !important;
        }
        mjx-vec {
            max-width: none !important;
            padding: 0 2px !important;
        }
      
      
        dotted {
            border-bottom: 4px dotted var(--text-color);
            padding: 0px 2px;
            margin: 0px 2px;
        }
    }
}
</style>
<style lang="scss" scoped>
.classroom-record-recommand-question-dialog {
    width: 100vw;
    height: 100vh;
    position: absolute;
    z-index: 200;
    top: 0;

    .content {
        background: #FFFFFF;
        display: flex;
        border-radius: 0 0 26px 26px;
        width: 100%;
        height: 100%;
        overflow: hidden;
        align-items: center;
        position: relative;

        .paper-component {
            width: 100%;
            height: 100%;
            position: relative;
            padding-bottom: 50px;
            overflow-y: scroll;
            box-sizing: border-box;
            scrollbar-width: none;
        }

        .refresh-btn {
            width: 240px;
            position: absolute;
            bottom: 80px;
            left: calc((100% - 240px)/2);
        }



        .paper-component::-webkit-scrollbar {
            display: none;
            /* Chrome, Safari 和 Opera */
        }


        .paper-wrap {
            position: relative;
            flex: 1;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: end;
        }

        .paper {
            width: calc(100% - 24px);
            padding: 0px 12px;
            min-height: 64px;
        }

        .paper p {
            width: 100% !important;
            font-size: 42px !important;
            color: var(--text-color);
        }

        .launch-btn {
            margin-top: 12px;
            margin-right: 12px;
        }

        .area {
            z-index: 10;
            position: absolute;
            // background-color: rgba($color: #000000, $alpha: 0.5);
            display: flex;
            justify-content: end;
        }

    }

}
</style>