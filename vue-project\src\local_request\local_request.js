import axios from "axios";
import { LocalUrls } from "./local_urls";
import { loginInstance } from "@/login_instance/login_instance";
import { getSubjectName } from "@/classroom/classroom_helper";




export class LocalRequest {

    static defaultConfig() {
        return {
            headers: {
                'Content-Type': 'application/json'
            }
        }
    }

    static async sendStartClass(token, teacher, classroom) {
        const url = LocalUrls.startClassUrl()
        const data = {
            "token": token,
            "subjectId": classroom.subjectId,
            "subjectName":getSubjectName(classroom.subjectId),
            "teacher": teacher,
            "classroom": classroom,
            "account": loginInstance.lastAccount ?? ""
        }
        
        try {
            const response = await axios.post(url, data, this.defaultConfig())
        }
        catch (e) {
            console.log(e)
        }
    }


    static async sendStopClass() {
        const url = LocalUrls.stopClassUrl()
        try {
            const response = await axios.post(url, {}, this.defaultConfig())
        }
        catch (e) {
            console.log(e)
        }
    }


    /// 启动互动
    /// 如果是组合题需要带 questions
    static async sendStartInteract(taskId, interact, questions = []) {
        const url = LocalUrls.startInteractUrl()
        const data = {
            "taskId": taskId,
            "mode": interact,
            "questions": questions,
        }
        try {
            const response = await axios.post(url, data, this.defaultConfig())
        }
        catch (e) {
            console.log(e)
        }
    }



    static async sendStopInteract() {
        const url = LocalUrls.stopInteractUrl()
        const data = {
        }
        try {
            const response = await axios.post(url, data, this.defaultConfig())
        }
        catch (e) {
            console.log(e)
        }
    }

    static async saveDataToLocal(data) {
        const url = LocalUrls.saveDataUrl()
        try {
            const response = await axios.post(url, data, this.defaultConfig())
            return response.data
        }
        catch (e) {
            console.log(e)
        }
    }

    static async readDataFromLocal() {
        const url = LocalUrls.readDataUrl()
        try {
            const response = await axios.post(url, this.defaultConfig())
            return response.data
        }
        catch (e) {
            console.log(e)
        }
        return null
    }

    static async readConfig() {
        const url = LocalUrls.readConfigUrl()
        try {
            const response = await axios.get(url)
            return response.data
        }
        catch (e) {
            console.log(e)
        }
        return { environment: 'online' }
    }

    /// 设置遥控器登录 校验id
    static async setRemoteLoginId(remote_id) {
        const url = LocalUrls.setRemoteLoginIdUrl()
        const data = {
            "login_id": remote_id,
        }
        try {
            await axios.post(url, data, this.defaultConfig())
        }
        catch (e) {
            console.log(e)
        }
    }

    /// 想遥控器发送消息
    static async sendMessageToRemote(data) {
        const url = LocalUrls.sendMessageToRemoteUrl()
        try {
            return axios.post(url, data, this.defaultConfig())
        }
        catch (e) {
            return new Promise((resolve, reject) => {
                resolve({
                    code: -1,
                    message: '网络错误'
                })
            })
        }
    }

    static async getOssToken() {
        const url = LocalUrls.getOSSTokenUrl()
        try {
            const response = await axios.post(url, {}, this.defaultConfig())
            return response.data
        }
        catch (e) {
            console.log(e)
        }
        return null
    }

    /// 获取系统信息
    static async getSysInfo() {
        const url = LocalUrls.getSysInfoUrl()
        try {
            const response = await axios.post(url, {}, this.defaultConfig())
            return response.data
        }
        catch (e) {
            console.log(e)
        }
        return null
    }

    /// 获取本地地址
    static async getLocalIps() {
        const url = LocalUrls.getLocalIpsUrl()
        try {
            const response = await axios.post(url, {}, this.defaultConfig())
            return response.data
        }
        catch (e) {
            console.log(e)
        }
        return null
    }

    static async getLocalIpList() {
        const url = LocalUrls.getLocalIpListUrl()
        try {
            const response = await axios.post(url, {}, this.defaultConfig())
            return response.data
        }
        catch (e) {
            console.log(e)
        }
        return null
    }

    /// 更新app
    static async updateApp(downloadUrl) {
        const url = LocalUrls.updateAppUrl()
        try {
            const response = await axios.post(url, { "url": downloadUrl }, this.defaultConfig())
            return response.data
        }
        catch (e) {
            console.log(e)
        }
        return null
    }



    static async changeStudentMac(oldMac, newMac, studentId) {
        const url = LocalUrls.changeMacUrl()
        const data = {
            old_mac: oldMac,
            new_mac: newMac,
            stu_id: studentId
        }
        try {
            const response = await axios.post(url, data, this.defaultConfig())
            return response.data
        }
        catch (e) {
            return null
        }
    }

    /// 启动蓝牙网关
    static async startBleApServer() {
        const url = LocalUrls.startBleServerUrl()
        try {
            let resp = await axios.post(url, {}, this.defaultConfig())
            return resp
        }
        catch (e) {
            console.log(e)
        }
        return null
    }

    /// 停止蓝牙网关
    static async stopBleApServer() {
        const url = LocalUrls.stopBleServerUrl()
        try {
            var res = await axios.post(url, {}, this.defaultConfig())
            return res
        }
        catch (e) {
            console.log(e)
        }
        return null
    }

    /// 连接蓝牙设备
    static async connectBleDevice(mac) {
        const url = LocalUrls.connectBleDeviceUrl()
        try {
            await axios.post(url, { "mac_address": mac }, this.defaultConfig())
        }
        catch (e) {
            console.log(e)
        }
    }

    /// 断开蓝牙连接
    static async disconnectBleDevice(mac) {
        const url = LocalUrls.disconnectBleDeviceUrl()
        try {
            await axios.post(url, { "mac_address": mac }, this.defaultConfig())
        }
        catch (e) {
            console.log(e)
        }
    }
    ///连接多个蓝牙设备
    static async connectBleMultiDevice(mac) {
        const url = LocalUrls.connectBleMultiDeviceUrl()
        try {
            await axios.post(url, { "mac_addresses": mac }, this.defaultConfig())
        }
        catch (e) {
            console.log(e)
        }
    }

    static async disconnectAllBleDevice() {
        const url = LocalUrls.disconnectAllBleDeviceUrl()
        try {
            await axios.post(url, {}, this.defaultConfig())
        }
        catch (e) {
            console.log(e)
        }
    }

    static async startAutoTest() {
        const url = LocalUrls.startAutoTestUrl()
        try {
            await axios.post(url, {}, this.defaultConfig())
        }
        catch (e) {
            console.log(e)
        }
    }

    static async stopAutoTest() {
        const url = LocalUrls.stopAutoTestUrl()
        try {
            await axios.post(url, {}, this.defaultConfig())
        }
        catch (e) {
            console.log(e)
        }
    }

    //写入log
    static async writeLog(content) {
        const url = LocalUrls.writeLogUrl()
        try {
            const response = await axios.post(url, { content }, this.defaultConfig())
        }
        catch (e) {
            console.log(e)
        }
    }

    //上传本地log
    static async uploadLocalLog() {
        const url = LocalUrls.uploadLocalLogUrl()
        try {
            const response = await axios.post(url, {
                "account": loginInstance.lastAccount ?? "",
                "token": loginInstance.token
            }, this.defaultConfig())
            return response.data
        }
        catch (e) {
            console.log(e)
        }
        return null
    }

    //自动化测试
    static async autoTestControl(isOpen,time) {
        const url = LocalUrls.autoTestUrl()
        try {
            const response = await axios.post(url, {
                "is_open": isOpen,
                "time":parseInt(time),
            }, this.defaultConfig())
            return response.data
        }
        catch (e) {
            console.log(e)
        }
        return null
    }
    //上传音频
    static async uploadAudio(data) {
        const url = LocalUrls.uploadAudioUrl()
        try {
            const response = await axios.post(url, data)
            return response.data
        }
        catch (e) {
            console.log(e)
        }
        return null
    }

    static async moveMouse(x, y) {
        const url = LocalUrls.mouseEventUrl()
        try {
            await axios.post(url, { "x": x, "y": y,"event":"mousemove" },)
        }
        catch (e) {
            console.log(e)
        } 
    }
}