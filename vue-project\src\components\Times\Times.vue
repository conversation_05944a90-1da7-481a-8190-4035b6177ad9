<template>
    <div v-show="showBoard && isCounting" class="timekeeper" @click="timeKeeperClick">
        {{ timerValue }} {{ timeKeeperStore.getHourMinuteSecond() }}
    </div>
    <!-- <div v-show="showBoard && interact !== Interact.none && interactTime != ''" class="interact">
        互动 {{ interactTime }}
    </div> -->
    <div v-show="showBoard" class="clock">
        {{ clock }}
    </div>
</template>
<script setup>
import { ref, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { UIFrames } from '@/classroom/frame_enums'
import { useTimesStore } from '@/stores/times_store'
import { useInteractStore } from '@/stores/interact_store'
import { Interact } from '@/classroom/interact_enums.js'
import { useTimeKeeperStore } from '@/stores/time_keeper.js'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { useDesktopStore } from '@/stores/desktop_store'
const classroomUIStore = useClassroomUIStore()
const { showTimeKeeper } = storeToRefs(classroomUIStore)
const timeKeeperStore = useTimeKeeperStore()
const { isCounting, timerValue } = storeToRefs(timeKeeperStore)
const interactStore = useInteractStore()
const { interact } = storeToRefs(interactStore)
const timesStore = useTimesStore()
const { clock, interactTime } = storeToRefs(timesStore)
const showBoard = ref(true)
const desktopStore = useDesktopStore()
const { showDesktop, drawMode, hideToolBar } = storeToRefs(desktopStore)

watch(showDesktop, () => {
    showBoard.value = !(showDesktop.value && !drawMode.value)
    if(showBoard.value) {

    }
})

watch(drawMode, () => {
    showBoard.value = !(showDesktop.value && !drawMode.value)
})
function timeKeeperClick() {
    showTimeKeeper.value = true
}
</script>
<style lang="scss" scoped>
.timekeeper {
    position: absolute;
    z-index: 300;
    top: 10px;
    left: 50%;
    width: 160px;
    height: 40px;
    background-color: black;
    transform: translateX(-200%);
    border-radius: 20px;
    color: white;
    text-align: center;
    line-height: 40px;
    font-size: 18px;
    cursor: pointer;
}

.interact {
    position: absolute;
    z-index: 3;
    top: 10px;
    right: 50%;
    width: 120px;
    height: 40px;
    background-color: black;
    transform: translateX(250%);
    border-radius: 20px;
    color: white;
    text-align: center;
    line-height: 40px;
    font-size: 18px;
}

.clock {
    position: absolute;
    z-index: 3;
    top: 10px;
    left: 10px;
    width: 100px;
    height: 40px;
    background-color: black;
    border-radius: 20px;
    color: white;
    text-align: center;
    line-height: 40px;
    font-size: 18px;
}
</style>