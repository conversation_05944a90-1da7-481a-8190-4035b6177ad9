<template>
    <div class="network-tips">
        <div class="content box-shade">
            <div class="tips">
                当前连接多个网络，请只连接一个网络，虚拟机网络请忽略。
            </div>
            <div class="btn" @click="$emit('networkTipsClose')">确定</div>
        </div>
    </div>
</template>
<script setup>

</script>
<style lang="scss" scoped>
.network-tips {
    position: absolute;
    height: 100%;
    width: 100%;
    background-color: rgba($color: #000000, $alpha: 0);
    opacity: 1;
    z-index: 3;
    .box-shade{
        box-shadow: 0px 0px 12px rgba($color: var(--boxshaw-main-color-rgb), $alpha: 0.15);
    }

    .content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, 0);
        width: 600px;
        height: 250px;
        background-color: #F7FBFC;
        border-radius: 10px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .tips {
            font-size: 24px;
            color: #333333;
            margin-bottom: 30px;
            padding: 20px;
            line-height: 1.5;
        }

        .btn {
            width: 150px;
            height: 50px;
            line-height: 50px;
            text-align: center;
            background-color: #00A7E1;
            border-radius: 30px;
            color: #FFFFFF;
            font-size: 24px;
            cursor: pointer;
        }
    }
}
</style>