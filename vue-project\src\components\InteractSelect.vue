<template>
    <div class="interact-select" id="interact-selector" v-show="InteractMode.none !== interactMode">
        <div class="items" v-for="(item, index) in interactList" :key="index">
            <div v-if="item === Interact.none" :style="{ height: '30px' }"></div>
            <div v-else @click="didSelectInteract(item)" class="item">
                <div v-show="interactMode === InteractMode.big">{{ interactName(item) }}</div>
                <img :src="getInteractImage(item)">
            </div>
        </div>
    </div>
</template>
<script setup>
import { getInteractImage, Interact, interactName, InteractMode } from '@/classroom/interact_enums'
import { loginInstance } from '@/login_instance/login_instance'
import { ref, onBeforeMount, onMounted, onBeforeUnmount, computed } from 'vue'
import { DisplayDirection } from '@/classroom/frame_enums'
import { storeToRefs } from 'pinia'
import { LocalRequest } from '@/local_request/local_request'
import roomUpdater from '@/classroom/classroom_updater.js'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { useInteractStore } from '@/stores/interact_store'
import { useClassroomStore } from '@/stores/classroom'
import { serverHost } from '@/server_request/server_urls'
import { usePhetExpStore } from '@/stores/phet_exp_store'
import { ElLoading, ElMessage } from 'element-plus'

const phetExpStore = usePhetExpStore()
const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)

const interactStore = useInteractStore()
const { interact, interactMode } = storeToRefs(interactStore)

const classroomUIStore = useClassroomUIStore()
const { displayDirection,showPhetExpMenu, showTemplateProbability,showAIHelp } = storeToRefs(classroomUIStore)

const interactList = ref([])

function startAIHelp() {
    showAIHelp.value = true
}

function closeOtherInteract(){
    showPhetExpMenu.value = false
    showTemplateProbability.value =false
    showAIHelp.value = false
}
async function didSelectInteract(mode) {
    
    if(mode == Interact.aiHelp){
        closeOtherInteract()
        startAIHelp()
        return
    }
    if(mode == Interact.probability) {        
        closeOtherInteract()
        showTemplateProbability.value = true
        return;
    }
    if(mode == Interact.phetExp){
        closeOtherInteract()
        if(!phetExpStore.menuList.length){
            await phetExpStore.show()
            if(!phetExpStore.menuList.length){
                ElMessage.error("获取实验失败，请稍后再试")
                return
            }
        }
    }
    let loading = ElLoading.service({ background: 'transparent' })
    roomUpdater.startInteract(mode)
    loading.close()
}

const width = computed(() => {
    if(interactMode.value === InteractMode.small) {
        return '48px'
    }else {
        return '120px'
    }
})

onBeforeMount(async () => {
    interactList.value = [
        Interact.paperPen,
        Interact.classTest,
        // Interact.examMode,
        Interact.none,
        Interact.multiQuestions,
        Interact.singleChoice,
        Interact.multiChoice,
        Interact.trueFalse,
        Interact.responder,
        Interact.vote,
        
        // Interact.probability
    ]

    if (serverHost.probabilityEnable) {
        interactList.value.splice(3, 0, Interact.probability)
    }

    let res = await LocalRequest.readConfig()
    if (res.interact_all == true) {
        interactList.value.splice(2, 0, Interact.dictation)
        interactList.value.splice(3, 0, Interact.chineseWriting)
    } else {
        // 英语加上 单词听写
        if (loginInstance.subjectMainId === 3) {
            interactList.value.splice(2, 0, Interact.dictation)
        } else if (loginInstance.subjectMainId === 1) { //语文加上中文练字
            if (showChineseWriting()) {
                interactList.value.splice(2, 0, Interact.chineseWriting)
            }
        }
    }
    interactList.value.push(Interact.aiHelp)
})

onMounted(async () => {
    await phetExpStore.show()
    if(phetExpStore.menuList.length > 0||phetExpStore.errorShow) {
        interactList.value.push(Interact.phetExp)
    }
    if (window.electron) {
        const selector = document.querySelector("#interact-selector")
        selector.addEventListener('mouseenter', onMouseEnter)
        selector.addEventListener('pointerenter', onMouseEnter)
    }
})

function showChineseWriting() {
    //小于7 就是小学 大于等于7是初中高中
    return selectedClassroom.value.gid < 7 ? true : false
}

function onMouseEnter() {
    window.electron.unignoreMosue()
}

onBeforeUnmount(() => {
    const selector = document.querySelector("#interact-selector")
    if (window.electron) {
        selector.removeEventListener('mouseenter', onMouseEnter)
        selector.removeEventListener('pointerenter', onMouseEnter)
    }
})

</script>
<style lang="scss">
.interact-select{
    .el-loading-mask {
    background-color: transparent;
    width: 48px;
    height: 48px;
}

}

</style>
<style lang="scss" scoped>
.interact-select {
    position: absolute;
    left: v-bind("displayDirection === DisplayDirection.Left ? '6px' : null");
    right: v-bind("displayDirection === DisplayDirection.Left ? null : '6px'");
    // top: 50%;
    // transform: translateY(-65%);
    bottom: 150px;
    width: v-bind(width);
    display: grid;
    z-index: var(--interact-select-z-index);
    transform: scale(1.2);   /* 放大 1.2 倍 */
    transform-origin: v-bind("displayDirection !== DisplayDirection.Left ? 'right bottom' : 'left bottom'");  /* 放大时从左上角对齐，不偏移位置 */
    // background-color: yellow;

    .items {
        display: flex;
        cursor: pointer;

        .item {
            display: flex;
            flex-direction: v-bind("displayDirection === DisplayDirection.Left ? 'row-reverse' : 'row'");
            justify-content: space-between;
            align-items: center;
            width: 100%;
            height: 48px;
            margin-bottom: 10px;

            img {
                width: 48px;
                height: 48px;
            }

            div {
                font-size: 15px;
                color: var(--explanatory-text-color);
            }
        }
    }
}
</style>