import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { padZero } from '@/utils/date_time'
import { useRbpAudioStore } from '@/stores/rbp_audio_store'
import { remoteControl } from '@/remote_control/remote_control'

export const useTimeKeeperStore = defineStore('time_keeper', () => {

    /// 总计时秒数
    const totalSeconds = ref(0)

    /// 当前进行到哪一秒
    const currentSeconds = ref(0)

    const hours = ref(0)
    const minutes = ref(0)
    const seconds = ref(0)

    /// 是否在计时中
    const isCounting = ref(false)

    const timerValue = ref('倒计时')
    const options = ref([
        '正计时',
        '倒计时'
    ])

    const timeList = ref([
        {
            text: '30秒',
            value: 30,
            selected: false
        },
        {
            text: '1分钟',
            value: 60,
            selected: false
        },
        {
            text: '2分钟',
            value: 120,
            selected: false
        },
        {
            text: '3分钟',
            value: 180,
            selected: false
        },
        {
            text: '5分钟',
            value: 300,
            selected: false
        },
        {
            text: '10分钟',
            value: 600,
            selected: false
        },
        {
            text: '15分钟',
            value: 900,
            selected: false
        },
        {
            text: '30分钟',
            value: 1800,
            selected: false
        },
        {
            text: '60分钟',
            value: 3600,
            selected: false
        },
        {
            text: '90分钟',
            value: 5400,
            selected: false
        },
        {
            text: '100分钟',
            value: 6000,
            selected: false
        },
        {
            text: '120分钟',
            value: 7200,
            selected: false
        }
    ])

    const controlledSwiperHours = ref(null)
    const controlledSwiperMinutes = ref(null)
    const controlledSwiperSeconds = ref(null)

    function getHourMinuteSecond() {
        return padZero(hours.value) + ':' + padZero(minutes.value) + ':' + padZero(seconds.value)
    }

    function setTotalSecondsByHourMinuteSecond() {
        totalSeconds.value = hours.value * 3600 + minutes.value * 60 + seconds.value
    }

    function currentSecondsToHourMinuteSecond() {
        hours.value = Math.floor(currentSeconds.value / 3600)
        minutes.value = Math.floor((currentSeconds.value % 3600) / 60)
        seconds.value = currentSeconds.value % 60

        controlledSwiperHours.value.slideToLoop(hours.value)
        controlledSwiperMinutes.value.slideToLoop(minutes.value)
        controlledSwiperSeconds.value.slideToLoop(seconds.value)
    }

    const timer = ref(null)

    function closeTimer() {
        clearInterval(timer.value)
        timer.value = null
        totalSeconds.value = 0
        remoteControl.handleInteractState()
    }
    function createTimer() {
        timer.value = setInterval(() => {
            if (isCounting.value) {
                if (timerValue.value == '正计时') {
                    // 从0到totalSeconds
                    currentSeconds.value++
                    currentSecondsToHourMinuteSecond()
                    if (currentSeconds.value == totalSeconds.value) {
                        isCounting.value = false
                        playAudio()
                        closeTimer()
                    }
                } else if (timerValue.value == '倒计时') {
                    /// 从totalSeconds到0
                    currentSeconds.value--
                    currentSecondsToHourMinuteSecond()
                    if (currentSeconds.value == 0) {
                        isCounting.value = false
                        playAudio()
                        closeTimer()
                    }
                }
                
            }
        }, 1000)
    }

    function playAudio() {
        const rbpAudioStore = useRbpAudioStore()
        rbpAudioStore.play('/static/sound/time_ding.wav')
    }

    function cleanData() {
        totalSeconds.value = 0
        currentSeconds.value = 0
        hours.value = 0
        minutes.value = 0
        seconds.value = 0
        isCounting.value = false
        closeTimer()
        controlledSwiperHours.value.slideToLoop(0)
        controlledSwiperMinutes.value.slideToLoop(0)
        controlledSwiperSeconds.value.slideToLoop(0)

        const rbpAudioStore = useRbpAudioStore()
        rbpAudioStore.cleanData()
    }

    return {
        totalSeconds,
        currentSeconds,
        hours,
        minutes,
        seconds,
        isCounting,
        timerValue,
        options,
        timeList,
        controlledSwiperHours,
        controlledSwiperMinutes,
        controlledSwiperSeconds,
        timer,
        currentSecondsToHourMinuteSecond,
        cleanData,
        setTotalSecondsByHourMinuteSecond,
        getHourMinuteSecond,
        closeTimer,
        createTimer
    }
})