<template>
  <div class="root-style">
    <qrcode-vue :value="loginQrcodeStirng" :size="props.size" :background="background" :foreground="foreground" :margin="0" level="Q" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import QrcodeVue from 'qrcode.vue'
import { useQrcodeStore } from '@/stores/qrcode_store'
import { storeToRefs } from 'pinia'
import { type } from 'os'
const props = defineProps({
  size:{
    type:Number,
    default:187,
  }
})
const qrcode = useQrcodeStore()
const { loginQrcodeStirng } = storeToRefs(qrcode)

const size = ref(187)
const foreground = ref('#000000')
const background = ref('#ffffff00')
onMounted(() => {
  qrcode.setQrcode()
})
</script>

<style lang="scss" scoped>
.root-style {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 16px;
  border: 1px solid var(--border-bar-color);
  background-color: var(--main-bc-color);
}
</style>