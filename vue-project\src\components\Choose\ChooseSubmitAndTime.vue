<template>
    <div class="content">
        <div class="num">
            {{ currentSubmitCount }}
            <span>人</span>
        </div>
        <div class="illustrate">
            已提交人数
        </div>
    </div>
</template>
<script setup>
import { ref, computed } from 'vue'
import { useAnswersStore } from '@/stores/answers_store.js'
import { useTimesStore } from '@/stores/times_store'
import { storeToRefs } from 'pinia'
import { RBPColors } from '@/components/baseComponents/RBPColors.js'

const answerStore = useAnswersStore()
const timesStore = useTimesStore()
const { interactTime } = storeToRefs(timesStore)

const currentSubmitCount = computed(() => {
    return Object.keys(answerStore.studentAnswers)?.length ?? 0
})
</script>
<style lang="scss" scoped>
.content {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .num {
        color: var(--primary-color);
        font-size: 72px;

        span {
            font-size: 21px;
            color: var(--text-color);
        }
    }

    .illustrate {
        font-size: 21px;
        color: var(--text-color);
    }
}
</style>