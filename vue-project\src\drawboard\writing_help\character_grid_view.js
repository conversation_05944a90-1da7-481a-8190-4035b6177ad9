
import { BoardTool } from "../board_tool";
import { WritingGridView } from "./writing_grid_view";

// 田字格视图 - 统一版
export class CharacterGridView extends WritingGridView {

    constructor(application, pos, size, color,copyCallBack,type) {
        // 初始化父类
        super(application, pos, size,color,copyCallBack,type)
        // 竖线数量（不包括两端的边框线）
        this.verticalLines = 7        
        // 绘制田字格
        this.createGrid()
        

        
    }
   
    

    createGrid() {
        // 清除之前的线条
        BoardTool.disposeGroup(this.lineGroup)
        this.lineGroup.clear()
        
        let app = this.application?.deref()
        this.lineWidth = app.cameraInitSize.width / 100

        // 计算实际内容区域的大小（排除按钮的空间和额外间距）
        let contentWidth = this.size.width - this.buttonSize * this.spacingFactor
        let contentHeight = this.size.height
        contentWidth = contentHeight * 4
        let offsetX = -this.buttonSize * this.spacingFactor / 2
        let offsetY = 0

        // 绘制长方形外框
        // 左边
        this.createLine(
            offsetX - contentWidth/2, 
            offsetY - contentHeight/2,
            offsetX - contentWidth/2, 
            offsetY + contentHeight/2
        )
        
        // 右边
        this.createLine(
            offsetX + contentWidth/2, 
            offsetY - contentHeight/2,
            offsetX + contentWidth/2, 
            offsetY + contentHeight/2
        )
        
        // 上边
        this.createLine(
            offsetX - contentWidth/2, 
            offsetY + contentHeight/2,
            offsetX + contentWidth/2, 
            offsetY + contentHeight/2
        )
        
        // 下边
        this.createLine(
            offsetX - contentWidth/2, 
            offsetY - contentHeight/2,
            offsetX + contentWidth/2, 
            offsetY - contentHeight/2
        )
        
        // 绘制内部的7条均分竖线
        const spacing = contentWidth / (this.verticalLines + 1)
        
        for (let i = 1; i <= this.verticalLines; i++) {
            let x = offsetX - contentWidth/2 + spacing * i
            
            this.createLine(
                x,
                offsetY - contentHeight/2,
                x,
                offsetY + contentHeight/2
            )
        }
        
        // 添加竖向虚线（在每两条竖实线之间）
        const halfSpacing = spacing / 2
        const dashSize = contentWidth / 240
        
        // 左边框和第一条竖线之间
        this.createDashedLine(
            offsetX - contentWidth/2 + halfSpacing,
            offsetY - contentHeight/2,
            offsetX - contentWidth/2 + halfSpacing,
            offsetY + contentHeight/2,
            dashSize
        )
        
        // 内部竖线之间
        for (let i = 1; i < this.verticalLines; i++) {
            let x = offsetX - contentWidth/2 + spacing * i + halfSpacing
            
            this.createDashedLine(
                x,
                offsetY - contentHeight/2,
                x,
                offsetY + contentHeight/2,
                dashSize
            )
        }
        
        // 最后一条竖线和右边框之间
        this.createDashedLine(
            offsetX - contentWidth/2 + spacing * this.verticalLines + halfSpacing,
            offsetY - contentHeight/2,
            offsetX - contentWidth/2 + spacing * this.verticalLines + halfSpacing,
            offsetY + contentHeight/2,
            dashSize
        )
        
        // 计算横线之间的间距（将高度分为4等份）
        const horizontalSpacing = contentHeight / 4
        
        // 绘制中间的一条横线（实线），与上下两边距离相等
        this.createLine(
            offsetX - contentWidth/2,
            offsetY,
            offsetX + contentWidth/2,
            offsetY
        )
        
        // 绘制上方的虚线，位于中间横线和上边框之间的中点
        this.createDashedLine(
            offsetX - contentWidth/2,
            offsetY + horizontalSpacing,
            offsetX + contentWidth/2,
            offsetY + horizontalSpacing,
            contentWidth / 240
        )
        
        // 绘制下方的虚线，位于中间横线和下边框之间的中点
        this.createDashedLine(
            offsetX - contentWidth/2,
            offsetY - horizontalSpacing,
            offsetX + contentWidth/2,
            offsetY - horizontalSpacing,
            contentWidth / 240
        )
    }
}
