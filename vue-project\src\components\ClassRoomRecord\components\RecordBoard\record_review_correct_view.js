import { BoardView } from "@/drawboard/board_view";
import * as THREE from 'three';
import { PainterOrder } from "@/drawboard/draw_enums";
import { BoardLabel } from "@/drawboard/board_label";
import { BoardImageView } from "@/drawboard/board_image_view";
import { useRecordDrawboardStore } from "@/stores/record_drawboard_store";
import { BoardButton } from "@/drawboard/board_button";

export class RecordReviewCorrectView extends BoardView {
    //单独绘制批改内容 因为需要点击批改传递数据
    constructor(application, pos, size, area,  width,studentId) {
        super(application, pos, size)
        this.area = area
        this.studentId = studentId
        this.width = width
        this.setupUI(width)
    }


    setupUI(width) {
        let recordDrawBoardStore = useRecordDrawboardStore()
        //添加批改结果
        let correctS = width
        let correctX = this.size.width / 2 - correctS / 2
        let correctY = this.size.height / 2 - correctS / 2
        let area = this.area
        let correctView
        if (area.isHandle == 0 || (recordDrawBoardStore.statisticType && area.questionType == 1&&area.isHandle == 1)) {
            let buttonText = area.isHandle == 0 ? "未批":`${area.score}分` 
            correctView = new BoardButton(
                this.application,
                new THREE.Vector3(correctX - correctS / 2, correctY, 0),
                {
                    width:correctS * 2,height:correctS
                }
            )
            // let nameLabel = new BoardLabel(
            //     this.application,
            //     new THREE.Vector3(correctX, correctY, 0),
            //     this.size,
            //     area.questionType == 1 ? `${area.score}分` : "未批",
            //     { fontSize: 0.012, color: 0xff0000, align: 'center' })
            //     nameLabel.onTouch = true
            correctView.setText(buttonText)
            correctView.setFontSize(0.012)
            correctView.setTextColor(0xFF00000)
            correctView.setTextAlign('center')
            correctView.renderOrder = PainterOrder.customDisplay
            
        } else {
            correctView = new BoardImageView(
                this.application,
                new THREE.Vector3(correctX, correctY, 0),
                { width: correctS, height: correctS },
                true
            )
            correctView.setImageUrl(`/img/record/${area.markPoint == 0 ? 'correct_wrong' : area.markPoint == 1 ? 'correct_right' : 'correct_right1'}.png`)
            correctView.renderOrder = PainterOrder.customDisplay
        }
        correctView.onTouchUp = ()=>{
            this.clickCorrect()
        }
        this.correctView = correctView
        this.addSubView(correctView)


    }

    clickCorrect (){
        const recordDrawBoard = useRecordDrawboardStore()
        this.area.popover = true
        this.area.studentId = this.studentId
        this.area.finish = (newArea)=>{
            this.area = newArea
            if(this.correctView){
                this.removeSubView(this.correctView)
            }
            this.setupUI(this.width)
            this.animate()
        }
        recordDrawBoard.correctArea = this.area
    }



    dispose() {
        this.area = null
        this.clickCorrect = null
        super.dispose()
    }
}