<template>
    <div class="cloud-video-play-view">
        <video ref="videoRef" class="cloud-disk-video-show" :src="fileUrl" controls playsinline autoplay controlsList="nodownload"
            disablePictureInPicture></video>
        <div class="back-content">
            <span class="file-name">{{ displayName }}</span>
            <div class="back-area" @click="showVideoPlayView = false">
                <img class="back-icon" src="@/assets/img/quit2.png" alt="">
                <span class="back-title">退出</span>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useVideoPlayStore } from '@/stores/video_play_store'
import { storeToRefs } from 'pinia'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'

const classroomUIStore = useClassroomUIStore()
const { showVideoPlayView } = storeToRefs(classroomUIStore)
const videoPlayStore = useVideoPlayStore()
const { fileUrl, displayName } = storeToRefs(videoPlayStore)
const videoRef =  ref(null)

watch(fileUrl,()=>{
    if(videoRef.value){
        videoRef.value.currentTime = 0
    }
})
</script>

<style lang="scss" scoped>
video::-webkit-media-controls-fullscreen-button {
    display: none;
}

.cloud-video-play-view {
    width: 100vw;
    height: 100vh;
    background-color: #000;
    position: relative;

    .cloud-disk-video-show {
        width: 100%;
        height: calc(100% - 64px);
        object-fit: contain;
    }

    .back-content {
        display: flex;
        height: 40px;
        background-color: #000;
        align-items: center;

        justify-content: space-between;
        padding: 0px 16px;
        padding-bottom: 24px;

        .file-name {
            font-size: 16px;
            color: #fff;
        }

        .back-area {
            // width: 24px;
            height: 24px;
            padding: 8px 16px;
            background-color: rgba($color: #fff, $alpha: 0.6);
            border-radius: 20px;
            display: flex;
            align-items: center;
            cursor: pointer;

            .back-icon {
                height: 16px;
                object-fit: contain;
                margin-right: 8px;

            }

            .back-title {
                font-size: 14px;
                color: #2e4a66;
                // color: rgba($color: #fff, $alpha: 0.6);
            }
        }

        // .back-area:hover {
        //     background-color: rgba($color: #fff, $alpha: 0.2);
        // }
    }
}
</style>