/**
水平进度条
*/
<template>
    <div class="rbp-horizontal-progress-bar">
        <div class="title">
            {{ props.title }}
        </div>
        <div class="barchat">
            <div class="sub" :class="{ subSelected: props.selected }" :style="{ width: props.accuracy }">
            </div>
        </div>
        <div class="percentage">
            {{ props.accuracy }}
        </div>
    </div>
</template>
<script setup>
import { defineProps } from 'vue'
import { RBPColors } from '@/components/baseComponents/RBPColors.js'

const props = defineProps({
    title: String,
    accuracy: String,
    selected: <PERSON><PERSON><PERSON>
})
</script>
<style lang="scss" scoped>

.rbp-horizontal-progress-bar {
    display: flex;
    margin-right: 20px;
    margin-bottom: 2%;
    height: 18px;

    .title {
        width: 60px;
        font-size: 15px;
        color: var(--progress-bar-explanatory-text-color);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .barchat {
        margin-left: 10px;
        margin-right: 10px;
        height: 100%;
        background-color: var(--progress-bar-bg-color);
        flex: 1;
        border-radius: 15px;
        cursor: pointer;

        .sub {
            height: 100%;
            width: 50%;
            background-color: var(--primary-color);
            border-radius: 15px;
        }

        .subSelected {
            background-color: var(--correct-color);
            // color: white;
            border-radius: 15px;
        }
    }

    .percentage {
        width: 50px;
        text-align: end;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-size: 15px;
        color: var(--progress-bar-explanatory-text-color);
    }
}
</style>