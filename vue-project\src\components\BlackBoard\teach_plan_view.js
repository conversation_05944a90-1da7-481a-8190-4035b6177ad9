import { BoardImageView } from "@/drawboard/board_image_view";
import { BoardView } from "@/drawboard/board_view";
import { PainterOrder } from "@/drawboard/draw_enums";
import * as THREE from 'three';


export class TeachPlanView extends BoardView {

    constructor(application, pos, size, teachPlan) {
        super(application, pos, size);

        this.teachPlan = teachPlan

        this.pageRatio = 210.0 / 297.0

        this.renderOrder = PainterOrder.background

        this.imagesMap = new Map()

        this.displayPageImages()
    }

    displayPageImages() {
        const space = 0.01
        let col = 5
        if (this.teachPlan.pages.length < col) {
            col = this.teachPlan.pages.length
            if (col < 3) {
                col = 3
            }
        }
        let size = this.size
        const itemWidth = (size.width - space * (col - 1)) / col
        const itemHeight = itemWidth / this.pageRatio

        this.teachPlan.pages.forEach((page, i) => {
            // 计算 Group 的位置，使第一个 Group 的左上角位于屏幕 (0, 0) 位置
            var currentRow = Math.floor(i / col);
            var currentCol = i % col;

            const groupX = (currentCol * (itemWidth + space)) - (size.width / 2) + (itemWidth / 2)
            const groupY = -(currentRow * (itemHeight + space)) + (size.height / 2) - (itemHeight / 2)
            let imageView = new BoardImageView(this.application, new THREE.Vector3(groupX, groupY, 0), { width: itemWidth, height: itemHeight }, false)
            imageView.setImageUrl(page.recogImgurl)
            this.imagesMap.set(page.pageId, imageView)
            this.addSubView(imageView)
        })
    }

    dispose() {
        this.imagesMap.clear()
        super.dispose()
    }
}