<template>
    <RBPAlert :zIndex="getZIndex('--toolbar-top-score-statistics-z-index')" width="80%" @close="close" @click.stop>
        <template v-slot:rbpDiv>
            <div class="modal-content">
                <div class="modal-head">
                    <div class="title" v-for="(item, index) in optionsList" :key="index" @click="clickSort(item.key)">
                        <div>{{ item.key == 'name' ? (type == 2 ? '姓名' : '小组名称') : item.title }}</div>
                        <div v-if="item.key != 'option'" class="sort-icons">
                            <img class="sort-icon"
                                :src="sortItem.sort == -1 && sortItem.key == item.key ? 'icon/sort_uped.svg' : 'icon/sort_up.svg'"
                                alt="">
                            <img class="sort-icon"
                                :src="sortItem.sort == 1 && sortItem.key == item.key ? 'icon/sort_downed.svg' : 'icon/sort_down.svg'"
                                alt="">
                        </div>
                    </div>
                </div>
                <div class="modal-body">
                    <RBPTableScroll :list="(type == 2 ? studentList : groupList)" v-if="!isNoData">
                        <template v-slot:rbpDiv="slotProps">
                            <div class="row">
                                <div class="col">
                                    {{ slotProps.data.index + 1 }}
                                </div>
                                <div class="col">
                                    {{ slotProps.data.statisticName.split('(')[0] }}
                                </div>
                                <div class="col">
                                    {{ slotProps.data.total }}
                                </div>
                                <div class="col">
                                    {{ slotProps.data.lessonTotal }}
                                </div>
                                <div class="col">
                                    {{ slotProps.data.dailyTotal }}
                                </div>
                                <div class="col">
                                    {{ slotProps.data.weeklyTotal }}
                                </div>
                                <div class="col">
                                    {{ slotProps.data.monthlyTotal }}
                                </div>
                                <div class="col">
                                    {{ slotProps.data.semesterTotal }}
                                </div>
                                <div class="mx" @click="mxClick(slotProps.data)">明细</div>
                            </div>
                        </template>
                    </RBPTableScroll>
                    <RBPNoData v-if="isNoData">
                        <template #rbpTitle>
                            <span>暂无数据</span>
                        </template>
                    </RBPNoData>
                </div>
            </div>
        </template>
        <template #rbpBtns>
            <div class="btns">
                <RBPSegButton :options="options" :currentValue="currentValue" @updateCurrentValue="updateCurrentValue">
                </RBPSegButton>
            </div>
        </template>
    </RBPAlert>
</template>
<script setup>
import { ref, computed, onMounted, getCurrentInstance, watch, markRaw } from 'vue'
import { useScoreStatisticsStore } from '@/stores/score_statistics_store'
import { storeToRefs } from 'pinia'
import { useClassroomStore } from '@/stores/classroom'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { ElLoading, ElMessage } from 'element-plus'
import { Alert } from '@/utils/alert'
import { ClassRoomRequest } from '@/server_request/classroom_request'
import RBPAlert from '@/components/baseComponents/RBPAlert.vue'
import { RBPColors } from '@/components/baseComponents/RBPColors.js'
import RBPSegButton from '@/components/baseComponents/RBPSegButton.vue'
import RBPTableScroll from '@/components/baseComponents/RBPTableScroll.vue'
import RBPNoData from '@/components/baseComponents/RBPNoData.vue'
import { getZIndex } from '@/components/baseComponents/RBPZIndex'
import { remoteControl } from '@/remote_control/remote_control'
import utils from '@/utils/utils.js'
// const currentValue = ref('学生明细')
// const options = ref([
//     '学生明细',
//     '小组明细'
// ])
const studentList = ref([])
const groupList = ref([])
let oriStudents = []
let oriGroups = []
const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)
const classroomUIStore = useClassroomUIStore()
const { mainContentTopSpace, showScoreStatistics, showScoreStatisticsDetail } = storeToRefs(classroomUIStore)
const scoreStatisticsStore = useScoreStatisticsStore()
const { type, statisticId, title, currentValue, options,remoteFlag } = storeToRefs(scoreStatisticsStore)
const calcTop = computed(() => {
    return `calc(50% + ${mainContentTopSpace.value}px)`
})

const sortItem = ref({
    key: null,
    sort: 0
})
watch(remoteFlag, () => {
    sortItem.value = scoreStatisticsStore.sort;
    
    doSort(sortItem.value.key)
})

watch(type, () => {    
    loadData(1)
    sortItem.value = {
        key:null,
        sort: 0
    }     
})



const isNoData = computed(() => {
    return (type.value == 2 ? studentList.value : groupList.value).length == 0
})
function typeClick(t) {
    type.value = t
    loadData(1)
}
function updateCurrentValue(item) {
    currentValue.value = item
    if (item === '学生明细') {
        typeClick(2)
    } else if (item === '小组明细') {
        typeClick(1)
    }
    sortItem.value = {
        key:null,
        sort:-1,
    }
    doSort()
    remoteControl.handleInteractState()
}

const optionsList = [{
    title: '排名',
    sort: null, //1 上 0下
    key: 'rank'
}, {
    title: '姓名',
    sort: null,
    key: 'name'
}, {
    title: '总分',
    sort: null, //1 上 0下
    key: 'scores'
}, {
    title: '本节课',
    sort: null, //1 上 0下
    key: 'course'
}, {
    title: '本日',
    sort: null, //1 上 0下
    key: 'today'
}, {
    title: '本周',
    sort: null, //1 上 0下
    key: 'week'
}, {
    title: '本月',
    sort: null, //1 上 0下
    key: 'month'
}, {
    title: '本学期',
    sort: null, //1 上 0下
    key: 'semester'
}, {
    title: '操作',
    sort: null, //1 上 0下
    key: 'option'
}]
onMounted(async () => {
    loadData(1)
})
async function loadData(pageNo) {
    let loading = ElLoading.service({ background: 'transparent' })
    const res = await ClassRoomRequest.scoreStatistics(type.value, pageNo)
    loading.close()
    if (res.code == 1) {
        if (type.value == 2) {
            oriStudents = []
            studentList.value = res.data.list
            let i = 0
            for (let data of studentList.value) {
                data.index = i
                oriStudents.push(data)
                i++
            }
        } else {
            oriGroups = []
            groupList.value = res.data.list
            let i = 0

            for (let data of groupList.value) {
                data.index = i

                oriGroups.push(data)
                i++
            }
        }
    }
}
function close() {
    showScoreStatistics.value = false
    remoteControl.handleInteractState()
}
function mxClick(student) {
    title.value = student.statisticName.split('(')[0]
    statisticId.value = student.statisticId
    showScoreStatisticsDetail.value = true
    remoteControl.handleInteractState()
}

function clickSort(key) {
    if (sortItem.value.key == key) {
        sortItem.value.sort++
        if (sortItem.value.sort > 1) {
            sortItem.value.sort = -1
        }
    } else {
        sortItem.value.key = key
        sortItem.value.sort = 1
    }
    scoreStatisticsStore.sort = sortItem.value
    doSort(key)
    remoteControl.handleInteractState()
}


function doSort(key) {
    scoreStatisticsStore.sort = sortItem.value
    if(key == 'option'){
        return
    }
    let tempList = type.value == 2 ? oriStudents : oriGroups
    let list = []
    for(let data of tempList){
        list.push(data)
    }
    if (sortItem.value.sort == 0) {

    } else {
        let data = []
        switch (key) {

            
            case 'rank':

                data = list.sort((a, b) => {

                    return sortItem.value.sort == 1
                    ? b.index - a.index
                    : a.index - b.index
                })
                
                break;
            case 'name':
                data = list.sort((a, b) => {
                    const nameA = a.statisticName.split('(')[0].trim();
                    const nameB = b.statisticName.split('(')[0].trim();
                    return sortItem.value.sort == 1
                        ?  utils.naturalCompare(nameB, nameA)
                        : utils.naturalCompare(nameA, nameB);
                });
                break;
            case 'scores':
                data = list.sort((a, b) => sortItem.value.sort == 1
                    ? b.total - a.total
                    : a.total - b.total)

                break;
            case 'course':
                data = list.sort((a, b) => sortItem.value.sort == 1
                    ? b.lessonTotal - a.lessonTotal
                    : a.lessonTotal - b.lessonTotal)
                break;
            case 'today':
                data = list.sort((a, b) => sortItem.value.sort == 1
                    ? b.dailyTotal - a.dailyTotal
                    : a.dailyTotal - b.dailyTotal)
                break;
            case 'week':
                data= list.sort((a, b) => sortItem.value.sort == 1
                    ? b.weeklyTotal - a.weeklyTotal
                    : a.weeklyTotal - b.weeklyTotal)
                break;
            case 'month':
                data = list.sort((a, b) => sortItem.value.sort == 1
                    ? b.monthlyTotal - a.monthlyTotal
                    : a.monthlyTotal - b.monthlyTotal)
                break;
            case 'semester':
                data = list.sort((a, b) => sortItem.value.sort == 1
                    ? b.semesterTotal - a.semesterTotal
                    : a.semesterTotal - b.semesterTotal)
                break;
        }
        list = data

    }

    type.value == 2 ? studentList.value = list : groupList.value = list

}



</script>
<style lang="scss" scoped>
.btns {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 20px;
    margin-bottom: 20px;
}

.modal-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .modal-head {
        height: 72px;
        border-radius: 26px 26px 0px 0px;
        background-color: var(--secondary-color);
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .title {
            font-size: 30px;
            color: var(--text-color);
            width: calc(100% / 9);
            text-align: center;
            display: flex;
            align-items: center;
            cursor: pointer;
            justify-content: center;

            .sort-icons {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .sort-icon {
                    height: 16px;
                    width: 16px;
                    padding: 0px 4px;
                }
            }
        }
    }

    .modal-body {
        flex: 1;
        overflow-y: auto;
        position: relative;
    }
}

.row {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 0;

    .col {
        font-size: 27px;
        color: var(--secondary-text-color);
        width: calc(100% / 9);
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .mx {
        font-size: 27px;
        color: var(--primary-color);
        width: calc(100% / 9);
        cursor: pointer;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>