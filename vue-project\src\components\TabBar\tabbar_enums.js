import { useBlackBoardStore } from '@/stores/black_board_store'

export const TabBarItem = {
    systemSetting: 0,
    classRecords: 1,
    beikeSource: 2,
    homeworkCalendar: 3,
    teachPlan: 4,
    studentsScoreRate: 5,
    colorSelect: 6,
    canvasOptionArrow: 7,
    canvasOptionPen: 8,
    canvasOptionEraser: 9,
    randomRoll: 10,
    studentList: 11,
    timeKeeping: 12,
    desktop: 13,
    halfscreen: 14,
    cleanScreen: 15,
    bluetooth: 16,
    undoBrush: 17,
    sidebar: 18,
    phetExp: 19,
    preview: 20,
    restoreView: 21,
    plane2D: 22,
    aiHelp:23,
    writingGrids:24,
    mathCalculator:25,
}

export const clearType = {
    line:{
        name:'笔迹',
        key:1
    },
    image:{
        name:'图片',
        key:2
    },
    shape:{
        name:'图形',
        key:3
    },
    grid:{
        name:'线格',
        key:4
    },
    teachPlan:{
        name:'导学案',
        key:5
    },
    all:{
        name:'全部',
        key:6
    },

}

export function tabbarName(item, selected = false) {
    switch (item) {
        case TabBarItem.systemSetting:
            return "系统"
        case TabBarItem.classRecords:
            return "随堂记录"
        case TabBarItem.beikeSource:
            return "我的云盘"
        case TabBarItem.homeworkCalendar:
            return "作业讲评"
        case TabBarItem.teachPlan:
            return "导学案"
        case TabBarItem.studentsScoreRate:
            return "得分统计"
        case TabBarItem.colorSelect:
            return "颜色"
        case TabBarItem.canvasOptionArrow:
            return "选择"
        case TabBarItem.canvasOptionPen:
            return "画笔"
        case TabBarItem.canvasOptionEraser:
            return "橡皮"
        case TabBarItem.randomRoll:
            return "随机点名"
        case TabBarItem.studentList:
            {
                if (selected) {
                    return "收起学生"
                } else {
                    return "展开学生"
                }
            }
        case TabBarItem.timeKeeping:
            return "计时"
        case TabBarItem.desktop:
            return "桌面"
        case TabBarItem.halfscreen:
            {
                if (selected) {
                    return "还原屏幕"
                } else {
                    return "下拉屏幕"
                }
            }
        case TabBarItem.cleanScreen:
            {
                return "清屏"
            }
        case TabBarItem.plane2D: {
            return "图形"
        }
        case TabBarItem.bluetooth:
            return "蓝牙模式"
        case TabBarItem.preview:
            {
                if (selected) {
                    return "取消预览"
                } else {
                    return "预览视图"
                }
            }
        case TabBarItem.undoBrush:
            return "撤销画笔"
        case TabBarItem.sidebar:
            return "侧边栏"
        case TabBarItem.phetExp:
            return "实验室"
        case TabBarItem.restoreView:
            return "还原视图"
        case TabBarItem.aiHelp:
            return "智能助教"
        case TabBarItem.writingGrids:
            return "线格"
        case TabBarItem.mathCalculator:
            return "方程图形"
    }
}

export function tabbarImage(item, selected = false) {
    const blackBoardStore = useBlackBoardStore()
    switch (item) {
        case TabBarItem.systemSetting:
            {
                return "/img/svg/icon_tab_systemSetting.svg"
            }

        case TabBarItem.classRecords:
            {
                return "/img/svg/icon_tab_classRecords.svg"
            }

        case TabBarItem.beikeSource:
            {
                return "/img/svg/icon_tab_beikeSource.svg"
            }
        case TabBarItem.homeworkCalendar:
            {
                return "/img/svg/icon_tab_homeworkCalendar.svg"
            }
        case TabBarItem.teachPlan:
            {
                return "/img/svg/icon_tab_teachPlan.svg"
            }
        case TabBarItem.studentsScoreRate:
            {
                return "/img/svg/icon_tab_studentsScoreRate.svg"
            }
        case TabBarItem.colorSelect:
            return ""
        case TabBarItem.canvasOptionArrow:
            return "/img/svg/icon_tab_canvasOptionArrow.svg"
        case TabBarItem.canvasOptionPen:
            return "/img/svg/icon_tab_canvasOptionPen.svg"
        case TabBarItem.canvasOptionEraser:
            return "/img/svg/icon_tab_canvasOptionEraser.svg"
        case TabBarItem.plane2D:
            return "/img/svg/icon_tab_2d_plane.svg"
        case TabBarItem.randomRoll:
            {
                return "/img/svg/icon_tab_randomRoll.svg"
            }
        case TabBarItem.studentList:
            {
                if (selected) {
                    return "/img/svg/icon_tab_studentList.svg"
                } else {
                    return "/img/svg/icon_tab_studentList_s.svg"
                }
            }
        case TabBarItem.timeKeeping:
            {
                return "/img/svg/icon_tab_timeKeeping.svg"
            }
        case TabBarItem.desktop:
            {
                return "/img/svg/icon_tab_desktop.svg"
            }
        case TabBarItem.halfscreen:
            {
                if (selected) {
                    return "/img/svg/icon_tab_halfscreen.svg"
                } else {
                    return "/img/svg/icon_tab_halfscreen_s.svg"
                }
            }
        case TabBarItem.bluetooth:
            return "/img/icon_bluetooth_mode.png"
        case TabBarItem.cleanScreen:
            return "/img/svg/icon_tab_cleanScreen.svg"
        case TabBarItem.preview:
            {
                if (selected) {
                    return "/img/svg/icon_tab_preview_s.svg"
                } else {
                    return "/img/svg/icon_tab_preview.svg"
                }
            }
        case TabBarItem.undoBrush:
            return "/img/svg/icon_tab_undoBrush.svg"
        case TabBarItem.sidebar:
            return "/img/svg/icon_tab_sidebar.svg"
        case TabBarItem.phetExp:
            return "/img/svg/icon_tab_sidebar.svg"
        case TabBarItem.restoreView:
            return "/img/svg/icon_tab_preview.svg"
        case TabBarItem.aiHelp:
            return "/img/svg/icon_tab_ai.svg"
        case TabBarItem.writingGrids:
            return "/img/svg/icon_tab_writing_grids.svg"
        case TabBarItem.mathCalculator:
            return "/img/svg/icon_tab_math_calculator.svg"
    }
}