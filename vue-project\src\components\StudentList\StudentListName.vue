<template>
    <div class="studentlastname">
        <div class="mainContainer">
            <div class="group-grid">
                <StudentGridItem :student="stu" v-for="(stu, i) in lastList" :key="i"></StudentGridItem>
            </div>
        </div>
        <div class="groups">
            <div class="group-btn" :class="{ groupBtnSel: item.selected }" v-for="(item, i) in lastnameList" :key="i"
                @click="selectGroup(item)">
                {{ item.name }}
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref, onMounted, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useClassroomStore } from '@/stores/classroom.js'
import { StuStatus, getStuStatus } from '@/classroom/interact_enums.js'
import StudentGridItem from '@/components/StudentList/StudentGridItem.vue'
import { RBPColors } from '@/components/baseComponents/RBPColors.js'
import { useStudentListStore } from '@/stores/student_list_store'
import { remoteControl } from '@/remote_control/remote_control'

const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)
const studentListStore = useStudentListStore()
const {lastnameFlag} = storeToRefs(studentListStore);

const lastnameStudentMap = ref({})
const lastnameList = ref([])
const lastList = ref([])
watch(lastnameFlag, (val) => {
    lastnameList.value.forEach(e=>{
        if(e.name === lastnameFlag.value&&!e.selected){
            selectGroup(e)
        }
    })
})
onMounted(() => {
    lastnameStudentMap.value = selectedClassroom.value.lastnameStudentMap
    lastList.value = lastnameStudentMap.value['全部']

    let group = Object.keys(lastnameStudentMap.value)
    group.sort((a, b) => {
        if (a === '全部') {
            return -1;
        } else if (b === '全部') {
            return 1;
        } else {
            return a.localeCompare(b)
        }
    })
    group.forEach(g => {
        let selected = false
        if (g == '全部') {
            selected = true
        }
        lastnameList.value.push({ name: g, selected: selected })
    })
})
function selectGroup(item) {
    lastnameFlag.value = item.name
    if (!item.selected) {
        lastnameList.value.forEach(g => {
            g.selected = false
        })
        item.selected = true

        lastList.value = lastnameStudentMap.value[item.name]
    }
    remoteControl.handleInteractState()
}
</script>
<style lang="scss" scoped>
@import "@/assets/scss/mixin.scss";

.studentlastname {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .mainContainer {
        border-radius: 26px;
        border: 1px solid var(--border-bar-color);
        box-sizing: border-box;
        padding: 20px;
        width: 100%;
        flex: 1;
        overflow-y: auto;

        .group-grid {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(10, 1fr);
        }
    }

    .groups {
        width: 100%;
        height: 90px;
        display: flex;
        align-items: center;
        white-space: nowrap;
        overflow-x: auto;

        .group-btn {
            height: 50px;
            line-height: 50px;
            text-align: center;
            font-size: 18px;
            padding: 0 28px;
            margin-right: 17px;
            margin-left: 17px;
            border-radius: 15px;
            color: var(--text-color);
            border: 1px solid var(--border-bar-color);
            cursor: pointer;
        }

        .groupBtnSel {
            background-color: var(--primary-color);
            color: var(--anti-text-color);
        }
    }
}
</style>