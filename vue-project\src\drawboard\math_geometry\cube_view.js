import { BoardButton } from "../board_button";
import { BoardView } from "../board_view";
import { DrawZ, PainterOrder } from "../draw_enums";
import * as THREE from 'three';

import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial.js';
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry.js';
import { Line2 } from 'three/examples/jsm/lines/Line2.js';
import { BoardImageView } from "../board_image_view";
import { useBlackBoardStore } from "@/stores/black_board_store";

const MoveTag = {
    none: -1,
    move: 1,
}

// 立方体
export class CubeView extends BoardButton {
    constructor(type,application, pos, size, color, emissive = true) {
        super(application, pos, size)

        this.color = color

        this.renderOrder = PainterOrder.customDisplay

        this.onEdit = false
        this.type = type
        this.emissive = emissive

        // 创建立方体
        const geometry = new THREE.BoxGeometry(this.size.width, this.size.height, this.size.z);

        let material
        if (emissive) {
            material = new THREE.MeshBasicMaterial({ color: color });
        }
        else {
            material = new THREE.MeshPhongMaterial({
                color: color, // 基础颜色
                emissive: 0x000000, // 确保材质不发光
                specular: 0x000000, // 关闭镜面反射
                shininess: 0, // 关闭高光
            });
        }

        let cube = new THREE.Mesh(geometry, material);

        this.cube = cube

        let cubeGroup = new BoardView(
            this.application,
            new THREE.Vector3(0, 0, - Math.max(size.width, size.height, size.z)),
            { width: this.size.width, height: this.size.height })

        cubeGroup.touchEnable = false
        this.viewGroup = cubeGroup
        cubeGroup.renderOrder = PainterOrder.customDisplay
        cubeGroup.rotation.x = 0.265
        cubeGroup.rotation.y = -1.0
        this.addSubView(cubeGroup);

        this.viewGroup.add(cube);

        let rootView = application.deref().rootView
        rootView.addDrawInSubView(this)

        if (emissive) {
            // 创建 LineMaterial 并设置颜色
            this.lineMaterial = new LineMaterial({
                color: this.getDrawLineColor(), //
                linewidth: 2,    // 线条宽度
                vertexColors: false // 不使用顶点颜色
            });
            let x = this.size.width / 2
            let y = this.size.height / 2
            let z = this.size.z / 2
            let topPoints = [{ x: -x, y: -y, z }, { x: -x, y: y, z }, { x, y, z }, { x, y: -y, z }, { x: -x, y: -y, z }]

            let bottomPoints = [{ x: -x, y: -y, z: -z }, { x: -x, y: y, z: -z }, { x, y, z: -z }, { x, y: -y, z: -z }, { x: -x, y: -y, z: -z }]

            this.createLine(topPoints)

            this.createLine(bottomPoints)

            for (let i = 0; i < 4; i++) {
                this.createLine([topPoints[i], bottomPoints[i]])
            }
        }

        this.onClick(() => {
            const edit = !this.onEdit
            this.setOnEdit(edit)
            if (edit) {
                this.superView.deref().cancelEditWithout(this)
            }
        })

        this.setupToolViews()
    }

    getDrawLineColor() {
        // 耦合代码 获取黑板颜色
        const blackBoardStore = useBlackBoardStore()
        let color = blackBoardStore.color
        if (color === "#ffffff" || color === "#000000") {
            return 0x008080
        }
        return (this.color === 0xffffff ? 0x000000 : 0xffffff)
    }

    createLine(points) {
        const geometry = new LineGeometry();
        let vectors = [];
        points.forEach(point => {
            vectors.push(point.x, point.y, point.z);
        });
        geometry.setPositions(vectors);
        const line = new Line2(geometry, this.lineMaterial);
        line.computeLineDistances(); // 计算线段的距离（用于优化渲染）
        this.viewGroup.add(line);
    }

    setupToolViews() {
        let app = this.application?.deref()
        let height = app.cameraInitSize.width / 40
        let itemHeight = height * 0.8
        let itemWidth = itemHeight
        // let width = itemHeight * 3.5
        let width = itemHeight * 3.5
        let space = itemHeight * 1.5 / 3

        let startX = - width * 0.5

        this.toolWidth = width
        this.toolHeight = height

        let toolView = new BoardView(
            this.application,
            new THREE.Vector3(this.size.width / 2 + app.cameraInitSize.width / 80, - this.size.height / 2, 0),
            { width, height })
        let deleteButton = new BoardButton(
            this.application,
            new THREE.Vector3(startX + space + itemWidth / 2, 0, 0),
            { width: itemWidth, height: itemHeight }, true)
        deleteButton.setImage('img/math/delete.svg')
        deleteButton.onClick(() => {
            app.rootView.removeDrawInSubView(this)
            this.removeFromSuperView()
            this.dispose()
        })
        toolView.addSubView(deleteButton)


        let moveImageView = new BoardImageView(
            this.application,
            new THREE.Vector3(startX + space * 2 + itemWidth * (2 - 0.5), 0, 0),
            { width: itemWidth, height: itemHeight }, true)
        moveImageView.setImageUrl('img/plane3d/move.svg')
        moveImageView.setRenderOrder(PainterOrder.customDisplay + 1)

        this.moveImageView = moveImageView
        toolView.addSubView(moveImageView)

        toolView.visible = false
        toolView.setRenderOrder(PainterOrder.customDisplay + 1)
        this.addSubView(toolView)
        this.toolView = toolView
    }


    setOnEdit(onEdit) {
        this.onEdit = onEdit
        this.draggable = this.onEdit
        this.toolView.visible = this.onEdit
        this.moveImageView.draggable = this.onEdit
        if (onEdit) {
            this.superView.deref().bringSubViewToFront(this)
        }
    }


    onTouchDown(point) {
        let view = super.onTouchDown(point)
        if (!this.onEdit) {
            return view
        }
        let cvtPoint = this.convertPoint(point, DrawZ.objcZ)
        this.touchDownPoint = cvtPoint
        if (view === this.moveImageView) {
            this.moveTag = MoveTag.move
        }
        return view
    }

    add(obj) {
        if (obj.penStyle !== undefined) {
            // 记录物体的初始局部坐标（相对于 cubeGroup）
            obj.initLocalPos = obj.position.clone().sub(this.viewGroup.position);
            // 记录 cubeGroup 的初始旋转状态
            obj.initRotation = this.viewGroup.rotation.clone();
        }
        super.add(obj);
    }


    onTouchMove(point) {
        if (!this.onEdit || !this.touchDownPoint) {
            return super.onTouchMove(point);
        }

        let divPoint = point;
        let cvtPoint = this.convertPoint(point, DrawZ.objcZ);
        let spaceX = cvtPoint.x - this.touchDownPoint.x;
        let spaceY = cvtPoint.y - this.touchDownPoint.y;
        this.touchDownPoint = cvtPoint;

        if (this.moveTag === MoveTag.move) {
            let x = this.position.x + spaceX;
            let y = this.position.y + spaceY;
            this.position.set(x, y, this.position.z);
            return super.onTouchMove(point);
        }

        let ratioX = spaceX / this.size.width;
        let ratioY = spaceY / this.size.height;

        // 旋转 cubeGroup
        this.viewGroup.rotation.x -= ratioY;
        this.viewGroup.rotation.y += ratioX;

        // 获取 cubeGroup 的世界坐标
        let cubeGroupWorldPosition = new THREE.Vector3();
        this.viewGroup.getWorldPosition(cubeGroupWorldPosition);

        this.children.forEach((item) => {
            if (item.penStyle !== undefined) {
                // 计算 cubeGroup 旋转前后的四元数差值
                let initialQuaternion = new THREE.Quaternion();
                initialQuaternion.setFromEuler(item.initRotation); // 初始欧拉角转换为四元数

                let currentQuaternion = this.viewGroup.quaternion.clone(); // 当前 cubeGroup 四元数

                let deltaQuaternion = new THREE.Quaternion();
                deltaQuaternion.multiplyQuaternions(currentQuaternion, initialQuaternion.invert()); // 计算旋转变化量

                // 计算旋转后的偏移量
                let offset = item.initLocalPos.clone();
                offset.applyQuaternion(deltaQuaternion); // 使用四元数变换偏移量

                // 更新物体位置
                item.position.copy(this.viewGroup.position.clone().add(offset));
            }
        });

        return super.onTouchMove(divPoint);
    }


    onTouchUp(point) {
        this.touchDownPoint = null
        this.moveTag = MoveTag.none
        return super.onTouchUp(point)
    }

    onPointInside(point) {
        if (!this.visible) {
            return false
        }
        if (this.onEdit) {
            for (let subView of this.subViews) {
                if (subView === this.viewGroup) {
                    continue
                }
                if (subView.onPointInside(point)) {
                    return true
                }
            }
        }

        // 立方体需要添加
        let vector = new THREE.Vector3()
        this.viewGroup.localToWorld(vector)
        let cvtPoint = this.convertPoint(point, vector.z)
        const pos = new THREE.Vector3()
        this.viewGroup.getWorldPosition(pos)
        let max = Math.max(this.size.width, this.size.height, this.size.z)
        let leftX = pos.x - max  * 0.75
        let rightX = pos.x + max * 0.75
        let topY = pos.y + max * 0.75
        let bottomY = pos.y - max * 0.75
        if (cvtPoint.x > leftX && cvtPoint.x < rightX && cvtPoint.y > bottomY && cvtPoint.y < topY) {
            return true
        }
        return super.onPointInside(point)
    }
}