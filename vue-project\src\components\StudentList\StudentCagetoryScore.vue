<template>
    <RBPAlert v-if="showStudentCategoryScore" :zIndex="getZIndex('--toolbar-top-student-list-z-index')" width="60%" @close="close"
        @click.stop>
        <template v-slot:rbpDiv>
            <div class="score">
                <div class="add">
                    <div class="title">表扬</div>
                    <div class="list">
                        <RBPAddScoreEva :item="item" v-for="(item, index) in evaluationAddList" :key="index"
                            @evaluationItemClick="evaluationItemClick(item)"></RBPAddScoreEva>
                    </div>
                </div>
                <div class="add">
                    <div class="title">待改进</div>
                    <div class="list">
                        <RBPAddScoreEva :item="item" v-for="(item, index) in evaluationMinusList" :key="index"
                            @evaluationItemClick="evaluationItemClick(item)"></RBPAddScoreEva>
                    </div>
                </div>
            </div>
        </template>
        <template #rbpBtns>
            <div class="btns">
                <RBPButton btnText="单独加分" :btnSelected="alone == true" @click="aloneClick"></RBPButton>
                <div :style="{ width: '12px' }"></div>
                <div class="same">
                    <RBPButton btnText="同时加分" :btnSelected="alone == false" @click="sameClick"></RBPButton>
                    <RBPDoubt></RBPDoubt>
                </div>
            </div>
        </template>
    </RBPAlert>
</template>
<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { storeToRefs } from 'pinia'
import { useEvaluationScoreStore } from '@/stores/evaluation_score_store'
import { ClassRoomRequest } from '@/server_request/classroom_request'
import { useClassroomStore } from '@/stores/classroom'
import { loginInstance } from "@/login_instance/login_instance"
import { dbHelper } from '@/utils/db_helper'
import { ElLoading, ElMessage } from 'element-plus'
import roomUpdater from '@/classroom/classroom_updater.js'
import { Alert } from '@/utils/alert'
import { useScoreAudioStore } from '@/stores/score_audio_store'
import RBPAlert from '@/components/baseComponents/RBPAlert.vue'
import RBPAddScoreEva from '@/components/baseComponents/RBPAddScoreEva.vue'
import RBPButton from '@/components/baseComponents/RBPButton.vue'
import RBPDoubt from '@/components/baseComponents/RBPDoubt.vue'
import { RBPColors } from '@/components/baseComponents/RBPColors.js'
import { getZIndex } from '@/components/baseComponents/RBPZIndex'
import { remoteControl } from '@/remote_control/remote_control'

const scoreAudioStore = useScoreAudioStore()
const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)
const evaluationScoreStore = useEvaluationScoreStore()
const { evaluationAddList, evaluationMinusList, alone,remoteFlag } = storeToRefs(evaluationScoreStore)
const classroomUIStore = useClassroomUIStore()
const { showStudentCategoryScore, mainContentTopSpace } = storeToRefs(classroomUIStore)
const calcTop = computed(() => {
    return `calc(50% + ${mainContentTopSpace.value}px)`
})

onMounted(async () => {
    evaluationScoreStore.queryDb()
})

watch(remoteFlag,()=>{
    evaluationItemClick(evaluationScoreStore.score)
})

async function aloneClick() {
    alone.value = true
    dbHelper.updateAddScoreTypeByTeacherId('1')
    remoteControl.handleInteractState()
}

function sameClick() {
    alone.value = false
    dbHelper.updateAddScoreTypeByTeacherId('2')
    remoteControl.handleInteractState()
}

async function evaluationItemClick(item) {
    // 是否有学生选中
    let haveSelected = false
    selectedClassroom.value.studentList.forEach(student => {
        if (student.selected) {
            haveSelected = true
        }
    })
    selectedClassroom.value.groupStudentArray.forEach(group => {
        if (group.selected) {
            haveSelected = true
        }
    })
    if (!haveSelected) {
        Alert.showErrorMessage('请选择学生或小组')
        return
    }
    let success = await roomUpdater.studentsAddScoreTypeTwo(item.evaluationItemId, item.evaluationItemScore)
    if (success) {
        // 播放音频 播放动画
        scoreAudioStore.play(item.evaluationItemScore)
    }
    close()
}

function close(){
    showStudentCategoryScore.value = false
    remoteControl.handleInteractState()
}

</script>
<style lang="scss" scoped>
@import "@/assets/scss/mixin.scss";

.btns {
    width: 100%;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    margin-bottom: 20px;

    .same {
        position: relative;

        .doubt {
            position: absolute;
            top: -15px;
            right: -12px;
        }
    }
}

.score {
    width: 100%;
    height: 100%;
    padding: 10px 10px;
    box-sizing: border-box;
    overflow-y: auto;

    .add {
        width: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid var(--border-bar-color);
        border-radius: 26px;
        box-sizing: border-box;
        margin: 20px 0;

        .title {
            margin-top: 30px;
            margin-left: 42px;
            font-size: 26px;
            color: var(--text-color);
        }

        .list {
            padding: 20px 0px;
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 10px;
        }
    }
}
</style>