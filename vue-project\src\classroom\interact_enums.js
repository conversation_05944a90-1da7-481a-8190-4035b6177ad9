

export const Interact = {
    none: -1,
    paperPen: 0,
    classTest: 1,
    trueFalse: 2,
    singleChoice: 3,
    multiChoice: 4,
    responder: 5,
    dictation: 7,
    chineseWriting: 9,
    multiQuestions: 12, //组合题
    examMode: 102,  // 考试模式
    vote: 13, //投票
    probability: 104, //概率
    phetExp: 14, // 实验
    homework:6, // 作业讲评
    
}

export const InteractStatus = {
    none: 255,
    selecting: 0,
    undelivered: 1, // 显示收卷
    underway: 2, // 显示结束互动
}

export function isSingleObjective(interact) {
    if (interact === Interact.singleChoice
        || interact === Interact.multiChoice
        || interact === Interact.trueFalse
        || interact === Interact.responder
        || interact === Interact.vote) {
        return true
    }
    return false
}

export function isObjective(interact) {
    if (interact === Interact.singleChoice
        || interact === Interact.multiChoice
        || interact === Interact.trueFalse
        || interact === Interact.responder
        || interact === Interact.vote 
        || interact === Interact.multiQuestions) {
        return true
    }
    return false
}


export function isWriteMode(interact) {
    if (interact === Interact.classTest
        || interact === Interact.chineseWriting
        || interact === Interact.paperPen
        || interact === Interact.dictation) {
        return true
    }
    return false
}

/// 学生状态
export const StuStatus = {
    none: 0,
    online: 1,
    offline: 2,
    absent: 3, //缺席
}

export function getStuStatus(stu) {
    if (stu.stuStatus == StuStatus.online) {
        return '在线'
    } else {
        return '离线'
    }
}

/// 学生书写题状态
export const StuWriteState = {
    none: -1, //不在互动中或者离线
    checked: 1, //已批改
    noPaper: 2, //无试卷
    noCopyBook: 3, // 无字帖
    didSubmit: 4, // 已提交
    noReply: 5, //未作答
    waiting: 6, //等待中
    writing: 7, // 作答中
}


export const MultiQusLecture = {
    none: 0,  // 讲解状态  
    question: 1, //在单个问题页面
    statistics: 2, // 在统计页面
    studentList: 3, // 学生列表页面
    statisticsFullScreen: 4, // 全屏查看统计
}


export function interactName(interact) {
    if (interact == Interact.paperPen) {
        return "纸笔互动";
    }
    if (interact == Interact.classTest) {
        return "随堂测";
    }
    if (interact == Interact.singleChoice) {
        return "单选题";
    }
    if (interact == Interact.multiChoice) {
        return "多选题";
    }
    if (interact == Interact.trueFalse) {
        return "判断题";
    }
    if (interact == Interact.responder) {
        return "抢答题";
    }
    if (interact == Interact.dictation) {
        return "单词听写";
    }
    if (interact == Interact.chineseWriting) {
        return "中文练字";
    }
    if (interact == Interact.multiQuestions) {
        return "组合题";
    }
    if (interact == Interact.examMode) {
        return "考试模式";
    }
    if (interact == Interact.vote) {
        return "投票";
    }
    if (interact == Interact.probability) {
        return "概率";
    }
    if (interact == Interact.phetExp) {
        return "实验"
    }
    if (interact == Interact.aiHelp) {
        return "智能助教"
    }
    return "";
}

export function getInteractImage(mode) {
    switch (mode) {
        case Interact.paperPen:
            return "/img/svg/icon_interact_paperpen.svg";
        case Interact.classTest:
            return "/img/svg/icon_interact_classtest.svg";
        case Interact.singleChoice:
            return "/img/svg/icon_interact_singlechoice.svg";
        case Interact.multiChoice:
            return "/img/svg/icon_interact_mutilchoice.svg";
        case Interact.trueFalse:
            return "/img/svg/icon_interact_truefalse.svg";
        case Interact.responder:
            return "/img/svg/icon_interact_responder.svg";
        case Interact.multiQuestions:
            return "/img/svg/icon_interact_combchoice.svg";
        case Interact.chineseWriting:
            return "/img/svg/icon_interact_chinesepractice.svg";
        case Interact.dictation:
            return "/img/svg/icon_interact_dictation.svg";
        case Interact.examMode:
            return "/img/svg/icon_interact_exam.svg";
        case Interact.vote:
            return "/img/svg/icon_interact_vote.svg"
        case Interact.phetExp:
            return "/img/svg/icon_interact_exp.svg"
        case Interact.probability:
            return "/img/svg/icon_interact_probability.svg"
        case Interact.aiHelp:
            return "/img/svg/icon_interact_ai.svg"
    }
}

export function writeStateString(writeState) {
    switch (writeState) {
        case StuWriteState.checked:
            return "已批改"
        case StuWriteState.noPaper:
            return "无试卷"
        case StuWriteState.noCopyBook:
            return "无字帖"
        case StuWriteState.didSubmit:
            return "已提交"
        case StuWriteState.noReply:
            return "未作答"
        case StuWriteState.waiting:
            return "等待中"
        case StuWriteState.writing:
            return "作答中"
    }
    return "未作答"
}


export function writeStateColor(writeState) {
    switch (writeState) {
        case StuWriteState.checked:
            return 0x00ff00
        case StuWriteState.noPaper:
            return 0xff0000
        case StuWriteState.noCopyBook:
            return 0xff0000
        case StuWriteState.didSubmit:
            return 0x00ff00
        case StuWriteState.noReply:
            return 0xff0000
        case StuWriteState.waiting:
            return 0xffff00
        case StuWriteState.writing:
            return 0x00ff00
    }
    return 0xff0000
}

export const InteractMode = {
    none: 0,
    small: 1,
    big: 2
}