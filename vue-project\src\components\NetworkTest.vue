<template>
    <div class="networktest" @click="$emit('close')">
        <RBPAlert title="版本信息" width="673px" height="376px" @close="$emit('close')" :hide-bc="true">
            <template v-slot:rbpDiv>
                <div class="content" @click.stop>
                    <div class="item" v-for="item in testList">
                        <div class="title">{{ item.name }}</div>
                        <div class="status" :style="{ color: testResColor(item.testing, item.testResult) }">{{
                            testResString(item.testing, item.testResult) }}</div>
                        <div class="result">{{ item.delayMs }}ms</div>
                    </div>
                </div>
            </template>
        </RBPAlert>

    </div>
</template>
<script setup>
import { ref, onMounted } from "vue"
import { ServerUrls } from "@/server_request/server_urls"
import RBPAlert from "./baseComponents/RBPAlert.vue"
const testList = ref([
    { name: "百度", host: "baidu.com", testing: true, testResult: false, delayMs: 0 },
    { name: "应用域名", host: ServerUrls.getMgboardHost().replace(/^http:\/\//, '').replace(/^https:\/\//, ''), testing: true, testResult: false, delayMs: 0 },
    { name: "应用域名2", host: ServerUrls.getFutureInkHost().replace(/^http:\/\//, '').replace(/^https:\/\//, ''), testing: true, testResult: false, delayMs: 0 },
    { name: "MQTT", host: "aliyun.com", testing: true, testResult: false, delayMs: 0 }
])
onMounted(async () => {
    await startTest()
    // await startTest(testList.value)
})
function testResString(testing, testResult) {
    if (testing) {
        return "测试中..."
    } else {
        return testResult ? "通过" : "未通过"
    }
}
function testResColor(testing, testResult) {
    if (testing) {
        return ""
    } else {
        return testResult ? "var(--correct-color)" : "var(--error-color)"
    }
}
async function startTest() {
    for (const object of testList.value) {
        object.testing = true;
        const start = Date.now();
        try {
            await testConnection(object.host);
            const end = Date.now();
            const duration = end - start;

            object.testResult = true;
            object.delayMs = duration;
        } catch (error) {
            object.testResult = false;
        } finally {
            object.testing = false;
        }
    }
}
async function testConnection(url) {
    url = 'https://' + url
    const response = await fetch(url, { method: 'HEAD', mode: 'no-cors' }); // HEAD 请求，不下载内容
    // if (!response.ok) {
    //     throw new Error('Network response was not ok.');
    // }
}

</script>
<style lang="scss" scoped>
.networktest {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 100;
    background-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    -webkit-app-region: no-drag;

    .content {
        width: 100%;
        height: 100%;
        padding: 20px 100px;
        box-sizing: border-box;

        .item {
            display: flex;
            font-weight: 400;
            font-size: 21px;
            color: var(--text-color);
            line-height: 36px;
            margin-bottom: 12px;
            justify-content: space-between;

            .title {
                flex: 1;
            }

            .status {
                flex:1;
                text-align: center;
            }

            .result {
                flex:1;
                text-align: end;
            }
        }
    }
}
</style>