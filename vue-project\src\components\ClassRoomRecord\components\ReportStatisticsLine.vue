<template>
    <div class="report-line-content">
        <div class="report-line-body">
            <div class="percent-line">
                <div class="single-line" v-for="(item, index) in linesList" :key="index">
                    <div :class="`line-body ${item < 0 ? 'line-body-left' : ''}`"></div>
                    <div class="line-text">
                        {{ Math.abs(item) }}
                    </div>
                </div>
            </div>
            <div class="top-content" id="lineContent">
                <div class="single-point" v-for="(item, index) in (isDown ? pointsData : pointsData.slice().reverse())"
                    :key="index">
                    <div class="point-text">
                        <Marquee  font-size="16px" color="var(--text-color)" text-align="right" :text="item.knowledgePoint"></Marquee>

                    </div>

                    <!-- <div class="point-text">{{ item.knowledgePoint }}</div> -->
                    <div class="line-text" :style="{ right: `calc( (100% - 192px) / 2 *${2 - item.accuracy} + 36px)` }">
                        {{ 100 - Math.round(item.accuracy * 100) }}%</div>
                    <div class="line-text line-text-right"
                        :style="{ left: `calc(156px + (100% - 192px)/2 + (100% - 192px) / 2 * ${item.accuracy})` }">
                        {{ Math.round(item.accuracy * 100) }}%</div>

                    <div class="point-line point-line-left">
                        <div class="line" :style="{ width: `${100 - Math.round(item.accuracy * 100)}%` }">

                        </div>
                    </div>
                    <div class="point-line">
                        <div class="line" :style="{ width: `${Math.round(item.accuracy * 100)}%` }">
                        </div>
                    </div>
                    <div style="width: 36px;"></div>
                </div>
            </div>

        </div>

        <div class="report-sort-icon" @click="isDown = !isDown">
            <img :src="isDown ? gradeDown : gradeUp" alt="">
        </div>
    </div>
</template>

<script setup>
import Marquee from './marquee.vue';
import { nextTick, onMounted, ref, watch } from 'vue';
import gradeDown from '../assets/grade_down.svg'
import gradeUp from '../assets/grade_up.svg'
const props = defineProps({
    data: {
        type: Array,
        default: []
    }
})
const linesList = [-100, -75, -50, -25, 0, 25, 50, 75, 100]
const isDown = ref(true)
const pointsData = ref([])



watch(() => props.data, () => {
    pointsData.value = props.data.sort((a, b) => b.accuracy - a.accuracy)
}, { deep: true, immediate: true })


function scrollToBottom() {
    const element = document.getElementById('lineContent');
    element.scrollTo({
        top: element.scrollHeight,
        behavior: 'smooth'
    });
}

onMounted(() => {
    nextTick(() => {
        scrollToBottom()
    })
})
</script>

<style lang="scss" scoped>
.report-line-content {
    background: var(--main-bc-color);
    border-radius: 21px;
    border: 1px solid var(--border-bar-color);
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    position: relative;

    .report-sort-icon {
        cursor: pointer;
        position: absolute;
        top: 24px;
        right: 24px;

    }

}

.report-line-body {
    margin: 10px 60px;
    margin-top: 24px;
    width: calc(100% - 120px);
    height: calc(100% - 34px);
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    position: relative;

    .top-content {
        height: calc(100% - 36px);
        width: calc(100%);
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        align-items: start;
        position: absolute;
        top: 0;
        left: 0;

        .single-point {
            width: 100%;
            display: flex;
            align-items: center;
            padding: 3px 0px;
            position: relative;
            box-sizing: border-box;

            .point-text {
                width: 120px;
                box-sizing: border-box;
                margin-right: 36px;
            }

            

            .point-line {
                display: flex;
                flex: 1;
                height: 20px;

                font-size: 15px;
                color: var(--correct-color);

                .line {
                    border-radius: 0px 26px 26px 0px;



                    background-color: var(--correct-color);




                }


            }

            .line-text {
                position: absolute;
                font-size: 12px;
                color: var(--error-color);
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                line-height: 12px;
                font-weight: 500;
                text-align: center;

            }

            .line-text-right {
                color: var(--correct-color);

            }

            .point-line-left {
                justify-content: end;

                .line {
                    border-radius: 26px 0px 0px 26px;
                    // justify-content: start;
                    // padding-right: 0px;
                    // padding-left: 4px;
                    background-color: var(--error-color) !important;
                }
            }

        }

        scrollbar-width: none;
    }


    .top-content::-webkit-scrollbar {
        display: none;
        /* Chrome, Safari 和 Opera */
    }

    .percent-line {
        height: calc(100%);
        width: calc(100% - 144px);
        padding-left: 132px;
        padding-right: 12px;
        display: flex;
        justify-content: space-between;

        .single-line {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 48px;
            height: 100%;

            .line-text {
                height: 36px;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 48px;
                font-size: 12px;
                color: #a9a9a9;
            }

            .line-body {
                width: 1px;
                height: calc(100% - 36px);
                background-color: var(--border-bar-color);
            }

            .line-body-left {
                background-color: var(--border-bar-color);
            }
        }
    }
}
</style>