import { BoardImageView } from "@/drawboard/board_image_view";
import { BoardTool } from "@/drawboard/board_tool";
import { BoardView } from "@/drawboard/board_view";
import { LineMode, PainterOrder } from "@/drawboard/draw_enums";
import { PointState } from "@/drawboard/write_point";
import { MeshLineGeometry, MeshLineMaterial } from 'meshline';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import * as THREE from 'three';
import { serverHost } from "@/server_request/server_urls";


export class StudentPageView extends BoardView {
    constructor(application, pos, size, pageCode) {
        super(application, pos, size)

        this.pageCode = pageCode
        this.writePointsCache = []
        this.lineCaches = []
        this.maxCacheLineCount = this.randomCacheLineCount()
        this.lineCacheCount = 0
        this.lastSnapTexture = null
        this.starWidth = size.width / 50
        StudentPageView.getLineMaterial()
        this.setupUI()
    }


    static getLineMaterial() {
        if (!StudentPageView.lineMaterial) {
            let lineMaterial = new MeshLineMaterial({
                color: new THREE.Color(LineMode.StuLineColor),
                lineWidth: LineMode.WorldLinwWidth, // 线宽度
                sizeAttenuation: false,
            })
            lineMaterial.depthTest = false
            lineMaterial.depthWrite = false
            let texture = lineMaterial.map;
            if (texture) {
                texture.minFilter = THREE.NearestFilter;  // 使用 NearestFilter 可以避免 mipmapping
                texture.magFilter = THREE.NearestFilter;  // 使用 NearestFilter 防止模糊
                texture.generateMipmaps = false;  // 禁用 mipmap 生成
            }
            StudentPageView.lineMaterial = lineMaterial
        }
        return StudentPageView.lineMaterial
    }

    setupUI() {
        let drawView = new BoardView(this.application, new THREE.Vector3(0, 0, 0), this.size)
        drawView.renderOrder = PainterOrder.writePoints
        this.drawView = drawView
        this.addSubView(drawView)
    }

    setPageInfo(pageInfo) {
        this.pageInfo = pageInfo
        this.setImageUrl(pageInfo.recogImgurl)
    }

    setImageUrl(imageUrl) {
        if (!this.imageView) {
            this.imageView = new BoardImageView(this.application, new THREE.Vector3(0, 0, 0), this.size)
            this.imageView.setImageUrl(imageUrl)
            this.imageView.renderOrder = PainterOrder.background
            this.addSubView(this.imageView)
        }
        else {
            this.imageView.setImageUrl(imageUrl)
        }
        this.imageView.visible = this.imageShow
    }

    setCheckResult(imageUrl) {
        this.setImageUrl(imageUrl)
        this.drawView.visible = false
        this.animate()
    }

    /// 转为当前视图坐标
    pointToSence(x, y) {
        const rx = (x * 2 - 1) * this.size.width / 2.0
        const ry = (-y * 2 + 1) * this.size.height / 2.0
        let vector = new THREE.Vector3(rx, ry, 0);
        return vector
    }


    drawLine() {
        if (this.writePointsCache.length > 0) {   //节省性能，写完一笔再绘制
            let points = []
            this.writePointsCache.forEach((item) => {
                points.push(this.pointToSence(item.x, item.y))
            })
            if (points.length === 0) {
                this.writePointsCache = []
                return
            }
            if (points.length < 2) {
                points.push(points[0])
            }
            
            // 使用均值平滑算法，保持轨迹形状，减少放大时的弯曲问题
            const smoothPoints = this.applyMovingAverageSmoothing(points);
            
            let lineGeometry = new MeshLineGeometry();
            let mesh = new THREE.Mesh(lineGeometry, StudentPageView.lineMaterial);
            mesh.renderOrder = PainterOrder.writePoints
            lineGeometry.setPoints(smoothPoints);
            mesh.geometry = lineGeometry;
            this.drawView.add(mesh)
            this.lineCaches.push(mesh)
            this.writePointsCache = []
            this.handleEndLine()
            this.animate()
            this.writePointsCache = []
        }
    }

    addWritePoint(point) {
        if (point.f === PointState.Move) {
            this.writePointsCache.push(point)
        }
        else {
            this.drawLine()
        }
        this.lastWritePoint = point
    }

    // 均值平滑算法
    applyMovingAverageSmoothing(points) {
        if (points.length <= 2) return points;
        
        // 参数配置
        const windowSize = 3; // 增大均值窗口大小，提高平滑效果
        const iterations = 1;  // 迭代次数，越多越平滑
        const preserveEnds = true; // 是否保留端点
        
        // 直接应用均值滤波，不做插值
        let smoothedPoints = [];
        // 复制原始点数组，避免修改原始数据
        points.forEach(p => smoothedPoints.push(p.clone()));
        
        for (let iter = 0; iter < iterations; iter++) {
            const tempPoints = [];
            // 深度复制点数组
            smoothedPoints.forEach(p => tempPoints.push(p.clone()));
            
            for (let i = 0; i < smoothedPoints.length; i++) {
                // 保留端点
                if (preserveEnds && (i === 0 || i === smoothedPoints.length - 1)) {
                    continue;
                }
                
                // 计算窗口范围
                const halfWindow = Math.floor(windowSize / 2);
                const start = Math.max(0, i - halfWindow);
                const end = Math.min(smoothedPoints.length - 1, i + halfWindow);
                
                // 计算均值
                let sumX = 0, sumY = 0, sumZ = 0;
                let count = 0;
                
                for (let j = start; j <= end; j++) {
                    sumX += smoothedPoints[j].x;
                    sumY += smoothedPoints[j].y;
                    sumZ += smoothedPoints[j].z;
                    count++;
                }
                
                // 应用均值
                tempPoints[i].x = sumX / count;
                tempPoints[i].y = sumY / count;
                tempPoints[i].z = sumZ / count;
            }
            
            smoothedPoints = tempPoints;
        }
        
        return smoothedPoints;
    }
    
    // 计算两点间距离
    distance(p1, p2) {
        const dx = p2.x - p1.x;
        const dy = p2.y - p1.y;
        const dz = p2.z - p1.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }

    showBackground(show) {
        this.imageShow = show
        if (this.imageView) {
            this.imageView.visible = show
        }
    }

    appendWritePoints(points) {
        points.forEach((point) => {
            this.addWritePoint(point)
        })
    }


    randomCacheLineCount() {
        let min = 300
        let max = 500
        if (serverHost.appConfig.render_limit) {
            min = Math.floor(serverHost.appConfig.render_limit / 10)
            max = Math.floor(serverHost.appConfig.render_limit * 1.5 / 10)
        }
        //以上范围随机一个10的倍数
        return (Math.floor(Math.random() * (max - min + 1)) + min) * 10
    }

    handleEndLine() {
        this.lineCacheCount += 1
        if (this.lineCaches.length < 3) {
            return
        }

        try {
            const geometries = this.lineCaches.map(cache => {
                return cache.geometry
            }).filter(geometry => geometry !== null);
            const mergelineGeometry = BufferGeometryUtils.mergeGeometries(geometries)
            const mesh = new THREE.Mesh(mergelineGeometry, StudentPageView.lineMaterial);
            mesh.renderOrder = PainterOrder.writePoints
            for (let element of this.lineCaches) {
                BoardTool.disposeMesh(element)
            }
            this.lineCaches = []
            let application = this.application?.deref()
            if (this.lineCacheCount > this.maxCacheLineCount && application) {
                let shooter = application.getPageShooter()
                let scale = shooter.depthSize.width / this.size.width
                let texture = shooter.snapShootMesh(mesh, this.lastSnapTexture, scale)
                if (this.lastSnapTexture) {
                    this.lastSnapTexture.dispose()
                }
                this.lastSnapTexture = texture
                BoardTool.disposeMesh(mesh)
                this.refreshShootImage(texture)
                this.lineCacheCount = 0
                this.maxCacheLineCount = this.randomCacheLineCount()
            }
            else {
                this.lineCaches.push(mesh)
                this.drawView.add(mesh)
            }
        }
        catch (e) {
            console.log(e)
            this.lineCaches = []
        }

    }

    refreshShootImage(texture) {
        if (!this.shootMesh) {
            let shootMaterial = new THREE.MeshBasicMaterial({
                map: texture,
                transparent: true,
                blending: THREE.NormalBlending,
                depthTest: false,
                depthWrite: false,
                precision: 'highp'
            })
            let geometry = new THREE.PlaneGeometry(this.size.width, this.size.height)
            this.shootMesh = new THREE.Mesh(geometry, shootMaterial)
            this.shootMesh.renderOrder = PainterOrder.snapShoot
            this.add(this.shootMesh)
        }
        else {
            this.shootMesh.material.map = texture
        }
    }


    getStarImage(star, index) {
        if (star == 0) {
            return "img/icon_star_emp.png"
        } else {
            if (star >= index + 1) {
                return "img/icon_star_full.png"
            } else if (star >= index + 0.5) {
                return "img/icon_star_half.png"
            }
        }
        return "img/icon_star_emp.png"
    }

    drawWorldStars(item) {
        if (!this.starView) {
            this.starView = new BoardView(this.application, new THREE.Vector3(0, 0, 0), this.size)
            this.starView.renderOrder = PainterOrder.studentName
            this.addSubView(this.starView)
        }
        this.starView.removeAndDisposeSubViews()
        item.words.forEach((word) => {
            const areaPoints = JSON.parse(word.areaPoints)
            const star = word.starLevel
            //转换为 Group实际坐标
            const x = (areaPoints.x / 21000 * 2 - 1) * this.size.width / 2.0
            const y = (-areaPoints.y / 29700 * 2 + 1) * this.size.height / 2.0
            const space = this.starWidth * 0.2
            for (let i = 0; i < 3; i++) {
                const imageUrl = this.getStarImage(star, i)
                let vector = new THREE.Vector3(x + i * (this.starWidth + space), y - 3 * this.starWidth, 0)
                let size = { width: this.starWidth, height: this.starWidth }
                let starImage = new BoardImageView(this.application, vector, size, true)
                starImage.setImageUrl(imageUrl)
                this.starView.addSubView(starImage)
            }
        })

        this.animate()
    }

    dispose() {
        this.lineCaches = []
        this.lineCacheCount = 0
        if (this.lastSnapTexture) {
            this.lastSnapTexture.dispose()
            this.lastSnapTexture = null
        }
        super.dispose()
    }
}