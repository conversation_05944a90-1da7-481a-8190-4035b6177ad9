<template>
    <div class="record-tree">
        <div class="tree">
            <div class="top">
                <RecordBoard :id="stuId" :image="imgSrc" :show-width="showWidth" :show-height="showHeight">
                </RecordBoard>

                <div v-show="!isFullScreen" class="top-l" ref="topl">

                    <div class="type-top-area"></div>
                    <div class="type-list-area">
                        <div style="height:480px"></div>
                        <div class="typewrap" v-for="(item, index) in treeData" :key="index">
                            <div class="quesitem" v-for="(ques, idx) in item.questions" :key="idx"
                                :id="`${index}-${idx}`" :class="{ active: tIdx == index && qIdx == idx }"
                                @click="quesItemClick(index, idx)">
                                <div>{{ ques.sno ? ques.sno : `第${index + 1}题` }}</div>
                                <div class="progress">
                                    <el-progress :show-text="false" :text-inside="true" :stroke-width="18"
                                        stroke-linecap="square" :percentage="fmtRate(ques.correctRate)"
                                        color="var(--correct-color)"></el-progress>
                                </div>
                                <div class="percent">{{ fmtRate(ques.correctRate) }}%</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-show="!isFullScreen" class="top-r" @click="closePop"
                    :style="{ height: showStus ? '270px' : '64px' }" v-if="quesInfo">
                    <div class="stus">
                        <div @click="toogleShowStus()" class="stus-expand">
                            <img src="/icon/icon_arrow_down.svg"
                                :style="{ transform: showStus ? '' : 'rotate(180deg)' }" />
                        </div>

                        <template v-if="quesInfo.quesType == 2 || quesInfo.quesType == 3 || quesInfo.quesType == 5">
                            <div class="block">
                                <img class="block-random" @click="detailR.detailRandom()"
                                    src="/img/svg/icon_tab_randomRoll.svg" />
                                <div class="tit_wrap wrong" style="box-sizing:border-box">
                                    <div>错误人数总计：{{ quesInfo.falseStu.length }}人</div>
                                </div>
                                <detail ref="detailR" v-show="showStus" :quesInfo="quesInfo" :stuId="stuId"
                                    @setQuesStuInfo="setQuesStuInfo" @setRandomList="setRandomList"></detail>
                            </div>
                        </template>
                        <template v-else>
                            <!-- 正确 -->
                            <div class="block right" v-if="quesInfo.trueStu && quesInfo.trueStu.length">
                                <img class="block-random" @click="setRandomList('trueStu')"
                                    src="/img/svg/icon_tab_randomRoll.svg" />
                                <div class="tit_wrap">
                                    正确: {{ quesInfo.trueStu.length }}人
                                </div>
                                <status v-show="showStus" :list="quesInfo.trueStu" :stuId="stuId" :ansType="'true'"
                                    @setQuesStuInfo="setQuesStuInfo"></status>
                            </div>
                            <!-- 错误 -->
                            <div class="block wrong" v-if="quesInfo.falseStu && quesInfo.falseStu.length">
                                <img class="block-random" @click="setRandomList('falseStu')"
                                    src="/img/svg/icon_tab_randomRoll.svg" />
                                <div class="tit_wrap">
                                    错误: {{ quesInfo.falseStu.length }}人
                                </div>
                                <div v-if="quesInfo.trueStu && quesInfo.trueStu.length" class="block-border"></div>
                                <status v-show="showStus" :list="quesInfo.falseStu" :stuId="stuId" :ansType="'false'"
                                    @setQuesStuInfo="setQuesStuInfo"></status>
                            </div>
                            <!-- 未作答 -->
                            <div class="block undo" v-if="quesInfo.unAnswerStu && quesInfo.unAnswerStu.length">
                                <img class="block-random" @click="setRandomList('unAnswerStu')"
                                    src="/img/svg/icon_tab_randomRoll.svg" />
                                <div class="tit_wrap">
                                    未作答: {{ quesInfo.unAnswerStu.length }}人
                                </div>
                                <div v-if="(quesInfo.trueStu && quesInfo.trueStu.length) || (quesInfo.falseStu && quesInfo.falseStu.length)"
                                    class="block-border"></div>
                                <status v-show="showStus" :list="quesInfo.unAnswerStu" :stuId="stuId"
                                    @setQuesStuInfo="setQuesStuInfo"></status>
                            </div>
                            <!-- 未批改 -->
                            <div class="block undo" v-if="quesInfo.unCorrectStu && quesInfo.unCorrectStu.length">
                                <img class="block-random" @click="setRandomList('unCorrectStu')"
                                    src="/img/svg/icon_tab_randomRoll.svg" />
                                <div class="tit_wrap">
                                    其他: {{ quesInfo.unCorrectStu.length }}人
                                </div>
                                <div v-if="(quesInfo.trueStu && quesInfo.trueStu.length) || (quesInfo.falseStu && quesInfo.falseStu.length) || (quesInfo.unAnswerStu && quesInfo.unAnswerStu.length)"
                                    class="block-border"></div>
                                <status v-show="showStus" :list="quesInfo.unCorrectStu" :stuId="stuId"
                                    @setQuesStuInfo="setQuesStuInfo"></status>
                            </div>
                        </template>
                    </div>

                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import $ from 'jquery'
import Detail from './Detail.vue';
import Status from './Status.vue';
import RecordBoard from './RecordBoard/RecordBoard.vue';
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import { ClassroomRecordRequest } from '@/server_request/classroom_record';
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { useRandomRollCallStore } from '@/stores/random_roll_call_store'
import { storeToRefs } from 'pinia'
import { useClassroomStore } from '@/stores/classroom.js'
import { useRecordDrawboardStore } from '@/stores/record_drawboard_store';
const recordDrawboardStore = useRecordDrawboardStore()
const { isFullScreen } = storeToRefs(recordDrawboardStore)
const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)
const randomRollCallStore = useRandomRollCallStore()
const { sourceGroupList } = storeToRefs(randomRollCallStore)
const classroomUIStore = useClassroomUIStore()
const { showRandomRollCall, showClassRoomRecord, showRecordResult } = storeToRefs(classroomUIStore)

const props = defineProps({
    taskId: String,
    dpr: Number
})

const emits = defineEmits(['setQues', 'newCanvas', 'setCanvasSize'])
const treeData = ref([])
const quesList = ref([])
const tIdx = ref(null)
const qIdx = ref(null)
const qid = ref(null)
const qIndex = ref(0)
const quesInfo = ref(null)
const stuInfo = ref(null)
const stuId = ref('')
const paperId = ref(0)
const showStus = ref(true)
const randomStu = ref(null)
const order = ref('')
let treeDataCopy = []
let dbclick = 0;
let randomStuId = null
let randomList = []
const score = ref(null)
const scoreBtn = ref(null)
const answerImg = ref(null)
const answerImgHeight = ref(null)
//及时
let getTimer
let stopTimer
//组件ref
const topl = ref()
const ques = ref()
const detailR = ref()
const imgSrc = computed(() => {
    if (!stuInfo.value && !quesInfo.value) {
        return null
    }

    let url = stuInfo.value?.questionImgurl || stuInfo.value?.recogImgurl || quesInfo.value?.cutImage
    return url
})

watch(() => stuInfo.value, () => {
    if (!stuInfo.value && !quesInfo.value) {
        answerImg.value = ""
        return null
    }

    let url = stuInfo.value?.questionImgurl || stuInfo.value?.recogImgurl || quesInfo.value?.cutImage
    answerImg.value = url
})

const imgSrcs = computed(() => {
    let res = []
    if (stuInfo.value && stuInfo.value.pageImgUrls) {
        res = stuInfo.value.pageImgUrls
    } else if (quesInfo.value && quesInfo.value.pageImgUrls) {
        res = quesInfo.value.pageImgUrls
    }
    if (res.length) {
        let naturalHeight = 0, naturalWidth = 0
        let x, y, w, h
        res.forEach(item => {
            x = parseInt(item.questionAreaX / 17.6)
            y = parseInt(item.questionAreaY / 17.6)
            w = parseInt(item.questionAreaW / 17.6)
            h = parseInt(item.questionAreaH / 17.6)
            item.pageImg = item.pageImgUrl + `?x-oss-process=image/crop,x_${x},y_${y},w_${w},h_${h}`

            // 计算显示的高度和宽度
            naturalHeight += h
            w > naturalWidth && (naturalWidth = w)
            item.h = h

            // 答题框显示在哪个区域上
            if ((quesInfo.value && quesInfo.value.cutImage.indexOf(item.pageImgUrl) > -1) || (stuInfo.value && stuInfo.value.questionImgurl.indexOf(item.pageImgUrl) > -1)) {
                item.areaX = quesInfo.value.areaX
                item.areaY = quesInfo.value.areaY
                item.areaW = quesInfo.value.areaW
                item.areaH = quesInfo.value.areaH
            }
        })

        let quesWrap = $('#canvasWrap')
        if (naturalHeight / naturalWidth > quesWrap.height() / quesWrap.width()) {
            res.forEach(item => {
                item.h = item.h / naturalHeight
                item.width = 'auto'
                item.height = (item.h * (quesWrap.height() - 10)) + 'px'
            })
        } else {
            res.forEach(item => {
                item.h = item.h / naturalHeight
                item.width = 'calc(100vw - 300px)'
                item.height = 'auto'
            })
        }
    }
    return res
})

function getRandomDisplay() {
    return showClassRoomRecord.value || showRecordResult.value ? '' : 'none'
}
const wrapHeight = computed(() => {
    let stusHeight = showStus.value ? '270px' : '64px'
    answerImgHeight.value = `calc(100vh - 78px - ${stusHeight})`
    return `calc(100vh - 78px - ${stusHeight})`
})
//展示宽高
const showWidth = window.innerWidth - 260
const showHeight = window.innerHeight - 270 - 78
watch(() => wrapHeight, () => {
    nextTick(() => {
        ques.value && ques.value.load()
    })
})
onMounted(() => {
    getQues()
})



async function getQues() {
    try {
        let { data } = await ClassroomRecordRequest.taskQuestionsCorrect({ taskId: props.taskId })
        quesList.value = data
        getTaskData()
    } catch (e) {
        //TODO handle the exception
    }
}
async function getTaskData() {
    try {
        let { data } = await ClassroomRecordRequest.questionsAnswerStatistic({ taskId: props.taskId })
        paperId.value = data.paperId
        treeData.value = data.typeList
        // let isRight = true
        // for (let e of treeData.value) {
        //     for (var ques of e.questions) {
        //         isRight = ques.correctRate == 1
        //         if (!isRight) {
        //             break
        //         }
        //     }
        //     if (!isRight) {
        //         break
        //     }
        // }
        // if (isRight) {
        //     $('.foot-show-item-recommand').hide()
        // }
        treeDataCopy = data.typeList

        quesItemClick(0, 0)
        nextTick(() => {
            domScroll(`0-0`)
        })
    } catch (e) {
        //TODO handle the exception
    }
}
async function getQuesDetail(ques) {
    try {
        randomStu.value = null
        let params = {
            questionId: qid.value,
            taskId: props.taskId
        }
        let { data } = await ClassroomRecordRequest.studentQuestionsAnswerStatistic(params)
        setStuInfo(null, stuId.value)
        data.trueStu.forEach(item => {
            item.key = 'trueStu'
            if (item.studentExtId == stuId.value) {
                setStuInfo(item)
            }
        })
        data.falseStu.forEach(item => {
            item.key = 'falseStu'
            if (item.studentExtId == stuId.value) {

                setStuInfo(item)
            }
        })
        data.unAnswerStu.forEach(item => {
            item.key = 'unAnswerStu'
            if (item.studentExtId == stuId.value) {
                setStuInfo(item)
            }
        })
        data.unCorrectStu.forEach(item => {
            item.key = 'unCorrectStu'
            if (item.studentExtId == stuId.value) {
                setStuInfo(item)
            }
        })
        quesInfo.value = {
            ...ques,
            ...data
        }
    } catch (e) {
        console.log(e)
        //TODO handle the exception
    }
}
// function resetStu(stu, toKey) {
//     let fromKey = stu.key
//     let fromIndex = quesInfo.value[fromKey].findIndex(item => { return item.studentExtId == stu.studentExtId })
//     quesInfo.value[fromKey].splice(fromIndex, 1)

//     stu.key = toKey
//     quesInfo.value[toKey].push(stu)

//     // 设置正确率
//     try {
//         // 当前题目的正确率
//         let correctRate = quesInfo.value['trueStu'].length / (quesInfo.value['trueStu'].length + quesInfo.value['falseStu'].length)

//         // 设置当前树正确率
//         setTreeData(treeData.value, tIdx.value, qIdx.value, correctRate, true)

//         //设置原始树正确率，重批后排序
//         let tIdxO = -1, qIdxO = -1
//         treeDataCopy.forEach((item, index) => {
//             item.questions.forEach((ite, idx) => {
//                 if (ite.qid == quesInfo.value.qid) {
//                     tIdxO = index
//                     qIdxO = idx
//                 }
//             })
//         })
//         setTreeData(treeDataCopy, tIdxO, qIdxO, correctRate, false)
//     } catch (e) {
//         console.log(e)
//     }
// }
// function setTreeData(keyData, index, idx, correctRate, isTree) {
//     let data = JSON.parse(JSON.stringify(keyData))
//     data[index]['questions'][idx].correctRate = correctRate
//     let rate = 0
//     let count = 0
//     data[index]['questions'].forEach(item => {
//         rate += item.correctRate
//         count += 1
//     })
//     data[index].correctRate = rate / count
//     if (isTree) treeData.value = data
//     else treeDataCopy = data
// }
function toogleShowStus() {
    showStus.value = !showStus.value
}
function closePop() {
    ques.value && ques.value.closePop()
}
function quesItemClick(index, idx) {
    tIdx.value = index
    qIdx.value = idx
    getQuesId()
}

function getQuesId() {
    treeData.value.forEach((item, index) => {
        if (tIdx.value == index) {
            item.questions.forEach((ques, idx) => {
                if (qIdx.value == idx) {
                    qid.value = ques.qid
                    getQIndex(ques.qid)
                    getQuesDetail(ques)
                    domScroll(qid.value)
                }
            })
        }
    })
}
function getQIndex(data) {
    qIndex.value = quesList.value.findIndex(item => { return item.qid == data })
}

function domScroll(id) {
    try {
        const dom = document.getElementById(id)　　// 获取想要滚动的dom元素
        dom.scrollIntoView({ block: 'center' })　　//　通过scrollIntoView方法滚动到可视窗口中间
    } catch (e) {
        // domScroll(id)
    }
}
function setQuesStuInfo(stu) {
    if (stuInfo.value && stu &&
        stuInfo.value.studentExtId == stu.studentExtId
        && stuInfo.value.questionImgurl == stu.questionImgurl
        && stuInfo.value.recogImgurl == stu.recogImgurl
    ) {
        // console.log("---------------------------",stuInfo.value,stu);

        // setStuInfo(null)
        return
    }
    setStuInfo(stu)
}
function setStuInfo(stu, stuIdData) {
    if (stu) {
        stuInfo.value = stu
        stuId.value = stu.studentExtId
        if (stu.answerImg) {
            answerImg.value = JSON.parse(stu.answerImg)
        } else {
            answerImg.value = null
        }
    } else {
        stuInfo.value = null
        stuId.value = stuIdData || null
        answerImg.value = null
    }
}
function setRandomList(key) {
    if (typeof (key) == 'string') {
        randomList = quesInfo.value[key]
    } else {
        randomList = key
    }
    random()
}
function random() {
    sourceGroupList.value = []


    randomList.forEach(item => {
        let stu = selectedClassroom.value.idStudentMap.get(parseInt(item.studentExtId))
        if (stu) {
            sourceGroupList.value.push(stu)
        }
    })

    randomRollCallStore.setup(selectedClassroom.value.studentList, sourceGroupList.value)
    showRandomRollCall.value = true


    return
    /**
     * 30 毫秒获取一次，600毫秒后获取最终学生，最终学生显示10秒弹框自动消失
     */
    score.value = null
    scoreBtn.value = null
    getTimer && clearInterval(getTimer)
    stopTimer && clearInterval(stopTimer)
    getTimer = setInterval(() => {
        let index = parseInt(Math.random() * randomList.length)
        let stu = randomList[index]
        randomStu.value = stu.deviceAlias
        randomStuId = stu.studentExtId
    }, 30)
    stopTimer = setTimeout(() => {
        getTimer && clearInterval(getTimer)

    }, 600)
}

function fmtRate(value) {
    return Math.round(value * 100)
}

</script>
<style lang="scss">
.tree .el-progress-bar__outer {
    background-color: var(--progress-bar-bg-color) !important;
    border-radius: 13.5px;
}

.tree .el-progress-bar__inner {
    border-radius: 13.5px;
}
</style>
<style lang="scss" scoped>
.record-tree {






















    .el-table .caret-wrapper {
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        height: 34px;
        width: 24px;
        vertical-align: middle;
        cursor: pointer;
        overflow: initial;
        position: relative;
        // background-color: yellow;
    }

    .el-table::before {
        background-color: transparent;
    }

    .el-table.asc .sort-caret.ascending {
        border-bottom-color: rgb(46, 74, 102) !important
    }

    .el-table.desc .sort-caret.descending {
        border-top-color: rgb(46, 74, 102) !important
    }

    .tree {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 78px);
    }

    .top {
        flex: 1;
        display: flex;
        position: relative;
    }

    .top-l {
        height: calc(100vh - 81px);
        width: 260px;
        display: flex;
        flex-direction: column;
        justify-content: end;
        position: absolute;
        left: 0;
        top: 0;
        padding-bottom: 3px;
        background-color: var(--toolbar-bg-color);

        .type-top-area {
            min-height: 48px;
            flex: 1;
        }

        .type-list-area {
            overflow-y: auto;
            overflow-x: hidden;

        }
    }

    .top-r {
        width: calc(100% - 260px);
        position: absolute;
        bottom: 0;
        right: 0;
        display: flex;
        flex-direction: column;
    }



    .typewrap {
        padding: 0 9px;
    }

    .typename {
        font-size: 20px;
    }

    .quesitem {
        display: flex;
        padding: 0px 10px;
        height: 54px;
        background: var(--main-bc-color);
        border-radius: 9px;
        align-items: center;
        margin-bottom: 6px;
        font-weight: 500;
        font-size: 15px;
        color: var(--explanatory-text-color);
        line-height: 21px;
    }

    .quesitem.active {
        background-color: var(--record-question-bg-color);
        border: 2px solid var(--primary-color);
        color: var(--primary-color);
    }



    .progress {
        flex: 1;
        margin: 0px 6px;
    }

    .percent {
        font-size: 12px;
        color: var(--explanatory-text-color);
        line-height: 14px;
        width: 40px;
    }



    .list {
        display: flex;
        width: calc(100vw - 250px);
        white-space: nowrap;
        // height: 100%;
        text-align: left;
        overflow: auto;
        // background-color: yellow;
    }





    /* 题目展示 */
    .wrap {
        flex: 1;
        width: calc(100vw - 260px);
        background-color: #0B423D;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
    }

    /* 学生列表 */
    .stus {
        height: 270px;
        display: flex;
        justify-content: space-between;
        background-color: var(--main-bc-color);
        position: relative;

        .stus-expand {
            z-index: 1;
            position: absolute;
            width: 40px;
            box-sizing: border-box;

            display: flex;
            justify-content: center;
            top: 0;
            left: calc(50% - 20px);
        }
    }

    .block {
        flex: 1;
        height: calc(100% - 9px);
        margin-top: 9px;
        position: relative;

        .block-border {
            position: absolute;
            left: 0px;
            height: 180px;
            margin-top: 16px;
            margin-bottom: 10px;
            min-height: 0px;
            width: 1px;
            background: var(--border-bar-color);
        }

        .block-random {
            display: v-bind(getRandomDisplay());
            position: absolute;
            right: 30px;
            top: 0;
        }

    }

    .block:first-child {
        .block-border {
            display: none;
        }

    }

    .right {
        color: var(--correct-color);
    }

    .wrong {
        color: var(--error-color);
    }

    .undo {
        color: var(--unfinished-color);
    }

    .tit_wrap {
        height: 48px;
        width: 100%;
        font-weight: 500;
        font-size: 21px;
        line-height: 36px;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        margin-top: 8px;
    }
}
</style>