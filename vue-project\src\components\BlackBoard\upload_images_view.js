import { BoardButton } from "@/drawboard/board_button";
import { BoardView } from "@/drawboard/board_view";
import { useDrawBoardStore } from "@/stores/drawboard_store";
import * as THREE from 'three';
import { ImageDisplayView } from "./image_display_view";
import { DrawMode, DrawZ, EditMode, PainterOrder } from "@/drawboard/draw_enums";
import { markRaw } from "vue";

export class UploadImagesView extends BoardView {

    constructor(application, pos, size, taskId) {
        super(application, pos, size)

        this.imageList = []
        this.taskId = taskId
        this.pageRatio = 1.0
        this.space = 0.05
        this.col = 4
        this.renderOrder = PainterOrder.background
        this.imageCacheMap = {}
        this.setupUI()
    }


    setupUI() {
        const space = this.space
        const selectorWidth = this.size.width / 40
        this.selectorWidth = selectorWidth

        let leftButton = BoardButton.buttonWithImageUrl(
            this.application,
            new THREE.Vector3(-this.size.width / 2 + selectorWidth / 2 + space, - this.size.height / 6, 0),
            { width: selectorWidth, height: selectorWidth },
            "img/choose_pictures.png", true)
        leftButton.onClick(() => {
            this.showImageSelector(true)
        })
        this.addSubView(leftButton)

        let rightButton = BoardButton.buttonWithImageUrl(
            this.application,
            new THREE.Vector3(this.size.width / 2 - selectorWidth / 2 - space, - this.size.height / 6, 0),
            { width: selectorWidth, height: selectorWidth },
            "img/choose_pictures.png", true)
        rightButton.onClick(() => {
            this.showImageSelector(false)
        })
        this.addSubView(rightButton)
    }


    showImageSelector(onLeft) {
        const drawBoardStore = useDrawBoardStore()
        drawBoardStore.imgsGroup = markRaw(this)
        drawBoardStore.uploadImageList = this.imageList
        drawBoardStore.showImgsSelector.show = true
        drawBoardStore.showImgsSelector.onLeft = onLeft
    }

    getOrGreatImageView(url,itemWidth,itemHeight){
        let imageView = null
        for(let item of this.imageList){
            if(item.imageUrl === url){
                imageView = item
                break
            }
        }
        
        if(!imageView){
            let imageUrl = url
            imageView = new ImageDisplayView(
                this.application,
                new THREE.Vector3(0, 0, 0),
                { width: itemWidth, height: itemHeight },
                imageUrl,true)
                imageView.onDeleted((view) => {
                this.deleteImage(view.imageUrl)
            })
            imageView.selected = false
            this.imageList.push(imageView)
            let application = this.application?.deref()
            if (application) {
                application.rootView.addDrawInSubView(imageView)
            }
            imageView.onDoubleClick((item) => {
                let application = item.application?.deref()
                if (application) {
                    if (item.isFull) {
                        item.setFullDisplay(false)
                        this.updateDisplay()
                        const center = new THREE.Vector3();
                        this.getWorldPosition(center);
                        const itemCenter = new THREE.Vector3();
                        item.getWorldPosition(itemCenter);
                        application.camera.position.set(center.x, itemCenter.y, DrawZ.initZ)
                    }
                    else {
                        item.setFullDisplay(true)
                        this.updateDisplay()
                        const center = new THREE.Vector3();
                        item.getWorldPosition(center);
                        const z = DrawZ.objcZ + (DrawZ.initZ - DrawZ.objcZ) * 0.4
                        application.camera.position.set(center.x, center.y, z)
                    }
                    const drawboardStore = useDrawBoardStore()
                    drawboardStore.previewMode = false
                }

            })

            imageView.onNextPage((item) => {
                let index = item.index
                let length = item.dataLength
                let page = index
                if (index + 1 < length) {
                    page = index + 1
                }
                else {
                    page = 0
                }
                item.setFullDisplay(false)
                let list = item.dataList?.deref()
                if (list) {
                    let nextItem = list[page]
                    nextItem.setFullDisplay(true)
                    this.updateDisplay()
                }
            })

            imageView.onPrevPage((item) => {
                let index = item.index
                let length = item.dataLength
                let page = index
                if (index - 1 >= 0) {
                    page = index - 1
                }
                else {
                    page = length - 1
                }
                item.setFullDisplay(false)
                let list = item.dataList?.deref()
                if (list) {
                    let preItem = list[page]
                    preItem.setFullDisplay(true)
                    this.updateDisplay()
                }
            })
        }
        return imageView
    }

    addImages(urls) {
        const drawBoardStore = useDrawBoardStore()
        drawBoardStore.showImgsSelector.show = false
        const space = this.space
        let col = this.col
        const size = this.size
        const itemWidth = (size.width - (this.selectorWidth + space * 2) * 2 - space * (col - 1)) / col
        const itemHeight = itemWidth / this.pageRatio
        this.itemWidth = itemWidth
        this.itemHeight = itemHeight
        urls.forEach((url, index) => {
            let imageView = this.getOrGreatImageView(url,itemWidth,itemHeight)
            this.addSubView(imageView)
        })
        this.imageList.forEach((item, index) => {
            item.setFullDisplay(false)
            item.selected = false
            item.visible = true
        })

        this.updateDisplay()
    }

    recoveryImageInitState() {
        this.imageList.forEach((item) => {
            item.setFullDisplay(false)
            item.selected = false
        })
        this.updateDisplay()
    }

    getLastImagePosition() {
        if (this.imageList.length > 0) {
            const last = this.imageList[this.imageList.length - 1];
            const pos = new THREE.Vector3();
            last.getWorldPosition(pos);
            return pos
        }
        return this.position
    }

    deleteImage(url) {
        const drawBoardStore = useDrawBoardStore()
        drawBoardStore.showImgsSelector.show = false

        let list = this.imageList.filter((item) => item.imageUrl === url)
        if (list.length > 0) {
            let view = list[0]
            let app = this.application?.deref()
            app.rootView.removeDrawInSubView(view)
            view.removeFromSuperView()
            view.dispose()
            let index = this.imageList.indexOf(view)
            this.imageList = this.imageList.filter((item) => item.imageUrl !== url)
            if (this.imageList.length === 0) {
                if (this.deleteAllCallback) {
                    this.deleteAllCallback()
                }
            }
            else {
                if (view.isFull) {
                    if (index >= this.imageList.length) {
                        index = 0
                    }
                    this.imageList[index].setFullDisplay(true)
                }
                this.updateDisplay()
            }
        }

    }

    onAllDelete(callback) {
        this.deleteAllCallback = callback
    }

    cancelEditWithoutThis(view){
        this.imageList.forEach((item, index) => {
            
            if(item != view){
                item.setEdit(false)
            }
        })
    }

    /// 将图片按顺序排列
    updateDisplay() {
        const space = this.space
        let col = this.col
        const itemWidth = this.itemWidth
        const itemHeight = this.itemHeight
        const size = this.size

        let list = this.imageList
        let fullList = this.imageList.filter(a => a.isFull === true)
        if (fullList.length > 0) {
            list = fullList
            let unfullList = this.imageList.filter(a => !(a.isFull === true))
            unfullList.forEach((item, i) => {
                item.visible = false
            })
        }
        else {
            let selectedList = this.imageList.filter(a => a.selected)
            if (selectedList.length > 0) {
                list = selectedList
                let unselecteds = this.imageList.filter(a => !a.selected)
                unselecteds.forEach((item, i) => {
                    item.visible = false
                })
            }
        }
        let length = this.imageList.length
        let weakList = new WeakRef(this.imageList)

        this.imageList.forEach((item, index) => {
            item.index = index
            item.dataLength = length
            item.dataList = weakList
            item.setFullIndex(index, length)
        })

        for (let index = 0; index < list.length; index++) {
            let item = list[index]
            item.visible = true

            // 计算 Group 的位置，使第一个 Group 的左上角位于屏幕 (0, 0) 位置
            var currentRow = Math.floor(index / col);
            var currentCol = index % col;

            const groupX = this.selectorWidth + space * 2 + (currentCol * (itemWidth + space)) - (size.width / 2) + (itemWidth / 2)
            const groupY = -(currentRow * (itemHeight + space)) + (size.height / 2) - (itemHeight / 2)

            item.position.set(groupX, groupY, 0)
            item.renderOrder = PainterOrder.background
            if(item&&item.imageView&&item.imageView.changeClipingLines){
                item.imageView.changeClipingLines()
            }
            
        }

        this.animate()
    }
}