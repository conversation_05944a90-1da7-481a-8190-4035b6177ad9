<template>
    <div class="stuinfo" v-if="stuInfo" @click.stop>
        <div class="stu-status" v-if="props.from == 'classroom'">
            <div class="online-text"
                :style="stuInfo.stuStatus == StuStatus.online ? '' : { color: 'var(--explanatory-text-color)' }">
                {{ stuInfo.stuStatus == StuStatus.online ? '在线' : '离线' }}</div>
            <div class="battery-img" v-if="stuInfo.stuStatus == StuStatus.online">
                <img :src="batteryImageName(stuInfo)">
            </div>
        </div>
        <img class="close" src="/icon/icon_close.svg" @click.stop="close">

        <div class="avatar">
            <img v-if="stuInfo?.headUrl" :src="stuInfo.headUrl">
            <img v-else src="/icon/icon_head.png">
        </div>
        <div class="name">
            {{ stuInfo?.name ?? '暂无' }}
        </div>
        <div class="sex-sno">性别：{{ stuInfo?.sex ? '男' : '女' }} <div style="margin-left: 64px;"> 学号：{{ stuInfo?.sno }}
            </div>
        </div>
        <div class="device">
            <div>设备地址：</div>
            <div class="input-mac">
                <RBPInput height="54" border-radius="15" v-model="inputMac" font-size="21" has-drop="true"
                    :data-list="deviceMacList" :show-delete-icon="false" :show-drop-icon="false"
                    @click-item="selectDevice"></RBPInput>
            </div>
        </div>
        <RBPButton class="submit" btn-text="提交" btn-type="big" :btn-selected="true" @click="submit"></RBPButton>
    </div>
</template>
<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { storeToRefs } from 'pinia'
import RBPInput from '../baseComponents/RBPInput.vue'
import RBPButton from '../baseComponents/RBPButton.vue'
import { useStudentInfoStore } from '@/stores/student_info_store'
import { arrangeMacAddress } from '@/classroom/classroom_helper'
import { useClassroomStore } from '@/stores/classroom'
import { Alert } from '@/utils/alert'
import { LocalRequest } from '@/local_request/local_request'
import { useRoute } from 'vue-router'
import { StuStatus } from '@/classroom/interact_enums.js'

const route = useRoute()
const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)
const studentInfoStore = useStudentInfoStore()
const { deviceList, stuInfo, showDeviceMacList } = storeToRefs(studentInfoStore)
const props = defineProps(
    {
        from: {
            default: 'manage'
        } //来自哪个页面
    }
)
const deviceMacList = computed(() => {
    //deviceList的值中包含inputMac的
    let data = deviceList.value.filter(item => item.includes(inputMac.value) && item !== inputMac.value)
    return Object.fromEntries(data.map(value => [value, value]));
})
const inputMac = ref('')
onMounted(() => {
    getMac()


})
watch(() => stuInfo.value, () => {
    getMac()
})

function batteryImageName(stu) {

    if (!stu.batteryLevel || stu.batteryLevel === 0) {
        return "/img/battery/icon_batterylow.png";
    } else if (stu.batteryLevel == 0xfe) {
        return "/img/battery/icon_battery_charging.png";
    } else if (stu.batteryLevel == 0xff) {
        return "/img/battery/icon_battery_complete.png";
    }
    return "/img/battery/icon_battery_" + stu.batteryLevel + ".png"
}
function close() {
    studentInfoStore.cleanData()
}
function getFocus() {
    showDeviceMacList.value = true
}
function getMac() {
    inputMac.value = stuInfo.value?.deviceMac ?? ''
}
async function submit() {
    if (inputMac.value == stuInfo?.value.deviceMac) {
        Alert.showSuccessMessage('保存成功')
        return
    }
    let res = await studentInfoStore.changeMacAddress(stuInfo.value, inputMac.value)
    if (res) {
        if (route.path == '/classroom') {
            stuInfo.value.stuStatus = StuStatus.offline
            await LocalRequest.changeStudentMac(stuInfo?.value.deviceMac ?? '', inputMac.value, stuInfo.value.studentId)
        }
        stuInfo.value.deviceMac = inputMac.value
        Alert.showSuccessMessage('保存成功')
        arrangeMacAddress(selectedClassroom.value)
    }
}
function selectDevice(item) {
    inputMac.value = item
    showDeviceMacList.value = false
}
</script>
<style lang="scss" scoped>
.stuinfo {
    position: relative;
    background-color: var(--main-bc-color);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 20px;
    border: 1px solid var(--border-bar-color);

    .stu-status {
        position: absolute;
        top: 8px;
        left: 8px;
        display: flex;
        align-items: center;

        .online-text {
            font-size: 14px;
            color: var(--correct-color);
        }

        .battery-img {
            margin-left: 8px;

            img {
                width: 20px;
                height: 11px;
                object-fit: contain;
            }
        }

    }

    .avatar {
        width: 108px;
        height: 108px;
        border-radius: 50%;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 47px;

        img {
            width: 100%;
            height: 100%;
        }
    }

    .close {
        position: absolute;
        bottom: 9px;
        right: 9px;
        cursor: pointer;
    }

    .name {
        margin-top: 12px;
        font-weight: 500;
        font-size: 33px;
        color: var(--text-color);
        line-height: 49px;
    }

    .sex-sno {
        margin-top: 9px;
        font-weight: 400;
        font-size: 21px;
        color: var(--secondary-text-color);
        line-height: 31px;
        text-align: center;
        display: flex;
        align-items: center;
    }



    .device {
        height: 54px;
        margin-top: 32px;

        align-items: center;
        display: flex;
        font-size: 16px;
        font-weight: 400;
        font-size: 21px;
        color: var(--text-color);

        .input-mac {
            width: 226px;
            margin-left: 15px;
            height: 54px;

            .el-input {
                text-align: center !important;
            }
        }

        // background-color: yellow;
        position: relative;

        .deviceList {
            width: 200px;
            height: 100px;
            background-color: white;
            border: 1px solid #c3c3c3;
            border-radius: 4px;
            position: absolute;
            top: 50px;
            right: -20px;
            overflow-y: scroll;
            scrollbar-width: none;

            &::-webkit-scrollbar {
                display: none;
            }

            .deviceItem {
                // background-color: red;
                margin-top: 4px;
                height: 25px;
                line-height: 20px;
                width: 100%;
                text-align: center;
                cursor: pointer;

            }
        }
    }

    .submit {
        margin-top: 47px;
        margin-bottom: 36px;
        margin-right: 0px;

    }
}
</style>