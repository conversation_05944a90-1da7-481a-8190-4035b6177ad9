import { defineStore } from "pinia";
import { ref } from "vue";
import { useDesktopStore } from "./desktop_store";
import { useDrawBoardStore } from "./drawboard_store";
import { useClassroomUIStore } from '@/stores/classroom_ui_store';
import { getZIndex } from '@/components/baseComponents/RBPZIndex.js'
import { useInteractStore } from '@/stores/interact_store'
import { remoteControl } from "@/remote_control/remote_control";
import { useVideoPlayStore } from "./video_play_store";


export const useOfficeFilesStore = defineStore("office-files-store", () => {

    const cacheList = ref([])
    const currentFileItem = ref(null)
    const minimized = ref(false)

    const prevPage = ref(1)
    const nextPage = ref(1)

    const isFull = ref(true)
    const remoteFlag = ref(0)

    let remoteStatus = ""

    const count = ref(0)

    const wpsRef = ref(null)
    const sdk = ref(null)
    const isFullScreen = ref(true)
    const officeType = ref('')

    function addOfficeFile(url, fileName) {
        const key = encodeURIComponent(url)
        const list = cacheList.value.filter(item => item.url === key)
        if (list.length > 0) {
            currentFileItem.value = list[0]
            return
        }
        const wpsiframe = document.createElement('iframe')
        wpsiframe.src = url
        wpsiframe.style.width = '100%'
        wpsiframe.style.height = '100%'
        const item = {
            url: key,
            orignUrl: url,
            fileName,
            wpsiframe
        }

        // 现在只显示一个在线文件 后面再重新设计
        // cacheList.value.push(item)
        currentFileItem.value = item
        minimized.value = false
        const desktopStore = useDesktopStore()
        desktopStore.drawMode = false

        const drawBoardStore = useDrawBoardStore()
        drawBoardStore.minimizePaperPenView()
        const videoStore = useVideoPlayStore()
        const classroomUIStore = useClassroomUIStore()
        if(videoStore.fileUrl||classroomUIStore.showVideoPlayView){
            videoStore.cleanData()
            classroomUIStore.showVideoPlayView = false

        }
        remoteControl.handleInteractState()
    }


    function removeOfficeFile(url) {
        const key = encodeURIComponent(url)
        cacheList.value = cacheList.value.filter(item => item.url !== key)
        if (currentFileItem.value && currentFileItem.value.url === key) {
            currentFileItem.value = null
            remoteControl.handleInteractState()
        }
    }

    function minimizeOfficeView() {
        if (currentFileItem.value) {
            minimized.value = true
            remoteControl.handleInteractState()
        }
    }

    function clear() {
        setZIndexIn()
        minimized.value = false
        cacheList.value = []
        currentFileItem.value = null
        prevPage.value = 1
        nextPage.value = 1
        isFull.value = true
    }

    function setZIndexIn() {
        wpsRef.value.style.zIndex = getZIndex('--wps-in-z-index')
        const classroomUIStore = useClassroomUIStore()
        wpsRef.value.style.height = classroomUIStore.mainContentHeight + 'px'
        setToolbarZIndexNormal()
    }
    function setZIndexOut() {
        wpsRef.value.style.zIndex = getZIndex('--wps-out-z-index')
        wpsRef.value.style.height = '100%'
        document.querySelector('.web-office-iframe').style.height = '100%'
        setToolbarZIndexDown()
    }

    async function enterFullScreen() {
        const app = sdk.value.Application
        const SlideShowSettings = await app.ActivePresentation.SlideShowSettings
        // 进入幻灯片播放模式
        await SlideShowSettings.Run()
        // await SlideShowSettings.SetPlayToolbarPosition({
        //     Style: {
        //         Show: {top: '100%'},
        //         Hidden: {top: '100%'}
        //     }
        // })

        await SlideShowSettings.SetPlayInkPosition({
            Style: {
                top: '94%'
            }
        })

        const SlideShowWindow = await app.ActivePresentation.SlideShowWindow
        SlideShowWindow.IsFullScreen = false
        isFullScreen.value = false

        isFull.value = true
        setZIndexOut()

        // console.log('获取焦点')
        const iframe = document.querySelector('#office-iframe')
        iframe.contentWindow.focus();

        // 打开缩略图
        // await app.ActivePresentation.SlideShowSettings.SetMiniThumbnailVisible(true)
        const view = await SlideShowWindow.View
        // 【播放模式】显示页码
        view.ShowPage = true
    }

    async function exitFullScreen() {
        isFull.value = false
        setZIndexIn()
        // console.log('获取焦点')
        const iframe = document.querySelector('#office-iframe')
        iframe.contentWindow.focus();
    }

    async function setFullScreen() {
        minimized.value = false
        if (!minimized.value) {
            const drawBoardStore = useDrawBoardStore()
            drawBoardStore.minimizePaperPenView()
        }

        setFullAndNotScreen()
    }

    function setFullAndNotScreen() {
        if (officeType.value == 'p') {
            isFull.value = !isFull.value
            if (isFull.value) {
                enterFullScreen()
            } else {
                exitFullScreen()
            }
        } else {
            isFull.value = !isFull.value
            if (isFull.value) {
                setZIndexOut()
            } else {
                setZIndexIn()
            }
        }
    }

    function setToolbarZIndexNormal() {
        //打开wps并且非全屏的时候正常toolbar的层级
        const classroomUIStore = useClassroomUIStore()
        classroomUIStore.changeToolbarZIndex(getZIndex('--toolbar-z-index'))
        classroomUIStore.changeSystemToolbarHalfScreenRefZIndex(getZIndex('--toolbar-ex-z-index'))

        const interactStore = useInteractStore()
        if (interactStore.showPaperPenView()) {
            //关闭纸笔互动的时候降低systemToolbar的层级
            const classroomUIStore = useClassroomUIStore()
            classroomUIStore.changeSystemToolbarZIndex(getZIndex('--toolbar-open-paperpen-z-index'))
            classroomUIStore.changeSystemToolbarHalfScreenRefZIndex(getZIndex('--toolbar-open-paperpen-z-index'))
        }
    }

    function setToolbarZIndexDown() {
        //打开wps并且全屏的时候降低toolbar的层级
        const classroomUIStore = useClassroomUIStore()
        classroomUIStore.changeToolbarZIndex(getZIndex('--toolbar-open-wps-full-z-index'))
        classroomUIStore.changeSystemToolbarHalfScreenRefZIndex(getZIndex('--toolbar-open-wps-full-z-index'))
    }

    return {
        cacheList,
        currentFileItem,
        minimized,
        prevPage,
        nextPage,
        isFull,
        count,
        wpsRef,
        sdk,
        isFullScreen,
        officeType,
        remoteFlag,
        remoteStatus,
        setFullAndNotScreen,
        setFullScreen,
        enterFullScreen,
        exitFullScreen,
        setZIndexIn,
        setZIndexOut,
        addOfficeFile,
        removeOfficeFile,
        minimizeOfficeView,
        clear,
        setToolbarZIndexDown,
        setToolbarZIndexNormal
    }
})