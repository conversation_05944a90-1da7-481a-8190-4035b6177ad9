import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { StuStatus, InteractStatus, Interact } from '@/classroom/interact_enums'
import { useInteractStore } from '@/stores/interact_store'
import { storeToRefs } from 'pinia'
import { STUDENT_INDEX_MAX, STUDENT_INDEX_SUBMIT } from '@/stores/answers_store'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { useTimesStore } from '@/stores/times_store'
import { useTeachPlanStore } from '@/stores/teach_plan'
import { useTimeKeeperStore } from '@/stores/time_keeper'
import { useStudentInfoStore } from '@/stores/student_info_store'
import { useScoreStatisticsStore } from '@/stores/score_statistics_store'
import { useStudentListStore } from '@/stores/student_list_store'
import { useRandomRollCallStore } from '@/stores/random_roll_call_store'
import { useVideoPlayStore } from '@/stores/video_play_store'
import { useEvaluationScoreStore } from '@/stores/evaluation_score_store'
import { loginInstance } from '@/login_instance/login_instance'

export const useClassroomStore = defineStore('classroom', () => {

    const scoreStatisticsStore = useScoreStatisticsStore()
    const studentInfoStore = useStudentInfoStore()
    const timeKeeperStore = useTimeKeeperStore()
    const teachPlanStore = useTeachPlanStore()
    const timesStore = useTimesStore()

    const interactStore = useInteractStore()
    const { currentSubmitIndex, currentSelectedIndex, onlineNum } = storeToRefs(interactStore)
    const classroomUIStore = useClassroomUIStore()

    const studentListStore = useStudentListStore()

    const randomRollCallStore = useRandomRollCallStore()

    const videoPlayStore = useVideoPlayStore()

    const evaluationScoreStore = useEvaluationScoreStore()

    /// 当前选中的教室
    const selectedClassroom = ref(null)
    const studentStatusUpdated = ref(0)

    //查看指定学生，当前被选择的学生
    const selectedStudents = ref([])

    /// 书写互动 已经随机过的学生
    const paperRandomlyStudents = ref([])

    const currentStudent = ref(null)
    /// 在线学生map {stuId: Student}
    const onlineStudents = ref(new Map())

    /// 加分
    const addScore = ref(null)

    function setCurrentStudent(student) {
        currentStudent.value = student
    }

    function setAddScore(score) {
        addScore.value = score
    }

    function setSelectedClassroom(classroom) {
        selectedClassroom.value = classroom
        // timesStore.startClock()
    }

    ///退出课堂调用
    function cleanData() {
        currentStudent.value = null
        onlineStudents.value = new Map()
        selectedStudents.value = []
        interactStore.cleanData()
        classroomUIStore.cleanData()
        timesStore.cleanData()
        teachPlanStore.cleanData()
        timeKeeperStore.cleanData()
        studentInfoStore.cleanData()
        scoreStatisticsStore.cleanData()
        studentListStore.cleanData()
        randomRollCallStore.cleanData()
        videoPlayStore.cleanData()
        evaluationScoreStore.cleanData()
    }

    function startInteract(mode, interactId, taskId) {
        selectedClassroom.value.studentList.forEach(stu => {
            stu.answerSubmitIndex = STUDENT_INDEX_MAX
            stu.respondedTime = 0
            stu.writePageInfo = null
            stu.writing = false
        })
        paperRandomlyStudents.value.length = 0
        selectedStudents.value.length = 0

        selectedClassroom.value.interactId = interactId
        selectedClassroom.value.taskId = taskId
        interactStore.didSelectInteract(mode, InteractStatus.undelivered)
    }

    ///结束互动调用
    function endInteract() {
        selectedClassroom.value.studentList.forEach(stu => {
            stu.answerSubmitIndex = STUDENT_INDEX_MAX
            stu.respondedTime = 0
            stu.writePageInfo = null
            stu.correct = false
            stu.writing = false
        })
        selectedClassroom.value.interactId = undefined
        selectedClassroom.value.taskId = undefined
        paperRandomlyStudents.value.length = 0
        selectedStudents.value.length = 0
        interactStore.endInteract()
    }

    function stopAnswer() {
        // 将所有学生的状态都变为已提交
        selectedClassroom.value.studentList.forEach(element => {
            if (element.answerSubmitIndex === STUDENT_INDEX_MAX) {
                element.answerSubmitIndex = STUDENT_INDEX_SUBMIT
            }
        });
    }

    /// 随机选中一个在线学生 (纸笔互动)
    function paperRandomOneStudent() {
        if (!selectedClassroom.value) {
            return null
        }
        const onlineStudents = selectedClassroom.value.studentList.filter(v => {
            const index = paperRandomlyStudents.value.indexOf(v.studentId)
            return v.stuStatus === StuStatus.online && index < 0
        })
        if (onlineStudents.length <= 0) {
            return null
        }
        selectedStudents.value = []
        const randomIndex = Math.floor(Math.random() * onlineStudents.length);
        const student = onlineStudents[randomIndex]
        selectedStudents.value.push(student)
        paperRandomlyStudents.value.push(student.studentId)
        return student
    }

    function setStudentOnline(studentId) {
        if (selectedClassroom.value) {
            let stu = selectedClassroom.value.idStudentMap.get(studentId)
            if (stu) {
                stu.stuStatus = StuStatus.online
                onlineStudents.value.set(studentId, stu)
            }
        }
    }

    function setStudentOffline(studentId) {
        if (selectedClassroom.value) {
            let stu = selectedClassroom.value.idStudentMap.get(studentId)
            if (stu) {
                stu.stuStatus = StuStatus.offline
                onlineStudents.value.delete(studentId)
            }
        }
    }

    function studentWriting(studentId, pageCode, paperId) {
        let stu = selectedClassroom.value.idStudentMap.get(studentId)
        if (!paperId) {
            stu.writing = true
            return
        }

        // 检查是否有其他学生已经记录了这个页码
        // 如果有并且学生ID不同，说明所有权已经转移，需要清理旧记录

        if (loginInstance && loginInstance.teacher && loginInstance.teacher.paperMatrix === false) {
            selectedClassroom.value.studentList.forEach(otherStu => {
                if (otherStu.studentId !== studentId &&
                    otherStu.writePageInfo &&
                    otherStu.writePageInfo.paperId === paperId) {

                    // 检查是否包含当前页码
                    if (otherStu.writePageInfo.pageCodes.has(pageCode)) {
                        // 从之前学生的记录中删除该页码
                        otherStu.writePageInfo.pageCodes.delete(pageCode)

                        // 如果学生没有任何页码记录了，清空writePageInfo
                        if (otherStu.writePageInfo.pageCodes.size === 0) {
                            otherStu.writePageInfo = null
                        }
                    }
                }
            })
        }

        // 更新当前学生的页码记录
        if (!stu.writePageInfo || stu.writePageInfo.paperId != paperId) {
            stu.writePageInfo = {
                paperId,
                pageCodes: new Set([pageCode])
            }
        } else {
            if (stu.writePageInfo) {
                stu.writePageInfo.pageCodes.add(pageCode)
            }
        }
    }

    function studentSubmit(studentId, answer) {
        let stu = selectedClassroom.value.idStudentMap.get(studentId)
        interactStore.studentSubmit(stu, answer)
        //学生数组排序
        studentStateChange()
        //更新学生列表状态
        studentStatusUpdated.value++
    }

    function studentStateChange() {
        selectedStudents.value = []
        onlineNum.value = 0

        selectedStudents.value = selectedClassroom.value.studentList.filter(stu => {
            return stu.interactSelect
        })
        selectedClassroom.value.studentList.filter(stu => {
            if (stu.stuStatus == StuStatus.online) {
                onlineNum.value++
                return true
            }
            return false
        })

        sortStudentList(selectedClassroom.value.studentList)
    }
    function sortStudentList(students) {
        students.sort((a, b) => {
            if (currentSubmitIndex.value != 0) {
                if (a.answerSubmitIndex == STUDENT_INDEX_MAX && b.answerSubmitIndex == STUDENT_INDEX_MAX) {
                    return sortStudentWithStatus(a, b)
                }
                return a.answerSubmitIndex - b.answerSubmitIndex
            }
            if (currentSelectedIndex.value != 0) {
                if (a.currentSelectedIndex == STUDENT_INDEX_MAX && b.currentSelectedIndex == STUDENT_INDEX_MAX) {
                    return sortStudentWithStatus(a, b)
                }
                return a.currentSelectedIndex - b.currentSelectedIndex
            }
            return sortStudentWithStatus(a, b)
        })
    }

    function sortStudentWithStatus(a, b) {
        let compare = a.stuStatus - b.stuStatus
        if (compare === 0) {
            return sortStudentWithName(a, b)
        }
        return compare;
    }

    function sortStudentWithName(a, b) {
        // 按名字排序
        let al = a.name.split('').map(c => c.charCodeAt(0));
        let bl = b.name.split('').map(c => c.charCodeAt(0));

        let len = al.length < bl.length ? al.length : bl.length;

        for (let i = 0; i < len; i++) {
            let codeUnitA = al[i];
            let codeUnitB = bl[i];
            if (codeUnitA !== codeUnitB) {
                return codeUnitA - codeUnitB;
            }
        }

        return al.length - bl.length;
    }

    function updateDeviceBattery(studentId, batteryLevel) {
        if (selectedClassroom.value) {
            let stu = selectedClassroom.value.idStudentMap.get(studentId)
            if (stu) {
                stu.batteryLevel = batteryLevel
            }
        }
    }

    function getDeviceMac(studentId,) {
        if (selectedClassroom) {
            let stu = selectedClassroom.value.idStudentMap.get(studentId)
            if (stu) {
                return stu.deviceMac
            }
            return null
        }
    }

    return {
        currentStudent,
        addScore,
        selectedClassroom,
        selectedStudents,
        studentStatusUpdated,
        cleanData,
        setAddScore,
        setCurrentStudent,
        paperRandomOneStudent,
        setSelectedClassroom,
        setStudentOnline,
        setStudentOffline,
        updateDeviceBattery,
        studentSubmit,
        stopAnswer,
        endInteract,
        startInteract,
        studentWriting,
        getDeviceMac
    }
})