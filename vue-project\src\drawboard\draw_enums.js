

export const DrawQuality = {
    FHD: 1920,//1K
    QHD: 2560,//2K
    WQHD: 3200,//3K
    UHD: 4096//4K
}
export const DrawQualityName = {
    FHD: "1K",//1K
    QHD: "2K",//2K
    WQHD: "3K",//3K
    UHD: "4K"//4K
}

/// 编辑模式 
export const EditMode = {
    Drawing: 0,  // 绘制
    Dragging: 1,  // 拖拽 移动、放大等
}


export const DrawMode = {
    pen: 0,  //
    eraser: 1,  //橡皮
}

export const DrawZ = {
    initZ: 6.0,  // 相机初始z坐标
    maxsZ: 12.0,  // 最小缩放数，相机拉越远物体越小
    objcZ: 4.0,  // 绘制所在平面的z坐标
    nearZ: 0.1,  // 低于0.1的z距离，则在相机中不可见
}


export const PainterOrder = {
    bottom: 1,
    background: 2,  //白色背景或者图片 
    snapShoot: 3,
    writePoints: 4,     // 书写笔迹
    planeGeometry: 5,   // 平面几何
    customDisplay: 6,
    studentName: 7,
    interactive: 8, // 可以交互的， 比如button等
    teacherPoints: 9,   //  老师批注笔迹
    eraserObject: 10,    // 橡皮
}


export const PenStyle = {
    Line2: 0,
    MeshLine: 1,
}


export const LineMode = {
    StuLineColor: 0x000000,
    A4LineColor: 0x000000,
    A4LineWidth: 0.0045,
    WorldLinwWidth: 0.0005
}

/// 元素类型
export const MeshType = {
    Image: 0,
    Button: 1,
    Group: 2
}


export const BoardColor = [
    0x000000,
    0xFFFFFF,
    0xC7C7C7,
    0xFF4301,
    0xF09440,
    0xF6D154,
    0x79D475,
    0x5FCCE5,
    0x3D7FF0,
    // 0xB744D4
]


export const BoardLineWidth = [
    1,
    2,
    6,
    
]

/// Meshline画笔粗细 修改lineWidth 这里也需要修改
export const BoardMeshLineMap = {
    1: 0.001,
    2: 0.006,
    6: 0.010,
    
}
//最大最小
export const PXLineWidth = [
    8, 24, 40,
]
//波动取值-画笔
export const PXLineWidthShow = {
    min:8,
    default:16,
    range:72
}

export const BoardLineRange = {
    min:1,
    default:2,
    range:11
}

export const BoardMeshLineRange = {
    min:0.004,
    
    range:0.036
}

// 橡皮画笔粗细
export const PxEraserWidths = [
    8, 24, 40
]

export const EraserWorldWidths = {
    8: 0.02,
    24: 0.05,
    40: 0.12,
}

// 橡皮画笔粗细-波动
export const PxEraserWidthShow = {
    min:16,
    default:24,
    boardInit:20,
    range:24
}

export const EraserWorldWidthRange = {
    min: 0.004,
    range:0.32
}
