<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="48px" viewBox="0 0 48 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>工具栏icon/下拉屏幕</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="0切图" transform="translate(-1775, -1011)">
            <g id="工具栏icon/下拉屏幕" transform="translate(1775, 1011)">
                <rect id="矩形" fill="#5F9A9E" opacity="0.551176525" x="0" y="0" width="48" height="48" rx="24"></rect>
                <rect id="矩形" stroke="#979797" fill="#D8D8D8" opacity="0" x="0.5" y="0.5" width="47" height="47"></rect>
                <g id="编组" transform="translate(16.1967, 12)" stroke="#FFFFFF" stroke-linecap="round" stroke-linejoin="round" stroke-width="2">
                    <g id="编组-2" transform="translate(2.8033, 0)">
                        <polyline id="路径" points="10 0 5 5 0 0"></polyline>
                    </g>
                    <line x1="14.8032787" y1="8" x2="0.803278689" y2="8" id="路径"></line>
                </g>
                <line x1="28" y1="25" x2="20" y2="25" id="路径" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                <text id="下拉屏幕" font-family="NotoSansCJKsc-Medium, Noto Sans CJK SC" font-size="9" font-weight="400" fill="#FFFFFF">
                    <tspan x="6" y="40">下拉屏幕</tspan>
                </text>
                <path d="M17.4,24.9615385 L15.75,24.9615385 L15,24.9615385 C13.8954305,24.9615385 13,24.066108 13,22.9615385 L13,9.13340001 L13,9.13340001 C13,8.48619895 13.4924865,7.96153846 14.1,7.96153846 L33.9,7.96153846 C34.50753,7.96153846 35,8.48619895 35,9.13340001 L35,22.9615385 C35,24.066108 34.1045695,24.9615385 33,24.9615385 L30.6,24.9615385 L30.6,24.9615385" id="路径" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </g>
        </g>
    </g>
</svg>