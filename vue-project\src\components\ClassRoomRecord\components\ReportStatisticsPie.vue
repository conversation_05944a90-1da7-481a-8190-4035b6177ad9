<template>
    <div class="report-pie-body">
        <div class="pie-content" id="pieBody">
        </div>
        <div class="pie-des">
            <div v-for="(item, index) in data" :key="index" class="pie-des-column">
                <div v-for="(child, childIndex) in item" :key="childIndex" class="pie-item">
                    <div class="pie-color" :style="{ backgroundColor: option.color[index * 6 + childIndex] }">

                    </div>
                    <div class="pie-text">
                        {{ child.title }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import * as echarts from 'echarts';
import { nextTick, onMounted, ref, watch } from 'vue';

function getRandomHexColor() {
  // 生成一个 0 到 0xFFFFFF（16777215）之间的随机数
  const randomInt = Math.floor(Math.random() * 0xffffff);
  // 转成十六进制并填充前导零（总长度6）
  const hex = randomInt.toString(16).padStart(6, '0');
  return `#${hex}`;
}

const option = ref({
    color: [
                '#44D7B6',
                '#FFD60A',
                '#E072FF',
                '#64D7EA',
                '#B8DF6A',
                '#A8BC25',
                '#14A699',
                '#25B0FA',
                '#FFC6A6',
                '#FF8A66',
                '#8968FF',
                '#425D50',
                '#ABF0D1',
                '#FFA286',
                '#F4B6DB',
                '#CAAAF3',
                '#20D8D8',
                '#A5AFA6',
                '#D8D192',
                '#CD9C00',
                '#FFB64D',
                '#9C847C',
                '#E57373',
                '#B968C7'
            ],

    series: [
        {
            
            label: {
                formatter: function (params) {
    return params.name.split('+')[0];
  },
                show: true,
                fontSize: 12, // 👈 设置文字大小
                color: '#333' // 可选：设置颜色
            },
            type: 'pie',
            data: [
              
            ],
            radius: ['20%', '70%'], // 调整这里扩大图形半径
            center: ['45%', '50%'], // 控制图形在容器中的位置
        }
    ],
    
});

const data = ref([
   []
])
const myChart = ref(null);
const props = defineProps({
    data: {
        type: Array,
        default: []
    }
})

function addColor(){
    while (true) {
        let  color = getRandomHexColor().toUpperCase()
        
        if(option.value.color.indexOf(color) == -1){
            option.value.color.push(color)
            break
        }
   
    }
}



function init() {
    if(myChart.value){
        myChart.value.dispose()
    }
    var chartDom = document.getElementById('pieBody');
    myChart.value = echarts.init(chartDom, null, {
        devicePixelRatio: Math.max(window.devicePixelRatio, 2)
    });
    myChart.value.setOption(option.value)
}

onMounted(() => {
    
})

watch(()=>props.data, () => {    
    data.value = [[]]
    let overLength = props.data.length - option.value.color.length
    if(overLength > 0){
        for(let i = 0; i < overLength; i++){
            addColor()
        }
    }
    option.value.series[0].data = []
     props.data.forEach((e)=>{
        let item = {
            value:e.percent,
            name:Math.round(e.percent * 100)+'%+'+props.data.indexOf(e),
            title:e.knowledgePoint,
        }
        option.value.series[0].data.push(item);
        if(data.value[data.value.length-1].length >= 6){
            data.value.push([])
        }
        data.value[data.value.length-1].push(item)
     })
    
    nextTick(()=>{
        init()
    })
},{deep:true,immediate:true})

</script>

<style lang="scss" scoped>
.report-pie-body {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    background: var(--main-bc-color);
    border-radius: 21px;
    border: 1px solid var(--border-bar-color);

    .pie-content {
        height: 100%;
        width: 480px;
    }

    .pie-des {
        height: 100%;
        box-sizing: border-box;
        padding: 8px 0px;
        margin-right: 24px;
        width: calc(100% - 504px);
        display: flex;
        overflow-x: auto;


        .pie-des-column {
            height: 100%;
            width: 160px;
            margin-right: 12px;
        }


        .pie-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            height: 32px;

            &:last-child {
                margin-bottom: 0px;
            }

            .pie-color {
                width: 16px;
                height: 16px;
                border-radius: 50%;
                margin-right: 4px;
            }

            .pie-text {
                flex: 1;
                max-lines: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-size: 16px;
                color: var(--secondary-text-color);
            }
        }

    }
}
</style>