<template>
    <div class="random-roll-call" :style="isShowRecord()?{zIndex:'var(--toolbar-random-roll-high-z-index)'}:''">
        <RBPAlert :showMini="true" width="60%" @close="closeRandomRollCall" @mini="miniClick" @click.stop>
            <template v-slot:rbpDiv>
                <div class="content-div">
                    <div class="content-header" :style="{ height: loginInstance.evaluation ? '40%' : '80%' }">
                        <div class="name-score">
                            <div class="name">
                                {{ stu?.name }}
                            </div>
                        </div>
                        <div class="score-div">
                            <div class="score" v-show="stu?.name != '随机点名'">
                                {{ stu?.score ?? 0 }}<span> 分</span>
                            </div>
                            <div class="addScore" v-show="addScore != ''"
                                :style="{ color: parseInt(addScore) > 0 ? RBPColors.colorD : RBPColors.colorC }">
                                {{ addScore }}<span> 分</span></div>
                        </div>
                    </div>
                    <div class="content-footer" :style="{ height: loginInstance.evaluation ? '60%' : '20%' }">
                        <div class="score-btns" v-if="!loginInstance.evaluation">
                            <div class="score-btn" v-for="(score, index) in scoreList" :key="index">
                                <RBPAddScore :score="score" @click="addScoreClick(score)"></RBPAddScore>
                                <div :style="{ width: '30px' }"></div>
                            </div>
                        </div>
                        <div class="score-tag" v-else>
                            <div class="add">
                                <div class="title">表扬</div>
                                <div class="list">
                                    <RBPAddScoreEva :item="item" v-for="(item, index) in evaluationAddList" :key="index"
                                        @evaluationItemClick="evaluationItemClick(item)"></RBPAddScoreEva>
                                </div>
                            </div>
                            <div class="add minus">
                                <div class="title">待改进</div>
                                <div class="list">
                                    <RBPAddScoreEva :item="item" v-for="(item, index) in evaluationMinusList"
                                        :key="index" @evaluationItemClick="evaluationItemClick(item)"></RBPAddScoreEva>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template #rbpBtns>
                <div class="btns">
                    <RBPButton btnText="换一名" :btnSelected="true" btnType="big"
                        :class="{ changeBtnDisable: changeBtnDisable }" @click="clickChange">
                    </RBPButton>
                </div>
            </template>
        </RBPAlert>
    </div>
</template>
<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useClassroomStore } from '@/stores/classroom.js'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import roomUpdater from '@/classroom/classroom_updater.js'
import { useRandomRollCallStore } from '@/stores/random_roll_call_store'
import { loginInstance } from '@/login_instance/login_instance'
import { useEvaluationScoreStore } from '@/stores/evaluation_score_store'
import { Alert } from '@/utils/alert'
import { useScoreAudioStore } from '@/stores/score_audio_store'
import RBPAlert from '@/components/baseComponents/RBPAlert.vue'
import RBPAddScore from '@/components/baseComponents/RBPAddScore.vue'
import RBPAddScoreEva from '@/components/baseComponents/RBPAddScoreEva.vue'
import RBPButton from '@/components/baseComponents/RBPButton.vue'
import { RBPColors } from '@/components/baseComponents/RBPColors.js'
import { remoteControl } from '@/remote_control/remote_control'

const scoreAudioStore = useScoreAudioStore()
const evaluationScoreStore = useEvaluationScoreStore()
const { evaluationAddList, evaluationMinusList } = storeToRefs(evaluationScoreStore)
const randomRollCallStore = useRandomRollCallStore()
const { stu, isMinimize, changeBtnDisable,countFlag } = storeToRefs(randomRollCallStore)
const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)
const classroomUIStore = useClassroomUIStore()
const { showRandomRollCall, mainContentTopSpace,showClassRoomRecord,hideClassRoomRecord,showRecordResult } = storeToRefs(classroomUIStore)

const addScore = ref('')
const calcTop = computed(() => {
    return `calc(50% + ${mainContentTopSpace.value}px)`
})
watch(countFlag,()=>{
    if(loginInstance.evaluation){
        evaluationItemClick(randomRollCallStore.nowScore)
    }else{
        addScoreClick(randomRollCallStore.nowScore)
    }
    
})
const scoreList = ref([
    '-2', '-1', '+1', '+2', '+3', '+5'
])

function clickChange(){
    randomRollCallStore.changeStu()
    remoteControl.handleInteractState()
}


function addScoreClick(score, type) {
    if (stu.value.name == '随机点名') {
        return
    }
    ///加分显示
    addScore.value = score
    changeBtnDisable.value = true

    ///1s之后隐藏
    setTimeout(async () => {
        addScore.value = ''
        stu.value.score = String(parseInt(stu.value.score ?? '0') + parseInt(score))

        //上传数据
        let success = await roomUpdater.studentsAddScore([stu.value], score)
        if (success) {
            // 播放音频 播放动画
            scoreAudioStore.play(score)
        }
        changeBtnDisable.value = false
        remoteControl.handleInteractState()
    }, 1000)
}

function isShowRecord(){
    return (showClassRoomRecord.value&&!hideClassRoomRecord.value)||showRecordResult.value
}


onMounted(async () => {
    if (loginInstance.evaluation) {
        await evaluationScoreStore.getEvaluationItemItemList()
    }
})
function miniClick() {
    isMinimize.value = true
    showRandomRollCall.value = false
    remoteControl.handleInteractState()
}

const closeRandomRollCall = () => {
    isMinimize.value = false
    showRandomRollCall.value = false
    remoteControl.handleInteractState()
}
async function evaluationItemClick(item) {
    if (stu.value.name == '随机点名') {
        return
    }
    ///加分显示
    if (parseInt(item.evaluationItemScore) > 0) {
        addScore.value = '+' + item.evaluationItemScore
    } else {
        addScore.value = item.evaluationItemScore
    }

    changeBtnDisable.value = true
    ///1s之后隐藏
    setTimeout(async () => {
        addScore.value = ''
        stu.value.selected = true

        // 是否有学生选中
        let haveSelected = false
        selectedClassroom.value.studentList.forEach(student => {
            if (student.selected) {
                haveSelected = true
            }
        })
        if (!haveSelected) {
            Alert.showErrorMessage('当前无选中学生')
            changeBtnDisable.value = false
            return
        }

        //上传数据
        let success = await roomUpdater.studentsAddScoreTypeTwo(item.evaluationItemId, item.evaluationItemScore)
        if (success) {
            // 播放音频 播放动画
            scoreAudioStore.play(item.evaluationItemScore)
        }
        changeBtnDisable.value = false
        remoteControl.handleInteractState()
    }, 1000)
}
</script>
<style lang="scss" scoped>
@import "@/assets/scss/mixin.scss";

.random-roll-call {
    position: absolute;
    height: 100%;
    width: 100%;
    z-index: var(--toolbar-random-roll-high-z-index);
}

.content-div {
    height: 100%;
    width: 100%;

    .content-header {
        width: 100%;
        background-color: var(--random-roll-top-bg-color);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-radius: 26px 26px 0px 0px;

        .name-score {
            display: flex;
            justify-content: center;
            align-items: center;
            box-sizing: border-box;
            padding: 0 20px;

            .name {
                font-size: 8vh;
                color: var(--text-color);
            }
        }

        .score-div {
            display: flex;

            .score {
                font-size: 6vh;
                white-space: nowrap;
                color: var(--secondary-text-color);

                span {
                    font-size: 3vh;
                }
            }

            .addScore {
                margin-left: 10px;
                font-size: 6vh;
                white-space: nowrap;

                span {
                    font-size: 3vh;
                }
            }
        }

    }

    .content-footer {
        width: 100%;
        display: flex;
        flex-direction: column;
        border-radius: 26px;

        .score-btns {
            width: 100%;
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;

            .score-btn {
                display: flex;
            }
        }

        .score-tag {
            width: 100%;
            flex: 1;
            overflow-y: auto;
            padding: 10px;
            box-sizing: border-box;

            .add {
                width: 100%;
                display: flex;
                flex-direction: column;
                border: 1px solid var(--border-bar-color);
                border-radius: 10px;
                box-sizing: border-box;

                .title {
                    margin-top: 10px;
                    margin-left: 42px;
                    font-size: 26px;
                    color: var(--text-color);
                }

                .list {
                    padding: 20px 0px;
                    display: grid;
                    grid-template-columns: repeat(10, 1fr);
                    gap: 10px;
                }
            }

            .minus {
                margin-top: 10px;
            }
        }
    }
}

.btns {
    width: 100%;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;

    .changeBtnDisable {
        background-color: var(--border-bar-color);
        pointer-events: none;
    }
}
</style>