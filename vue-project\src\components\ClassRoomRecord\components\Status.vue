<template>
    <div class="record-status">
        <div class="students">
            <div class="stu" :class="{ 'stu-active': stuId == item.studentExtId }"
                 v-for="(item, index) in list" :key="index" 
                @click="stuDetail(item)">
                {{ item.deviceAlias }}
            </div>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    list: Array,
    stuId: String,
    ansType: String
})
const emits = defineEmits(['setQuesStuInfo'])
function stuDetail(item) {
    emits('setQuesStuInfo', item)
}
function getBackground() {
    if (props.ansType == 'true') {
        return 'rgba(85, 181, 127, 0.2)'
    } else if (props.ansType == 'false') {
        return 'rgba(247, 78, 89, 0.2)'
    }
}
</script>

<style lang="scss" scoped>
.record-status {
    height:206px;
    overflow-y:auto;
    .stuslist::-webkit-scrollbar {
        display: none;
    }

    .students {
        display: flex;
        flex-wrap: wrap;
        gap:6px 12px;
        margin-left:12px;
        margin-bottom: 12px;
        .stu {
            width: calc((100% - 36px) / 4);
            height: 64px;
            background: var(--main-bc-color);
            border-radius: 15px;
            border: 2px solid var(--border-bar-color);
            display:flex;
            align-items:center;
            justify-content:center;
            word-break: keep-all;
            white-space: nowrap;
            overflow: hidden;
            padding: 0px 4px;
            box-sizing:border-box;
            font-weight: 500;
            font-size: 16px;
            color: var(--text-color);
            line-height: 31px;
            text-align: center;
        }
        .stu-active {
            background: var(--primary-color);
            color: var(--anti-text-color);
            border:none
        }
    }
}
</style>