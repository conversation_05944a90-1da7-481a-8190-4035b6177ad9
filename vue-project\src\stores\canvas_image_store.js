import { defineStore } from "pinia";
import { ref, watch } from "vue";
import Konva from 'konva';

export const useCanvasImageStore = defineStore('canvasImage', () => {

    //是否展示绘制
    const showCanvas = ref(true)
    //是否拖拽画布
    const showTuozhuaiCanvas = ref(false)

    const canvasW = ref(window.innerWidth - 260)
    const canvasH = ref(0)
    const color = ref('#F74E59')
    const lineWidth = ref(4)

    const canvasHomeContainer = ref(null);
    const canvasContainer = ref(null);
    const isDrawing = ref(false);
    const lastPos = ref({ x: 0, y: 0 });
    const layer = ref(null);
    const stage = ref(null);
    const imageObj = ref(null);
    const konvaImage = ref(null);
    const startX = ref(0);
    const startY = ref(0);
    const endX = ref(0);
    const endY = ref(0);
    const moveX = ref(0);
    const moveY = ref(0);
    const isDrag = ref(false);
    const totalWidth = ref(0.0);
    const totalHeight = ref(0.0);
    const mag = ref(1.1);
    const touchMsg = ref(1.04)
    const initialDistance = ref(0);

    const isFullScreen = ref(false);

    const imageUrl = ref('');

    const initialScale = ref(1);

    const isMouseDown = ref(false);

    const xxx = ref(0);
    const yyy = ref(0);

    const fingerCount = ref(0);

    const big = () => {
        canvasW.value = window.innerWidth;
        canvasH.value = window.innerHeight - 75;

        isFullScreen.value = true;
        canvasHomeContainer.value.style.position = 'fixed';
        canvasHomeContainer.value.style.width = canvasW.value;
        canvasHomeContainer.value.style.height = canvasH.value;
        canvasHomeContainer.value.style.zIndex = 10;

        setup(canvasW.value, canvasH.value)
        addImage(imageUrl.value)
    }

    const small = () => {
        isFullScreen.value = false;
        canvasHomeContainer.value.style.position = 'absolute';
        canvasHomeContainer.value.style.left = '0';
        canvasHomeContainer.value.style.top = '0';
        canvasHomeContainer.value.style.width = '100%';
        canvasHomeContainer.value.style.height = '100%';

        setup(window.innerWidth - 260, window.innerHeight - 150 - 215)
        addImage(imageUrl.value)
    }

    // 清空画布的方法
    const clearCanvas = () => {
        if (layer.value) {
            layer.value.destroyChildren()
            layer.value.draw()
        }
    };

    // 移除所有线条（Line）
    const removeAllLines = () => {
        // 获取图层中的所有子节点
        layer.value.find('Line').forEach((line) => {
            line.destroy(); // 移除线条
        });

        layer.value.batchDraw(); // 更新图层
    };

    // 还原
    function zoomYuanClick() {
        stage.value.scaleX(1);
        stage.value.scaleY(1);
        stage.value.x(0);
        stage.value.y(0);
        startX.value = 0;
        startY.value = 0;
        endX.value = 0;
        endY.value = 0;
        moveX.value = 0;
        moveY.value = 0;
        totalWidth.value = canvasW.value;
        totalHeight.value = canvasH.value;
        showCanvas.value = true;
        showTuozhuaiCanvas.value = false;
        initialScale.value = 1;
        xxx.value = 0;
        yyy.value = 0;
        endDraging();
    }

    // 缩放
    function zoomClick() {

    }

    // 开启拖拽
    function startDraging() {
        isDrag.value = true
        // stage.value.draggable(true);
    }

    // 关闭拖拽
    function endDraging() {
        isDrag.value = false
        // stage.value.draggable(false);
    }

    // 添加图片
    function addImage(url) {
        imageUrl.value = url;
        zoomYuanClick();
        clearCanvas();
        // 创建 Konva 层
        layer.value = new Konva.Layer();
        stage.value.add(layer.value);

        // 创建一个 Image 对象
        imageObj.value = new Image();
        imageObj.value.src = url;


        // 等待图片加载完成后添加到画布
        imageObj.value.onload = () => {
            let width = stage.value.width() - 40;
            let height = (imageObj.value.height / imageObj.value.width * width);
            let x = (stage.value.width() - width) / 2;
            let y = (stage.value.height() - height) / 2;

            if (imageObj.value.height > imageObj.value.width) {
                height = totalHeight.value - 20;
                width = (imageObj.value.width / imageObj.value.height * height);
                x = (stage.value.width() - width) / 2;
                y = (stage.value.height() - height) / 2;
            }

            konvaImage.value = new Konva.Image({
                image: imageObj.value,  // 使用加载好的图片
                x: x,                  // 设置图片的 x 坐标
                y: y,                  // 设置图片的 y 坐标
                width: width,             // 设置图片宽度
                height: height,            // 设置图片高度
            });

            // 将图片添加到图层
            layer.value.add(konvaImage.value);
            layer.value.draw();  // 绘制图层
        };
    }

    function setStageHeight(height) {
        if(stage.value) {
            // stage.value.height(height);
        }
    }

    function setup(width, height) {
        if (stage.value) {
            stage.value.destroy();
        }
        totalWidth.value = width;
        totalHeight.value = height;
        // 创建 Konva 舞台
        stage.value = new Konva.Stage({
            container: canvasContainer.value,
            width: width,
            height: height,
        });

        // 开始拖拽
        stage.value.on('dragstart', (e) => {
            startX.value = stage.value.x();
            startY.value = stage.value.y();
        })

        // 停止拖拽
        stage.value.on('dragend', (e) => {
            endX.value = stage.value.x();
            endY.value = stage.value.y();
            moveX.value = endX.value - startX.value;
            moveY.value = endY.value - startY.value;
        })

        stage.value.on('dragmove', (e) => {
            
        })

        // 滑动滚轮放大缩小
        canvasContainer.value.addEventListener('wheel', (e) => {
            let magnification = (e.deltaY > 0 ? (1 / mag.value) : mag.value);
            setScaleXY(magnification);
        })

        // 监听鼠标事件
        stage.value.on('mousedown', handleMouseDown);
        stage.value.on('mousemove', handleMouseMove);
        stage.value.on('mouseup', handleMouseUp);

        stage.value.on('touchstart', handleTouchStart);
        stage.value.on('touchmove', handleTouchMove);
        stage.value.on('touchend', handleTouchUp);
    }

    function setTouchScaleXY(magnification) {
        if (magnification < 1) {
            return;
        }
        stage.value.scaleX(magnification);
        stage.value.scaleY(magnification);

        let x = xxx.value - (stage.value.width() * (stage.value.scaleX() - 1) / 2);
        let y = yyy.value - (stage.value.height() * (stage.value.scaleY() - 1) / 2);

        stage.value.x(x);
        stage.value.y(y);
    }

    function setScaleXY(magnification) {
        if(stage.value.scaleX() * magnification < 1) {
            return;
        }
        stage.value.scaleX(stage.value.scaleX() * magnification);
        stage.value.scaleY(stage.value.scaleY() * magnification);

        let x = xxx.value - (stage.value.width() * (stage.value.scaleX() - 1) / 2);
        let y = yyy.value - (stage.value.height() * (stage.value.scaleY() - 1) / 2);
        stage.value.x(x);
        stage.value.y(y);
    }

    // 触屏开始拖拽
    const dragStart = (event) => {
        const touch1 = event.evt.touches[0];
        let rect = canvasContainer.value.getBoundingClientRect();
        startX.value = (touch1.pageX - rect.left);
        startY.value = (touch1.pageY - rect.top);
        xxx.value = stage.value.x();
        yyy.value = stage.value.y();
    }

    // 触屏拖拽
    const dragMove = (event) => {
        const touch1 = event.evt.touches[0];
        let rect = canvasContainer.value.getBoundingClientRect();
        endX.value = (touch1.pageX - rect.left);
        endY.value = (touch1.pageY - rect.top);
        moveX.value = endX.value - startX.value;
        moveY.value = endY.value - startY.value;
        stage.value.x(xxx.value + moveX.value)
        stage.value.y(yyy.value + moveY.value)
    }

    // 触碰拖拽结束
    const dragEnd = (event) => {
        xxx.value = stage.value.x();
        yyy.value = stage.value.y();
    }

    // 鼠标开始拖拽
    const mouseDragStart = (event) => {
        startX.value = event.evt.offsetX;
        startY.value = event.evt.offsetY;
        xxx.value = stage.value.x();
        yyy.value = stage.value.y();
    }

    // 鼠标拖拽
    const mouseDragMove = (event) => {
        endX.value = event.evt.offsetX;
        endY.value = event.evt.offsetY;
        moveX.value = endX.value - startX.value;
        moveY.value = endY.value - startY.value;
        stage.value.x(xxx.value + moveX.value)
        stage.value.y(yyy.value + moveY.value)
    }

    // 鼠标结束拖拽
    const mouseDragEnd = () => {
        xxx.value = stage.value.x();
        yyy.value = stage.value.y();
    }

    const handleTouchStart = (event) => {
        fingerCount.value = event.evt.touches.length;
        if (event.evt.touches.length === 2) {
            // 计算两个触摸点之间的初始距离
            initialDistance.value = getTouchDistance(event.evt);
            initialScale.value = stage.value.scaleX(); //当前的缩放比例
        } else if (event.evt.touches.length === 1) {
            if (isDrag.value) {
                //开始移动
                dragStart(event)
                return
            }
            isDrawing.value = true;

            const touch1 = event.evt.touches[0];
            let rect = canvasContainer.value.getBoundingClientRect();
            lastPos.value = {
                x: (touch1.pageX - rect.left - stage.value.x()) / stage.value.scaleX(),
                y: (touch1.pageY - rect.top - stage.value.y()) / stage.value.scaleY(),
            };
        }
    }

    const handleTouchMove = (event) => {
        if (event.evt.touches.length === 2) {
            const currentDistance = getTouchDistance(event.evt);
            let magnification = ((currentDistance - initialDistance.value) / initialDistance.value);
            setTouchScaleXY(magnification + initialScale.value);

        } else if (fingerCount.value === 1) {
            if (isDrag.value) {
                // 移动
                dragMove(event)
                return
            }
            if (!isDrawing.value) {
                return;
            }

            const touch1 = event.evt.touches[0];
            let rect = canvasContainer.value.getBoundingClientRect();
            const pos = {
                x: (touch1.pageX - rect.left - stage.value.x()) / stage.value.scaleX(),
                y: (touch1.pageY - rect.top - stage.value.y()) / stage.value.scaleY(),
            };
            // 创建一条从上一个点到当前位置的线条
            const line = new Konva.Line({
                points: [lastPos.value.x, lastPos.value.y, pos.x, pos.y],
                stroke: color.value,  // 画笔颜色
                strokeWidth: lineWidth.value,   // 画笔宽度
                lineCap: 'round',
                lineJoin: 'round',
            });

            layer.value.add(line);
            layer.value.batchDraw();

            lastPos.value = pos;
        }
    }

    const handleTouchUp = (event) => {
        dragEnd(event);
        isDrawing.value = false;
        fingerCount.value = 0;
        if (event.evt.touches.length < 2) {
            // 如果没有多于两个触摸点，重置距离和缩放比例
            initialDistance.value = 0;
        }
    }

    const getTouchDistance = (event) => {
        const dx = event.touches[0].clientX - event.touches[1].clientX;
        const dy = event.touches[0].clientY - event.touches[1].clientY;
        return Math.sqrt(dx * dx + dy * dy);
    }

    // 开始绘制
    const handleMouseDown = (event) => {
        isMouseDown.value = true;
        if (isDrag.value) {
            mouseDragStart(event)
            return
        }
        isDrawing.value = true;

        lastPos.value = {
            x: (event.evt.offsetX - stage.value.x()) / stage.value.scaleX(),
            y: (event.evt.offsetY - stage.value.y()) / stage.value.scaleY(),
        };
    };

    // 绘制线条
    const handleMouseMove = (event) => {
        if (!isMouseDown.value) {
            return
        }
        if (isDrag.value) {
            mouseDragMove(event);
            return;
        }
        if (!isDrawing.value) {
            return;
        }

        const pos = {
            x: (event.evt.offsetX - stage.value.x()) / stage.value.scaleX(),
            y: (event.evt.offsetY - stage.value.y()) / stage.value.scaleY(),
        };
        // 创建一条从上一个点到当前位置的线条
        const line = new Konva.Line({
            points: [lastPos.value.x, lastPos.value.y, pos.x, pos.y],
            stroke: color.value,  // 画笔颜色
            strokeWidth: lineWidth.value,   // 画笔宽度
            lineCap: 'round',
            lineJoin: 'round',
        });

        layer.value.add(line);
        layer.value.batchDraw();

        lastPos.value = pos;
    };

    // 结束绘制
    const handleMouseUp = () => {
        mouseDragEnd();
        isMouseDown.value = false;
        isDrawing.value = false;
    };

    return {
        canvasW, canvasH, color, lineWidth, canvasContainer, isDrawing, lastPos, layer,
        stage, imageObj, konvaImage, startX, startY, endX, endY, moveX, moveY, isDrag,
        totalWidth, totalHeight, mag, touchMsg, initialDistance, canvasHomeContainer,
        isFullScreen, showCanvas, showTuozhuaiCanvas,
        clearCanvas, addImage, setup, startDraging, endDraging, removeAllLines, 
        big, small, setStageHeight,
    }
})