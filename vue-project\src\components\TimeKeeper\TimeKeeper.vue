<template>
    <div class="timeKeeper">
        <RBPAlert :showMini="true" :isSupportClose="false" width="60%" @close="exitClick" @mini="miniClick" @click.stop>
            <template v-slot:rbpDiv>
                <div class="content-div">
                    <div class="content-header">
                        <div class="wheel">
                            <RBPCarousel @updateTime="onSlideChangeTime" v-if="!wheelNext"></RBPCarousel>
                            <div class="wheel-item" v-show="wheelNext">
                                <Swiper :direction="'vertical'" loop="true" class="mySwiper"
                                    @slideChange="onSlideChangeHours" :modules="[Controller]"
                                    @swiper="setControlledSwiperHours">
                                    <swiper-slide class="mySwiperItem" v-for="i in 24" :key="i">
                                        {{ (i - 1).toString().padStart(2, '0') }}
                                    </swiper-slide>
                                </Swiper>
                            </div>
                            <div class="maohao" v-show="wheelNext">:</div>
                            <div class="wheel-item" v-show="wheelNext">
                                <Swiper :direction="'vertical'" loop="true" class="mySwiper"
                                    @slideChange="onSlideChangeMinutes" :modules="[Controller]"
                                    @swiper="setControlledSwiperMinutes">
                                    <swiper-slide class="mySwiperItem" v-for="i in 60" :key="i">
                                        {{ (i - 1).toString().padStart(2, '0') }}
                                    </swiper-slide>
                                </Swiper>
                            </div>
                            <div class="maohao" v-show="wheelNext">:</div>
                            <div class="wheel-item" v-show="wheelNext">
                                <Swiper :direction="'vertical'" loop="true" class="mySwiper"
                                    @slideChange="onSlideChangeSeconds" :modules="[Controller]"
                                    @swiper="setControlledSwiperSeconds">
                                    <swiper-slide class="mySwiperItem" v-for="i in 60" :key="i">
                                        {{ (i - 1).toString().padStart(2, '0') }}
                                    </swiper-slide>
                                </Swiper>
                            </div>
                            <div class="whell-alert" v-show="isCounting"></div>
                        </div>
                    </div>
                    <div class="content-footer">
                        <div class="btns">
                            <div class="time" :class="{ active: item.selected }" v-for="(item, index) in timeList"
                                :key="index" @click="selectTime(item)">
                                {{ item.text }}
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template #rbpBtns>
                <div class="rbpBtns">
                    <div class="timer">
                        <RBPSegButton :options="options" :currentValue="timerValue" @updateCurrentValue="updateTimer"
                            :disable="isCounting"></RBPSegButton>
                    </div>
                    <div class="start">
                        <RBPTimeButton :isCounting="isCounting" @startCountdown="startCountdown"></RBPTimeButton>
                    </div>
                </div>
            </template>
        </RBPAlert>
    </div>
</template>
<script setup>
import { ref, computed, getCurrentInstance, watch, defineEmits } from 'vue'
import { Swiper, SwiperSlide, useSwiper } from 'swiper/vue'
import { Controller } from 'swiper/modules'
import 'swiper/css'
import { useTimeKeeperStore } from '@/stores/time_keeper.js'
import { storeToRefs } from 'pinia'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { Alert } from '@/utils/alert'
import RBPAlert from '@/components/baseComponents/RBPAlert.vue'
import { RBPColors } from '@/components/baseComponents/RBPColors.js'
import RBPSegButton from '@/components/baseComponents/RBPSegButton.vue'
import RBPTimeButton from '@/components/baseComponents/RBPTimeButton.vue'
import RBPCarousel from '@/components/baseComponents/RBPCarousel.vue'
import { remoteControl } from '@/remote_control/remote_control'

const wheelNext = ref(false)
const { proxy } = getCurrentInstance()
const timeKeeperStore = useTimeKeeperStore()
const { totalSeconds, currentSeconds, isCounting,
    hours, minutes, seconds, timerValue, options, timeList,
    controlledSwiperHours,
    controlledSwiperMinutes,
    controlledSwiperSeconds,
    timer } = storeToRefs(timeKeeperStore)
const classroomUIStore = useClassroomUIStore()
const { showTimeKeeper, mainContentTopSpace } = storeToRefs(classroomUIStore)
const calcTop = computed(() => {
    return `calc(50% + ${mainContentTopSpace.value}px)`
})

function updateTimer(item) {
    timerValue.value = item
    remoteControl.handleInteractState()
}

watch(() => timerValue.value, () => {
    setCurrentSeconds()
})

function setCurrentSeconds() {
    if (timerValue.value == '正计时') {
        currentSeconds.value = 0
    } else if (timerValue.value == '倒计时') {
        currentSeconds.value = totalSeconds.value
    }
}

function startCountdown() {
    if (totalSeconds.value > 0) {
        isCounting.value = !isCounting.value
        if (!timer.value) {
            timeKeeperStore.createTimer()
        }
    } else {
        Alert.showErrorMessage('请设置计时时间')
    }
}

const swiper = useSwiper()


const setControlledSwiperHours = (swiper) => {
    controlledSwiperHours.value = swiper
}
const setControlledSwiperMinutes = (swiper) => {
    controlledSwiperMinutes.value = swiper
}
const setControlledSwiperSeconds = (swiper) => {
    controlledSwiperSeconds.value = swiper
}
function resetTimeButtons() {
    timeList.value.forEach((item) => {
        item.selected = false
    })
}
function onSlideChangeTime() {
    if (!isCounting.value) {
        resetTimeButtons()
        timeKeeperStore.setTotalSecondsByHourMinuteSecond()
        setCurrentSeconds()
    }
}
function onSlideChangeHours(a) {
    if (!isCounting.value) {
        resetTimeButtons()
        hours.value = a.realIndex
        timeKeeperStore.setTotalSecondsByHourMinuteSecond()
        setCurrentSeconds()
    }
}
function onSlideChangeMinutes(a) {
    if (!isCounting.value) {
        resetTimeButtons()
        minutes.value = a.realIndex
        timeKeeperStore.setTotalSecondsByHourMinuteSecond()
        setCurrentSeconds()
    }
}
function onSlideChangeSeconds(a) {
    if (!isCounting.value) {
        resetTimeButtons()
        seconds.value = a.realIndex
        timeKeeperStore.setTotalSecondsByHourMinuteSecond()
        setCurrentSeconds()
    }
}
function miniClick() {
    showTimeKeeper.value = false
    remoteControl.handleInteractState()
}

function exitClick() {
    timeKeeperStore.cleanData()
    resetTimeButtons()
    showTimeKeeper.value = false
    remoteControl.handleInteractState()
}
function selectTime(item) {
    ///重置计时器
    timeKeeperStore.cleanData()

    ///重置按钮
    resetTimeButtons()
    item.selected = true

    ///开始计时
    totalSeconds.value = item.value
    setCurrentSeconds()
    timeKeeperStore.currentSecondsToHourMinuteSecond()
    startCountdown()
    remoteControl.handleInteractState()
}
</script>
<style lang="scss" scoped>
$wheel-height: 200px;

.timeKeeper {
    position: absolute;
    height: 100%;
    width: 100%;
    // z-index: var(--toolbar-top-z-index);
    z-index: var(--toolbar-top-time-keeper-z-index);
}

.content-div {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;

    .content-header {
        background-color: var(--timer-header-bg-color);
        flex: 1;
        padding: 0px 50px;
        border-radius: 26px 26px 0 0;
        display: flex;
        justify-content: center;
        align-items: center;

        .wheel {
            width: 100%;
            overflow: hidden;
            display: flex;
            align-items: center;
            position: relative;

            .wheel-item {
                width: 30%;
                height: $wheel-height;
                border-radius: 10px;
                position: relative;

                .mySwiper {
                    width: 100%;
                    height: $wheel-height;

                    .mySwiperItem {
                        font-size: 160px;
                        text-align: center;
                        height: $wheel-height;
                        line-height: $wheel-height;
                        color: var(--timer-time-color);
                    }
                }
            }

            .maohao {
                font-size: 100px;
                color: var(--timer-time-color);
                width: 5%;
                text-align: center;
            }

            .whell-alert {
                background-color: transparent;
                position: absolute;
                width: 100%;
                height: 100%;
                z-index: 999;
            }
        }
    }

    .content-footer {
        padding: 20px 50px;

        .btns {
            width: 100%;
            height: 100px;
            display: grid;
            grid-gap: 10px;
            grid-template-columns: repeat(6, 1fr);

            .time {
                background-color: var(--main-bc-color);
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                border: 1px solid var(--border-bar-color);
                color: var(--text-color);
                border-radius: 15px;
            }

            .active {
                background-color: var(--primary-color);
                color: var(--anti-text-color);
            }
        }
    }
}

.rbpBtns {
    width: 100%;
    height: 180px;
    position: relative;
    margin-bottom: 20px;

    .timer {
        position: absolute;
        left: 39px;
        bottom: 10px;
    }

    .start {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 20px;
    }
}
</style>