/**
水平进度条
*/
<template>
    <div class="rbp-vertical-progress-bar">
        <div class="persons">
            {{ props.persons }}
        </div>
        <div :style="{ height: '4px' }"></div>
        <div class="barchat">
            <div class="sub" :style="{ height: props.accuracy }">
            </div>
        </div>
        <RBPLine></RBPLine>
        <div :style="{ height: '4px' }"></div>
        <div class="option">
            {{ props.option }}
        </div>
    </div>
</template>
<script setup>
import { defineProps } from 'vue'
import { RBPColors } from '@/components/baseComponents/RBPColors.js'
import RBPLine from '@/components/baseComponents/RBPLine.vue';

const props = defineProps({
    option: String,
    accuracy: String,
    color: String,
    persons: String,
})
</script>
<style lang="scss" scoped>
.rbp-vertical-progress-bar {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    width: 100%;

    .persons {
        width: 100%;
        font-size: 15px;
        color: var(--progress-bar-explanatory-text-color);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .barchat {
        background-color: var(--progress-bar-bg-color);
        flex: 1;
        width: 18px;
        border-radius: 15px 15px 0 0;
        cursor: pointer;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: center;

        .sub {
            width: 100%;
            background-color: v-bind("props.color");
            border-radius: 15px 15px 0 0;
        }
    }

    .option {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 15px;
        color: var(--progress-bar-explanatory-text-color);
    }
}
</style>