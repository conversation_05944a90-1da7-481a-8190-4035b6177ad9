import { BoardView } from "@/drawboard/board_view";
import * as THREE from 'three';
import { Painter<PERSON>rder } from "@/drawboard/draw_enums";
import { BoardLabel } from "@/drawboard/board_label";
import { CircleLabel } from "./circle_label";
import { BoardImageView } from "@/drawboard/board_image_view";
import { RecordReviewCorrectView } from "./record_review_correct_view";
export const  RecordViewType= {
    
    answers: 0, //答案
    orders: 1, // 顺序
    corrects:2 // 批改
}

export class RecordSingleStudentPaperView extends BoardView {

    constructor(application, pos, size, image,studentId) {
        super(application, pos, size)
        this.image = image
        this.studentId = studentId
        //视图存储
        this.viewMap = {
           

        }
        this.viewMap[RecordViewType.answers] = []
        this.viewMap[RecordViewType.orders] = []
        this.viewMap[RecordViewType.corrects] = []

        this.setupUI()
    }


    setupUI() {
        if (this.image.statistics) {
            let ratio = 1
            let isPaper = this.image.isPaper === undefined ? true : this.image.isPaper
            if (isPaper) {
                ratio = this.size.width / 21000  // 21000 * 29700
            } else {
                ratio = this.size.width / 1190  // 1190 * 1684
            }
            for (let i = 0; i < this.image.statistics.length; i++) {
                let area = this.image.statistics[i]
                area.H = area.areaH * ratio
                area.W = area.areaW * ratio
                area.X = area.areaX * ratio
                area.Y = area.areaY * ratio
                //添加答案
                if (area.quesAnswer) {
                    let ansView = null
                    ansView = new BoardImageView(
                        this.application,
                        new THREE.Vector3(0, 0, 0),
                        { width: this.size.width / 4, height: this.size.height / 4 },
                        true            
                    )
                    ansView.setImageUrl(
                        `https://www.zhihu.com/equation?tex=\\color{red}{${encodeURI(area.questionType == 3 ? this.getTrueOrFalse(area.quesAnswer) : area.quesAnswer)}}`,
                        (ratio, index,thisView) => {
                            let nowArea = this.image.statistics[index]
                            let width = this.size.width / 60
                            let x = -this.size.width / 2 + nowArea.X + nowArea.W - width / 2
                            //获取回调后重置UI
                            let height = width / ratio
                            let y = this.size.height / 2 - nowArea.Y - nowArea.H + height / 2
                            thisView.position.set(x, y, 0)
                            thisView.setSize({ width, height })
                            thisView.animate()
                        },i)
                        ansView.visible = false
                        ansView.renderOrder = PainterOrder.customDisplay
                        this.addSubView(ansView)
                        this.viewMap[RecordViewType.answers].push(ansView)
                }
                //添加序号
                if (area.indexs) {
                    let numberS = this.size.width / 30
                    let numberX = -this.size.width / 2 + area.X + numberS / 2
                    let numberY = this.size.height / 2 - area.Y - numberS / 2
                    let circleLabel = new CircleLabel(
                        this.application,
                        new THREE.Vector3(numberX, numberY, 0),
                        { width: numberS, height: numberS },
                        area.indexs + "",
                    )
                    circleLabel.renderOrder = PainterOrder.studentName
                    circleLabel.visible = false
                    this.addSubView(circleLabel)
                    this.viewMap[RecordViewType.orders].push(circleLabel)
                }
                //添加批改结果
                let correctS = this.size.width / 30
                let correctX = -this.size.width / 2 + area.X + area.W/2
                let correctY = this.size.height / 2 - area.Y - area.H/2
                let correctView = new RecordReviewCorrectView(
                    this.application,
                    new THREE.Vector3(correctX, correctY, 0),
                    { width: area.W, height: area.H },
                    area,
                    correctS,
                    this.studentId
                )
                this.addSubView(correctView)
                this.viewMap[RecordViewType.corrects].push(correctView)
                //     let correctX = -this.size.width / 2 + area.X + area.W - correctS / 2
                // let correctY = this.size.height / 2 - area.Y - correctS / 2
                // if (area.isHandle == 0 || (this.statisticType && area.questionType == 1)) {
                //     let nameLabel = new BoardLabel(
                //         this.application,
                //         new THREE.Vector3(correctX, correctY, 0),
                //         { width: area.W, height: area.H },
                //         area.questionType == 1 ? `${area.score}分` : "未批",
                //         { fontSize: 0.012, color: 0xff0000, align: 'center' })
                //     nameLabel.renderOrder = PainterOrder.studentName

                //     this.addSubView(nameLabel)
                //     this.viewMap[RecordViewType.corrects].push(nameLabel)

                // } else { 
                //     let correctView = new BoardImageView(
                //         this.application,
                //         new THREE.Vector3(correctX, correctY, 0),
                //         { width: correctS, height: correctS },
                //         true
                //     )
                //     correctView.setImageUrl(`/img/record/${area.markPoint == 0 ? 'correct_wrong' : area.markPoint == 1 ? 'correct_right' : 'correct_right1'}.png`)
                //     correctView.renderOrder = PainterOrder.customDisplay
                //     this.addSubView(correctView)
                //     this.viewMap[RecordViewType.corrects].push(correctView)

                // }
            }
        }
        //添加背景图
        let imageView = new BoardImageView(
            this.application,
            new THREE.Vector3(0, 0, 0),
            { width: this.size.width, height: this.size.height },
        )
        imageView.setImageUrl(this.image.img)
        imageView.renderOrder = PainterOrder.background
        this.addSubView(imageView)

    }
    //获取答案
    getTrueOrFalse(ans) {
        return ans == 0 ? 'x' : '✓'
    }
    //修改view
    changeView(type,visible){
        this.viewMap[type].forEach((v) => {
            if (v) {
                v.visible = visible
            }
        })
        this.animate()
    }

    dispose() {
        this.viewMap.clear
        super.dispose()
    }
}