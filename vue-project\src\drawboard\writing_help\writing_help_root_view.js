import { FourLineView } from "./four_line_view"
import { MusicalStaff } from "./musical_staff"
import * as THREE from 'three';
import { BoardButton } from "../board_button";
import { DrawZ, PainterOrder } from "../draw_enums";
import { BoardView } from "../board_view";
import { MiGridView } from "./mi_grid_view";
import { FourMiGridView } from "./four_mi_grid_view";
import { FourCharacterGridView } from "./four_character_grid_view";
import { CharacterGridView } from "./character_grid_view";
export const WrintngHelp = {
    characterGrid: 1,
    fourLine: 2,
    musicalStaff: 3,
    miGrid:4,
    fourMiGrid:5,
    fourCharacterGrid:6
}

export class WritingHelpView extends BoardView {

    constructor(application, pos, size) {
        super(application, pos, size)

        this.setup()
        this.prepareColor = 0x00ff00
        // this.addTestViews()

        // 立体几何
        // this.addSolidTestViews()

    }




    setup() {
        // 添加一个占满的 button 当选择图形后点击按钮 会添加一个图形
        let viewButton = new BoardButton(
            this.application,
            new THREE.Vector3(0, 0, 0),
            { width: this.size.width, height: this.size.height })
        viewButton.setRenderOrder(PainterOrder.bottom)
        viewButton.onClick(() => {
            // 取消所有view的编辑
            for (let view of this.subViews) {
                if (view === this.viewButton) {
                    continue
                }
                view.setOnEdit(false)
            }

            if (this.prepareToAdd) {
                let point = this.convertPoint(viewButton.clickPoint, DrawZ.objcZ)
                this.addPlaneViewWithType(this.prepareToAdd, point)
                this.prepareToAdd = null
                if (this.addCallback) {
                    this.addCallback()
                }
            }
            viewButton.visible = false
        })
        viewButton.visible = false
        this.addSubView(viewButton)
        this.viewButton = viewButton
    }

    addPlaneCallback(callback) {
        this.addCallback = callback
    }



    onCopy(position, type, height) {
        let point = {
            x:position.x,
            y:position.y
        }
        point.y = position.y - height * 1.5
        this.addPlaneViewWithType(type, point)
    }

    addPlaneViewWithType(type, centerPoint,color) {
        let position = new THREE.Vector3(centerPoint.x, centerPoint.y, 0)
        let cameraInitSize = this.application?.deref()?.cameraInitSize
        let view = null
        
        
        switch (type) {
            case WrintngHelp.characterGrid: {
                view = new CharacterGridView(
                    this.application,
                    position,
                    { width: cameraInitSize.width * 0.65, height: cameraInitSize.width * 0.6 / 4 },
                    color??this.prepareColor, (position, type, height) => { this.onCopy(position, type, height) },
                    WrintngHelp.characterGrid)
                break;
            }
            case WrintngHelp.fourLine: {
                view = new FourLineView(
                    this.application,
                    position,
                    { width: cameraInitSize.width * 0.65, height: cameraInitSize.width * 0.2},
                    color??this.prepareColor,

                    (position, type, height) => { this.onCopy(position, type, height) },
                    WrintngHelp.fourLine)
                break;
            }
            case WrintngHelp.musicalStaff: {
                view = new MusicalStaff(
                    this.application,
                    position,
                    { width: cameraInitSize.width * 0.65, height: cameraInitSize.width * 0.2 },
                    color??this.prepareColor,
                    (position, type, height) => { this.onCopy(position, type, height) },
                    WrintngHelp.musicalStaff)
                
                break;
            }
            case WrintngHelp.miGrid: {
                view = new MiGridView(
                    this.application,
                    position,
                    { width: cameraInitSize.width * 0.65, height: cameraInitSize.width * 0.6 / 4 },
                    color??this.prepareColor,
                    (position, type, height) => { this.onCopy(position, type, height) },
                    WrintngHelp.miGrid)
                
                break;
            }
            case WrintngHelp.fourCharacterGrid: {
                view = new FourCharacterGridView(
                    this.application,
                    position,
                    { width: cameraInitSize.width * 0.65, height: cameraInitSize.width * 0.6 / 2 },
                    color??this.prepareColor,
                    (position, type, height) => { this.onCopy(position, type, height) },
                    WrintngHelp.fourCharacterGrid)
                
                break;
            }
            case WrintngHelp.fourMiGrid: {
                view = new FourMiGridView(
                    this.application,
                    position,
                    { width: cameraInitSize.width * 0.65, height: cameraInitSize.width * 0.6 / 2 },
                    color??this.prepareColor,
                    (position, type, height) => { this.onCopy(position, type, height) },
                    WrintngHelp.fourMiGrid)
                
                break;
            }
            default:
                break;
        }
        if(view){
            this.addSubView(view)
            this.bringSubViewToFront(view)
            return view
        }
    }



    addWrintingHelp(type, color = 0x00ff00) {
        this.prepareToAdd = type
        this.prepareColor = color
        if (type == null) {
            this.viewButton.visible = false
        }
        else {
            this.viewButton.visible = true
        }
    }

    updateColor(color) {
        this.prepareColor = color
    }

    cancelEditWithout(subView) {
        for (let view of this.subViews) {
            if (view === this.viewButton) {
                continue
            }
            if (view !== subView) {
                view.setOnEdit(false)
            }
        }
        this.animate()
    }

    clearShape() {
        let tempView = [...this.subViews]
        for (let i = 0; i < tempView.length; i++) {
            let view = tempView[i]
            if (view === this.viewButton) {
                continue
            } else {
                view.removeFromSuperView()
                view.dispose()
            }
        }
        tempView.length = 0
        this.animate()
    }

    onPointInside(point) {
        if (!this.visible) {
            return false
        }
        for (let subView of this.subViews) {
            if (subView.onPointInside(point)) {
                return true
            }
        }
        return false
    }

    onTouchDown(point) {
        for (let subView of this.subViews) {
            let target = subView.onTouchDown(point)
            if (target) {
                this.touchDownSubview = subView
                return target
            }
        }
        return view
    }

    onTouchMove(point) {
        if (this.touchDownSubview) {
            return this.touchDownSubview.onTouchMove(point)
        }
        return null
    }

    onTouchUp(point) {
        if (this.touchDownSubview) {
            this.touchDownSubview.onTouchUp(point)
            this.touchDownSubview = null
        }
        return null
    }


    dispose() {
        this.prepareToAdd = null
        super.dispose()
    }







}