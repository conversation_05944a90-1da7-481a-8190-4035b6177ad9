import { pinyin } from 'pinyin-pro'

export default {
    detectOS() {
        const userAgent = navigator.userAgent;

        if (/Windows/.test(userAgent)) {
            return 'Windows';
        } else if (/Android/.test(userAgent)) {
            return 'Android';
        } else {
            return '未知操作系统';
        }
    },
    naturalCompare(a, b) {
        // 假设 getPinyin 是将中文转为拼音的小写字符串的方法（需自行实现或引入库）
        const intA = parseInt(a);
        const intB = parseInt(b);

        if (!isNaN(intA) && !isNaN(intB)) {
            return intA - intB;
        }
        const pinyinA = convertToPinyin(a);
        const pinyinB = convertToPinyin(b);




        const matchA = splitStringBlocks(pinyinA);
        const matchB = splitStringBlocks(pinyinB);

        const textA = matchA.length > 0 ? matchA[0] : pinyinA;
        const textB = matchB.length > 0 ? matchB[0] : pinyinB;

        if (textA === textB && matchA.length > 1 && matchB.length > 1) {
            const numA = parseInt(matchA[1]) || 0;
            const numB = parseInt(matchB[1]) || 0;
            if (numA === numB) {
                return pinyinA.localeCompare(pinyinB);
            }
            return numA - numB;
        }

        const intTextA = parseInt(textA);
        const intTextB = parseInt(textB);

        if (!isNaN(intTextA) && !isNaN(intTextB)) {
            return intTextA - intTextB;
        }

        return textA.localeCompare(textB);
    },

    
}

function splitStringBlocks(str, includeDecimals = true) {
        if (includeDecimals) {
            return str.match(/\d+(?:\.\d+)?|\.\d+|[^\d.]+/g) || [str];
        }
        return str.match(/\d+|\D+/g) || [str];
    }
    
function convertToPinyin(str) {
    if (!str) return '';
    // 将字符串转换为小写
    str = str.toLowerCase();
    // 将中文转换为拼音，保留英文和数字
    return pinyin(str, {
        toneType: 'none',
        type: 'string',
        nonZh: 'retain'
    }).replace(/\s+/g, '');

}