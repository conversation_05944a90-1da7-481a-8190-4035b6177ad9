<template>
    <div class="rbp-alert" v-if="props.showBg" @click="bgCloseClick" :style="hideBc?{backgroundColor:'transparent'}:''">
        <div :class="`rbp-alert-content ${hideBc?'rbp-alert-border':''}`"  >
            <RBPAlertContent :title="props.title" :showMini="props.showMini" :haveBorderRadius="props.haveBorderRadius"
                @close="closeClick" @mini="miniClick" :hide-close="hideClose" @click.stop>
                <template v-slot:rbpDiv>
                    <slot name="rbpDiv"></slot>
                </template>
                <template #rbpBtns>
                    <slot name="rbpBtns"></slot>
                </template>
            </RBPAlertContent>
        </div>
    </div>
    <div v-else class="rbp-alert-no rbp-alert-border">
        <div class="rbp-alert-content-no">
            <RBPAlertContent :title="props.title" :showMini="props.showMini" :haveBorderRadius="props.haveBorderRadius"
                @close="closeClick" @mini="miniClick" :hide-close="hideClose" @click.stop>
                <template v-slot:rbpDiv>
                    <slot name="rbpDiv"></slot>
                </template>
                <template #rbpBtns>
                    <slot name="rbpBtns"></slot>
                </template>
            </RBPAlertContent>
        </div>
    </div>
</template>
<script setup>
import { computed, defineEmits, defineProps, onMounted } from 'vue'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { storeToRefs } from 'pinia'
import RBPAlertContent from '@/components/baseComponents/RBPAlertContent.vue'

const emits = defineEmits(['close', 'mini'])

const props = defineProps({
    zIndex: {
        type: Number,
        default: 1
    },
    title: String,
    showMini: Boolean,
    showBg: {
        type: Boolean,
        default: true
    },
    width: {
        type: String,
        default: '80%'
    },
    height: {
        type: String,
        default: '80%'
    },
    top: {
        type: String,
        default: '50%'
    },
    haveBorderRadius: {
        type: Boolean,
        default: true
    },
    isSupportClose: {
        type: Boolean,
        default: true
    },
    hideBc:{
        default:false
    },
    hideClose:{
        default:false
    }
})

const classroomUIStore = useClassroomUIStore()
const { mainContentTopSpace } = storeToRefs(classroomUIStore)
const calcTop = computed(() => {
    return `calc(${props.top} + ${mainContentTopSpace.value}px)`
})

function miniClick() {
    emits('mini')
}

function closeClick() {
    emits('close')
}

function bgCloseClick() {
    if(props.isSupportClose){
        closeClick()
    }
}
</script>
<style lang="scss" scoped>
@import "@/components/baseComponents/RBPColors.scss";
@import "@/assets/scss/mixin.scss";

.rbp-alert {
    position: absolute;
    height: 100%;
    width: 100%;
    background-color: var(--main-anti-bc-alpha-color);
    z-index: v-bind("props.zIndex");

    .rbp-alert-content {
        position: absolute;
        top: v-bind(calcTop);
        left: 50%;
        transform: translate(-50%, -50%);
        width: v-bind(width);
        height: v-bind(height);
    }
}
.rbp-alert-border{
    border: 1px solid var(--border-bar-color);
    border-radius: 26px;
}

.rbp-alert-no {
    position: absolute;
    top: v-bind(calcTop);
    left: 50%;
    transform: translate(-50%, -50%);
    width: v-bind(width);
    height: v-bind(height);
    
    border-radius: 26px;
    z-index: v-bind("props.zIndex");

    .rbp-alert-content-no {
        width: 100%;
        height: 100%;
    }
}
</style>