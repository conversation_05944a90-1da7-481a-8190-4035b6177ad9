<template>
    <div id="showPaperpenBox" v-if="paperViewState === ViewStatus.minimize" class="showPaperPen">
        <RBPButton btnText="查看书写" :btnSelected="true" @click="showWritePage"></RBPButton>
    </div>
</template>
<script setup>
import { ViewStatus } from '@/classroom/frame_enums';
import { useDesktopStore } from '@/stores/desktop_store';
import { useDrawBoardStore } from '@/stores/drawboard_store';
import { useOfficeFilesStore } from '@/stores/office_files_store';
import { storeToRefs } from 'pinia';
import RBPButton from '@/components/baseComponents/RBPButton.vue'

const drawBoardStore = useDrawBoardStore()

const { paperViewState } = storeToRefs(drawBoardStore)

function showWritePage() {

    paperViewState.value = ViewStatus.normal
    const desktopStore = useDesktopStore()
    desktopStore.drawMode = false

    drawBoardStore.setPaperpenLineColor()

    // const officeStore = useOfficeFilesStore()
    // officeStore.minimizeOfficeView()
}


</script>
<style lang="scss" scoped>
.showPaperPen {
    position: absolute;
    z-index: var(--interact-alert-z-index);
    right: 10px;
    bottom: 150px;
}
</style>