<template>
  <div id="classRoomRecord" class="class-room-record-style"
    :style="isFullScreen() ? { zIndex: 'var(--interact-practice-z-index)' } : ''" @click.stop="clickBody"
    v-show="!showClassRoomRecord || (showClassRoomRecord && !hideClassRoomRecord)">
    <RBPAlert v-if="!isFullScreen()" :title="isHomework() ? '作业记录' : '随堂记录'" width="1357px" height="846px"
      @close="closeWindow">
      <template v-slot:rbpDiv>
        <div class="content" @click.stop="clickBody(true)">
          <div v-if="shwoCalendar && !isHomework()" class="show-calendar">
            <RecordCalendar :date="bottomDate" @hideCalendar="hideCalendar"></RecordCalendar>
          </div>
          <RecordResult v-if="showInteract" @hideInteract="hideInteract"></RecordResult>
          <div class="record-content" v-else>
            <ClassCourseList :record-id="params.classRecordId" :date="bottomDate" @hideCalendar="hideCalendar"
              v-if="!isHomework()" class="record-class"></ClassCourseList>
            <div v-else class="calendar-content">
              <RecordCalendar ref="calendar" :date="bottomDate" @hideCalendar="hideCalendar"></RecordCalendar>
            </div>
            <RBPLine width="6px" height="100%"></RBPLine>
            <RecordList :show-calendar="shwoCalendar" class="expand"
              :style="{ padding: isHomework() ? '0px 30px' : '0px 16px' }" ref="recordsRef"
              @handleShowInteract="handleShowInteract"></RecordList>
          </div>
          <div v-if="!showInteract && !isHomework()" class="bottom-btn" :style="isHomework() ? '' : { left: '140px' }">
            <RBPButton style="margin-right: 0px;" @click.stop="backCalendar" btn-text="历史记录" :btn-selected="true"
              btn-type="big"></RBPButton>
          </div>

        </div>
      </template>
    </RBPAlert>
    <div class="practice-body" v-else>
      <RecordResult @hideInteract="hideInteract"></RecordResult>
    </div>
  </div>
  <ColorSelect style="z-index: var(--color-select-z-index);top: 0;" v-if="showColorSelect && !showClassRoomRecord">
  </ColorSelect>
  <EraserSelect style="z-index: var(--color-select-z-index);top: 0;" v-if="showEraserSelect && !showClassRoomRecord">
  </EraserSelect>

</template>

<script setup>
import { nextTick, onMounted, reactive, ref, provide, watch } from 'vue'
import RecordCalendar from './RecordCalendar.vue';
import RecordList from './RecordList.vue'
import ColorSelect from '../TabBar/ColorSelect.vue';
import EraserSelect from '@/components/TabBar/EraserSelect.vue'
import RecordResult from './RecordResult.vue';
import RBPAlert from '../baseComponents/RBPAlert.vue';
import RBPLine from '../baseComponents/RBPLine.vue';
import RBPButton from '../baseComponents/RBPButton.vue';
import ClassCourseList from './components/ClassCourseList.vue';
import { storeToRefs } from 'pinia'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { useDrawBoardStore } from '@/stores/drawboard_store'
const classroomUIStore = useClassroomUIStore()
const { showClassRoomRecord, showClassRecord, hideClassRoomRecord, mainContentTopSpace } = storeToRefs(classroomUIStore)
import router from '@/router';
import { useClassroomStore } from '@/stores/classroom.js'
const classroomStore = useClassroomStore()
const calendar = ref(null)
import { useInteractStore } from "@/stores/interact_store.js"
import { Interact } from '@/classroom/interact_enums';
const drawBoardStore = useDrawBoardStore()
const { showColorSelect, showEraserSelect, } = storeToRefs(drawBoardStore)
const interactStore = useInteractStore()
const bottomDate = ref('')
const shwoCalendar = ref(false)
const showInteract = ref(false)
const recordsRef = ref(null)
const params = reactive({
  classId: classroomStore.selectedClassroom.classId,
  classRecordId: classroomStore.selectedClassroom.classRecordId,
  subjectId: classroomStore.selectedClassroom.subjectId,
  date: '',
})

function isFullScreen() {
  let type = interactStore.interactResult.type

  return showInteract.value && (type == Interact.paperPen || type == Interact.classTest || type == Interact.homework);
}


onMounted(() => {
  if (showClassRecord.value) {
    shwoCalendar.value = true
  } else {
    shwoCalendar.value = false
  }
  nextTick(()=>{
  if(interactStore.interactResult.remote){
    handleShowInteract()
  }
  })
  
  getRecords()
})

function isHomework() {

  return classroomUIStore.showRecordType == 'homework';
}

function clickBody(isContent) {
  if (shwoCalendar.value || isContent) {


    shwoCalendar.value = false
  }else{
    showClassRoomRecord.value = false
  }
}

// 退出
function closeWindow() {
  if (showInteract.value) {
    hideInteract()
    return;
  }
  showClassRoomRecord.value = false

  if (showClassRecord.value) {
    showClassRecord.value = false
    // router.back()
  }
}

// 显示历史记录-日历		
function backCalendar() {
  shwoCalendar.value = true
}

const handleShowInteract = () => {
  showInteract.value = true
}

const hideInteract = () => {
  showInteract.value = false
  getRecords()
}
/**
 * 隐藏历史记录：
 * 点击有数据的某一天，有classId和classRecordId
 * 如果是课程返回来的不需要隐藏日历
 * 日历直接返回，没有classId和classRecordId
 */
const hideCalendar = (classId, classRecordId, date, isCourse) => {
  if (!isCourse && !isHomework()) {
    shwoCalendar.value = false
  }
  if (classId) {
    params.classId = classId
    params.classRecordId = classRecordId
  } else {
    params.classId = classroomStore.selectedClassroom.classId
    params.classRecordId = classroomStore.selectedClassroom.classRecordId
  }
  if (date) {
    params.date = date
    bottomDate.value = `${date.substring(0, 4)}-${date.substring(4, 6)}-${date.substring(6)}`;
  } else {
    params.date = ''
  }
  if (!isHomework() && date) {
    return
  }
  getRecords()
}

const getRecords = () => {
  nextTick(async () => {
    if (recordsRef.value) {
      try {
        let res = await recordsRef.value.getRecords(params)
        if (res.length && isHomework() && bottomDate.value === '') {

          let date = res[res.length - 1].startTime + ''
          if (date && date.length > 7) {
            let newData = `${date.substring(0, 4)}-${date.substring(4, 6)}-${date.substring(6, 8)}`;

            if (calendar.value) {
              calendar.value.changeMonth(new Date(newData), true)
            }
          }
        }
      } catch (e) {
        console.error("处理作业列表数据异常", e);

      }
    }
  })
}
provide('hideInteract', hideInteract)
</script>


<style lang="scss" scoped>
@import '../../assets/scss/components.scss';

.class-room-record-style {
  z-index: var(--toolbar-cloud-disk-z-index);
  background-color: rgba($color: var(--main-anti-bc-color-rgb), $alpha: 0.2);
  opacity: 1;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  position: fixed;

  .practice-body {

    background-color: var(--toolbar-bg-color);
    width: 100%;
    height: 100%;
  }

  .content {
    height: 100%;
    width: 100%;
    position: relative;

    .show-calendar {
      z-index: 1;
      position: absolute;
      top: 10%;
      left: 14%;
      width: 72%;
      background: var(--main-bc-color);
      border-radius: 15px;
      border: 2px solid var(--border-bar-color);
    }

    .bottom-btn {
      position: absolute;
      bottom: 36px;
      left: calc(50% - 144px);
    }

    .record-content {
      display: flex;
      width: 100%;
      height: 100%;

      .expand {
        flex: 1;
        box-sizing: border-box;
      }

      .calendar-content {
        width: 40%;
        height: 100%;

      }

      .record-class {
        width: 571px;
        box-sizing: border-box;
        padding: 0px 20px;
        padding-bottom: 136px;
      }
    }
  }


}
</style>