/**
小按钮
*/
<template>
    <div class="rbp-button" :class="{ 'btn-selected': props.btnSelected, 'btn-have-border': props.btnHaveBorder }">
        {{ props.btnText }}
    </div>
</template>
<script setup>
import { computed, defineProps, onMounted, ref } from 'vue'
import { RBPColors, getColor } from '@/components/baseComponents/RBPColors.js'

const props = defineProps({
    btnText: String,
    btnSelected: Boolean,
    btnType: String,
    backgroundColor: {
        type: String,
        default() {
            return getColor('--secondary-color');
        },
    },
    color: {
        type: String,
        default() {
            return getColor('--text-color');
        },
    },
    width: {
        type: String,
        default: '141px',
    },
    height: {
        type: String,
        default: '54px',
    },
    btnHaveBorder: {
        type: Boolean,
        default: false,
    },
    fontSize:{
        default:'21px'
    },
    borderRadius:{
        default:'15px'
    }
})
const cw = computed(() => {
    if (props.btnType == 'big') {
        return '288px'
    } else if (props.btnType == 'small') {
        return '141px'
    } else {
        return props.width
    }
})
const ch = computed(() => {
    return props.height
})
</script>
<style lang="scss" scoped>

.rbp-button {
    width: v-bind(cw);
    height: v-bind(ch);
    background-color: v-bind("props.backgroundColor");
    border-radius: v-bind("props.borderRadius");
    color: v-bind("props.color");
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: v-bind("props.fontSize");
    cursor: pointer;
}

.btn-selected {
    background-color: var(--primary-color);
    color: var(--anti-text-color);
}

.btn-have-border {
    border: 1px solid var(--border-bar-color);
}
</style>