<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="48px" viewBox="0 0 48 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>工具栏icon/下拉屏幕</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="0切图" transform="translate(-1716, -1011)">
            <g id="工具栏icon/下拉屏幕" transform="translate(1716, 1011)">
                <rect id="矩形" stroke="#979797" fill="#D8D8D8" opacity="0" x="0.5" y="0.5" width="47" height="47"></rect>
                <g id="编组" transform="translate(14, 18)" stroke="#565656" stroke-linecap="round" stroke-linejoin="round" stroke-width="2">
                    <g id="编组-2" transform="translate(4, 0)">
                        <polyline id="路径" points="12 0 6 6 0 0"></polyline>
                    </g>
                    <line x1="20" y1="10" x2="-3.38618023e-14" y2="10.1666667" id="路径"></line>
                    <line x1="16" y1="18" x2="4" y2="18" id="路径"></line>
                </g>
                <path d="M14.4,36 L12,36 L10,36 C8.8954305,36 8,35.1045695 8,34 L8,12.7233258 L8,12.7233258 C8,11.7715595 8.716344,11 9.6,11 L38.4,11 C39.28368,11 40,11.7715595 40,12.7233258 L40,34 C40,35.1045695 39.1045695,36 38,36 L33.6,36 L33.6,36" id="路径" stroke="#565656" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </g>
        </g>
    </g>
</svg>