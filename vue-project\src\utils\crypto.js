// 导入所需模块
import CryptoJS from 'crypto-js';

export const cryptoKeyketang = 'woshihudongketan'; // 密钥长度为16字节（128位

// 加密函数
export function encryptAES(data, cryptoKeyketang) {
    const ciphertext = CryptoJS.AES.encrypt(data, cryptoKeyketang).toString();
    return ciphertext;
}

// 解密函数
export function decryptAES(ciphertext, cryptoKeyketang) {
    const bytes = CryptoJS.AES.decrypt(ciphertext, cryptoKeyketang);
    const originalText = bytes.toString(CryptoJS.enc.Utf8);
    return originalText;
}
