import { ElLoading } from "element-plus";
import config from "./config.js";

import { CloudDiskRequest } from '@/server_request/cloud_disk.js';
import { useDrawBoardStore } from '@/stores/drawboard_store.js';
import { useClassroomUIStore } from '@/stores/classroom_ui_store.js';
import { useOfficeFilesStore } from '@/stores/office_files_store.js';
import { ClassRoomRequest } from '@/server_request/classroom_request.js'
import roomUpdater from '@/classroom/classroom_updater.js'
import { Interact } from '@/classroom/interact_enums'
import { loginInstance } from '@/login_instance/login_instance'
import { ElMessageBox } from 'element-plus';
import { useDesktopStore } from '@/stores/desktop_store.js';
import { tdRecord } from '@/utils/talkingdata_tool'
import { useVideoPlayStore } from "@/stores/video_play_store.js";
import { useAudioPlayStore } from "@/stores/audio_play_store.js";
import { useDictationStore } from '@/stores/dictation_store'
import { useInteractStore } from '@/stores/interact_store'
export default {
  /**
   * 根据文件类型，获取文件图标、小图标
   */
  getFileIcon(item) {
    try {
      let fileType = "";
      let imgKey = "none";
      if (item.fileSuffix) {
        fileType = item.fileSuffix;
      } else {
        let dot = item.fileUrl.lastIndexOf(".");
        fileType = item.fileUrl.substr(dot + 1);
      }
      fileType = fileType.toLowerCase();
      Object.keys(config.fileType).forEach((key) => { 
        if (config.fileType[key].includes(fileType)) {
          imgKey = key;
        }
      });
      item.imgKey = imgKey;
      item.fileType = fileType;
      return item;
    } catch (e) {
      console.log(e);
      return item;
    }
  },
  bytesToSize(bytes) {
    if (bytes === 0) return "0 B";
    let k = 1024; // or 1000
    let sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
    let i = Math.floor(Math.log(bytes) / Math.log(k));
    return (bytes / Math.pow(k, i)).toFixed(2) + " " + sizes[i];
  },

  formatTime: function (time) {
    return (
      time.slice(0, 4) +
      "/" +
      time.slice(4, 6) +
      "/" +
      time.slice(6, 8) +
      " " +
      time.slice(8, 10) +
      ":" +
      time.slice(10, 12) +
      ":" +
      time.slice(12)
    );
  },
  getAssetsFile(url) {
    return new URL(url, import.meta.url).href
  },
  //预览
  cloudFilePreview(item) {
    try {
      let { word, excel, ppt, pdf, video, audio } = config.fileType
      let { fileType } = item
      let wps = [...word, ...excel, ...ppt, ...pdf]
      // 打开媒体文件
      
      if (video.includes(fileType) || audio.includes(fileType)) {
        if (audio.includes(fileType)) {
          const audioPlayStore = useAudioPlayStore()
          audioPlayStore.changeAudioUrl(item.fileUrl)
          audioPlayStore.audioName = item.displayName
        } else {
          const videoPlayStore = useVideoPlayStore()
          const classroomUIStore = useClassroomUIStore()
          videoPlayStore.fileUrl = item.fileUrl
          videoPlayStore.displayName = item.displayName
          classroomUIStore.showVideoPlayView = true
        }

        return
      }
      // wps在线预览
      if (wps.includes(fileType)) {
        const officeStore = useOfficeFilesStore()
        officeStore.count = officeStore.count + 1
        openWps(item, fileType)
        tdRecord('打开wps', { 'fileName': item.fileName })
        return
      }
      else if (this.isTxtURL(item.fileUrl)) {
        getTxtData(item.fileUrl)
        return
      }
      openReview(item.fileUrl)
    } catch (e) {
      console.log(e)
    }
  },
  cloudDownloadFile(item) {
    startDownload(item.fileUrl)
  },
  isTxtURL(url) {

    try {
      const urlObj = new URL(url);  // 使用 URL 对象解析
      const pathname = urlObj.pathname;  // 获取 URL 的路径部分
      return /\.(TXT|txt)$/i.test(pathname);
    } catch (error) {
      console.error('Invalid URL:', error);
      return false;
    }
  }
};


async function getTxtData(fileUrl) {

  // 英语加上 单词听写
  if (loginInstance.subjectMainId === 3) {
    const interactStore = useInteractStore()
    const dictationStore = useDictationStore()
    const classroomUIStore = useClassroomUIStore()
    let loading = ElLoading.service({ background: 'transparent' })
    let res = await ClassRoomRequest.getTxtData(fileUrl)
    if (interactStore.interact == Interact.none) {
      await roomUpdater.startInteract(Interact.dictation)
    }
    loading.close()
    dictationStore.fromCloudDiskFileContent = res
    classroomUIStore.showCloudDisk = false
  }
  else {
    startDownload(fileUrl)
  }
}


function isImageURL(url) {
  try {
    const urlObj = new URL(url);  // 使用 URL 对象解析
    const pathname = urlObj.pathname;  // 获取 URL 的路径部分
    return /\.(jpeg|jpg|gif|png|bmp|webp|svg)$/i.test(pathname);
  } catch (error) {
    console.error('Invalid URL:', error);
    return false;
  }
}

//打开预览
async function openReview(url) {

  if (isImageURL(url)) {
    const drawBoardStore = useDrawBoardStore()
    drawBoardStore.addImageToBlackBoard(url)
    drawBoardStore.previewMode = false
    drawBoardStore.minimizePaperPenView()

    const officeStore = useOfficeFilesStore()
    officeStore.minimizeOfficeView()

    const uiStore = useClassroomUIStore()
    uiStore.showCloudDisk = false
    const desktopStore = useDesktopStore()
    desktopStore.drawMode = true

  }
  else {
    startDownload(url)
  }

}

function startDownload(url) {
  if (window.electron) {
    ElMessageBox.confirm("文件将保存到下载文件夹", {
      confirmButtonText: '下载',
      cancelButtonText: '取消',
      center: true,
    }).then(() => {
      window.electron.downloadWithUrl(url)
    }).catch(() => {

    })
  }
}



//wps预览
async function openWps(file, fileType) {

  let loading = ElLoading.service({ background: 'transparent' })
  try {
    let { ppt, pdf, word } = config.fileType
    let params = {
      fileId: file.cdId,
      ossType: 'CLOUD_DISK'
    }

    let needEditTypes = [...pdf]
    let edit = false
    if (needEditTypes.includes(fileType)) {
      edit = true
    }
    let { data } = await CloudDiskRequest.getWpsInfo(params, edit) // ppt.includes(fileType)
    const officeStore = useOfficeFilesStore()
    officeStore.addOfficeFile(data.link, file.displayName)
    const uiStore = useClassroomUIStore()
    uiStore.showCloudDisk = false
  } catch (e) {
    console.error("--------------------------网盘请求失败", e)
  }
  loading.close()

}
