import { defineStore } from "pinia";
import { ref } from "vue";
import { v4 as uuidv4 } from 'uuid'
import { LocalRequest } from "@/local_request/local_request"


export const useQrcodeStore = defineStore('qrcode', () => {

    const loginQrcodeStirng = ref('')

    const ipString = ref('')

    async function setQrcode() {
        // 遥控器二维码 [ip:port&uuid&ips]
        let ipsList = []
        let res = await LocalRequest.getLocalIps()
        if(res.code == 1 && res.ips) {
            ipsList = [...res.ips]
        }
        if(ipString.value.length > 0) {
            ipsList.push(ipString.value)
        }
        ipsList = ipsList.filter(item => !item.endsWith('.1'))
        let ips = JSON.stringify(ipsList)
        let uuid = uuidv4()
        loginQrcodeStirng.value = window.location.host.split(':')[1] + '&' + uuid + '&' + ips
        LocalRequest.setRemoteLoginId(uuid)
    }

    return {
        loginQrcodeStirng,
        ipString,
        setQrcode
    }
})