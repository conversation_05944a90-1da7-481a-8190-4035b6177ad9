<template>
	<div class="english">
		<div class="main_con">
			<div class="user_list" v-if="state.curr == 1">
				<div class="list" style="padding-bottom: 50px;">
					<div class="item" v-for="(item, index) in state.users" :key="index" @click="showImg(item)">
						<div class="left">
							<div class="tag">
								<img src="/icon/icon_gold.svg" width="100%" alt="" v-if="index == 0">
								<img src="/icon/icon_silver.svg" width="100%" alt="" v-else-if="index == 1">
								<img src="/icon/icon_copper.svg" width="100%" alt="" v-else-if="index == 2">
								<span v-else>{{ index + 1 }}</span>
							</div>
							<div class="con">
								<div class="name">{{ item.username }}</div>
								<div class="desc" v-if="item.status === undefined">
									<span style="margin-right: 20px;">正确：{{ item.right_count }}</span>
								</div>
								<template v-else>
									<div v-if="item.status == -1" class="desc">未作答</div>
									<div v-if="item.status == 1" class="desc" style="color: #29CB97;">
										已批改<span style="margin: 0 20px;">正确：{{ item.right_count }}</span>
									</div>
									<div v-if="item.status == 0" class="desc" style="color: #FA9B48;">批改中</div>
								</template>
							</div>
						</div>
						<div class="process">
							<div style="width: 100%;">
								<el-progress :stroke-width="4" :percentage="item.process"></el-progress>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="statistics" v-if="state.curr == 2">
				<div class="top">
					<div class="statistics_title">
						<img src="./assets/<EMAIL>" alt="">提交完成率
					</div>
					<div style="width: 100%;">
						<el-progress :show-text="true" :stroke-width="16" :percentage="state.process"></el-progress>
					</div>
					<div class="tips">
						<div class="tip-item">
							<div class="dot"></div>
							已完成
						</div>
						<div class="tip-item">
							<div class="dot" style="background-color: #C0C8D1;"></div>
							未完成
						</div>
					</div>
				</div>
				<div class="bottom">
					<div style="margin: 10px 0;">
						<div class="statistics_title">
							<img src="./assets/<EMAIL>" alt="">单词完成率
						</div>
					</div>
					<!-- :style="{ height: state.wordsHeight }" -->
					<div class="words" >
						<div class="word" v-for="(item, index) in state.words" :key="index">
							<div class="word_title">{{ item.title }}</div>
							<div class="process">
								<div style="width: 100%;">
									<el-progress :stroke-width="4" :percentage="item.process"></el-progress>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="btns">
			<RBPSegButton :options="['选项统计','学生统计']" :currentValue="state.curr == 1?'选项统计':'学生统计'" @updateCurrentValue="changeMode">
			</RBPSegButton>
			
		</div>

		<div class="mask" :style="{ display: state.showMask ? 'block' : 'none' }">
			<div class="maskcon" id="maskcon">
				<div class="imgWrap">
					<img :src="state.imgUrl" height="100%">
				</div>
			</div>
			<div class="foot-wrap">
				<Foot ref="footRef" @closeQues="closeMask"></Foot>
			</div>
		</div>
	</div>
</template>

<script setup>
import $ from "jquery"
import RBPSegButton from "../baseComponents/RBPSegButton.vue";
import { ref, onMounted, onBeforeUnmount, reactive, nextTick, onUnmounted } from "vue"
import { ClassroomRecordRequest } from "@/server_request/classroom_record";
import { useInteractStore } from '@/stores/interact_store';
import { loginInstance } from "@/login_instance/login_instance"
import Foot from './components/FootEnglish.vue'
const interactStore = useInteractStore()
const queryData = interactStore.interactResult

const footRef = ref(null)
const state = reactive({
	curr: 1,
	users: [],
	process: 0,
	words: [],
	showMask: false,
	imgUrl: '',
	qid: queryData.taskId,
	userId: loginInstance.teacher.teacherId,
	loadingService: null,
	wordsNum: 0,
	showView: false,
	showClose: 1,
	answered: queryData.answered,
	timer: null,
	timerCount: 0,
	wordsHeight: 0,
})
onMounted(() => {
	getUsers()
})

function changeMode(e) {
	state.curr = e=='选项统计'?1:2
}

function closeMask() {
	state.showMask = false
	$('.class-manage-bottom-control').show()
}

function showImg(item) {
	if (item.user_url) {
		state.imgUrl = item.user_url
		state.showMask = true
		$('.class-manage-bottom-control').hide()
		nextTick(() => {
			footRef.value && footRef.value.newCanvas('#maskcon')
		})
	}
}

async function getUsers() {
	let params = {
		qid: state.qid,
		user_id: state.userId,
		limit: 200,
		ignore_my: 1, // 忽略自己
	}
	try {
		let { data } = await ClassroomRecordRequest.getEnglishUsers(params)
		let users = []
		let refresh = false

		data.forEach(item => {
			if (state.answered) {
				let answeredIndex = state.answered.findIndex(ite => {
					return item.user_id == ite
				})
				if (answeredIndex < 0) {
					item.status = -1
				} else if (item.user_url) {
					item.status = 1
				} else {
					refresh = true
					item.status = 0
				}
			} else {
				if (item.user_url) {
					item.status = 1
				}
			}
			if (item.username) {
				let index = users.findIndex(ite => {
					return ite.user_id == item.user_id
				})
				if (index < 0) {
					if (item.status === 1 || item.status === 0) {
						users.unshift(item)
					} else {
						users.push(item)
					}
				} else {
					users[index] = {
						...users[index],
						...item
					}
				}
			}
		})
		state.users = users
		calcProcess()
		if (refresh) {
			state.timer && clearTimeout(state.timer)
			if (state.timerCount < 30) {
				state.timer = setTimeout(() => {
					getUsers()
					state.timerCount++
				}, 2000)
			}
		}
	} catch (error) {
		console.log(error)
	}
}

async function calcProcess() {
	let params = {
		qid: state.qid,
		user_id: state.userId,
		ignore_my: 1, // 忽略自己
	}
	try {
		let { data } = await ClassroomRecordRequest.getEnglishStatistics(params)
		if (data.user_count) {
			let user_complete = data.user_complete
			state.process = parseInt(data.user_complete / data.user_count * 100)
			data.items.forEach(item => {
				if (user_complete) {
					item.process = parseInt(item.right_count / user_complete * 100)
				} else {
					item.process = 0
				}
			})
			state.words = data.items
			if (state.words && state.words.length) {
				// 每个单词高度大约为 0.074vh
				let float = (state.words.length * 0.074) > 0.65 ? 0.65 : state.words.length * 0.074
				state.wordsHeight = parseInt(float * 100) + 'vh'
			}
			state.wordsNum = data.items.length
			let users = JSON.parse(JSON.stringify(state.users))
			let answered = []
			let unanswered = []
			users.forEach((item, index) => {
				let process = parseInt(item.right_count / state.wordsNum * 100)
				if (item.process !== process) {
					item.process = process
				}
				if (item.status == 1 || item.status == 0) {
					answered.push(item)
				} else {
					unanswered.push(item)
				}
			})
			answered.sort((a, b) => {
				return b.process - a.process
			})
			unanswered.sort((a, b) => {
				return a.username.localeCompare(b.username, 'zh');
			})
			users = [...answered, ...unanswered]
			state.users = users
			state.showView = true
		}
	} catch (error) {
		console.log(error)
	}
}



onBeforeUnmount(() => {
	state.timer && clearTimeout(state.timer)
})
onUnmounted(()=>{
	$('.class-manage-bottom-control').show()
})
</script>

<style lang="scss" scoped>
.mask {
	width: 100vw;
	height: 100vh;
	position: fixed;
	top: 0;
	left: 0;
	top: calc(423px - 50vh );
	left: calc((1357px - 100vw) / 2);
	background-color: rgba(0, 0, 0, 0.7);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 100022;

	.maskcon {
		width: 100%;
		height: calc(100vh - 80px);
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.imgWrap {
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
	}

	.foot-wrap {
		height: 80px;
		background-color: #030A10;
		position: absolute;
		bottom: 0;
		width: 100%;
		z-index: 10000;
	}
}

.english {
	height: 100%;

	.btns {
		font-size: 23px;
		padding-top: 0;
		color: #2e4a66;
		height: 52px;
		margin-top: 42px;
		margin-bottom: 36px;
		font-weight: 600;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 0 20px;
	}

	.main_con {
		height: calc(100% - 130px);
		margin: 0 auto;
		display: flex;
		flex-direction: column;

		.user_list {
			height: 100%;
			padding: 0 120px;
			overflow-y: scroll;
			scrollbar-width: none;

			&::-webkit-scrollbar {
				display: none;
			}

			.list {
				padding: 20px;
				font-size: 20px;
				color: #2D4A66;

				.item {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 20px 0;
					cursor: pointer;
					position: relative;

					.left {
						display: flex;
						align-items: center;

						.tag {
							width: 30px;
							height: 30px;
							margin-right: 20px;
							display: flex;
							justify-content: center;
							align-items: center;
							font-size: 20px;

							img {
								width: 28px;
								height: 28px;
							}
						}

						.con {
							display: flex;
							align-items: center;

							.name {
								font-size: 22px;
								margin-right: 20px;
								color: var(--text-color);
							}

							.desc {
								font-size: 18px;
								color: var(--secondary-text-color);
							}
						}
					}

					.process {
						width: 100%;
						position: absolute;
						bottom: -10px;
					}
				}
			}
		}

		.statistics {
			padding: 20px 50px;
			display: flex;
			flex-direction: column;
			height: calc(100% - 40px);

			.statistics_title {
				display: flex;
				align-items: center;
				font-size: 18px;
				margin-bottom: 10px;
				color: #2D4A66;

				img {
					width: 20px;
					height: 20px;
					margin-right: 10px;
				}
			}

			.top {}

			.tips {
				display: flex;
				justify-content: flex-end;
				margin: 10px 0;
				font-size: 18px;
				color: #96A4B2;

				.tip-item {
					display: flex;
					align-items: center;

					.dot {
						width: 16px;
						height: 16px;
						background-color: #409EFF;
						border-radius: 50%;
						display: inline-block;
						margin-left: 20px;
						margin-right: 10px;
					}
				}
			}

			.bottom {
				height: calc(100% - 100px);

				.words {
					height: calc(100% - 30px);
					overflow-y: scroll;
					scrollbar-width: none;

					&::-webkit-scrollbar {
						display: none;
					}

					.word {
						padding: 10px 0;
						width: 98%;

						.word_title {
							font-size: 20px;
							color: #2D4A66;
							margin-bottom: 10px;
						}

						.process {
							display: flex;
							align-items: center;
						}
					}
				}
			}
		}
	}
}
</style>