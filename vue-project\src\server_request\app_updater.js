import { AppChannel } from "@/app_verson";
import { serverHost, ServerUrls } from "./server_urls";
import { LocalRequest } from "@/local_request/local_request";
import axios from "axios"
import { loginInstance } from '@/login_instance/login_instance'
import { AppVersion, VersionCode } from '@/app_verson'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { useUpdaterStore } from '@/stores/updater_store'
import { Alert } from "@/utils/alert";
import { ElLoading, ElMessage } from 'element-plus'


export const UpdateType = {
    GRAY: "GRAY",  // 灰度发布 
    MANDATORY: "MANDATORY", // 强制更新
    NON_MANDATORY_PROMPT: "NON_MANDATORY_PROMPT", // 非强制提示更新
    NON_MANDATORY_SILENT: "NON_MANDATORY_SILENT", // 非强制不提示更新 //暂时不用这个
    SILENT: "SILENT" // 静默升级 //暂时不用这个
}


export class AppUpdater {

    static async checkUpdate(latestTip, stopClass = false) {
        let channel = serverHost.getChannelString()
        let cateType = "INTERACTION_CLASS"

        let data = {
            businessName: channel.toUpperCase(),
            cateType,
        }
        if (AppChannel) {
            data["appChannelName"] = AppChannel.toUpperCase()
        }
        if (loginInstance.lastSchoolId && loginInstance.lastSchoolId !== '') {
            let grayBoxCheck = {
                memberId: loginInstance.lastMemberId,
                schoolId: loginInstance.lastSchoolId,
            }
            data["grayBoxCheck"] = grayBoxCheck
        }
        let osInfo = await LocalRequest.getSysInfo()
        if (!osInfo) {
            return
        }
        data["platformName"] = osInfo.os.toUpperCase()
        data["marchName"] = this.dealAARCH64(osInfo.arch.toUpperCase())
        data["platformVersion"] = osInfo.os_version
        data["versionTypeName"] = window&&window.electron? "INCREMENTAL":"FULL"

        
        let url = ServerUrls.checkAppUpdateUrl()
        try {
            let res = await axios.post(url, data, {
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            const resData = res.data.data

            if (res.data.code === 1 && resData.versionCode > VersionCode) {
                if (stopClass && (resData.updateStatusName == UpdateType.MANDATORY || resData.updateStatusName == UpdateType.SILENT)) {
                    // 点击升级调用rust下载
                    let loading = ElLoading.service({ background: 'transparent' })
                    await LocalRequest.updateApp(resData.downloadUrl)
                    loading.close()
                } else {
                    let updaterStore = useUpdaterStore()
                    updaterStore.updateDescription = resData.updateDescription
                    updaterStore.versionName = resData.versionName
                    updaterStore.updateStatusName = resData.updateStatusName
                    let classroomUIStore = useClassroomUIStore()
                    classroomUIStore.showUpdater = true
                    updaterStore.downloadUrl = resData.downloadUrl
                }
            }
            else if (res.data.code === -1) {
                // 网络错误
            }
            else {
                // 已是最新版本
                if (latestTip) {
                    Alert.showSuccessMessage("当前版本已是最新版本")
                }
            }
        }
        catch (e) {
            console.log("check update error", e)
            return {
                code: -1,
                message: '网络错误'
            }
        }

    }

    static  dealAARCH64(type){
        if(type == 'AARCH64'){
            return 'AARCH_64'
        }
        return type
    }
}