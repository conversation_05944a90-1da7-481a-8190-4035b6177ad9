/**
开始计时按钮
*/
<template>
    <div class="rbp-time-btn" :style="{ backgroundColor: getTest().color1 }" @click="startCountdown">
        <div class="start" :style="{ backgroundColor: getTest().color2 }">
            <img src="/img/svg/icon_time_btn.svg">
            <span>
                {{ getTest().text }}
            </span>
        </div>
    </div>
</template>
<script setup>
import { RBPColors, getColor } from '@/components/baseComponents/RBPColors'
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
    isCounting: Boolean,
})

function getTest() {
    if (props.isCounting) {
        return {
            text: '暂停计时',
            color1: getColor('--error-bg-color'),
            color2: getColor('--error-color'),
        }
    } else {
        return {
            text: '开始计时',
            color1: getColor('--secondary-color'),
            color2: getColor('--primary-color'),
        }
    }
}

const emits = defineEmits(['startCountdown'])
function startCountdown() {
    emits('startCountdown')
}
</script>
<style lang="scss" scoped>
.rbp-time-btn {
    width: 132px;
    height: 132px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

    .start {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: calc(100% - 6px);
        height: calc(100% - 6px);
        border-radius: 50%;

        img {
            width: 48px;
            height: 48px;
        }

        span {
            margin-top: 5px;
            font-size: 18px;
            color: var(--anti-text-color);
        }
    }
}
</style>