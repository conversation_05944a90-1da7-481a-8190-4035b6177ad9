<template>
    <div class="template-probability" v-if="showTemplateProbability" @click="showTemplateProbability=false">
        <div class="content" @click.stop>
            <div class="modal-header">
                <div class="modal-header-title">请选择模版</div>
            </div>
            <div class="modal-body">
                <div class="list" v-for="(item, i) in probabilityTemplates" v-bind:key="i" @click="templateClick(item)"
                    :class="{ selected: item.name === templateName }">
                    {{ item.name }}
                </div>
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn2" v-on:click="startClick">开始</a>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { useProbabilityStore } from '@/stores/probability_store'
import roomUpdater from '@/classroom/classroom_updater.js'
import { Interact } from '@/classroom/interact_enums'

const probabilityStore = useProbabilityStore()
const { probabilityTemplates, templateName } = storeToRefs(probabilityStore)
const classroomUIStore = useClassroomUIStore()
const { mainContentTopSpace, showTemplateProbability } = storeToRefs(classroomUIStore)
const calcTop = computed(() => {
    return `calc(50% + ${mainContentTopSpace.value}px)`
})
function templateClick(item) {
    templateName.value = item.name
}
function startClick() {
    showTemplateProbability.value = false
    roomUpdater.startInteract(Interact.probability)
}
</script>

<style lang="scss" scoped>
.template-probability {
    position: absolute;
    height: 100%;
    width: 100%;
    background-color: rgba($color: #000000, $alpha: 0.2);
    opacity: 1;
    z-index: 1;

    .content {
        position: absolute;
        top: v-bind(calcTop);
        left: 50%;
        width: 30%;
        height: 40%;
        transform: translate(-50%, -30%);
        background-color: #f0f0f0;
        border-radius: 10px;
        padding: 20px;

        .modal-header {
            width: 100%;
            height: 15%;
            display: flex;
            justify-content: center;
            align-items: center;

            .modal-header-title {
                font-size: 33px;
            }
        }

        .modal-body {
            width: 90%;
            padding: 0px 5%;
            height: 70%;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-gap: 20px;
            // background-color: yellow;
            align-items: center;

            .list {
                // width: 30%;
                height: 40%;
                background-color: #fff;
                border-radius: 10px;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 22px;
                border: 2px solid #BFCEDD;
                cursor: pointer;
            }

            .selected {
                background-color: #2E4A66;
                color: white;
            }
        }

        .modal-footer {
            width: 100%;
            height: 15%;
            display: flex;
            justify-content: center;
            align-items: center;

            .btn {
                display: inline-block;
                width: 12vw;
                line-height: 5vh;
                height: 5vh;
                text-align: center;
                border: none;
                cursor: pointer;
                border-radius: 10px;
                text-decoration: none;
                font-size: 18px;
            }

            .btn1 {
                margin-right: 2vw;
                background-color: #BFCEDD;
                color: #2E4A66;
            }

            .btn2 {
                margin-left: 2vw;
                background-color: #2E4A66;
                color: white;
            }
        }
    }
}
</style>