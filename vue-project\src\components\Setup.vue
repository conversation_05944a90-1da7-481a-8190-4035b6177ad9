<template>
  <!-- <div class="setup" v-show="props.showSetup" @click="handleClose">
        <div class="content" @click.stop="">
        </div>
    </div> -->
  <el-dialog
    v-model="props.showSetup"
    title="设置"
    width="900"
    @close="handleClose"
  >
    <el-tabs tab-position="left" style="height: 300px" class="demo-tabs">
      <el-tab-pane label="基本信息">
        <div class="basic-style">
          <div>
            <span>{{version}}</span>
            <el-button type="primary" class="detection-btn">检测更新</el-button>
          </div>
          <div class="title-style">
            Copyright © 2021 robotpen.com. All rights reserved.
          </div>
          <div class="btn-style">
            <el-button type="primary">上传日志</el-button>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="单词听写">
        <div class="basic-style">
          <div>每个单词播放次数：</div>
          <div class="ipt-style">
            <el-input
              v-model="playbackcCunt"
              style="width: 240px"
              placeholder="请输入每个单词播放次数"
            />
          </div>
          <div class="title-style">
            每个单词播放时间间隔（如3，表示3秒读一次）：
          </div>
          <div class="ipt-style">
            <el-input
              v-model="intervalTime"
              style="width: 240px"
              placeholder="请输入每个单词播放时间间隔"
            />
          </div>
          <el-button type="primary" class="btn-style">设置</el-button>
        </div>
      </el-tab-pane>
      <el-tab-pane label="意见反馈">
        <div class="basic-style">
          <div>您的意见：</div>
          <div class="ipt-style">
            <el-input
              v-model="opinion"
              style="width: 340px"
              :rows="3"
              type="textarea"
              placeholder="请输入您的反馈意见（200字以内）"
            />
          </div>
          <div class="title-style">您的联系方式：</div>
          <div class="ipt-style">
            <el-input
              v-model="contactInformation"
              style="width: 340px"
              placeholder="请输入您的邮箱或手机号"
            />
          </div>
          <div class="button-container btn-style">
            <el-button type="primary">提交</el-button>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <el-button type="danger">退出登录</el-button>
  </el-dialog>
</template>
<script setup>
import { AppVersion } from "@/app_verson";
import { defineProps, defineEmits, ref } from "vue";
const props = defineProps({
  showSetup: Boolean,
});
const playbackcCunt = ref("3");
const intervalTime = ref("3");
const opinion = ref("");
const contactInformation = ref("");

const emit = defineEmits(["close"]);

const version = ref('软件版本: ' + AppVersion)
function handleClose(done) {
  emit("close", false);
}
</script>
<style lang="scss" scoped>
// .setup {
//   position: fixed;
//   width: 100%;
//   height: 100%;
//   top: 0;
//   left: 0;
//   z-index: 100;
//   background-color: #55585a7a;
//   opacity: 1;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   .content {
//     width: 60%;
//     height: 50%;
//     background-color: white;
//     border-radius: 10px;
//     padding: 20px;
//     position: relative;
//   }
// }
.basic-style {
  padding: 10px 0 0 10px;
  -webkit-app-region: no-drag;

  .detection-btn {
    margin-left: 10px;
  }
  .ipt-style {
    margin-top: 10px;
  }
  .title-style {
    margin-top: 20px;
  }
  .btn-style {
    margin-top: 40px;
  }
  .btn-ml {
    margin-left: 280px;
  }
  .button-container {
    display: flex;
    justify-content: flex-end; /* 将子元素推向右侧 */
    margin-right: 10px;
  }
}
</style>
