<template>
    <div class="responser">
        <div class="tips" v-if="loginInstance.evaluation">点击学生姓名进行加减分</div>
        <div class="students" v-for="(item, index) in responseNumber" :key="index">
            <div class="student" @click="evaluationAddScoreClick(studentList[index])">
                <img :src="`/img/icon_response_no${item}.png`" />
                <div class="stuname">
                    {{ studentList[index]?.name ?? '?' }}
                </div>
                <div v-if="studentList[index]" class="stuscore">
                    {{ studentList[index]?.score ?? '0' }}分
                </div>
                <div class="addScore"
                    v-show="addScore != '' && studentList[index]?.studentId == currentStudents[0]?.studentId"
                    :style="{ color: parseInt(addScore) > 0 ? 'green' : 'red' }">{{
                        addScore }}分</div>
            </div>
            <div class="scores" v-show="studentList[index] != null" v-if="!loginInstance.evaluation">
                <div class="score-minus" @click="addScoreClick('-1', 'error', studentList[index])">-1</div>
                <div class="score-add" @click="addScoreClick('+1', 'success', studentList[index])">+1</div>
                <div class="score-add" @click="addScoreClick('+3', 'success', studentList[index])">+3</div>
                <div class="score-add" @click="addScoreClick('+5', 'success', studentList[index])">+5</div>
            </div>
        </div>
        <div class="more" v-if="(studentList.length ?? 0) > 3"> <!-- v-if="studentList.length > 3" -->
            <div class="button" @click="showMoreClick">更多</div>
        </div>
        <ResponderFullList v-if="showMore" :studentList="studentList" :hiddenCallback="showMoreClick">
        </ResponderFullList>
    </div>
</template>
<script setup>
import { ref, computed, onMounted, getCurrentInstance, watch, defineProps, defineEmits } from 'vue'
import { storeToRefs } from 'pinia'
import { useClassroomStore } from '@/stores/classroom.js'
import { useAnswersStore, STUDENT_INDEX_MAX, STUDENT_INDEX_SUBMIT } from '@/stores/answers_store.js'
import roomUpdater from '@/classroom/classroom_updater.js'
import { loginInstance } from '@/login_instance/login_instance'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { useEvaluationScoreStore } from '@/stores/evaluation_score_store'
import { useScoreAudioStore } from '@/stores/score_audio_store'
import ResponderFullList from './ResponderFullList.vue'

const scoreAudioStore = useScoreAudioStore()
const evaluationScoreStore = useEvaluationScoreStore()
const { addScore, currentStudents } = storeToRefs(evaluationScoreStore)
const classroomUIStore = useClassroomUIStore()
const { showStudentResponderScore } = storeToRefs(classroomUIStore)
const { proxy } = getCurrentInstance()
const answerStore = useAnswersStore()
const { responseNumber } = storeToRefs(answerStore)
const classroomStore = useClassroomStore()
const { selectedClassroom, status } = storeToRefs(classroomStore)

const showMore = ref(false)

const studentList = computed(() => {
    // 拷贝 selectedClassroom.value.studentList
    let list = [...selectedClassroom.value.studentList];

    // 过滤掉 answerSubmitIndex 不存在或者等于 STUDENT_INDEX_MAX 的学生
    list = list.filter(student =>
        student.answerSubmitIndex !== undefined &&
        student.answerSubmitIndex !== STUDENT_INDEX_MAX &&
        student.answerSubmitIndex !== STUDENT_INDEX_SUBMIT
    );

    // 按 answerSubmitIndex 升序排序
    list.sort((a, b) => a.answerSubmitIndex - b.answerSubmitIndex);

    return list;
})

async function addScoreClick(score, type, student) {
    ///加分显示
    addScore.value = score
    currentStudents.value = [student]

    ///1s之后隐藏
    setTimeout(async () => {
        addScore.value = ''
        currentStudents.value = []

        student.score = String(parseInt(student.score ?? '0') + parseInt(score))
        //上传数据
        let success = await roomUpdater.studentsAddScore([student], score)
        if (success) {
            // 播放音频 播放动画
            scoreAudioStore.play(score)
        }
    }, 1000)
}
function evaluationAddScoreClick(student) {
    if (loginInstance.evaluation) {
        if (student) {
            student.selected = true
            currentStudents.value = [student]
            showStudentResponderScore.value = true
            evaluationScoreStore.getEvaluationItemItemList()
        }
    }
}

function showMoreClick() {
    showMore.value = !showMore.value
}

</script>
<style lang="scss" scoped>
.responser {
    width: 100%;
    height: 100%;
    // background-color: white;
    position: absolute;

    .tips {
        font-size: 16px;
        color: var(--secondary-text-color);
        align-self: center;
        text-align: center;
        margin-bottom: 20px;
    }

    .students {
        .student {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px;

            img {
                width: 40px;
                height: 40px;
            }

            .stuname {
                flex: 1;
                margin: 10px;
                font-size: 26px;
            }

            .stuscore {
                font-size: 14px;
            }

            .addScore {
                margin-left: 10px;
                font-size: 14px;
            }
        }

        .scores {
            display: flex;
            // justify-content: center;

            .score-minus {
                width: 40px;
                height: 40px;
                border: 2px solid #FC5C67;
                border-radius: 50%;
                line-height: 40px;
                text-align: center;
                color: #FC5C67;
                margin-left: 25px;
                cursor: pointer;
            }

            .score-add {
                width: 40px;
                height: 40px;
                border: 2px solid green;
                border-radius: 50%;
                line-height: 40px;
                text-align: center;
                color: green;
                margin-left: 10px;
                cursor: pointer;
            }
        }
    }

    .more {
        width: 100%;
        height: 40px;
        margin-top: 90px;
        display: flex;
        align-items: center;
        justify-content: center;

        .button {
            width: 100px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--primary-color);
            border-radius: 5px;
            color: white;
        }
    }
}
</style>