import { LineMaterial } from "three/examples/jsm/lines/LineMaterial";
import { BoardView } from "../../board_view";
import { Line2 } from "three/examples/jsm/lines/Line2";
import { LineGeometry } from "three/examples/jsm/lines/LineGeometry";
import * as THREE from 'three';
import { PainterOrder } from "@/drawboard/draw_enums";

// 田字格视图 - 统一版
export class FourAndCharacterGridView extends BoardView {

    constructor(application, pos, size, color, isNeedFirst = true) {
        // 初始化父类
        super(application, pos, size, color)
        // 竖线数量（不包括两端的边框线）
        this.verticalLines = 7
        this.color = color
        this.isNeedFirst = isNeedFirst

        // 创建线条组
        this.lineGroup = new THREE.Group()
        
        this.lineGroup.renderOrder = PainterOrder.customDisplay
        this.add(this.lineGroup)
        // 绘制田字格
        this.createGrid()

    }



    createGrid() {
        

        let app = this.application?.deref()
        this.lineWidth = app.cameraInitSize.width / 100

        // 计算实际内容区域的大小（排除按钮的空间和额外间距）
        let contentWidth = this.size.width
        let contentHeight = this.size.height
        contentWidth = contentHeight * 4

        let offsetX = 0
        let offsetY = 0

        // 计算四条水平线的位置
        const lineSpacing = contentHeight / 2 / 3

        // 一声线
        this.createLine(
            offsetX - contentWidth / 2, offsetY + contentHeight / 2 - lineSpacing,
            offsetX + contentWidth / 2, offsetY + contentHeight / 2 - lineSpacing,
        )
        

        // 二声线
        this.createLine(
            offsetX - contentWidth / 2, offsetY + contentHeight / 2 - lineSpacing * 2,
            offsetX + contentWidth / 2, offsetY + contentHeight / 2 - lineSpacing * 2
        )

        // 三声线（顶线）
        if (this.isNeedFirst) {
            this.createLine(
                offsetX - contentWidth / 2, offsetY + contentHeight / 2,
                offsetX + contentWidth / 2, offsetY + contentHeight / 2
            )
        }
        
        // 绘制长方形外框
        // 左边
        this.createLine(
            offsetX - contentWidth / 2,
            offsetY - contentHeight / 2,
            offsetX - contentWidth / 2,
            offsetY
        )

        // 右边
        this.createLine(
            offsetX + contentWidth / 2,
            offsetY - contentHeight / 2,
            offsetX + contentWidth / 2,
            offsetY
        )

        // 上边
        this.createLine(
            offsetX - contentWidth / 2,
            offsetY,
            offsetX + contentWidth / 2,
            offsetY
        )

        // 下边
        this.createLine(
            offsetX - contentWidth / 2,
            offsetY - contentHeight / 2,
            offsetX + contentWidth / 2,
            offsetY - contentHeight / 2
        )

        // 绘制内部的7条均分竖线
        const spacing = contentWidth / (this.verticalLines + 1)

        for (let i = 1; i <= this.verticalLines; i++) {
            let x = offsetX - contentWidth / 2 + spacing * i

            this.createLine(
                x,
                offsetY - contentHeight / 2,
                x,
                offsetY
            )
        }

        // 添加竖向虚线（在每两条竖实线之间）
        const halfSpacing = spacing / 2
        const dashSize = contentWidth / 240

        // 左边框和第一条竖线之间
        this.createDashedLine(
            offsetX - contentWidth / 2 + halfSpacing,
            offsetY - contentHeight / 2,
            offsetX - contentWidth / 2 + halfSpacing,
            offsetY,
            dashSize
        )

        // 内部竖线之间
        for (let i = 1; i < this.verticalLines; i++) {
            let x = offsetX - contentWidth / 2 + spacing * i + halfSpacing

            this.createDashedLine(
                x,
                offsetY - contentHeight / 2,
                x,
                offsetY,
                dashSize
            )
        }

        // 最后一条竖线和右边框之间
        this.createDashedLine(
            offsetX - contentWidth / 2 + spacing * this.verticalLines + halfSpacing,
            offsetY - contentHeight / 2,
            offsetX - contentWidth / 2 + spacing * this.verticalLines + halfSpacing,
            offsetY,
            dashSize
        )

        // 计算横线之间的间距（将高度分为4等份）
        const horizontalSpacing = contentHeight / 4

        // 绘制下方的虚线，位于中间横线和下边框之间的中点
        this.createDashedLine(
            offsetX - contentWidth / 2,
            offsetY - horizontalSpacing,
            offsetX + contentWidth / 2,
            offsetY - horizontalSpacing,
            dashSize
        )
    }

    createLine(x1, y1, x2, y2) {

        const material = new LineMaterial({
            color: this.color ?? 0x00ff00,
            linewidth: 2,
            dashed: false,
        });
        let lineGeometry = new LineGeometry()
        let points = [x1, y1, 0, x2, y2, 0]

        lineGeometry.setPositions(points);

        const line = new Line2(lineGeometry, material);
        line.computeLineDistances();

        this.lineGroup.add(line)
        return line
    }

    createDashedLine(x1, y1, x2, y2, size) {
        const material = new LineMaterial({
            color: this.color ?? 0x00ff00,
            linewidth: 1,
            dashed: true,
            dashSize: size,
            gapSize: size
        });

        let lineGeometry = new LineGeometry()
        let points = [x1, y1, 0, x2, y2, 0]

        lineGeometry.setPositions(points);

        const line = new Line2(lineGeometry, material);
        line.computeLineDistances();

        this.lineGroup.add(line)
        return line
    }
}
