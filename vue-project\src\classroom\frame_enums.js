
export const UIFrames = {
    blackboradHeight: 30,
    tabbarHeight: 78,
    interactSelectHeight: 0,
    studentTipListHeight: 80,
    paperPenLineBtnsHeight: 78,
    paperPenTitleHeight: 60,
    timeHeight: 60,
    pptTabHeight: 0,
    pptTabMiniHeight: 50,

    getScreenPxWidth() {
        // return window.screen.width * window.devicePixelRatio
        return  window.innerWidth * window.devicePixelRatio
    },

    getScreenPxHeight() {
        // return window.screen.height * window.devicePixelRatio
        return window.innerHeight * window.devicePixelRatio
    },

    getInnerPxWidth() {
        return window.innerWidth * window.devicePixelRatio
    },

    getInnerPxHeight() {
        return window.innerHeight * window.devicePixelRatio
    },
    /// 进入课堂就为全屏模式 不再改变窗口大小
    getPaperPenBoardHeight() {
        // return window.screen.height - this.tabbarHeight - this.paperPenTitleHeight - this.paperPenLineBtnsHeight
        // return window.innerHeight - this.tabbarHeight - this.paperPenTitleHeight - this.paperPenLineBtnsHeight
        return window.innerHeight - this.paperPenTitleHeight - this.paperPenLineBtnsHeight
    },
    
    getBlackBoardHeight() {
        // return window.screen.height - this.tabbarHeight - this.interactSelectHeight
        return window.innerHeight - this.tabbarHeight - this.interactSelectHeight
    }
}

export const DisplayDirection = {
    Left: 0,
    Right: 1
}

export const ViewStatus = {
    closed: -1,
    normal: 0,
    minimize: 1
}


