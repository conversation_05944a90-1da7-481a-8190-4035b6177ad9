import { BoardButton } from "../board_button";
import { BoardView } from "../board_view";
import * as THREE from 'three';
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial.js';
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry.js';
import { Line2 } from 'three/examples/jsm/lines/Line2.js';
import { BoardTool } from "../board_tool";
import { BoardLabel } from "../board_label";
import { CircleView } from "./circle_view";
import { DrawZ, PainterOrder } from "../draw_enums";
import { MathGeometry } from "./math_root_view";

const MoveTag = {
    none: -1,
    a: 0,
    b: 1,
    c: 2,
    self: 3 // 移动整个三角形
}

// 三角形
export class TriangleView extends BoardView {

    constructor(type,application, pos, size, color, oneAngle = false) {
        super(application, pos, size)

        this.renderOrder = PainterOrder.customDisplay

        this.onEdit = false

        this.color = color
        this.type = type
        // 只显示一个角度
        this.oneAngle = oneAngle

        //顶点
        this.aPoint = { x: 0, y: 0.5 * size.height }

        this.bPoint = { x: -0.5 * size.width, y: -0.5 * size.height }

        this.cPoint = { x: 0.5 * size.width, y: -0.5 * size.height }

        // 创建材质：线条材质
        const material = new LineMaterial({
            color: color ?? 0x00ff00,        // 边框颜色
            linewidth: 5,           // 线条宽度
            dashed: false,          // 不使用虚线
        });
        let stringApplication = application?.deref()
        this.lineWidth = stringApplication.cameraInitSize.width / 100
        let lineGeometry = new LineGeometry()
        let points = oneAngle ? [
            this.aPoint.x, this.aPoint.y, 0,
            this.bPoint.x, this.bPoint.y, 0,
            this.cPoint.x, this.cPoint.y, 0,
        ] : [
            this.aPoint.x, this.aPoint.y, 0,
            this.bPoint.x, this.bPoint.y, 0,
            this.cPoint.x, this.cPoint.y, 0,
            this.aPoint.x, this.aPoint.y, 0
        ]

        lineGeometry.setPositions(points);
        // 创建 Line2 对象
        const line = new Line2(lineGeometry, material);
        line.computeLineDistances();  // 必须计算线段的距离，以便正确渲染

        let triangleGroup = new THREE.Group()
        triangleGroup.add(line)
        triangleGroup.renderOrder = PainterOrder.customDisplay
        this.triangleGroup = triangleGroup
        this.add(triangleGroup);

        this.triangle = line

        this.setupEditViews()

        this.setupToolViews()
    }


    setupToolViews() {

        let app = this.application?.deref()
        let height = app.cameraInitSize.width / 40
        let itemHeight = height * 0.8
        let itemWidth = itemHeight
        let width = itemHeight * 3.5
        let space = itemHeight * 1.5 / 3

        let startX = - width * 0.5

        this.toolWidth = width
        this.toolHeight = height
        this.toolItemWidth = itemWidth
        let toolView = new BoardView(
            this.application,
            new THREE.Vector3(this.cPoint.x + this.toolItemWidth * 2, this.cPoint.y + height * 2, 0),
            { width, height })
        let deleteButton = new BoardButton(
            this.application,
            new THREE.Vector3(startX + space + itemWidth / 2, 0, 0),
            { width: itemWidth, height: itemHeight }, true)
        deleteButton.setImage('img/math/delete.svg')
        deleteButton.onClick(() => {
            this.removeFromSuperView()
            this.dispose()
        })
        toolView.addSubView(deleteButton)


        let selectorButton = new BoardButton(
            this.application,
            new THREE.Vector3(startX + space * 2 + itemWidth * (2 - 0.5), 0, 0),
            { width: itemWidth, height: itemHeight }, true)
        selectorButton.setImage('img/math/selector.svg')
        selectorButton.onClick(() => {
            this.showOptionsView(!(this.optionsView?.visible ?? false))
        })
        toolView.addSubView(selectorButton)

        // toolView.setBackgroundColor(0x0000ff)
        toolView.setRenderOrder(PainterOrder.customDisplay + 1)

        toolView.visible = false
        this.addSubView(toolView)
        this.toolView = toolView
    }

    showOptionsView(show) {
        if (!this.optionsView) {
            let app = this.application?.deref()
            let width = app.cameraInitSize.width / 20
            let itemHeight = app.cameraInitSize.width / 40
            let height = itemHeight * (this.oneAngle ? 2 : 4)

            this.optionsWidth = width
            this.optionsHeight = height
            this.optionsItemHeight = itemHeight
            this.optionsView = new BoardView(
                this.application,
                new THREE.Vector3(
                    this.cVertexView.position.x + this.cVertexView.size.width + width / 2,
                    this.toolView.position.y - this.toolHeight / 2 - this.optionsHeight / 2,
                    0
                ),
                { width, height })
            this.optionsView.setRenderOrder(PainterOrder.customDisplay + 1)
            this.addSubView(this.optionsView)

            let startY = height / 2

            if (this.oneAngle) {
                let rightAangleButton = new BoardButton(
                    this.application,
                    new THREE.Vector3(0, startY - itemHeight / 2, 0),
                    { width, height: itemHeight })
                rightAangleButton.setFontSize(0.015)
                rightAangleButton.setTextAlign('left')
                rightAangleButton.setTextColor(0xffffff)
                rightAangleButton.setText("直角")
                rightAangleButton.onClick(() => {
                    this.handleRightAangle()
                    this.optionsView.visible = false
                })
                rightAangleButton.setBackgroundColor(0xFF6347)
                this.optionsView.addSubView(rightAangleButton)


                let flatAangleButton = new BoardButton(
                    this.application,
                    new THREE.Vector3(0, startY - itemHeight / 2 - itemHeight, 0),
                    { width, height: itemHeight })
                flatAangleButton.setFontSize(0.015)
                flatAangleButton.setTextAlign('left')
                flatAangleButton.setTextColor(0xffffff)
                flatAangleButton.setText("平角")
                flatAangleButton.onClick(() => {
                    this.handleFlatAangle()
                    this.optionsView.visible = false
                })
                flatAangleButton.setBackgroundColor(0xFF6347)
                this.optionsView.addSubView(flatAangleButton)
            }
            else {
                let equilateralButton = new BoardButton(
                    this.application,
                    new THREE.Vector3(0, startY - itemHeight / 2, 0),
                    { width, height: itemHeight })
                equilateralButton.setFontSize(0.015)
                equilateralButton.setTextAlign('left')
                equilateralButton.setTextColor(0xffffff)
                equilateralButton.setText("等边三角形")
                equilateralButton.onClick(() => {
                    this.handleEquilateral()
                    this.optionsView.visible = false
                })
                equilateralButton.setBackgroundColor(0xFF6347)
                this.optionsView.addSubView(equilateralButton)


                let isoscelesButton = new BoardButton(
                    this.application,
                    new THREE.Vector3(0, startY - itemHeight / 2 - itemHeight, 0),
                    { width, height: itemHeight })
                isoscelesButton.setFontSize(0.015)
                isoscelesButton.setTextAlign('left')
                isoscelesButton.setTextColor(0xffffff)
                isoscelesButton.setText("等腰三角形")
                isoscelesButton.onClick(() => {
                    this.handleIsosceles()
                    this.optionsView.visible = false
                })
                isoscelesButton.setBackgroundColor(0xFF6347)
                this.optionsView.addSubView(isoscelesButton)


                let circumscribedButton = new BoardButton(
                    this.application,
                    new THREE.Vector3(0, startY - itemHeight / 2 - itemHeight * 2, 0),
                    { width, height: itemHeight })
                circumscribedButton.setFontSize(0.015)
                circumscribedButton.setTextAlign('left')
                circumscribedButton.setTextColor(0xffffff)
                circumscribedButton.setText("作外接圆")
                circumscribedButton.onClick(() => {
                    this.circumscribedCircle()
                    this.optionsView.visible = false
                })
                circumscribedButton.setBackgroundColor(0xFF6347)
                this.optionsView.addSubView(circumscribedButton)

                let inscribedButton = new BoardButton(
                    this.application,
                    new THREE.Vector3(0, startY - itemHeight / 2 - itemHeight * 3, 0),
                    { width, height: itemHeight })
                inscribedButton.setFontSize(0.015)
                inscribedButton.setTextAlign('left')
                inscribedButton.setTextColor(0xffffff)
                inscribedButton.setText("作内切圆")
                inscribedButton.onClick(() => {
                    this.inscribedCircle()
                    this.optionsView.visible = false
                })
                inscribedButton.setBackgroundColor(0xFF6347)
                this.optionsView.addSubView(inscribedButton)
            }
        }

        this.optionsView.visible = show
    }

    // 计算两点之间的距离
    calculateDistance(x1, y1, x2, y2) {
        return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
    }


    handleRightAangle() {
        let ba = this.calculateDistance(this.aPoint.x, this.aPoint.y, this.bPoint.x, this.bPoint.y)
        let bc = this.calculateDistance(this.bPoint.x, this.bPoint.y, this.cPoint.x, this.cPoint.y)

        this.aPoint.x = this.bPoint.x
        this.aPoint.y = this.bPoint.y + ba

        this.cPoint.x = this.bPoint.x + bc
        this.cPoint.y = this.bPoint.y

        this.updateTriangle()
    }

    handleFlatAangle() {

        let ba = this.calculateDistance(this.aPoint.x, this.aPoint.y, this.bPoint.x, this.bPoint.y)
        let bc = this.calculateDistance(this.bPoint.x, this.bPoint.y, this.cPoint.x, this.cPoint.y)
        this.aPoint.x = this.bPoint.x - ba
        this.aPoint.y = this.bPoint.y

        this.cPoint.x = this.bPoint.x + bc
        this.cPoint.y = this.bPoint.y

        this.updateTriangle()

    }

    adjustToEquilateralTriangle() {
        this.cPoint.y =  this.bPoint.y
        if (this.cPoint.x < this.bPoint.x) {
            this.cPoint.x = this.bPoint.x + this.bPoint.x - this.cPoint.x
        }
        let bPoint = this.bPoint
        let cPoint = this.cPoint;

        // A 点应该位于 B 和 C 形成的等边三角形的顶部
        let centerX = (bPoint.x + cPoint.x) / 2;
        let centerY = (bPoint.y + cPoint.y) / 2;

        // 计算 A 点的位置, 使其满足等边三角形条件
        let dx = cPoint.x - bPoint.x;
        let dy = cPoint.y - bPoint.y;

        // 旋转 60 度（π/3）使得 A 点从 B-C 线的中点垂直于该线
        let offsetX = (dy * Math.sqrt(3)) / 2;
        let offsetY = -(dx * Math.sqrt(3)) / 2;

        // 新的 A 点位置
        let newAPoint = {
            x: centerX + offsetX,
            y: centerY + offsetY
        };

        if (this.aPoint.y > this.bPoint.y) {
            // 确保 A 点的 y 坐标大于 B 和 C 点
            if (newAPoint.y <= bPoint.y || newAPoint.y <= cPoint.y) {
                newAPoint.y = centerY + Math.abs(offsetY);  // 调整 y 坐标，确保 A 点在顶部
            }
        }

        return newAPoint;
    }

    handleEquilateral() {
        this.aPoint = this.adjustToEquilateralTriangle()
        this.updateTriangle()
    }

    moveToIsoscelesTriangle(bPoint, cPoint) {
        // 计算 B 和 C 点之间的距离（底边的长度）
        let baseLength = Math.sqrt(
            Math.pow(cPoint.x - bPoint.x, 2) + Math.pow(cPoint.y - bPoint.y, 2)
        );

        // 计算底边 BC 的中点
        let midPoint = {
            x: (bPoint.x + cPoint.x) / 2,
            y: (bPoint.y + cPoint.y) / 2
        };

        // 计算 B 点和 C 点的方向向量
        let dx = cPoint.x - bPoint.x;
        let dy = cPoint.y - bPoint.y;

        // 计算平分线的方向向量，即 BC 线段的垂直方向
        let perpDx = -dy;  // 垂直方向的 x 分量
        let perpDy = dx;   // 垂直方向的 y 分量

        // 归一化垂直方向的向量，确保单位长度
        let length = Math.sqrt(perpDx * perpDx + perpDy * perpDy);
        perpDx /= length;
        perpDy /= length;

        // 计算 A 点的位置，A 点沿着平分线方向移动，确保 A 到 B 和 A 到 C 的距离相等
        // 可以选择 A 点在 BC 上方或下方，假设我们让 A 点在上方
        let aPoint = {
            x: midPoint.x + perpDx * Math.sqrt(Math.pow(baseLength / 2, 2) - Math.pow(baseLength / 4, 2)),
            y: midPoint.y + perpDy * Math.sqrt(Math.pow(baseLength / 2, 2) - Math.pow(baseLength / 4, 2))
        };

        return aPoint;
    }

    handleIsosceles() {
        this.aPoint = this.moveToIsoscelesTriangle(this.bPoint, this.cPoint)
        this.updateTriangle()
    }


    circumscribedCircumcenter() {

        const { x: x1, y: y1 } = this.aPoint
        const { x: x2, y: y2 } = this.bPoint
        const { x: x3, y: y3 } = this.cPoint

        const D = 2 * (x1 * (y2 - y3) + x2 * (y3 - y1) + x3 * (y1 - y2));
        const Ux = ((Math.pow(x1, 2) + Math.pow(y1, 2)) * (y2 - y3) +
            (Math.pow(x2, 2) + Math.pow(y2, 2)) * (y3 - y1) +
            (Math.pow(x3, 2) + Math.pow(y3, 2)) * (y1 - y2)) / D;
        const Uy = ((Math.pow(x1, 2) + Math.pow(y1, 2)) * (x3 - x2) +
            (Math.pow(x2, 2) + Math.pow(y2, 2)) * (x1 - x3) +
            (Math.pow(x3, 2) + Math.pow(y3, 2)) * (x2 - x1)) / D;

        return { x: Ux, y: Uy };
    }


    circumscribedCircumradius() {

        const { x: x1, y: y1 } = this.aPoint
        const { x: x2, y: y2 } = this.bPoint
        const { x: x3, y: y3 } = this.cPoint

        const a = Math.sqrt(Math.pow(x2 - x3, 2) + Math.pow(y2 - y3, 2));
        const b = Math.sqrt(Math.pow(x1 - x3, 2) + Math.pow(y1 - y3, 2));
        const c = Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));

        const s = (a + b + c) / 2;
        const area = Math.sqrt(s * (s - a) * (s - b) * (s - c));

        return (a * b * c) / (4 * area);
    }

    circumscribedCircle() {
        let center = this.circumscribedCircumcenter();
        let radius = this.circumscribedCircumradius();
        let color = this.superView.deref()?.prepareColor ?? this.color

        let circelView = new CircleView(
            MathGeometry.Circle,
            
            this.application,
            new THREE.Vector3(this.position.x + center.x, this.position.y + center.y, 0),
            { width: radius * 2, height: radius * 2 }, color)
        this.setOnEdit(false)
        this.superView.deref()?.addSubView(circelView)
    }

    calculateIncenter() {

        const { x: x1, y: y1 } = this.aPoint
        const { x: x2, y: y2 } = this.bPoint
        const { x: x3, y: y3 } = this.cPoint

        // 计算三角形边长
        const a = Math.sqrt(Math.pow(x2 - x3, 2) + Math.pow(y2 - y3, 2)); // BC
        const b = Math.sqrt(Math.pow(x1 - x3, 2) + Math.pow(y1 - y3, 2)); // AC
        const c = Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2)); // AB

        // 内心坐标公式
        const Ix = (a * x1 + b * x2 + c * x3) / (a + b + c);
        const Iy = (a * y1 + b * y2 + c * y3) / (a + b + c);

        return { x: Ix, y: Iy };
    }

    calculateInradius() {

        const { x: x1, y: y1 } = this.aPoint
        const { x: x2, y: y2 } = this.bPoint
        const { x: x3, y: y3 } = this.cPoint

        // 计算三角形边长
        const a = Math.sqrt(Math.pow(x2 - x3, 2) + Math.pow(y2 - y3, 2)); // BC
        const b = Math.sqrt(Math.pow(x1 - x3, 2) + Math.pow(y1 - y3, 2)); // AC
        const c = Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2)); // AB

        // 计算半周长
        const s = (a + b + c) / 2;

        // 计算面积（海伦公式）
        const area = Math.sqrt(s * (s - a) * (s - b) * (s - c));

        // 内切圆半径
        return area / s;
    }

    inscribedCircle() {
        let center = this.calculateIncenter();
        let radius = this.calculateInradius();
        let color = this.superView.deref()?.prepareColor ?? this.color

        let circelView = new CircleView(
            MathGeometry.Circle,
            this.application,
            new THREE.Vector3(this.position.x + center.x, this.position.y + center.y, 0),
            { width: radius * 2, height: radius * 2 }, color)

        this.setOnEdit(false)
        this.superView.deref()?.addSubView(circelView)
    }

    setOnEdit(onEdit) {
        this.onEdit = onEdit
        this.aVertexView.visible = this.onEdit
        this.aVertexView.draggable = this.onEdit
        this.bVertexView.visible = this.onEdit
        this.bVertexView.draggable = this.onEdit
        this.cVertexView.visible = this.onEdit
        this.cVertexView.draggable = this.onEdit
        this.viewButton.draggable = this.onEdit
        this.draggable = this.onEdit

        this.toolView.visible = this.onEdit
        if (!this.onEdit && this.optionsView) {
            this.optionsView.visible = false
        }
        if (onEdit) {
            this.superView.deref().bringSubViewToFront(this)
        }
    }

    setupEditViews() {

        let application = this.application?.deref()
        let areaWidth = application.cameraInitSize.width / 40
        let dragAreaSize = { width: areaWidth, height: areaWidth }
        this.dragAreaSize = dragAreaSize

        let aVertexView = new BoardView(
            this.application,
            new THREE.Vector3(this.aPoint.x, this.aPoint.y + dragAreaSize.height, 0),
            dragAreaSize)
        aVertexView.renderOrder = 1
        aVertexView.setBackgroundColor(0xff6347)
        aVertexView.visible = false
        aVertexView.setRenderOrder(PainterOrder.customDisplay)
        this.aVertexView = aVertexView
        this.addSubView(aVertexView)

        let bVertexView = new BoardView(
            this.application,
            new THREE.Vector3(this.bPoint.x - dragAreaSize.width, this.bPoint.y, 0),
            dragAreaSize)
        bVertexView.renderOrder = 1
        bVertexView.setBackgroundColor(0xff6347)
        bVertexView.visible = false
        bVertexView.setRenderOrder(PainterOrder.customDisplay)
        this.bVertexView = bVertexView
        this.addSubView(bVertexView)

        if (this.oneAngle) {
            let angle = this.calculateAngle(this.aPoint, this.bPoint, this.cPoint)
            let angleLabel = new BoardLabel(
                this.application,
                new THREE.Vector3(this.bPoint.x - dragAreaSize.width, this.bPoint.y, 0),
                { width: areaWidth, height: areaWidth }, angle, { fontSize: 0.03, color: 0xffffff, align: 'center' })
            angleLabel.renderOrder = PainterOrder.customDisplay + 1
            this.addSubView(angleLabel)
            this.angleLabel = angleLabel
        }

        let cVertexView = new BoardView(
            this.application,
            new THREE.Vector3(this.cPoint.x + dragAreaSize.width, this.cPoint.y, 0),
            dragAreaSize)
        cVertexView.renderOrder = 1
        cVertexView.setBackgroundColor(0xff6347)
        cVertexView.visible = false
        cVertexView.setRenderOrder(PainterOrder.customDisplay)
        this.cVertexView = cVertexView
        this.addSubView(cVertexView)


        let viewButton = new BoardButton(
            this.application,
            new THREE.Vector3(0, 0, 0),
            { width: this.size.width + areaWidth * 2, height: this.size.height + areaWidth * 2 })

        viewButton.renderOrder = PainterOrder.customDisplay - 1
        viewButton.onClick(() => {
            this.setOnEdit(!this.onEdit)
            if (this.onEdit) {
                this.superView.deref().cancelEditWithout(this)
            }
        })
        this.viewButton = viewButton
        this.addSubView(viewButton)
        // viewButton.setBackgroundColor(0xff0000)
        // this.updateTriangle()
    }


    calculateAngle(aPoint, bPoint, cPoint) {

        // 检查是否共线（角度为180度）
        if (Math.abs((bPoint.x - aPoint.x) * (cPoint.y - aPoint.y) - (bPoint.y - aPoint.y) * (cPoint.x - aPoint.x)) < 0.006) {
            if ((aPoint.x < bPoint.x && cPoint.x < bPoint.x) || (aPoint.x > bPoint.x && cPoint.x > bPoint.x)) {
                return "0"
            }
            return "180";
        }

        // 计算各边的长度
        const a = Math.sqrt(Math.pow(cPoint.x - bPoint.x, 2) + Math.pow(cPoint.y - bPoint.y, 2));
        const b = Math.sqrt(Math.pow(cPoint.x - aPoint.x, 2) + Math.pow(cPoint.y - aPoint.y, 2));
        const c = Math.sqrt(Math.pow(bPoint.x - aPoint.x, 2) + Math.pow(bPoint.y - aPoint.y, 2));

        // 使用余弦定理计算夹角
        const cosTheta = (Math.pow(a, 2) + Math.pow(c, 2) - Math.pow(b, 2)) / (2 * a * c);
        const theta = Math.acos(cosTheta);

        // 将弧度转换为角度
        let angle = theta * (180 / Math.PI);

        if (angle > 89.1 && angle < 90.1) {
            angle = 90
        }

        return parseInt(angle).toString();
    }


    updateTriangle(a, b, c) {

        let ax = a ? a.x : this.aPoint.x
        let ay = a ? a.y : this.aPoint.y
        let bx = b ? b.x : this.bPoint.x
        let by = b ? b.y : this.bPoint.y
        let cx = c ? c.x : this.cPoint.x
        let cy = c ? c.y : this.cPoint.y

        // 定义三角形的顶点
        const vertices = this.oneAngle ? new Float32Array([
            ax, ay, 0.0,  // 顶点 A
            bx, by, 0.0, // 顶点 B
            cx, cy, 0.0,   // 顶点 C
        ]) : new Float32Array([
            ax, ay, 0.0,  // 顶点 A
            bx, by, 0.0, // 顶点 B
            cx, cy, 0.0,   // 顶点 C
            ax, ay, 0.0,  // 顶点 A
        ]);
        this.triangle.geometry.dispose()
        let lineGeometry = new LineGeometry();
        // 更新 MeshLineGeometry
        lineGeometry.setPositions(vertices);

        this.triangle.geometry = lineGeometry;

        if (this.oneAngle) {
            let angle = this.calculateAngle(this.aPoint, this.bPoint, this.cPoint)
            this.angleLabel.setText(angle)
        }

        this.aVertexView.position.set(this.aPoint.x, this.aPoint.y + this.dragAreaSize.height, 0)
        this.bVertexView.position.set(this.bPoint.x - this.dragAreaSize.width, this.bPoint.y, 0)
        if (this.angleLabel) {
            this.angleLabel.position.set(this.bPoint.x - this.dragAreaSize.width, this.bPoint.y, 0)
        }
        this.cVertexView.position.set(this.cPoint.x + this.dragAreaSize.width, this.cPoint.y, 0)

        this.toolView.position.set(this.cPoint.x + this.toolItemWidth * 2, this.cPoint.y + this.toolHeight * 2, 0)

        if (this.optionsView) {
            this.optionsView.position.set(
                this.cVertexView.position.x + this.cVertexView.size.width + this.optionsWidth / 2,
                this.toolView.position.y - this.toolHeight / 2 - this.optionsHeight / 2,
                0
            )
        }

        let leftX = Math.min(this.aPoint.x, this.bPoint.x, this.cPoint.x)
        let rightX = Math.max(this.aPoint.x, this.bPoint.x, this.cPoint.x)
        let topY = Math.max(this.aPoint.y, this.bPoint.y, this.cPoint.y)
        let bottomY = Math.min(this.aPoint.y, this.bPoint.y, this.cPoint.y)
        let width = rightX - leftX
        let height = topY - bottomY
        if (height < this.dragAreaSize.height) {
            height = this.dragAreaSize.height
            bottomY = bottomY - this.dragAreaSize.height / 2
        }
        this.viewButton.setSize({ width: width + this.dragAreaSize.width * 2, height: height + this.dragAreaSize.height * 2 })
        this.viewButton.position.set(leftX + width / 2, bottomY + height / 2, 0)
    }

    onTouchDown(point) {
        let view = super.onTouchDown(point)
        if (!this.onEdit) {
            return view
        }
        let cvtPoint = this.convertPoint(point, DrawZ.objcZ)
        this.touchDownPoint = cvtPoint
        if (view == this.aVertexView) {
            this.moveTag = MoveTag.a
        }
        else if (view == this.bVertexView) {
            this.moveTag = MoveTag.b
        }
        else if (view == this.cVertexView) {
            this.moveTag = MoveTag.c
        }
        else if (view == this.viewButton) {
            this.moveTag = MoveTag.self
        }
        else {
            this.moveTag = MoveTag.none
        }
        return view
    }


    onTouchMove(point) {
        if (!this.onEdit || !this.touchDownPoint) {
            return super.onTouchMove(point)
        }
        let divPoint = point
        let cvtPoint =  this.convertPoint(point, DrawZ.objcZ)
        let spaceX = cvtPoint.x - this.touchDownPoint.x
        let spaceY = cvtPoint.y - this.touchDownPoint.y
        this.touchDownPoint = cvtPoint
        if (this.moveTag === MoveTag.a) {
            let x = this.aPoint.x + spaceX
            let y = this.aPoint.y + spaceY
            this.aPoint = { x, y }
            this.updateTriangle({ x, y })
        }
        else if (this.moveTag === MoveTag.b) {
            let x = this.bPoint.x + spaceX
            let y = this.bPoint.y + spaceY
            this.bPoint = { x, y }
            this.updateTriangle(null, { x, y })
        }
        else if (this.moveTag === MoveTag.c) {
            let x = this.cPoint.x + spaceX
            let y = this.cPoint.y + spaceY
            this.cPoint = { x, y }
            this.updateTriangle(null, null, { x, y })
        }
        else if (this.moveTag === MoveTag.self) {
            let x = this.position.x + spaceX
            let y = this.position.y + spaceY
            this.position.set(x, y, 0)
        }
        return super.onTouchMove(divPoint)
    }

    onTouchUp(point) {
        this.moveTag = MoveTag.none
        return super.onTouchUp(point)
    }


    isPointOnLine(p1, p2, touchPoint, lineWidth) {
        const { x: x1, y: y1 } = p1;
        const { x: x2, y: y2 } = p2;
        const { x: touchX, y: touchY } = touchPoint;

        // 检查 touchPoint 是否在线段的 x 和 y 范围内
        const isInXRange = touchX >= Math.min(x1, x2) - lineWidth && touchX <= Math.max(x1, x2) + lineWidth;
        const isInYRange = touchY >= Math.min(y1, y2) - lineWidth && touchY <= Math.max(y1, y2) + lineWidth;
        if (!isInXRange || !isInYRange) {
            return false;
        }
        // 处理垂直线段（x1 === x2）
        if (x1 === x2) {
            return Math.abs(touchX - x1) <= lineWidth;
        }
        // 处理水平线段（y1 === y2）
        if (y1 === y2) {
            return Math.abs(touchY - y1) <= lineWidth;
        }
        // 计算点到线的距离
        const numerator = Math.abs((y2 - y1) * touchX - (x2 - x1) * touchY + x2 * y1 - y2 * x1);
        const denominator = Math.sqrt((y2 - y1) ** 2 + (x2 - x1) ** 2);
        const distance = numerator / denominator;
        // 判断距离是否在边的宽度范围内
        return distance <= lineWidth;
    }

    isTouchOnTriangleEdges(touchPoint, aPoint, bPoint, cPoint, lineWidth) {
        // 判断是否在边AB上
        const onAB = this.isPointOnLine(aPoint, bPoint, touchPoint, lineWidth);

        // 判断是否在边BC上
        const onBC = this.isPointOnLine(bPoint, cPoint, touchPoint, lineWidth);

        // 判断是否在边CA上
        const onCA = this.oneAngle ? false : this.isPointOnLine(cPoint, aPoint, touchPoint, lineWidth);

        // 返回是否命中任意一条边
        return onAB || onBC || onCA;
    }


    onPointInside(point) {
        if (!this.visible) {
            return false
        }
        for (let subView of this.subViews) {
            if (subView !== this.viewButton) {
                if (subView.onPointInside(point)) {
                    return true
                }
            }
            // else if (this.onEdit) {
            //     if (subView.onPointInside(point)) {
            //         return true
            //     }
            // }
        }
        let cvtPoint = this.convertPoint(point, DrawZ.objcZ)
        let localPoint = this.worldToLocal(cvtPoint)
        return this.isTouchOnTriangleEdges(localPoint, this.aPoint, this.bPoint, this.cPoint, this.lineWidth)
    }

    dispose() {
        BoardTool.disposeGroup(this.triangleGroup)
        super.dispose()
    }

}