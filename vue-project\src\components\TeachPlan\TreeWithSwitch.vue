<template>
    <div class="TreeWithSwitch" v-for="item in dataList" :key="item.chapterId">
        <div class="TreeName" :class="getChildStatus(item)" @click.stop="changeOpenStatus(item)">
            <div class="treeNameText" :style="{ paddingLeft: getLeftRight(item) }" :class="getChildStatusText(item)">{{ item.name }}</div>
            <div class="triangle" v-show="item.children.length > 0" :style="{ paddingRight: getLeftRight(item) }">
                <img :src="triangle(item)">
            </div>
        </div>
        <div class="nodeChildren">
            <TreeWithSwitch :dataList="item.children" v-if="item.children && item.open"></TreeWithSwitch>
        </div>
    </div>
</template>
<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import { storeToRefs } from 'pinia'
import TreeWithSwitch from '@/components/TeachPlan/TreeWithSwitch.vue'
import { useTeachPlanStore } from '@/stores/teach_plan'
import { ClassRoomRequest } from '@/server_request/classroom_request'
import { ElLoading, ElMessage } from 'element-plus'
const teachPlanStore = useTeachPlanStore()
const { treeList, mineTeachPlanList, schoolTeachPlanList } = storeToRefs(teachPlanStore)
const props = defineProps({
    dataList: Object
})

function treeListOpenAllFalse(list) {
    list.forEach(item => {
        if (item.children.length > 0) {
            treeListOpenAllFalse(item.children)
        }
        item.selected = false
    })
}
async function changeOpenStatus(item) {
    if (item.children.length > 0) {
        if (item.root) {
            item.open = !item.open
        } else {
            if (item.selected) {
                item.open = !item.open
            } else {
                item.open = true
                treeListOpenAllFalse(treeList.value)
                item.selected = true
                loadData(item)
            }
        }
    } else {
        treeListOpenAllFalse(treeList.value)
        item.selected = true
        loadData(item)
    }
}
async function loadData(item) {
    let loading = ElLoading.service({ background: 'transparent' })
    const resMine = await ClassRoomRequest.getTeachPlan(item.chapterId, true)
    if (resMine.code == 1) {
        mineTeachPlanList.value = resMine.data.list
    }
    const resSchool = await ClassRoomRequest.getTeachPlan(item.chapterId, false)
    if (resSchool.code == 1) {
        schoolTeachPlanList.value = resSchool.data.list
    }
    loading.close()
}
function getChildStatus(item) {
    if (item.children.length > 0) {
        if (item.root) {
            return { 'TreeNameHaveChildren': true }
        } else {
            if (item.selected) {
                return { 'TreeNameNotHaveChildrenAndSelected': true }
            }
        }
    } else {
        if (item.selected) {
            return { 'TreeNameNotHaveChildrenAndSelected': true }
        }
    }
}
function getChildStatusText(item) {
    if (item.children.length > 0) {
        if (item.root) {
            
        } else {
            if (item.selected) {
                return { 'treeNameTextSelected': true }
            }
        }
    } else {
        if (item.selected) {
            return { 'treeNameTextSelected': true }
        }
    }
}
function triangle(item) {
    if (item.children.length > 0) {
        if (item.open) {
            return '/img/svg/icon_arrow_open.svg'
        } else {
            return '/img/svg/icon_arrow_close.svg'
        }
    }
}
const getLeftRight = (item) => {
    return `${item.index * 20}px`
}
</script>
<style lang="scss" scoped>
.TreeWithSwitch {
    // padding: 0px 20px;
    // background-color: yellow;
    box-sizing: border-box;

    .TreeName {
        cursor: pointer;
        padding: 20px;
        display: flex;
        align-items: center;
        box-sizing: border-box;

        .treeNameText {
            flex: 1;
            font-size: 21px;
            color: var(--text-color);
        }

        .treeNameTextSelected {
            color: var(--anti-text-color);
        }

        .triangle {
            width: 31px;
            height: 31px;

            img {
                width: 100%;
                height: 100%;
            }
        }
    }

    .TreeNameHaveChildren {
        background-color: var(--toolbar-bg-color);
        color: var(--text-color);
        font-size: 21px;
    }

    .TreeNameNotHaveChildrenAndSelected {
        background-color: var(--primary-color);
        color: var(--anti-text-color);
    }
}
</style>