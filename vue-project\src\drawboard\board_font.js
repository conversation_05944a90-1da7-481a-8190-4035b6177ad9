
import { FontLoader } from 'three/addons/loaders/FontLoader.js'
class FontHelper {

    constructor() {
        if (!FontHelper.instance) {
            FontHelper.instance = this
        }
        return FontHelper.instance
    }

    async setup() {
        const loader = new FontLoader()
        const host = window.location.host
        const mediumUrl = 'http://' + host + '/static/fonts/MiSansLight.json'

        loader.load(mediumUrl, (font) => {
            this.defaultFont = font
        })

        let url = 'http://' + host + '/fonts/MiSansLight.json'
        loader.load(url, (font) => {
            this.defaultFont = font
        })
    }
}

const fontLoader = new FontHelper()

export default fontLoader