

class LocalUrls {

    static startClassUrl() {
        const host = window.location.host
        return "http://" + host + "/start_class"
    }

    static stopClassUrl() {
        const host = window.location.host
        return "http://" + host + "/stop_class"
    }

    static sseEventUrl() {
        const host = window.location.host
        return "http://" + host + "/sse"
    }

    /// 本地server一些消息接收
    static serverSseEventUrl() {
        const host = window.location.host
        return "http://" + host + "/server_sse"
    }

    static startInteractUrl() {
        const host = window.location.host
        return "http://" + host + "/start_interact"
    }

    static stopInteractUrl() {
        const host = window.location.host
        return "http://" + host + "/stop_interact"
    }

    ///保存数据到本地
    static saveDataUrl() {
        const host = window.location.host
        return "http://" + host + "/save_data"
    }

    ///从本地读取数据
    static readDataUrl() {
        const host = window.location.host
        // return "http://" + host + "/store/data/data.json"
        return "http://" + host + "/read_data"
    }

    /// 读取配置文件
    static readConfigUrl() {
        const host = window.location.host
        return "http://" + host + "/static/app_config.json"
    }

    static changeMacUrl() {
        const host = window.location.host
        return "http://" + host + "/change_mac"
    }


    static setRemoteLoginIdUrl() {
        const host = window.location.host
        return "http://" + host + "/set_remote_id"
    }


    static sendMessageToRemoteUrl() {
        const host = window.location.host
        return "http://" + host + "/send_to_remote"
    }

    static remoteSseEventUrl() {
        const host = window.location.host
        return "http://" + host + "/remote_sse"
    }

    static getOSSTokenUrl() {
        const host = window.location.host
        return "http://" + host + "/get_oss_token"
    }

    /// 获取系统信息
    static getSysInfoUrl() {
        const host = window.location.host
        return "http://" + host + "/get_sys_info"
    }

    /// 获取本地ip地址
    static getLocalIpsUrl() {
        const host = window.location.host
        return "http://" + host + "/get_local_ips"
    }

    static getLocalIpListUrl() {
        const host = window.location.host
        return "http://" + host + "/get_local_ip_list"
    }

    static updateAppUrl() {
        const host = window.location.host
        return "http://" + host + "/update_app"
    }

    /// 启动蓝牙网关
    static startBleServerUrl() {
        const host = window.location.host
        return "http://" + host + "/start_ble_server"
    }

    /// 关闭蓝牙网关
    static stopBleServerUrl() {
        const host = window.location.host
        return "http://" + host + "/stop_ble_server"
    }

    /// 连接蓝牙设备
    static connectBleDeviceUrl() {
        const host = window.location.host
        return "http://" + host + "/ble_connect"
    }

    /// 断开连接
    static disconnectBleDeviceUrl() {
        const host = window.location.host
        return "http://" + host + "/ble_disconnect"
    }
    /// 连接多个蓝牙设备
    static connectBleMultiDeviceUrl() {
        const host = window.location.host
        return "http://" + host + "/ble_connect_devices"
    }


    static disconnectAllBleDeviceUrl() {
        const host = window.location.host
        return "http://" + host + "/ble_disconnect_all"
    }

    static startAutoTestUrl() {
        const host = window.location.host
        return "http://" + host + "/start_auto_test"
    }

    static stopAutoTestUrl() {
        const host = window.location.host
        return "http://" + host + "/stop_auto_test"
    }

    //写入log
    static writeLogUrl() {
        const host = window.location.host
        return "http://" + host + "/write_log"
    }

    //上传本地日志
    static uploadLocalLogUrl() {
        const host = window.location.host
        return "http://" + host + "/upload_local_log"
    }

    //自动测试
    static autoTestUrl() {
        const host = window.location.host
        return "http://" + host + "/auto_test_control"
    }
    //存储audio 
    static uploadAudioUrl() {
        const host = window.location.host
        return "http://" + host + "/upload_audio"
    }

    //存储audio 
    static mouseEventUrl() {
        const host = window.location.host
        return "http://" + host + "/mouse_event"
    }


}


export {
    LocalUrls
}