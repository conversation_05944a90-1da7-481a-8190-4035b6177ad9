<template>
  <div class="responder" v-show="showView">
    <div class="topList" v-if="topThree.length">
      <div class="student tiny">
        <template v-if="topThree.length > 1">
          <div class="top-list-item">
            <div class="avatar tiny"><img :src="topThree[1].avatar" /><img class="crown tiny" :src="no2" /></div>
            <div class="nickname">{{ topThree[1].name }}</div>
            <div class="usetime">{{ topThree[1].time }}</div>
          </div>
        </template>
      </div>
      <div class="student">
        <template v-if="topThree.length > 0">
          <div class="top-list-item">
            <div class="avatar "><img :src="topThree[0].avatar" /><img class="crown" :src="no1" /></div>
            <div class="nickname">{{ topThree[0].name }}</div>
            <div class="usetime">{{ topThree[0].time }}</div>
          </div>
        </template>
      </div>
      <div class="student tiny">
        <template v-if="topThree.length > 2">
          <div class="top-list-item">
            <div class="avatar tiny"><img :src="topThree[2].avatar" /><img class="crown tiny" :src="no3" /></div>
            <div class="nickname">{{ topThree[2].name }}</div>
            <div class="usetime">{{ topThree[2].time }}</div>
          </div>
        </template>
      </div>
    </div>
    <el-empty v-else description="无人参与"></el-empty>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import no1 from "./assets/<EMAIL>"
import no2 from "./assets/<EMAIL>"
import no3 from "./assets/<EMAIL>"
import { ClassroomRecordRequest } from '@/server_request/classroom_record';
import { useInteractStore } from '@/stores/interact_store';
const interactStore = useInteractStore()
//路由参数
const queryData = interactStore.interactResult
const topThree = ref([])
const showView = ref(false)
onMounted(() => {
  if (queryData.interactId) {
    getData()
  } else {
    showView.value = true
  }
})

const compare = (property) => {
  return function (a, b) {
    let value1 = a[property];
    let value2 = b[property];
    return value1 - value2;
  };
}
const getData = async () => {
  try {
    let params = {
      interactId: queryData.interactId,
      classId: queryData.classId,
    }
    let { data } = await ClassroomRecordRequest.studentInteractDeails(params)
    data = data.filter(item => { return item.useTime })
    data.sort(compare("useTime"));
    for (const stu of data) {
      let student = {
        avatar:
          stu.headUrl || "http://res.mgboard.com/avatar/online.png",
        name: stu.studentName,
        // time: stu.useTime,
      };
      topThree.value.push(student);
    }
    showView.value = true
  } catch (error) {
    console.log(error)
  }
}
</script>

<style lang="scss" scoped>
  .responder {
    display: flex;
    justify-content: space-around;
    align-items: center;
    overflow: hidden;
    height: calc(100% - 60px);

    .topList {
      display: flex;
      justify-content: space-around;
      align-items: baseline;
      padding: 0 20px;

      .top-list-item {
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .student {
        width: 150px;
        margin: 0px 16px;
        box-sizing: border-box;
        &.tiny {
          width: 100px;
          margin-top: 24px;
        }

        .crown {
          width: 45px;
          bottom: 150px;
          margin-left: -22.5px;
          left: 50%;
          position: absolute;

          &.tiny {
            bottom: 100px;
          }
        }
      }

      .avatar {
        width: 150px;
        height: 150px;
        
        transition: all 0.3s linear;
        animation: 0.3s bounceIn;
        padding-top: 95px;
        padding-bottom: 10px;
        position: relative;
        &.tiny {
          width: 100px;
          height: 100px;
        }

        img {
          width: 100%;
          border-radius: 50%;
        }
      }

      .nickname {
        font-size: 16px;
        color: var(--text-color);
        line-height: 23px;
        margin-bottom: 5px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      .usetime {
        font-size: 14px;
        line-height: 23px;
        color: var(--text-sencondary-color);
      }
    }

  }

  @-webkit-keyframes bounceIn {

    from,
    20%,
    40%,
    60%,
    80%,
    to {
      -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
      animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    }

    0% {
      opacity: 0;
      -webkit-transform: scale3d(0.3, 0.3, 0.3);
      transform: scale3d(0.3, 0.3, 0.3);
    }

    50% {
      -webkit-transform: scale3d(1.1, 1.1, 1.1);
      transform: scale3d(1.1, 1.1, 1.1);
    }

    to {
      opacity: 1;
      -webkit-transform: scale3d(1, 1, 1);
      transform: scale3d(1, 1, 1);
    }
  }
</style>