<template>
	<div class="record-words">
		<el-main ref="elmain" class="main-con">
			<div class="paper-list">
				<div v-if="showView && list.length"
					style="display: flex;flex-direction: column;justify-content: flex-end;">
					<div class="item_wrap" v-for="(item, index) in list" :key="index">
						<div class="item" v-for="(ite, ind) in item" :key="ind" @click="openDialog(ite)"
							:style="{ opacity: ite.studentName ? 1 : 0, cursor: ite.studentName ? 'pointer' : 'normal' }">
							<img class="img" :src="ite.mergeImgUri"
								:style="{ width: width + 'px', height: width * 1.41 + 'px' }">
							<div class="desc">
								<div class="left">
									<div class="avatar"
										:style="{ backgroundImage: `url(${ite.headUrl || baseAvatar})` }"></div>
									<span style="color: #000000;">{{ ite.studentName }}</span>
								</div>
								<div class="center">
									<Rate :value="scoreToStar(ite.avgScore)"></Rate>
								</div>
							</div>
						</div>
					</div>
				</div>
				<RBPEmpty class="empty" v-else-if="showView" :des="'未查询到任何学生，请重新输入查询条件'">
				</RBPEmpty>
			</div>
		</el-main>

		<div class="search">
			<div class="input_wrap input">
				<RBPInput :placeholder="'按学生姓名查找'" v-model="keyword" class="input"></RBPInput>
			</div>
		</div>

		<div class="dialog_content" v-if="dialogVisible">
				<img class="icon_arrow" @click="pre()" v-if="showImg" src="./assets/arrow_left.png"
					:style="{ opacity: dialogIndex > 0 ? 1 : 0.5 }" alt="">
				<div style="height: 100%; position: relative;display: inline-block;" v-if="dialogObj">
					<div class="img-body">
						<div :style="{ width: widthControl, position: 'relative' }">
							<img class="img" ref="imgRefs" :src="dialogRecords[dialogIndex].mergeImgUri"
								@load="imgLoad(dialogRecords[dialogIndex], dialogIndex)">
							<div v-if="area.words&&!refreshArea" v-for="p in area.words" class="area"
								:style="{ top: p.areaPoints.y, left: p.areaPoints.x, width: p.areaPoints.width, height: p.areaPoints.height }">
								<Rate class="Rate" :value="scoreToStar(p.wordScore)" :size="'10px'" :margin="'1px'">
								</Rate>
							</div>
						</div>
					</div>

					<div>
						<div class="desc">
							<div class="left">
								<div class="avatar"
									:style="{ backgroundImage: `url(${dialogObj.headUrl || baseAvatar})` }"></div>
								<span>{{ dialogObj.studentName }}</span>
							</div>
							<div class="center">
								<span>{{ '熟练度' }}:</span>
								<Rate :value="scoreToStar(dialogObj.avgScore)"></Rate>

							</div>
							<div class="right" @click="openPlayer()">
								<el-button size="mini" type="primary">{{ '笔迹回放' }}</el-button>
							</div>
							<el-icon class="el-icon-circle-close"
								@click="dialogObj = null; areas = []; area = {}; dialogVisible = false">
								<CircleClose />
							</el-icon>
						</div>
					</div>
				</div>
				<img class="icon_arrow" @click="next()" v-if="showImg" src="./assets/arrow_left.png"
					:style="{ transform: 'rotate(180deg)', opacity: dialogIndex < dialogRecords.length - 1 ? 1 : 0.5 }"
					alt="">
			</div>


		<!-- <el-dialog modal-class="record-words-body" v-model="dialogVisible"
			@close="dialogObj = null; areas = []; area = {}" :show-close="false">
			
		</el-dialog> -->

		<div class="player-mask" v-if="showPlayer">
			<WordsPlayer ref="player" @closePlayer="closePlayer" :recordId="recordId" :userImg="userImg"
				:originImg="originImg"></WordsPlayer>
		</div>
	</div>
</template>

<script setup>
import { nextTick, onMounted, ref, watch } from 'vue';
import { Search } from '@element-plus/icons-vue'
import { scoreToStar } from './js/utils';
import RBPEmpty from '../baseComponents/RBPEmpty.vue';
import { ClassroomRecordRequest } from '@/server_request/classroom_record';
import { ElMessage } from 'element-plus';
import WordsPlayer from './components/WordsPlayer.vue';
import Rate from './components/Rate.vue';
import { useInteractStore } from '@/stores/interact_store';
import RBPInput from '../baseComponents/RBPInput.vue';
const interactStore = useInteractStore()
const queryData = interactStore.interactResult
const keyword = ref('')
const width = ref(0)
const list = ref([])
let allList = []
const dialogVisible = ref(false)
const dialogObj = ref(null)
const dialogRecords = ref([])
const dialogIndex = ref(0)
const baseAvatar = 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png'
const showView = ref(false)
const showImg = ref(false)
let areas = []
const area = ref({})
let ratio = 1
const userImg = ref('')
let originImg = ''
const recordId = ref('')
const showPlayer = ref(false)
let dpr = 1
let taskId
//组件ref
const imgRefs = ref()
onMounted(() => {
	nextTick(() => {
		width.value = 1300 / 5
	})
	getChineseList()
})
watch(() => keyword.value, () => {
	let listData = []
	let tempList = JSON.parse(JSON.stringify(allList))
	tempList.forEach(item => {
		if (item.studentName.toUpperCase().indexOf(keyword.value.toUpperCase()) > -1) {
			listData.push(item)
		}
	})
	list.value = []
	nextTick(() => {
		list.value = calcList(listData, 5)
	})
})
const widthControl = ref('100%')
const refreshArea = ref(false)
watch(() => area.value, () => {
	changePoints()
})
//原始points
let oriPoints= []
//修改比例''
function changePoints(){
	if (area.value && area.value.words) {
		let index = 0;
		area.value.words.forEach(item => {			
			if (typeof (item.areaPoints) == 'string') {
				oriPoints.push(item.areaPoints)
			}
			item.areaPoints = JSON.parse(oriPoints[index])
			item.areaPoints.x = parseInt(item.areaPoints.x * ratio) / dpr + 'px'
			item.areaPoints.y = (parseInt(item.areaPoints.y * ratio)) / dpr + 'px'
			item.areaPoints.width = parseInt(item.areaPoints.width * ratio) / dpr + 'px'
			item.areaPoints.height = parseInt(item.areaPoints.height * ratio) / dpr + 'px'
			index++
		})
	}
}

function closePlayer() {
	showPlayer.value = false
}
function getChineseList() {
	ClassroomRecordRequest.studentInteractDeails({ interactId: queryData.interactId }).then(res => {
		if (res.code == 1) {
			list.value = calcList(res.data, 5)
			allList = res.data
			taskId = res.data[0].taskId
		}
		showView.value = true
	}).catch(error => {
		console.log(error)
	})
}
function calcList(arr, num) {
	let newArr = []
	if (arr.length) {
		for (let i = 0; i < arr.length;) {
			newArr.push(arr.slice(i, i += num));
		}
	}
	const item = newArr[newArr.length - 1]
	if (item.length < num) {
		for (let i = item.length; i < num; i++) {
			item.push({})
		}
	}

	return newArr
}
function openDialog(item) {
	if (!item.interactId) {
		return
	}
	if (item.state == 1) {
		ElMessage.error(('该学生未作答'))
		return
	}
	let params = {
		"interactId": item.interactId,
		"studentId": item.studentId
	}
	ClassroomRecordRequest.interactStudentWords(params).then(res => {
		dialogObj.value = { ...item, ...res.data.detail }
		res.data.records.forEach(item => {
			if (!item.mergeImgUri) {
				item.mergeImgUri = item.page.recogImgurl
			}
		})
		dialogRecords.value = res.data.records
		dialogIndex.value = 0
		dialogVisible.value = true
	}).catch(error => {
		if (error.message) {
			ElMessage.error(error.message)
		}
	})
}
function changeWidth() {
	widthControl.value = "50%"
	refreshArea.value = true
	setTimeout(()=>{
		
		ratio = imgRefs.value.clientWidth / 21000
		changePoints()
		refreshArea.value = false
	},200)
	
}

function openPlayer() {
	
	if (dialogRecords.value[dialogIndex.value].recordStatus == 0) {
		ElMessage.error('未作答，没有笔迹数据')
		return
	}
	let record = dialogRecords.value[dialogIndex.value]
	userImg.value = record.mergeImgUri
	originImg = record.page.recogImgurl
	recordId.value = record.recordId
	showPlayer.value = true
}
function next() {
	if (dialogIndex.value < dialogRecords.value.length - 1) {
		dialogIndex.value++
	}
}
function pre() {
	if (dialogIndex.value > 0) {
		dialogIndex.value--
	}
}
function imgLoad(item, index) {
	showImg.value = true
	if (areas.length) {
		area.value = areas[index]
		return
	}
	ratio = imgRefs.value.clientWidth / 21000
	ClassroomRecordRequest.wordsRecordScore({ taskId: taskId, extIds: [item.extId] }).then(res => {
		if (res && res.data.length > 0) {
			areas = res.data
			area.value = res.data[0]
		}
	}).catch(err => console.log(err))
}

</script>

<style lang="scss">
.record-words-body {
	.el-dialog {
		padding: 0px !important;
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-top: 0px !important;
		width: auto;
		background-color: transparent;
		margin-bottom: 0px !important;

		.el-dialog__body {
			height: 846px;
		}

		.el-dialog__header {
			display: none;
		}

		.el-dialog__footer {
			display: none;
		}
	}
}
</style>
<style lang="scss" scoped>
.record-words {
	height: 100%;

	.main-con {
		height: calc(100% - 86px);
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		padding-bottom: 0;
	}

	.search {
		font-size: 23px;
		padding-top: 0;
		color: #2e4a66;
		font-weight: 600;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 16px 20px;
		height: 86px;
		box-sizing: border-box;
	}

	.paper-list {
		overflow: auto;

	}

	.paper-list::-webkit-scrollbar {
		display: none;
	}

	.player-mask {
		width: 1357px;
		height: 846px;
		position: fixed;
		left: 0;
		top: 0;
		z-index: 100000;
		border-radius: 26px;
		background-color: rgba(0, 0, 0, 0.8);
	}

	.rate {
		position: absolute;
		width: 48px;
		bottom: -23px;
		left: -12px;
	}

	.area {
		position: absolute;
	}

	.icon_arrow {
		width: 30px !important;
		height: 30px !important;
		margin: 20px;
		cursor: pointer;
		position: relative;
		// bottom: 40vh;
	}

	.empty {
		text-align: center;
		color: #999;
		font-size: 16px;
		margin-top: 100px;
	}

	.dialog_content {
		height: 840px;
		display: flex;
		justify-content: center;
		align-items: center;
		position: fixed;
		border-radius: 26px;
		top: 0;
		background-color: rgba(0, 0, 0, 0.2);
		width: 100%;
		height: 100%;
		

		.area .record-rate {
			position: absolute;
			width: 48px;
			bottom: -17px;
			left: -12px;
		}
	}

	.icon {
		font-size: 50px;
		margin: 0 20px;
		color: #fff;
		cursor: pointer;
	}

	.desc {
		color: #fff;
	}

	.el-icon-circle-close {
		color: #fff;
		position: absolute;
		bottom: 5px;
		font-size: 30px;
		right: -50px;
	}

	.input_wrap {
		margin: 10px;
		display: flex;
		justify-content: flex-end;
	}

	.input {
		width: 300px;
	}

	.item_wrap {
		display: flex;
		justify-content: space-between;
	}

	.item {
		margin-bottom: 20px;
		display: flex;
		flex-direction: column;
		justify-content: space-around;
	}

	.img-body::-webkit-scrollbar {
		display: none;
		/* 适用于 Chrome, Safari 和 Opera */
	}

	.img-body {
		flex: 1;
		height: calc(100% - 50px);
		width: 100%;
		overflow-y: auto;

		scrollbar-width: 0px;
		display: flex;
		justify-content: center;
	}

	.img {
		object-fit: contain;
		flex: 1;
		background-color: #fff;
		width: 100%;
		height: 100%;
	}

	.desc {
		height: 50px;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.left {
		display: flex;
		align-items: center;
		font-size: 16px;
	}

	.right {
		cursor: pointer;
	}

	.avatar {
		width: 30px;
		height: 30px;
		border-radius: 15px;
		border: 1px solid #ccc;
		margin-right: 10px;
		overflow: hidden;
		background-size: 100% 100%;
	}

	.center {
		display: flex;
	}
}
</style>