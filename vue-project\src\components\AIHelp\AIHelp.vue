<template>
  <div class="ai-help-body-style" @click.stop="clickBody" v-show="showAIHelp">
    <RBPAlert :title="'智能助教'" width="1200px" height="846px" @close="closeWindow">
      <template v-slot:rbpDiv>
        <div class="content" @click.stop="" v-loading="loading">

          <div class="message-area">
            <AIMessageArea ref="aiMessages" :messages="messages" @reSend="reSend"></AIMessageArea>
          </div>
          <div class="recording-area">
            <RBPButton width="288px" :btnSelected="true" class="record-button" :class="{ 'recording': isRecording }"
              :btnText="isRecording ? '发送问题' : '开始提问'" @click="toggleRecording"></RBPButton>
          </div>

          <div class="new-chat">
            <RBPButton width="120px" btnText="新对话" @click="openAI"></RBPButton>
          </div>
        </div>
      </template>
    </RBPAlert>
    >
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref } from 'vue'
import RBPButton from '../baseComponents/RBPButton.vue';
import RBPAlert from '../baseComponents/RBPAlert.vue';
import AIMessageArea from './AIMessageArea.vue';
import { storeToRefs } from 'pinia'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { LocalRequest } from '@/local_request/local_request';
import { ElMessage } from 'element-plus';
import { AIRequest } from '@/ai_request/ai_request';
import { loginInstance } from '@/login_instance/login_instance';
const loading = ref(false)
const aiMessages = ref(null)
const classroomUIStore = useClassroomUIStore()
const { showAIHelp } = storeToRefs(classroomUIStore)
const messages = ref([]);
const isRecording = ref(false);
const mediaRecorder = ref(null);
const audioChunks = ref([]);

let nowRequestKey = -1
let countKey = 0

function scrollBottom() {
  if (aiMessages.value && aiMessages.value.scrollBottom) {
    setTimeout(() => {
      aiMessages.value.scrollBottom()
    }, 200)
  }
}
const toggleRecording = async () => {

  if (!isRecording.value) {
    try {
      stopPlay()
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorder.value = new MediaRecorder(stream);
      audioChunks.value = [];

      mediaRecorder.value.ondataavailable = (event) => {
        audioChunks.value.push(event.data);
      };

      mediaRecorder.value.onstop = async () => {
        const audioBlob = new Blob(audioChunks.value, { type: 'audio/wav' });
        const formData = new FormData();
        let fileName = `${Date.now()}voice.wav`
        const blobArrayBuffer = await audioBlob.arrayBuffer()
        formData.append('file', audioBlob, fileName)
        let audioCOntext = new AudioContext()
        const audioBuffer = await audioCOntext.decodeAudioData(blobArrayBuffer)
        let duration = formatDuration(audioBuffer.duration)
        // sendAI(audioBlob)
        // return
        try {
          const response = await LocalRequest.uploadAudio(formData);
          if (response) {
            countKey++
            messages.value.push({
              type: 'user',
              audioUrl: getAudioPath(fileName),
              duration,
              isPlaying: false,
              progress: 0,
              blob: audioBlob,
              key: countKey
            });
            scrollBottom()
            nowRequestKey = countKey
            sendAI(audioBlob)
          }
        } catch (error) {
          ElMessage.error("录音失败")
          console.error('上传音频失败:', error);
        }
      };

      mediaRecorder.value.start();
      isRecording.value = true;
    } catch (error) {
      ElMessage.error("录音失败，请查看麦克风")
      console.error('获取麦克风权限失败:', error);
      isRecording.value = false;
    }
  } else {
    stopRecord()
  }
}

function dealError() {
  let item = messages.value.length ? messages.value[messages.value.length - 1] : {}
  if (item.type == 'ai' && item.key == nowRequestKey) {
    messages.value.length = messages.value.length - 1
  }
  for (let i = messages.value.length - 1; i >= 0; i--) {
    let item = messages.value[i]
    if (item.key == nowRequestKey) {
      item.error = true
      break
    }
  }

}

function stopRecord() {
  mediaRecorder.value.stop();
  isRecording.value = false;
  mediaRecorder.value.stream.getTracks().forEach(track => track.stop());
}




function formatDuration(seconds) {
  if (seconds === undefined || seconds === null) {
    return 0
  }
  seconds = Math.floor(seconds); // 保证是整数

  const hrs = Math.floor(seconds / 3600);
  const mins = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  const paddedSecs = secs.toString().padStart(2, '0');
  const paddedMins = mins.toString().padStart(2, '0');

  if (hrs > 0) {
    return `${hrs}:${paddedMins}:${paddedSecs}`;
  } else {
    return `${paddedMins}:${paddedSecs}`;
  }
}


function closeWindow() {
  showAIHelp.value = false
}


function getAudioPath(name) {
  let host = window.location.host
  return `http://${host}/store/audios/${name}`
}


onMounted(() => {
  openAI()
})



onUnmounted(() => {
  if (isRecording.value) {
    stopRecord()
  }
})
async function openAI() {
  loading.value = true
  var res = await AIRequest.openChat(loginInstance.teacher.teacherId)
  loading.value = false
  if (!res) {
    ElMessage.error("网络错误")
    // closeWindow()
  } else {
    stopPlay()
    //开启新对话清空
    messages.value.length = 0

  }
}

function stopPlay() {
  if (aiMessages.value) {
    aiMessages.value.togglePlay()
  }
}
function reSend(blob, key) {
  nowRequestKey = key
  sendAI(blob)
}
// let tempCount = 0
async function sendAI(blob) {
  loading.value = true

  var res = await AIRequest.uploadVoiceStreamReal(
    loginInstance.teacher.teacherId,
    blob,
    {
      onText: (data) => {
        addContent(data, false)
      },
      onAudio: (data) => {
        addContent(data, true)
      },
      onError: (e) => {

        ElMessage.error(e ? (e.message ?? "提问失败") : '提问失败')
        dealError()
        loading.value = false
      }
    }
  )
  loading.value = false
}

function addContent(data, isAudio) {
  let item = messages.value.length ? messages.value[messages.value.length - 1] : {}
  if (item.type != 'ai' || item.key != nowRequestKey) {
    item = {
      type: 'ai',
      audioUrl: '',
      duration: 0,
      isPlaying: false,
      progress: 0,
      content: '',
      key: nowRequestKey,
      aiContent: '',
    }
    messages.value.push(item)
  }
  if (isAudio) {
    item.audioUrl = data
    for (let i = messages.value.length - 2; i >= 0; i--) {
      let itemData = messages.value[i]
      if (itemData.key == nowRequestKey) {
        itemData.blob = null
        itemData.error = false
        break
      }
    }
    scrollBottom()
  } else {
    let content = data + ''
    if (content.includes('→(AI生成)')) {
      let contentS = content.split('→(AI生成)')
      for (let i = messages.value.length - 2; i >= 0; i--) {
        let itemData = messages.value[i]
        if (itemData.key == nowRequestKey) {
          itemData.content = contentS[0].substring(0, contentS[0].length - 1)
          break
        }
      }
    } else {
      item.content += data
    }
    scrollBottom()

  }
}




</script>

<style lang="scss">
.ai-help-body-style {
  .el-loading-mask {
    background-color: transparent !important;
  }
}
</style>

<style lang="scss" scoped>
.ai-help-body-style {
  z-index: var(--interact-alert-phet-z-index);
  background-color: rgba($color: var(--main-anti-bc-color-rgb), $alpha: 0.2);
  opacity: 1;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  position: fixed;

  .content {
    height: 100%;
    width: 100%;
    position: relative;
    display: flex;
    flex-direction: column;

    .new-chat {
      position: absolute;
      left: 24px;
      bottom: 25px;
    }

    .message-area {
      height: calc(100% - 100px);
    }

    .recording-area {
      height: 75px;
      background-color: var(--main-bc-color);
      border-top: 1px solid var(--border-bar-color);
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      padding-top: 21px;
    }

    .record-button {
      transition: all 0.3s ease;
    }


    .record-button.recording {
      background-color: #dc3545;
      animation: pulse 1.5s infinite;
    }

    @keyframes pulse {
      0% {
        transform: scale(1);
      }

      50% {
        transform: scale(1.05);
      }

      100% {
        transform: scale(1);
      }
    }
  }



}
</style>