import * as THREE from 'three';
import { MeshLineGeometry, MeshLineMaterial } from 'meshline';
import { Line2 } from 'three/examples/jsm/lines/Line2.js';
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial.js';
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry.js';

import { BoardView } from "./board_view";
import { PenStyle, PainterOrder, EditMode, DrawMode, DrawZ, BoardLineRange, BoardMeshLineRange } from './draw_enums';
import { BoardImageView } from './board_image_view';
import { BoardTool } from './board_tool';

/// 老师绘制视图

export class BoardRootView extends BoardView {

    constructor(application, pos, size) {
        super(application, pos, size)

        this.lineWidth = 2
        this.lineColor = 0x000000
        this.meshLineWidth = 0.003

        this.lineMaterial = new LineMaterial({
            color: this.lineColor,
            linewidth: this.lineWidth, // 线条宽度
        });

        this.meshLineMaterial = new MeshLineMaterial({
            color: new THREE.Color(this.lineColor),
            lineWidth: this.meshLineWidth, // 线宽度
            side: THREE.DoubleSide,
            sizeAttenuation: false
        })

        this.totalLineCaches = []

        this.tempLineCaches = []

        this.points = []

        this.drawInSubviews = new Set()

        this.eraserSize = 0.02;

        // 画笔样式
        this.penStyle = PenStyle.Line2

        // 默认画笔
        this.drawMode = DrawMode.pen

        // 默认模式是拖拽模式
        this.editMode = EditMode.Dragging

        // 临时绘制
        this.tempDraw = false

        this.addDrawView()

        this.addEraserView()
    }

    addEraserView() {
        if (!this.eraserView) {
            this.eraserView = new BoardImageView(
                this.application,
                new THREE.Vector3(0, 0, 0),
                { width: this.eraserSize * 2, height: this.eraserSize * 2 }, true)
            // this.eraserView.setImageUrl('img/eraser_button.png')
            this.eraserView.setImageUrl('img/svg/eraser_button.svg')
            this.eraserView.renderOrder = PainterOrder.eraserObject
            this.eraserView.visible = false
            this.addSubView(this.eraserView)
        }
    }

    resetEeaserImage(imageUrl) {
        if (this.eraserView) {
            this.eraserView.setImageUrl(imageUrl)
        }
    }

    addDrawView() {
        if (!this.totalDrawGroup) {
            this.totalDrawGroup = new BoardView(this.application, new THREE.Vector3(0, 0, 0), { width: 0, height: 0 })
            this.totalDrawGroup.renderOrder = PainterOrder.teacherPoints
            this.totalDrawGroup.visible = !this.tempDraw
            this.addSubView(this.totalDrawGroup)

            this.tempDrawGroup = new BoardView(this.application, new THREE.Vector3(0, 0, 0), { width: 0, height: 0 })
            this.tempDrawGroup.renderOrder = PainterOrder.teacherPoints
            this.tempDrawGroup.visible = this.tempDraw
            this.addSubView(this.tempDrawGroup)
        }
    }


    /// 将笔迹添加到其中子视图中， 跟随子视图移动
    addDrawInSubView(subview) {
        this.drawInSubviews.add(subview)
    }

    removeDrawInSubView(subview) {
        subview.children.forEach(child => {
            if (child.penStyle !== undefined) {
                // 找到 child 在 this.totalLineCaches 中的索引
                const index = this.totalLineCaches.indexOf(child);
                // 如果存在，则从 this.totalLineCaches 中移除
                if (index !== -1) {
                    this.totalLineCaches.splice(index, 1);
                }
            }
        })
        this.drawInSubviews.delete(subview)
    }

    updateEraserWidth(width) {
        this.eraserSize = width
        if (this.eraserView) {
            this.eraserView.setSize({ width: width * 2, height: width * 2 })
        }
    }

    /// 设置线宽和颜色 老师绘制使用
    /// 默认宽度是0.003 这里是相对three的3D世界坐标比例
    setLineStyle(lineWidth = 2, lineColor = 0xff0000) {
        if (this.lineWidth === lineWidth && this.lineColor === lineColor) {
            return
        }
        let percent = (lineWidth - BoardLineRange.min) / BoardLineRange.range
        this.meshLineWidth = BoardMeshLineRange.min + BoardMeshLineRange.range * percent

        // 创建 MeshLineMaterial
        this.lineWidth = lineWidth
        this.lineColor = lineColor
        this.lineMaterial = new LineMaterial({
            color: this.lineColor,
            linewidth: lineWidth, // 线条宽度
        });

        this.meshLineMaterial = new MeshLineMaterial({
            color: new THREE.Color(this.lineColor),
            lineWidth: this.meshLineWidth, // 线宽度
            side: THREE.DoubleSide,
            sizeAttenuation: false
        })
        this.animate()
    }

    setDrawMode(mode) {
        this.drawMode = mode
    }

    setPenStyle(penStyle) {
        this.penStyle = penStyle
    }

    setEditMode(mode) {
        this.editMode = mode
    }


    setTempDraw(tempDraw) {
        this.tempDraw = tempDraw
        this.tempDrawGroup.visible = tempDraw
        this.totalDrawGroup.visible = !tempDraw
        if (!tempDraw) {
            this.tempLineCaches.forEach((item) => {
                BoardTool.disposeMesh(item)
            })
        }
        this.animate()
    }

    startDrawing() {
        
        if (this.penStyle === PenStyle.MeshLine) {
            // 创建初始的 MeshLineGeometry
            let lineGeometry = new MeshLineGeometry()
            this.lineMesh = new THREE.Mesh(lineGeometry, this.meshLineMaterial)
            this.lineMesh.penStyle = PenStyle.MeshLine
        }
        else {
            let lineGeometry = new LineGeometry()
            this.lineMesh = new Line2(lineGeometry, this.lineMaterial)
            this.lineMesh.penStyle = PenStyle.Line2
        }
        this.lineMesh.renderOrder = PainterOrder.teacherPoints

        if (this.tempDraw) {
            this.tempLineCaches.push(this.lineMesh)
        }
        else {
            this.totalLineCaches.push(this.lineMesh)
        }

        if (this.drawInView) {
            this.drawInView.add(this.lineMesh)
        }
        else {
            if (this.tempDraw) {
                this.tempDrawGroup.add(this.lineMesh)
            }
            else {
                this.totalDrawGroup.add(this.lineMesh)
            }
        }
    }

    limitCurveVariation(points, maxAngle = Math.PI / 6) {
        if (points.length < 3) return points;
        let filtered = [points[0]];
        for (let i = 1; i < points.length - 1; i++) {
            let v1 = new THREE.Vector3().subVectors(points[i], filtered[filtered.length - 1]).normalize();
            let v2 = new THREE.Vector3().subVectors(points[i + 1], points[i]).normalize();
            let angle = v1.angleTo(v2);
            if (angle < maxAngle) {
                filtered.push(points[i]);
            }
        }
        filtered.push(points[points.length - 1]);
        return filtered;
    }

    medianFilter(points, windowSize = 3) {
        const filteredPoints = [];
        for (let i = 0; i < points.length; i++) {
            const start = Math.max(0, i - Math.floor(windowSize / 2));
            const end = Math.min(points.length, i + Math.floor(windowSize / 2) + 1);
            const neighbors = points.slice(start, end);

            // 获取当前点的坐标（x, y, z）
            const xValues = neighbors.map(p => p.x);
            const yValues = neighbors.map(p => p.y);
            const zValues = neighbors.map(p => p.z);

            // 排序并取中值
            const median = (arr) => arr.sort((a, b) => a - b)[Math.floor(arr.length / 2)];
            filteredPoints.push(new THREE.Vector3(median(xValues), median(yValues), median(zValues)));
        }
        return filteredPoints;
    }

    updateLine() {

        if (!this.points || this.points.length < 2) {
            return;
        }
        if (!this.lineMesh) {
            this.startDrawing()
        }
        if (this.penStyle === PenStyle.MeshLine) {
            // 更新 MeshLineGeometry
            // 确保 Mesh 的几何体是最新的
            this.lineMesh?.geometry?.setPoints(this.points)
        }
        else {
            let points = []
            for (let i = 0; i < this.points.length; i++) {
                points.push(this.points[i].x, this.points[i].y, this.points[i].z)
            }
            this.lineMesh?.geometry?.dispose()
            let lineGeometry = new LineGeometry();
            // 更新 MeshLineGeometry
            lineGeometry.setPositions(points);
            // 确保 Mesh 的几何体是最新的
            if (this.lineMesh) {
                this.lineMesh.geometry = lineGeometry;
            }
        }
        this.lineMesh.cachePoints = this.points
    }

    onEraseWithPoint(group, pos) {
        group.children.forEach((line) => {
            // 将擦除点转换到当前线条的局部坐标系
            // 检测线条与橡皮擦的位置，删除靠近橡皮擦的点
            const localPoint = new THREE.Vector3().copy(pos);
            line.worldToLocal(localPoint);
            if (line.penStyle!== undefined) {
                let points = line.cachePoints
                let change = false
                let remove = true
                for (let point of points) {
                    const distance = Math.sqrt(
                        Math.pow(Math.abs(point.x - localPoint.x), 2) +
                        Math.pow(Math.abs(point.y - localPoint.y), 2)
                    );
                    if (distance < this.eraserSize) {
                        point.z = undefined;
                        change = true
                    }
                    if( point.z!== Infinity && point.z!==-Infinity && point.z !== undefined){
                        remove = false
                    }
                    
                }
                if (remove) {
                    
                    line.parent.remove(line)
                    let index = this.totalLineCaches.indexOf(line)
                    if (index >= 0) {
                        this.totalLineCaches.splice(index, 1); // 从索引位置删除1个元素
                    }
                    index = this.tempLineCaches.indexOf(line)
                    if (index >= 0) {
                        this.tempLineCaches.splice(index, 1); // 从索引位置删除1个元素
                    }
                }
                else if (change) {
                    if (line.penStyle === PenStyle.MeshLine) {
                        line.geometry.setPoints(points)
                    }
                    else if (line.penStyle === PenStyle.Line2) {
                        let newPoints = []
                        for (let i = 0; i < points.length; i++) {
                            newPoints.push(points[i].x, points[i].y, points[i].z)
                        }
                        // 更新 MeshLineGeometry
                        line.geometry.setPositions(newPoints);
                    }
                }
            }
        });
    }

    onTouchDown(point) {
        let view = super.onTouchDown(point)
        if (view?.draggable ?? false) {
            // 如果按下点所在的视图可以拖动，则不进行以下绘制
            this.subviewDragging = true
            return view
        }
        if (this.editMode === EditMode.Drawing) {
            this.drawInView = null
            for (let item of this.drawInSubviews) {
                if (item.onPointInside(point)) {
                    this.drawInView = item
                    break
                }
            }
            if (this.drawMode === DrawMode.pen) {
                this.points = []
            }
            else if (this.drawMode === DrawMode.eraser) {
                this.eraserView.visible = true
                let cvtPoint = this.convertPoint(point, DrawZ.objcZ)
                this.eraserView.position.set(cvtPoint.x, cvtPoint.y, 0)
                this.animate()
            }
        }
        return view
    }

    onMultiTouchDown(event,point){
        
        let view = super.onMultiDown(point)
        let canSacle = view?.scalable ?? false
        if(!canSacle&&view?.parent){
            view = view.parent
        }
        if (view?.scalable ?? false) {
            // 如果按下点所在的视图可以拖动，则不进行以下绘制
            this.subviewScaling = true
            this.scaleView = view
            return view
        }
        return null
    }

    onRootWhell(event,point){
        let view = super.onWhell(point)
        if (view?.scalable ?? false) {
            return view
        }
        return null
    }
    
    onSubViewScale(scale){
        
        if(this.scaleView&&this.scaleView.changeScale){
            this.scaleView.changeScale(scale)
        }
    }

    onTouchMove(point) {
        if (this.subviewDragging||this.subviewScaling) {
            return super.onTouchMove(point)
        }
        if (this.editMode === EditMode.Drawing) {
            let cvtPoint = this.convertPoint(point, DrawZ.objcZ)
            if (this.drawMode === DrawMode.pen) {
                if (this.drawInView) {
                    let localPos = this.drawInView.worldToLocal(cvtPoint)
                    localPos.z = 0
                    this.points.push(localPos)
                }
                else {
                    cvtPoint.z = 0
                    this.points.push(cvtPoint)
                }
                this.updateLine()
                let application = this.application?.deref()
                application?.setLastDrawX(cvtPoint.x)
            }
            else if (this.drawMode === DrawMode.eraser) {
                this.eraserView.position.set(cvtPoint.x, cvtPoint.y, 0)
                this.onEraseWithPoint(this.tempDraw ? this.tempDrawGroup : this.totalDrawGroup, this.eraserView.position)
                this.drawInSubviews.forEach((view) => {
                    this.onEraseWithPoint(view, this.eraserView.position)
                })
                this.animate()
            }
        }
        return super.onTouchMove(point)
    }

    

    onTouchUp(point) {
        if (this.points.length > 2) {
            const curve = new THREE.CatmullRomCurve3(this.points);
            curve.curveType = 'centripetal';
            let smoothPoints = curve.getPoints(this.points.length * 4);
            this.points = this.limitCurveVariation(smoothPoints);
            this.updateLine()
        }
        this.points = []
        this.drawInView = null
        this.lineMesh = null
        this.eraserView.visible = false
        this.subviewDragging = false
        this.subviewScaling = false
        this.scaleView = null
        return super.onTouchUp(point)
    }

    /// 撤销一条线
    cancelALine() {
        let lineCaches = this.totalLineCaches
        if (this.tempDraw) {
            lineCaches = this.tempLineCaches
        }
        if (lineCaches.length && lineCaches.length > 0) {
            const caches = lineCaches.splice(-1)
            BoardTool.disposeMesh(caches[0])
        }
        this.animate()
    }

    /// 清除所有笔画
    clearAllLines() {
        if (this.tempDraw) {
            this.tempLineCaches.forEach((item) => {
                BoardTool.disposeMesh(item)
            })
            this.tempLineCaches = []
        }
        else {
            this.totalLineCaches.forEach((item) => {
                BoardTool.disposeMesh(item)
            })
            this.totalLineCaches = []
        }
        this.animate()
    }

    /// 清除所有内容
    clearAllContents() {
        this.dispose()
        this.addEraserView()
        this.addDrawView()
        this.animate()
    }


    dispose() {
        this.totalLineCaches = []
        this.tempLineCaches = []
        this.eraserView = null
        this.totalDrawGroup = null
        this.tempDrawGroup = null
        super.dispose()
    }
}