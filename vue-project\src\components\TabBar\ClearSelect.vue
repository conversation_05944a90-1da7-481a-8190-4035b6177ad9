<template>
    <div class="clear-select" @click.stop="showClearSelect = false">
        <div class="content" @click.stop>
            <RBPButton v-for="(item, index) in optionsList" @click="clickClear(item.key)" :key="index" :btn-text="item.name"
                :btn-selected="index == optionsList.length" font-size='14px' :width="`${index == optionsList.length?144:100}px`" height='40px'></RBPButton>

        </div>
    </div>
</template>
<script setup>
import { computed, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useDrawBoardStore } from '@/stores/drawboard_store'
import { UIFrames } from '@/classroom/frame_enums'
import RBPButton from '../baseComponents/RBPButton.vue';
import { clearType } from './tabbar_enums';
import { useClassroomUIStore } from '@/stores/classroom_ui_store';
const drawBoardStore = useDrawBoardStore()
const { showClearSelect } = storeToRefs(drawBoardStore)
const classroomUIStore = useClassroomUIStore()
const tabbarHeight = computed(() => UIFrames.tabbarHeight)
const optionsList = Object.values(clearType)


function clickClear(index) {
    classroomUIStore.showClearScreenAlert = index
    showClearSelect.value = false
}

</script>
<style lang="scss" scoped>
.clear-select {
    z-index: var(--color-select-z-index);
    position: absolute;
    height: 100%;
    width: 100%;
    background-color: var(--main-anti-bc-alpha-color);
    opacity: 1;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    padding-bottom: v-bind("tabbarHeight + 'px'");
    box-sizing: border-box;

    .content {
        width: 356px;
        background-color: var(--main-bc-color);
        border-radius: 10px;
        box-sizing: border-box;
        padding: 20px 16px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        gap: 12px 8px;
        // height: 160px;

        // .pen-lines {
        //     height: 80px;
        //     display: flex;
        //     justify-content: space-between;
        //     align-items: center;



        //     .active {
        //         border: 1px solid var(--primary-color);
        //     }
        // }
    }
}
</style>