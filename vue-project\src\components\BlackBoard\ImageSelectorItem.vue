<template>

    <div class="item-content">
        <img :src="getImageUrl()" @click="onImageClick" />
        <el-checkbox class="check-box" v-model="selected" @change="selectedChange" size="large"></el-checkbox>
    </div>

</template>

<script setup>
import { useDrawBoardStore } from '@/stores/drawboard_store';
import { storeToRefs } from 'pinia';
import { onMounted, ref, watch } from 'vue';

const drawStore = useDrawBoardStore()

const { imgSelectorRefresh } = storeToRefs(drawStore)

const props = defineProps({

    index: {
        type: Number
    },
    source: {
        type: Object
    }

})


const selected = ref(false)

onMounted(() => {
    selected.value = props.source.selected
})

watch(imgSelectorRefresh, () => {
    selected.value = props.source.selected
})


function getImageUrl() {
    return props.source.imageUrl
}


function selectedChange(selected) {
    props.source.selected = selected
    updateImageState()
}

function onImageClick() {
    props.source.selected = !props.source.selected
    selected.value = props.source.selected
    updateImageState()
}

function updateImageState() {
    if (drawStore.imgsGroup) {
        drawStore.imgsGroup.imageList.forEach((item) => {
            item.setFullDisplay(false)
        })
        drawStore.imgsGroup.updateDisplay()
    }
}
</script>


<style scoped>
.item-content {
    padding: 5px;
    width: 400px;
    height: 260px;
    position: relative;
    /* 添加这个属性 */
}

.item-content img {
    position: absolute;
    width: 400px;
    height: 260px;
    object-fit: cover;
    z-index: 0;
    /* 让图片以 cover 方式填充 */
}

.check-box {
    position: absolute;
    right: 5px;
    bottom: 5px;
    width: 15px;
    height: 15px;
    z-index: 1;
}
</style>