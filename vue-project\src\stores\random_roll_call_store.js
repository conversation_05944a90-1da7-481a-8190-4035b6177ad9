import { defineStore } from "pinia"
import { ref } from "vue"
import { ClassRoomRequest } from '@/server_request/classroom_request'
import { tdRecord } from '@/utils/talkingdata_tool'
import { useRbpAudioStore } from '@/stores/rbp_audio_store'

export const useRandomRollCallStore = defineStore('randomRollCall', () => {
    const sourceGroupList = ref([])
    const levelMap = ref(null)
    const groupList = ref([])
    const stu = ref(null)
    let stuId = -1
    const isMinimize = ref(false)
    const changeBtnDisable = ref(false)
    const countFlag = ref(0)
    let nowScore = 0

    async function levelClassCall() {
        const res = await ClassRoomRequest.levelClassCall()
        if (res.code == 1) {
            if (Array.isArray(res.data?.levelCall)) {
                let levelCall = JSON.parse(res.data?.levelCall)
                levelMap.value = new Map()
                levelCall.forEach(level => {
                    if (level.value == 0) {
                        levelMap.value[level.levelId] = 0
                    } else if (level.value == 50) {
                        levelMap.value[level.levelId] = 1
                    } else if (level.value == 100) {
                        levelMap.value[level.levelId] = 2
                    }
                })
            } else {

            }
        }
    }

    async function setup(studentList, sourceList,student) {
        await levelClassCall()
        sourceGroupList.value = sourceList
        if (sourceGroupList.value?.length > 0) {
            groupList.value = getGroupList(sourceGroupList.value)
        } else {
            groupList.value = getGroupList(studentList)
        }
        if (!isMinimize.value) {
            changeStu(student)
            tdRecord('随机点名')
        }else if(student){
            stu.value = student
            const rbpAudioStore = useRbpAudioStore();
            rbpAudioStore.play(student.nameTts); 
        }
    }

    function getGroupList(stuList) {
        if (levelMap.value == null || levelMap.value.size == 0) {
            return stuList
        } else {
            let copyList = []
            stuList.forEach(stu => {
                let num = levelMap.value[stu.levelId] ?? 0
                for (let i = 0; i < num; i++) {
                    copyList.push(stu)
                }
            })
            return copyList
        }
    }

    function changeStu(stuudent) {
        const rbpAudioStore = useRbpAudioStore();
        let selectedStu;
        let count = 10;
        if(groupList.value.length == 0) {
            return;
        }

        while (true&&!stuudent) {
            // 随机选择一个学生
            const randomIndex = Math.floor(Math.random() * groupList.value.length);
            selectedStu = groupList.value[randomIndex];
    
            // 如果只有一个学生，直接选择并退出循环
            if (groupList.value.length === 1) {
                break;
            }
    
            // 如果当前选中的学生与上一次选中的学生不同，退出循环
            if (stuId !== selectedStu.studentId) {
                break;
            }

            count = count - 1;
            if(count == 0) {
                break;
            }
        }
        if(stuudent){
            selectedStu = stuudent;
        }
        // 更新选中的学生ID
        stuId = selectedStu.studentId;
        stu.value = selectedStu;
    
        // 播放语音
        rbpAudioStore.play(selectedStu.nameTts);
    }

    function changeStu2() {
        const rbpAudioStore = useRbpAudioStore()
        var randomIndex = Math.floor(Math.random() * groupList.value.length)
        stu.value = groupList.value[randomIndex]
        if (stuId != -1 && groupList.value.length > 1) {
            if (stuId == stu.value.studentId) {
                changeStu()
            } else {
                stuId = stu.value.studentId
                //播放语音
                rbpAudioStore.play(stu.value.nameTts)
            }
        } else {
            stuId = stu.value.studentId
            //播放语音
            rbpAudioStore.play(stu.value.nameTts)
        }
    }

    function cleanData() {
        sourceGroupList.value = []
        levelMap.value = null
        groupList.value = []
        stu.value = null
        stuId = -1
        isMinimize.value = false
        changeBtnDisable.value = false

        const rbpAudioStore = useRbpAudioStore()
        rbpAudioStore.cleanData()
    }

    return {
        sourceGroupList,
        levelMap,
        groupList,
        stu,
        isMinimize,
        changeBtnDisable,
        countFlag,
        nowScore,
        levelClassCall,
        setup,
        changeStu,
        cleanData
    }
})