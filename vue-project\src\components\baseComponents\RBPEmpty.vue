<template>
    <div class="rbp-empty-body">
        <el-empty image="/img/empty_content.png" :description="des"></el-empty>
    </div>
</template>

<script setup>
const props = defineProps(
    {
        des:{
            default:"暂无内容"
        },
        imageWidth:{
            default:"269px"
        },
        imageHeight:{
            default:"246px"
        }
    }
)
</script>
<style lang="scss">
.rbp-empty-body{
    .el-empty__image {
        width: v-bind(imageWidth);
        height: v-bind(imageHeight);
    }
    .el-empty__description {
    margin-top: 26px;
    p{
        font-weight: 500;
    font-size: 24px;
    color: var(--explanatory-text-color);
    line-height: 36px;
    text-align: center;
    }
    }
}

</style>

<style lang="scss" scoped>

</style>