<template>
    <div class="modal-explain-alert" v-show="showAlert">
        <div class="content" :style="{ color: alertColor }">
            {{ alertTips }}
        </div>
    </div>
</template>
<script setup>
import { ref, computed, onMounted, getCurrentInstance, watch, defineProps, defineEmits, toRefs } from 'vue'
import { useExplainAlertStore } from '@/stores/explain_alert_store'
import { storeToRefs } from 'pinia'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { DisplayDirection, ViewStatus } from '@/classroom/frame_enums'

const explainAlertStore = useExplainAlertStore()
const { showAlert, alertTips, alertColor } = storeToRefs(explainAlertStore)

const classroomUIStore = useClassroomUIStore()
const { displayDirection, mainContentHeight, mainContentTopSpace } = storeToRefs(classroomUIStore)

const calcTop = computed(() => {
    return `calc(20px + ${mainContentTopSpace.value}px)`
})

const contentHeight = computed(() => {
    return `calc(${mainContentHeight.value}px - 20px)`
})
</script>
<style lang="scss">
.modal-explain-alert {
    position: absolute;
    top: v-bind(calcTop);
    width: 400px;
    height: v-bind(contentHeight);
    left: v-bind("displayDirection === DisplayDirection.Left ? '0' : null");
    right: v-bind("displayDirection === DisplayDirection.Left ? null : '0'");
    z-index: var(--tip-z-index);
    background-color: var(--main-anti-bc-alpha-color);
    opacity: 1;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
        background-color: var(--main-bc-color);
        max-width: 80%;
        font-size: 2vh;
        padding: 10px 20px;
        border-radius: 4px;
    }
}
</style>