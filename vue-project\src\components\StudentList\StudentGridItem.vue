<template>
    <div class="grid-item"
        :style="{ backgroundColor: getBgColor(student).bgColor, opacity: getBgColor(student).opacity }"
        :class="{ itemHidden: JSON.stringify(student) === '{}', changeBtnDisable: props.disable }" @click="studentClick(student)"
        @touchstart="handleTouchStart(student)" @touchend="handleTouchEnd" @dblclick="handleDoubleClick(student)">
        <div class="studentname" :style="{ color: getBgColor(student).nameColor }">
            {{ student.name }}
        </div>
        <div v-if="student&&student.score&&student.score!=0" class="score" :style="{ color: getBgColor(student).scoreColor }">
            {{ student.score ?? '0' }}分
        </div>
        <div class="battery-img" v-if="student.stuStatus == StuStatus.online">
            <img :src="batteryImageName(student)">
        </div>
        <div class="submit-index" v-show="student.answerSubmitIndex < 4">
            <img v-if="student.answerSubmitIndex < 4" :src="`/img/svg/icon_n_no${student.answerSubmitIndex}.svg`">
        </div>
        <div v-if="props.showStatus" class="status" :style="{ color: getStatus().color }">
            {{ getStatus().name }}
        </div>
    </div>
</template>
<script setup>
import { ref, onMounted, defineProps, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { StuStatus, getStuStatus } from '@/classroom/interact_enums.js'
import { useStudentInfoStore } from '@/stores/student_info_store'
import roomUpdater from '@/classroom/classroom_updater.js'
import { STUDENT_INDEX_MAX } from '@/stores/answers_store'
import { useInteractStore } from '@/stores/interact_store'
import { Interact, InteractStatus, isSingleObjective, isWriteMode } from "@/classroom/interact_enums"
import { useAnswersStore } from '@/stores/answers_store'
import { useClassroomStore } from '@/stores/classroom.js'
import { RBPColors, getColor } from '@/components/baseComponents/RBPColors.js'
import { remoteControl } from '@/remote_control/remote_control'

const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)
const answersStore = useAnswersStore()
const { studentAnswers, rightAnswer } = storeToRefs(answersStore)
const interactStore = useInteractStore()
const { interact } = storeToRefs(interactStore)
const studentInfoStore = useStudentInfoStore()
const { stuInfo } = storeToRefs(studentInfoStore)

const props = defineProps({
    student: Object,
    disable: {
        type: Boolean,
        default: false
    },
    isInterfaceSelection: {
        type: Boolean,
        default: true
    },
    showStatus:{
        default:true
    }
})
function getBgColor(student) {
    if (student.selected && props.isInterfaceSelection) {
        return {
            bgColor: getColor('--primary-color'),
            nameColor: getColor('--anti-text-color'),
            scoreColor: getColor('--anti-text-color'),
            opacity: 1
        }
    } else {
        if (student.stuStatus == StuStatus.online) {
            return {
                bgColor: getColor('--student-card-bg-online-color'),
                nameColor: getColor('--text-color'),
                scoreColor: getColor('--secondary-text-color'),
                opacity: 1
            }
        } else {
            return {
                bgColor: getColor('--student-card-bg-offline-color'),
                nameColor: getColor('--text-color'),
                scoreColor: getColor('--secondary-text-color'),
                opacity: 0.3
            }
        }
    }
}
function getStatus() {
    if (isWriteMode(interact.value)) {
        if (props.student.stuStatus == StuStatus.online) {
            if (props.student.writePageInfo|| props.student.writing) {
                if (props.student.answerSubmitIndex != STUDENT_INDEX_MAX) {
                    if (props.student.correct) {
                        return {
                            name: '已批改',
                            color: getColor('--correct-color')
                        }
                    }
                    return {
                        name: '已提交',
                        color: getColor('--correct-color')
                    }
                }
                return {
                    name: '作答中',
                    color: getColor('--text-color')
                }
            }
            return {
                name: '未作答',
                color: getColor('--error-color')
            }
        }
        return {
            name: '',
            color: getColor('--text-color')
        }
    }
    if (isSingleObjective(interact.value)) {
        if (props.student.stuStatus == StuStatus.online) {
            if (props.student.answerSubmitIndex != STUDENT_INDEX_MAX) {
                if (interact.value == Interact.singleChoice || interact.value == Interact.multiChoice) {
                    let answers = studentAnswers.value[String(props.student.studentId)][0]
                    let answer = answers.join('')
                    if (rightAnswer.value == '') {
                        return {
                            name: answer,
                            color: getColor('--text-color')
                        }
                    } else {
                        if (answer == rightAnswer.value) {
                            return {
                                name: answer,
                                color: getColor('--correct-color')
                            }
                        } else {
                            return {
                                name: answer,
                                color: getColor('--text-color')
                            }
                        }
                    }
                } else if (interact.value == Interact.trueFalse) {
                    let answers = studentAnswers.value[String(props.student.studentId)][0]
                    let answer = answers[0] == 'RIGHT' ? '对' : '错'
                    if (rightAnswer.value == '') {
                        return {
                            name: answer,
                            color: getColor('--text-color')
                        }
                    } else {
                        if (answer == rightAnswer.value) {
                            return {
                                name: answer,
                                color: getColor('--correct-color')
                            }
                        } else {
                            return {
                                name: answer,
                                color: getColor('--error-color')
                            }
                        }
                    }
                } else if (interact.value == Interact.responder) {
                    return {
                        name: '已提交',
                        color: getColor('--correct-color')
                    }
                }
            }
            return {
                name: '未作答',
                color: getColor('--text-color')
            }
        }
        return {
            name: '',
            color: getColor('--text-color')
        }
    }
    if (interact.value == Interact.multiQuestions) {
        if (props.student.stuStatus == StuStatus.online) {
            if (props.student.answerSubmitIndex != STUDENT_INDEX_MAX) {
                return {
                    name: '已提交',
                    color: getColor('--correct-color')
                }
            }
        }
    }
    return {
        name: '',
        color: getColor('--text-color')
    }
}
function batteryImageName(stu) {
    if (!stu.batteryLevel || stu.batteryLevel === 0) {
        return "/img/battery/icon_batterylow.png";
    } else if (stu.batteryLevel == 0xfe) {
        return "/img/battery/icon_battery_charging.png";
    } else if (stu.batteryLevel == 0xff) {
        return "/img/battery/icon_battery_complete.png";
    }
    return "/img/battery/icon_battery_" + stu.batteryLevel + ".png"
}

function studentClick(item) {
    item.selected = !item.selected

    selectedClassroom.value.groupStudentArray.forEach(item => {
        item.selected = false
    })
    remoteControl.handleInteractState()
}
function showStudentAddress(item) {
    stuInfo.value = item
    studentInfoStore.setOnlineDevices(roomUpdater.onlineDevices, roomUpdater.currentClassroom)
}
function handleDoubleClick(item) {
    showStudentAddress(item)
}
const touchTimeout = ref(null)
function handleTouchStart(item) {
    touchTimeout.value = setTimeout(() => {
        showStudentAddress(item)
    }, 1000)
}
function handleTouchEnd() {
    clearTimeout(touchTimeout.value)
}
</script>
<style lang="scss" scoped>
.grid-item {
    margin: 4px;
    height: 80px;
    // aspect-ratio: 5 / 3;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    cursor: pointer;

    .studentname {
        font-size: 21px;
    }

    .score {
        font-size: 18px;
        margin-top: 4px;
    }

    .battery-img {
        position: absolute;
        top: 4px;
        right: 10px;

        img {
            width: 20px;
            height: 11px;
            object-fit: contain;
        }
    }

    .submit-index {
        position: absolute;
        left: 3px;
        top: 0;
        width: 26px;
        height: 39px;

        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    }

    .status {
        position: absolute;
        right: 4px;
        bottom: 4px;
        font-size: 10px;
    }
}

.itemHidden {
    visibility: hidden;
}

.changeBtnDisable {
    pointer-events: none;
}
</style>