<template>
    <div class="blackBoard-bc-body">
        <div class="current">
            <div class="text">当前颜色:</div>
            <div class="color-bar" :style="{ background: color }"></div>
        </div>
        <div class="base">
            <div class="text">基础颜色:</div>
            <div class="btns">
                <div class="btn1 color-bar" @click="btnClick('#000000')"></div>
                <div class="btn2 color-bar" @click="btnClick('#ffffff')"></div>
                <div class="btn3 color-bar" @click="btnClick(defaultColor)"></div>
            </div>
        </div>
        <div class="custom">
            <div class="text">自定义颜色:</div>
            <div class="color-bar">
                <el-color-picker popper-class="color-select-picker" v-model="color" @change="changeColor">
                </el-color-picker>
                <!-- <input class="input-style" :style="{ background: color }" type="color" :value="color"
                    @change="changeColor"> -->
            </div>
        </div>
        <div class="paint-quality">
            <div v-if="isManage">黑板画质：</div>
            <div class="rbp-select" v-if="isManage">
                <el-select popper-class="rbq-select-body" v-model="nowPaint" placeholder="请选择" placement="top">
                    <el-option v-for="(item, index) in paintQualityList" :key="index" :label="item.name"
                        :value="item.value"></el-option>
                </el-select>
            </div>
            <!-- <RBPButton v-if="!isManage" btn-text="画板存档" class="painter-btn" btn-type="big" @click="painterSave()">
            </RBPButton>
            <RBPButton v-if="!isManage" btn-text="画板读档" background-color="var(--secondary-anti-color)" class="painter-btn" btn-type="big"
                @click="painterWrite()">
            </RBPButton> -->
        </div>
        <RBPButton btn-text="恢复默认" class="default-btn" :btn-selected="true" btn-type="big" @click="defaultColorClick">
        </RBPButton>
    </div>
</template>
<script setup>
import { onMounted, ref, watch } from 'vue'
import { useBlackBoardStore } from '@/stores/black_board_store'
import { storeToRefs } from 'pinia'
import { defaultColor } from '@/utils/db_helper'
import RBPButton from '../baseComponents/RBPButton.vue'
import { serverHost, } from '@/server_request/server_urls'
import { DrawQuality, DrawQualityName } from '@/drawboard/draw_enums'
import { useDrawBoardStore } from '@/stores/drawboard_store'
import { useDesktopStore } from '@/stores/desktop_store'
import { loginInstance } from '@/login_instance/login_instance'
import { useClassroomStore } from '@/stores/classroom'
import { ElMessage } from 'element-plus'
const emits = defineEmits(['close'])

const blackBoardStore = useBlackBoardStore()
const { color, } = storeToRefs(blackBoardStore)
const nowPaint = ref(serverHost.canvasResolution)
const paintQualityList = Object.keys(DrawQuality).map(key => ({
    name: DrawQualityName[key],
    value: DrawQuality[key],
    key,
}))
//是否在管理页面
const isManage = window.location.href.includes('/manage')
onMounted(() => {
    blackBoardStore.getColor()
})

watch(nowPaint, () => {
    serverHost.canvasResolution = nowPaint.value
    if (serverHost.appConfig) {
        for (let i = 0; i < paintQualityList.length; i++) {
            let item = paintQualityList[i]
            if (item.value == nowPaint.value) {
                serverHost.appConfig.canvas_resolution = item.key
                window.electron.updateAppConfig(JSON.stringify(serverHost.appConfig))
                break;
            }
        }

    }

})

function defaultColorClick() {
    nowPaint.value = DrawQuality.WQHD
    blackBoardStore.setDefaultColor()
}
function changeColor(event) {
    // let newColor = event.target.value
    blackBoardStore.setColor(event)
}
function btnClick(newColor) {
    blackBoardStore.setColor(newColor)
}

async function painterSave(){
    const drawBoardStore = useDrawBoardStore()
    drawBoardStore.saveBlackBoard()
    if(window.electron&&window.electron.savePainterConfig){
        const desktopStore = useDesktopStore()
        desktopStore.showDesktopView()
        emits('close')
        const classroomStore = useClassroomStore()
        let nowTime = new Date()
        drawBoardStore.serializeData.folderName = `${classroomStore.selectedClassroom.className}_${nowTime.getFullYear()}${(nowTime.getMonth() + 1+'').padStart(2, '0')}${(nowTime.getDate()+'').padStart(2, '0')}_${Date.now()}`

        let res = await window.electron.savePainterConfig(JSON.stringify(drawBoardStore.serializeData, null, 2))
        if(res === true){
            ElMessage.success("保存成功")
        }else{
            console.error("存档失败",res)
            ElMessage.error("保存失败")
        }
        desktopStore.quitDesktop()
    }

}

async function painterWrite(){
    if(window.electron&&window.electron.readPainterConfig){
        const desktopStore = useDesktopStore()
        desktopStore.showDesktopView()
        emits('close')
        let res = await window.electron.readPainterConfig()
        desktopStore.quitDesktop()
        if(res){
            const drawBoardStore = useDrawBoardStore()
            drawBoardStore.serializeData = JSON.parse(res)
            drawBoardStore.deserializeBlackBoard()
        }else{
            ElMessage.error("读档失败")
        }
    }
}
</script>
<style lang="scss">
@import url(../../components/baseComponents/RBPSelect.scss);


.blackBoard-bc-body {
    .el-color-picker--large {
        height: 72px;
    }

    .el-color-picker {
        .el-color-picker__trigger {
            width: 72px;
            height: 72px;
            border: none;
            padding: 0px !important;

            .el-color-picker__color {
                border: none;
            }

            .el-icon {
                display: none !important;
            }
        }

    }
}

.color-select-picker {
    .el-button.is-text {
        display: none; //隐藏清空按钮
    }
}
</style>
<style lang="scss" scoped>
.blackBoard-bc-body {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    padding: 36px 28px;

    .text {
        font-weight: 400;
        font-size: 21px;
        color: var(--text-color);
        line-height: 36px;
        margin-bottom: 6px;
    }

    .color-bar {
        width: 72px;
        height: 72px;
        border-radius: 9px;
        border: 2px solid var(--border-bar-color);
        cursor: pointer;
        overflow: hidden;
    }

    .current {
        width: 100%;
    }

    .base {
        width: 100%;
        margin-top: 15px;

        .btns {
            display: flex;
            align-items: center;
            gap: 40px;

            .btn1 {
                background-color: var(--main-anti-bc-color);
            }

            .btn2 {
                background-color: var(--main-bc-color);
            }

            .btn3 {
                background-color: v-bind(defaultColor);
            }
        }
    }

    .custom {
        width: 100%;
        margin-top: 15px;

        input[type="color"]::-moz-color-swatch {
            border: none;
        }

        input[type="color"]::-webkit-color-swatch-wrapper {
            padding: 0;
            border-radius: 0;
        }

        input[type="color"]::-webkit-color-swatch {
            border: none;
        }

        .input-style {
            width: 100%;
            height: 100%;
            border-radius: 9px;
            border: none !important;
            padding: 0px !important;
            margin: 0px;
        }
    }

    .paint-quality {
        width: 100%;
        display: flex;
        margin-top: 16px;
        height: 54px;
        font-weight: 400;
        align-items: center;
        font-size: 21px;
        color: var(--text-color);
        line-height: 36px;
        white-space: nowrap;

        .rbp-select {
            width: 200px;
        }

        .painter-btn {
            margin-right: 24px;
        }
    }

    .default-btn {
        margin-top: 35px;
        margin-right: 0px;
    }
}
</style>