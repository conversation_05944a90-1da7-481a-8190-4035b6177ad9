<template>
  <div class="calendar_con" @click.stop="">
    <div v-if="isCalendar" :class="'calendar ' + getHomeworkClass()">
      <v-calendar ref="calendar" :initial-page="initialPage" expanded :attributes="attrs" @dayclick="dayClick"
        @did-move="didMove"></v-calendar>
      <div class="dir-icon dir-icon-left" @click="move(true)">
        <el-icon :size="24">
          <ArrowLeftBold />
        </el-icon>
      </div>
      <div class="dir-icon dir-icon-right" @click="move(false)">
        <el-icon :size="24">
          <ArrowRightBold />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineEmits, nextTick, watch } from "vue"
import { formatRecordDate } from "./js/utils.js"
import { ClassroomRecordRequest } from '@/server_request/classroom_record.js'
import { useClassroomUIStore } from "@/stores/classroom_ui_store.js"
import { useClassroomStore } from "@/stores/classroom.js"
import { storeToRefs } from "pinia"
import { ArrowLeftBold, ArrowRightBold } from '@element-plus/icons-vue'
import { loginInstance } from "@/login_instance/login_instance.js"
const props = defineProps({
  date: {
    default: null
  }
})
const emits = defineEmits(['hideCalendar'])
const classroomUIStore = useClassroomUIStore()
const classroomStore = useClassroomStore()
const { showRecordType, showClassRecord } = storeToRefs(classroomUIStore)
const isCalendar = ref(true)
const calendar = ref(null)
const initialPage = ref(undefined)
const attrs = ref([
  {
    highlight: { contentStyle: { backgroundColor: '#25B0FA80' } },
    dates: [],
  },
  {
    key: "today",
    highlight: { contentStyle:{backgroundColor:'#ff0000'} },
    dates: new Date(),
    // dates: [new Date()],
  },
  {
    key: "choose",
    highlight: { contentStyle: { backgroundColor: '#25B0FA' } },
    dates: new Date(),
    // dates: [new Date()],
  },
])



onMounted(() => {
  getMonthData(formatRecordDate(new Date(), 'yyyyMM'))
  let nowDate = new Date()
  if (props.date) {
    nowDate = new Date(props.date)
  }
  changeMonth(nowDate)
})

function changeMonth(date, isEmits) {
  attrs.value[2].dates = date
  // let nowDate = new Date()
  // if (nowDate.getDate() == date.getDate()
  //   && nowDate.getFullYear() == date.getFullYear()
  //   && nowDate.getMonth() == date.getMonth()) {
  //   attrs.value[1].highlight.contentStyle.backgroundColor = '#ff0000'
  // } else {
  //   attrs.value[1].highlight.contentStyle.backgroundColor = '#25B0FA'
  // }
  if (calendar.value) {
    calendar.value.move(
      {
        month: date.getMonth() + 1,
        year: date.getFullYear(),
      }
    )

  }
  if (isEmits) {
    dayClick(
      {
        month: date.getMonth() + 1,
        year: date.getFullYear(), day: date.getDate()
      }
    )
  }
}
function move(isPrev) {
  if (calendar.value) {
    isPrev ? calendar.value.movePrev() : calendar.value.moveNext()
  }

}





const dayClick = async (currDay) => {

  let year = currDay.year
  let month = currDay.month > 9 ? "" + currDay.month : "0" + currDay.month
  let day = currDay.day > 9 ? "" + currDay.day : "0" + currDay.day
  let date = year + month + day
  let nowDate = new Date(`${year}-${month}-${day}`)
  changeMonth(nowDate)
  if (!isHomework()) {
    isCalendar.value = false

  }
  emits('hideCalendar', classroomStore.selectedClassroom.classId, '', date)
  // // 如果是作业，点击某一天直接关闭日历
  // if (showRecordType.value == 'homework') {

  //   return
  // }
  // let loading = ElLoading.service({ background: 'transparent' })
  // try {
  //   let params = {
  //     date: date,
  //     classId: classroomStore.selectedClassroom.classId,
  //     subjectId: loginInstance.subjectMainId,
  //   }
  //   let { data } = await ClassroomRecordRequest.getDayRecords(params)
  //   currDayList.value = data
  //   if (data && data.length) {
  //     data.forEach(item => {
  //       activeNames.value.push(item.classId)
  //     })
  //   }
  //   loading.close()
  // } catch (error) {
  //   console.log(error)
  //   loading.close()
  // }
}

const didMove = (fromPage) => {
  let year = fromPage[0].year
  let month = fromPage[0].month < 10 ? "0" + fromPage[0].month : "" + fromPage[0].month
  let date = year + month
  getMonthData(date)
}

const getMonthData = async (date) => {
  try {
    let params = {
      date: date,
      classId: classroomStore.selectedClassroom.classId,
      subjectId: !showClassRecord.value ? classroomStore.selectedClassroom.subjectId : loginInstance.subjectMainId,
    }
    let dates = []
    if (showRecordType.value == 'homework') { // 作业日历
      params.type = "6"
      params.current = true
      params.subjectMainId = params.subjectId
      params.subjectId = null
      let { data } = await ClassroomRecordRequest.getHomeworkHistory(params)
      dates = data
    } else { // 互动日历
      let { data } = await ClassroomRecordRequest.getHistory(params)
      dates = data
    }
    let selected = [];
    for (let d of dates) {
      let year = d.substring(0, 4);
      let month = d.substring(4, 6).indexOf(0) == 0 ? parseInt(d.substring(5, 6)) - 1 : parseInt(d.substring(4, 6)) - 1;
      let day = d.substring(6, 8);
      selected.push(new Date(year, month, day));
    }
    attrs.value[0].dates = selected;
  } catch (e) {
    console.log(e)
  }
}

function isHomework() {
  return showRecordType.value == 'homework'
}

function getHomeworkClass() {
  return isHomework() ? 'calendar-homework' : ''
}
defineExpose({ changeMonth })
</script>

<style lang="scss" scoped>
:deep(.vc-container) {
  background-color: transparent;
}

:deep(.vc-title) {
  color: var(--text-color);
  font-weight: 500;
  font-size: 32px;
  line-height: 47px;
  background-color: transparent;
}


:deep(.vc-arrow) {
  display: none;
}

:deep(.vc-weeks) {
  padding: 0px;
}

:deep(.vc-week) {
  padding-top: 4px;
  background-color: var(--main-bc-color);

  .vc-highlight {
    background-color: transparent;
  }

  --vc-focus-ring:0 0 0 2px rgb(59, 131, 246, 0)
}

:deep(.vc-weekdays) {
  background-color: rgba(255, 255, 255, 0.5);

}

:deep(.vc-header) {
  height: 101px;
  background-color: transparent;
  margin-top: 0px;
}

:deep(.vc-weekday) {
  height: 62px;
  font-weight: bold;
  font-size: 24px;
  color: var(--text-color);
  line-height: 36px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.vc-nav-header) {
  height: 70px;
  display: flex;
  justify-content: center;

  .vc-nav-arrow {
    height: 70px;
    background-color: transparent;
    color: #7D7D7D;
    position: absolute;
  }

  .is-left {
    left: 20% !important;
  }

  .is-right {
    right: 20%;
  }

  .vc-focus {
    border: none;
    box-shadow: none;
  }

  .vc-nav-title {
    height: 70px;
    background-color: transparent;
    font-weight: 500;
    font-size: 21px;
    color: var(--text-color);
    line-height: 31px;
  }
}

:deep(.vc-highlight-content-solid) {
  color: var(--anti-text-color) !important;
  // background-color: var(--primary-color);
}

:deep(.vc-day-content) {
  margin-bottom: 4px;
  font-size: 18px;
  height: 72px;
  width: 72px;
  font-weight: 500;
  font-size: 24px;
  line-height: 36px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;

}

:deep(.vc-bordered) {
  border: none !important;
}

:deep(.vc-popover-content.direction-bottom) {
  border-radius: 15px;
  border: 2px solid var(--border-bar-color);
  background-color: var(--course-record-calendar-header-bc-top-color);
  box-shadow: none;
}

:deep(.vc-popover-caret) {
  display: none;
}

:deep(.vc-nav-item) {
  width: 60px;
  height: 60px;
  box-shadow: none !important;
  text-align: center;
  font-weight: 500;
  font-size: 21px;
  color: var(--text-color);
  line-height: 31px;
  text-align: center;
  background-color: transparent;
  border-radius: 50%;
  margin: 2px 20px;

}

:deep(.vc-nav-item.is-active) {
  background-color: var(--primary-color);
  color: var(--anti-text-color);

}

.calendar_con {
  display: flex;
  flex-direction: column;
}

.calendar {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  overflow-y: auto;
  border-radius: 15px;
  background: var(--course-record-calendar-header-bc-color);
  position: relative;

  .dir-icon {
    position: absolute;
    height: 101px;
    display: flex;
    align-items: center;
    top: 0;
    color: #7D7D7D;

  }

  .dir-icon-left {
    left: 36%;
  }

  .dir-icon-right {
    right: 36%;
  }


}

.calendar-homework {
  border-radius: 0px !important;

  .dir-icon-left {
    left: 18%;
  }

  .dir-icon-right {
    right: 18%;
  }
}
</style>