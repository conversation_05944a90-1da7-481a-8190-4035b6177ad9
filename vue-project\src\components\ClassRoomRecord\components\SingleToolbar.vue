<template>
    <div class="single-board-toolbar" id="single-tab-bar">
        <div class="single-board-toolbar-left">
            
        </div>
        <div class="single-board-toolbar-center">
            <ToolbarColorItem :item="TabBarItem.colorSelect" @itemClick="colorSelectClick"></ToolbarColorItem>

            <ToolbarItem :item="TabBarItem.canvasOptionArrow" :active="editMode === EditMode.Dragging"
                @itemClick="canvasOptionArrowClick"></ToolbarItem>

            <ToolbarItem :item="TabBarItem.canvasOptionPen"
                :active="editMode === EditMode.Drawing && drawMode === DrawMode.pen" @itemClick="canvasOptionPenClick">
            </ToolbarItem>

            <ToolbarItem :item="TabBarItem.canvasOptionEraser"
                :active="editMode === EditMode.Drawing && drawMode === DrawMode.eraser"
                @itemClick="canvasOptionEraserClick" @canvasOptionEraserDoubleClick="canvasOptionEraserDoubleClick">
            </ToolbarItem>

            <ToolbarItem :item="TabBarItem.cleanScreen" @itemClick="cleanScreenClick"></ToolbarItem>
        </div>
        <div class="single-board-toolbar-right">
           
        </div>
    </div>
</template>
<script setup>
import { TabBarItem } from '@/components/TabBar/tabbar_enums'
import ToolbarItem from '@/components/TabBar/ToolbarItem.vue'
import ToolbarColorItem from '@/components/TabBar/ToolbarColorItem.vue'
import { storeToRefs } from 'pinia'
import { useDrawBoardStore } from '@/stores/drawboard_store'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { DrawMode,EditMode } from '@/drawboard/draw_enums'
import { onBeforeUnmount, onMounted } from 'vue'


const drawBoardStore = useDrawBoardStore()
const { showColorSelect, editMode, drawMode, showEraserSelect } = storeToRefs(drawBoardStore)


const classroomUIStore = useClassroomUIStore()
const { 
    showClearScreenAlert, } = storeToRefs(classroomUIStore)




onMounted(() => {
    const tabBar = document.querySelector("#single-tab-bar")
    if (window.electron) {
        tabBar.addEventListener('mouseenter', onMouseEnter)
        tabBar.addEventListener('pointerenter', onMouseEnter)
    }
})

function onMouseEnter() {
    window.electron.unignoreMosue()

}

onBeforeUnmount(() => {
    const tabBar = document.querySelector("#single-tab-bar")
    if (window.electron) {
        tabBar.removeEventListener('mouseenter', onMouseEnter)
        tabBar.removeEventListener('pointerenter', onMouseEnter)
    }
})


function colorSelectClick(item) {
    //颜色
    showColorSelect.value = true
}

function canvasOptionArrowClick(item) {
    //选择
    drawBoardStore.setEditMode(EditMode.Dragging)
}

function canvasOptionPenClick(item) {
    //画笔
    drawBoardStore.setEditMode(EditMode.Drawing)
    drawBoardStore.setDrawMode(DrawMode.pen)
}

function canvasOptionEraserClick(item) {
    //橡皮
    drawBoardStore.setEditMode(EditMode.Drawing)
    drawBoardStore.setDrawMode(DrawMode.eraser)
}

function canvasOptionEraserDoubleClick() {
    // 调整橡皮粗细
    showEraserSelect.value = true
}

function cleanScreenClick(item) {
    //清屏
    showClearScreenAlert.value = true
    // drawBoardStore.clearDrawBoard()
}



</script>
<style lang="scss" scoped>
.single-board-toolbar {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    position: relative;

    .single-board-toolbar-left {
        display: flex;
    }

    .single-board-toolbar-center {
        display: flex;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
    }

    .single-board-toolbar-right {
        display: flex;
    }
}
</style>