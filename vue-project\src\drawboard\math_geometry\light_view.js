import { BoardButton } from "../board_button";
import { BoardView } from "../board_view";
import { DrawZ, PainterOrder } from "../draw_enums";
import * as THREE from 'three';

import { BoardImageView } from "../board_image_view";

export class LightView extends BoardButton {
    constructor(type,application, pos, size) {
        super(application, pos, size)

        this.renderOrder = PainterOrder.eraserObject

        this.onEdit = false

        let light = new THREE.PointLight(0xffffff, 5);
        this.type = type
        this.light = light

        this.add(light)

        let imageView = new BoardImageView(this.application, new THREE.Vector3(0, 0, 0), this.size, true)
        imageView.setImageUrl("img/plane3d/light.svg")
        imageView.touchEnable = false
        this.imageView = imageView
        this.addSubView(imageView)

        this.onClick(() => {
            const edit = !this.onEdit
            this.setOnEdit(edit)
            if (edit) {
                this.superView.deref().cancelEditWithout(this)
            }
        })

        this.setupToolViews()
    }


    setupToolViews() {
        let app = this.application?.deref()
        let height = app.cameraInitSize.width / 40
        let itemHeight = height * 0.8
        let itemWidth = itemHeight
        // let width = itemHeight * 3.5
        let width = itemHeight * 1.5
        let space = itemHeight * 1.5 / 3

        let startX = - width * 0.5

        this.toolWidth = width
        this.toolHeight = height

        let toolView = new BoardView(
            this.application,
            new THREE.Vector3(this.size.width / 2 + app.cameraInitSize.width / 40, - this.size.height / 2, 0),
            { width, height })
        let deleteButton = new BoardButton(
            this.application,
            new THREE.Vector3(startX + space + itemWidth / 2, 0, 0),
            { width: itemWidth, height: itemHeight }, true)
        deleteButton.setImage('img/math/delete.svg')
        deleteButton.onClick(() => {
            this.removeFromSuperView()
            this.dispose()
        })
        toolView.addSubView(deleteButton)
        

        // let selectorButton = new BoardButton(
        //     this.application,
        //     new THREE.Vector3(startX + space * 2 + itemWidth * (2 - 0.5), 0, 0),
        //     { width: itemWidth, height: itemHeight }, true)
        // selectorButton.setImage('img/math/selector.svg')
        // selectorButton.onClick(() => {
        //     this.showOptionsView(!(this.optionsView?.visible ?? false))
        // })
        // toolView.addSubView(selectorButton)
        // toolView.setRenderOrder(PainterOrder.customDisplay + 1)

        // toolView.setBackgroundColor(0x0000ff)
        toolView.visible = false
        this.addSubView(toolView)
        this.toolView = toolView
    }


    setOnEdit(onEdit) {
        this.onEdit = onEdit
        this.draggable = this.onEdit
        this.toolView.visible = this.onEdit
    }


    calculateAngle(aPoint, bPoint, cPoint) {

        // 计算各边的长度
        const a = Math.sqrt(Math.pow(cPoint.x - bPoint.x, 2) + Math.pow(cPoint.y - bPoint.y, 2));
        const b = Math.sqrt(Math.pow(cPoint.x - aPoint.x, 2) + Math.pow(cPoint.y - aPoint.y, 2));
        const c = Math.sqrt(Math.pow(bPoint.x - aPoint.x, 2) + Math.pow(bPoint.y - aPoint.y, 2));

        // 使用余弦定理计算夹角
        const cosTheta = (Math.pow(a, 2) + Math.pow(c, 2) - Math.pow(b, 2)) / (2 * a * c);
        const theta = Math.acos(cosTheta);

        // 将弧度转换为角度
        let angle = theta * (180 / Math.PI);

        return angle
    }

    onTouchDown(point) {
        let view = super.onTouchDown(point)
        if (!this.onEdit) {
            return view
        }
        let cvtPoint = this.convertPoint(point, DrawZ.objcZ + this.position.z)
        if (!cvtPoint) {
            return view
        }
        this.touchDownPoint = cvtPoint
        this.orginDownPoint = point
        return view
    }

    onTouchMove(point) {
        if (!this.onEdit || !this.touchDownPoint) {
            return super.onTouchMove(point)
        }

        // console.log("get angle .. ", angle)

        let divPoint = point
        let cvtPoint = this.convertPoint(point, DrawZ.objcZ + this.position.z)
        if (!cvtPoint) {
            return super.onTouchMove(point); // 避免无效计算
        }
        let angle = this.calculateAngle(point, this.orginDownPoint, { x: point.x, y: this.orginDownPoint.y })

        let spaceX = cvtPoint.x - this.touchDownPoint.x
        let spaceY = cvtPoint.y - this.touchDownPoint.y
        if (angle >= 30 && angle <= 75) { // 放宽角度限制
            if (spaceX < 0) {
                this.position.z += Math.abs(spaceY);
            } else {
                this.position.z -= Math.abs(spaceY);
            }
        } else {
            this.position.x += spaceX;
            this.position.y += spaceY;
        }
        this.touchDownPoint = cvtPoint;
        return super.onTouchMove(divPoint);
    }

    onTouchUp(point) {
        this.touchDownPoint = null
        return super.onTouchUp(point)
    }

    onPointInside(point) {
        if (!this.visible) {
            return false
        }
        if (this.onEdit) {
            for (let subView of this.subViews) {
                if (subView.onPointInside(point)) {
                    return true
                }
            }
        }
        if (this.imageView.onPointInside(point)) {
            return true
        }

        let vector = new THREE.Vector3()
        this.localToWorld(vector)
        let cvtPoint = this.convertPoint(point, vector.z)
        const pos = new THREE.Vector3()
        this.getWorldPosition(pos)
        let leftX = pos.x - this.size.width * 0.75
        let rightX = pos.x + this.size.width * 0.75
        let topY = pos.y + this.size.height * 0.75
        let bottomY = pos.y - this.size.height * 0.75
        if (cvtPoint.x > leftX && cvtPoint.x < rightX && cvtPoint.y > bottomY && cvtPoint.y < topY) {
            return true
        }
        return false
    }
}