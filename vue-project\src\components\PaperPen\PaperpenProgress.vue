<template>
    <div class="paperpen-progress-body">
        <div class="top-progress">
            <div>AI批改进度</div>
            <el-progress :show-text="false" class="progress-bar" :text-inside="true" :stroke-width="26"
                :percentage="Math.round(progress * 100)" />
            <div>{{ Math.round(progress * 100) }}%</div>
        </div>
        <div class="paper-area">
            <div class="paper-item" v-for="(item, index) in Object.values(showList)" :key="index">
                <div class="top-stu">
                    <div>{{ item.name }}</div>
                    <div :class="item.allProgress < 1 ? 'green-text' : 'primary-text'">{{ Math.round(item.allProgress *
                        100)
                        }}%</div>
                </div>
                <div class="stu-event">
                    {{ item.content }}
                </div>

            </div>

        </div>
        <div class="bottom-btn">
            <div :style="{ width: '10px' }"></div>
            <RBPButton width="120px" btnText="收起" @click="closeClick"></RBPButton>
            <div :style="{ flex: 1 }"></div>
            <RBPButton width="120px" btnText="结束互动" :btnSelected="true" @click="stopInteractClick">
            </RBPButton>
            <div :style="{ width: '10px' }"></div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import RBPButton from '../baseComponents/RBPButton.vue';
import roomUpdater from '@/classroom/classroom_updater';
import { AIRequest } from '@/ai_request/ai_request';
import { useClassroomStore } from '@/stores/classroom';
const props = defineProps(
    {
        bottomHeight: {
            default: 80
        }
    }
)
const emits = defineEmits(['close'])
const progress = ref(0)
const classroomStore = useClassroomStore()
function closeClick() {
    emits('close')
}

let requesting = false
const answerList = ref([])

const showList = ref({})
let answerPageCount = 0
let nowCount = 0
let reCount = 0
async function getPushStream() {


    if (requesting) {
        return
    }
    answerPageCount = 0
    answerList.value = []

    let taskId = classroomStore.selectedClassroom.taskId
    classroomStore.selectedClassroom.studentList.forEach(student => {

        if (student.writePageInfo) {

            let singleCount = student.writePageInfo.pageCodes.size ?? 0
            if (student.studentId && singleCount) {
                answerPageCount += singleCount
                answerList.value.push(student)
            }

        }

    })
    requesting = true
    AIRequest.sendMarkResultBatch(
        taskId,
        answerPageCount,
        {
            onProgress: (data) => {
                // console.log("--------------------", data);

                try {
                    if (data && data.user_id) {

                        if (!showList.value[data.user_id]) {
                            const stuItem = classroomStore.selectedClassroom.idStudentMap.get(parseInt(data.user_id + ""))
                            let singleCount = stuItem.writePageInfo ? (stuItem.writePageInfo.pageCodes.size ?? 0) : 0

                            if (stuItem && singleCount) {
                                showList.value[data.user_id] = {
                                    name: stuItem.name,
                                    allCount: singleCount,
                                    content: data.content + '',
                                    userId: data.user_id,
                                    correct: 0,
                                    progress: 0,
                                    allProgress: 0,
                                }
                            }

                        } else if (showList.value[data.user_id]) {
                            showList.value[data.user_id].content += data.content
                        }
                        let item = showList.value[data.user_id]
                        // console.log("---------------------收到消息", item, data.content);

                        if (data.content && (data.content + "").includes('正在批改(') && item) {
                            let itemProgress = dealProgressContent(data.content)

                            if (itemProgress !== null) {

                                let stuPorgress = (item.correct + itemProgress) / item.allCount
                                let changeProgress = (itemProgress - item.progress)
                                item.progress = itemProgress
                                item.allProgress = stuPorgress
                                if (changeProgress > 0) {
                                    progress.value += (changeProgress / answerPageCount)
                                }

                            }
                        }
                    }
                } catch (e) {
                    console.error("----解析批改进度失败data", e);

                }
            },
            onComplete: (data) => {
                nowCount++
                let nowProgress = nowCount / answerPageCount
                if (nowProgress > progress.value && nowProgress <= 1) {
                    progress.value = nowProgress
                }
                try {

                    let item = showList.value[data.user_id]
                    // console.log("---------------------收到complete", item, data.content);

                    item.correct++
                    item.allProgress = item.correct / item.allCount
                    item.progress = 0
                } catch (e) {
                    console.error("----解析批改进度失败complete", e);

                }
            },
            onError: (e) => {
                console.error(e);
                requesting = false
                reCount++
                if (reCount < 6) {
                    setTimeout(() => {
                        getPushStream()
                    }, 1000)
                }

            }
        }
    )
}

function dealProgressContent(value) {
    const str = value + "";
    const match = str.match(/正在批改\((\d+)\/(\d+)\)/);

    if (match) {
        const numerator = parseInt(match[1], 10);   // 6
        const denominator = parseInt(match[2], 10); // 7
        const result = (numerator - 1) / denominator;
        return result < 0 ? 0 : result
    } else {
        console.log("计算ai批改格式不匹配");

    }
    return null

}

async function stopInteractClick() {
    roomUpdater.stopInteract()
}

defineExpose({ getPushStream })
</script>

<style lang="scss" scoped>
.paperpen-progress-body {
    width: 100vw;
    height: 100vh;
    position: absolute;
    top: 0;
    left: 0;
    background-color: #29435C;
    display: flex;
    flex-direction: column;

    .top-progress {
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 21px;
        color: var(--anti-text-color);

        .progress-bar {
            width: 400px;
            margin: 0px 24px;
        }



    }

    .paper-area {
        overflow-y: auto;
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        box-sizing: border-box;
        padding: 8px;

        .paper-item {
            width: calc((100% - 56px) / 6);
            height: calc((100vw - 56px) / 6 * 297 / 210);
            background-color: var(--main-bc-color);
            box-sizing: border-box;
            padding: 8px;
            display: flex;
            flex-direction: column;

            .green-text {
                color: var(--correct-color);
            }

            .red-text {
                color: var(--error-color);
            }

            .primary-text {
                color: var(--primary-color);
            }

            .top-stu {
                height: 32px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                color: var(--text-color);
            }

            .stu-event {
                flex: 1;
                overflow-y: auto;
                white-space: pre-line;
                font-size: 16;
                color: var(--text-color);
            }
        }

    }

    .bottom-btn {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: v-bind("props.bottomHeight + 'px'");
    }
}
</style>