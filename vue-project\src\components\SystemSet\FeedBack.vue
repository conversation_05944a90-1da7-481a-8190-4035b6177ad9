<template>
    <div class="feedback-body">
        <RBPTitleInput :height="144" :rows="4" type="textarea" class="feedback" title="您的意见" placeholder="请输入您的反馈意见(200字以内)" width="calc(100% - 72px)" v-model="feedback" ></RBPTitleInput>
        <RBPTitleInput class="contact" title="您的联系方式" width="calc(100% - 72px)" v-model="contact" placeholder="请输入您的邮箱或手机号"></RBPTitleInput>
        <div class="expand"></div>
        <RBPButton class="submit" :btn-selected="true" btn-type="big" btn-text="提交"  @click="submitClick">      
        </RBPButton>
        
    </div>
</template>
<script setup>
import {  ref,  } from 'vue'
import { ClassManageRequest } from '@/server_request/classmanage_request'
import { Alert } from '@/utils/alert'
import RBPTitleInput from '../baseComponents/RBPTitleInput.vue'
import RBPButton from '../baseComponents/RBPButton.vue'
const feedback = ref('')
const contact = ref('')
async function submitClick() {
    if(!feedback.value) {
        Alert.showErrorMessage('请输入反馈意见')
        return
    }
    if(!contact.value) {
        Alert.showErrorMessage('请输入联系方式')
        return
    }
    let res = await ClassManageRequest.feedback(feedback.value, contact.value)
    if(res.code == 1) {
        Alert.showSuccessMessage('反馈成功')
        feedback.value = ''
        contact.value = ''
    }
}

</script>
<style lang="scss" scoped>
.feedback-body {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .expand{
        flex:1,
    }
    .feedback {
        margin-top: 26px;
    }
    .contact {
        margin-top: 15px;
    }

    
    .submit {
        margin-bottom: 36px;
        margin-right: 0px;
    }
}
</style>