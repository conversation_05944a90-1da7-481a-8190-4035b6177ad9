

import { BoardButton } from "../board_button";
import { BoardView } from "../board_view";
import * as THREE from 'three';
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial.js';
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry.js';
import { Line2 } from 'three/examples/jsm/lines/Line2.js';
import { BoardTool } from "../board_tool";
import { CircleView } from "./circle_view";
import { DrawZ, PainterOrder } from "../draw_enums";
import { MathGeometry } from "./math_root_view";

const MoveTag = {
    none: -1,
    a: 0,
    b: 1,
    self: 3 // 移动直线
}

// 三角形
export class LineView extends BoardView {

    constructor(type,application, pos, size, color) {
        super(application, pos, size)

        this.renderOrder = PainterOrder.customDisplay

        this.onEdit = false

        this.color = color
        this.type = type
        //顶点
        this.aPoint = { x: -0.5 * size.width, y: 0 }

        this.bPoint = { x: 0.5 * size.width, y: 0 }

        // 创建材质：线条材质
        const material = new LineMaterial({
            color: color ?? 0x00ff00,        // 边框颜色
            linewidth: 5,           // 线条宽度
            dashed: false,          // 不使用虚线
        });
        
        // let stringApplication = application?.deref()
        this.lineWidth = 0.01
        let lineGeometry = new LineGeometry()
        let points = [this.aPoint.x, this.aPoint.y, 0, this.bPoint.x, this.bPoint.y, 0]

        lineGeometry.setPositions(points);
        // 创建 Line2 对象
        const line = new Line2(lineGeometry, material);
        line.computeLineDistances();  // 必须计算线段的距离，以便正确渲染

        let lineGroup = new THREE.Group()
        lineGroup.add(line)
        lineGroup.renderOrder = PainterOrder.customDisplay
        this.lineGroup = lineGroup
        this.add(lineGroup);

        this.line = line

        this.setupEditViews()

        let viewButton = new BoardButton(
            application,
            new THREE.Vector3(0, 0, 0),
            { width: size.width * 0.95, height: this.size.height })
        viewButton.renderOrder = PainterOrder.customDisplay - 1
        viewButton.onClick(() => {
            this.setOnEdit(!this.onEdit)
            if (this.onEdit) {
                this.superView.deref().cancelEditWithout(this)
            }
        })
        this.viewButton = viewButton
        this.addSubView(viewButton)

        this.setupToolViews()

    }

    setupEditViews() {

        let application = this.application?.deref()
        let areaWidth = application.cameraInitSize.width / 40
        let dragAreaSize = { width: areaWidth, height: areaWidth }
        this.dragAreaSize = dragAreaSize

        let aVertexView = new BoardView(
            this.application,
            new THREE.Vector3(this.aPoint.x - dragAreaSize.width, this.aPoint.y, 0),
            dragAreaSize)
        aVertexView.renderOrder = 1
        aVertexView.setBackgroundColor(0xff6347)
        aVertexView.visible = false
        aVertexView.setRenderOrder(PainterOrder.customDisplay)
        this.aVertexView = aVertexView
        this.addSubView(aVertexView)

        let bVertexView = new BoardView(
            this.application,
            new THREE.Vector3(this.bPoint.x + dragAreaSize.width, this.bPoint.y, 0),
            dragAreaSize)
        bVertexView.renderOrder = 1
        bVertexView.setBackgroundColor(0xff6347)
        bVertexView.visible = false
        bVertexView.setRenderOrder(PainterOrder.customDisplay)
        this.bVertexView = bVertexView
        this.addSubView(bVertexView)
    }


    setupToolViews() {
        let app = this.application?.deref()
        let height = app.cameraInitSize.width / 40
        let itemHeight = height * 0.8
        let itemWidth = itemHeight
        let width = itemHeight * 3.5
        let space = itemHeight * 1.5 / 3

        let startX = - width * 0.5

        this.toolWidth = width
        this.toolHeight = height

        let toolView = new BoardView(
            this.application,
            new THREE.Vector3(this.bPoint.x, this.bPoint.y + height * 2, 0),
            { width, height })
        let deleteButton = new BoardButton(
            this.application,
            new THREE.Vector3(startX + space + itemWidth / 2, 0, 0),
            { width: itemWidth, height: itemHeight }, true)
        deleteButton.setImage('img/math/delete.svg')
        deleteButton.onClick(() => {
            this.removeFromSuperView()
            this.dispose()
        })
        toolView.addSubView(deleteButton)


        let selectorButton = new BoardButton(
            this.application,
            new THREE.Vector3(startX + space * 2 + itemWidth * (2 - 0.5), 0, 0),
            { width: itemWidth, height: itemHeight }, true)
        selectorButton.setImage('img/math/selector.svg')
        selectorButton.onClick(() => {
            this.showOptionsView(!(this.optionsView?.visible ?? false))
        })
        toolView.addSubView(selectorButton)
        toolView.setRenderOrder(PainterOrder.customDisplay + 1)

        // toolView.setBackgroundColor(0x0000ff)
        toolView.visible = false
        this.addSubView(toolView)
        this.toolView = toolView
    }

    showOptionsView(show) {
        if (!this.optionsView) {
            let app = this.application?.deref()
            let width = app.cameraInitSize.width / 20
            let itemHeight = app.cameraInitSize.width / 40
            let height = itemHeight * 4

            this.optionsWidth = width
            this.optionsHeight = height
            this.optionsItemHeight = itemHeight
            this.optionsView = new BoardView(
                this.application,
                new THREE.Vector3(
                    this.bVertexView.position.x + this.bVertexView.size.width + width / 2,
                    this.toolView.position.y - height / 2 + itemHeight / 2,
                    0
                ),
                { width, height })
            this.optionsView.setRenderOrder(PainterOrder.customDisplay + 1)
            this.addSubView(this.optionsView)

            let startY = height / 2

            let verticalButton = new BoardButton(
                this.application,
                new THREE.Vector3(0, startY - itemHeight / 2, 0),
                { width, height: itemHeight })
            verticalButton.setFontSize(0.015)
            verticalButton.setTextAlign('left')
            verticalButton.setTextColor(0xffffff)
            verticalButton.setText("作垂直")
            verticalButton.onClick(() => {
                this.addVerticalLine()
                this.optionsView.visible = false
            })
            verticalButton.setBackgroundColor(0xFF6347)
            this.optionsView.addSubView(verticalButton)


            let horizontalButton = new BoardButton(
                this.application,
                new THREE.Vector3(0, startY - itemHeight / 2 - itemHeight, 0),
                { width, height: itemHeight })
            horizontalButton.setFontSize(0.015)
            horizontalButton.setTextAlign('left')
            horizontalButton.setTextColor(0xffffff)
            horizontalButton.setText("作水平")
            horizontalButton.onClick(() => {
                this.addHorizontalLine()
                this.optionsView.visible = false
            })
            horizontalButton.setBackgroundColor(0xFF6347)
            this.optionsView.addSubView(horizontalButton)


            let radiusButton = new BoardButton(
                this.application,
                new THREE.Vector3(0, startY - itemHeight / 2 - itemHeight * 2, 0),
                { width, height: itemHeight })
            radiusButton.setFontSize(0.015)
            radiusButton.setTextAlign('left')
            radiusButton.setTextColor(0xffffff)
            radiusButton.setText("为半径作圆")
            radiusButton.onClick(() => {
                this.addRadiusCircle()
                this.optionsView.visible = false
            })
            radiusButton.setBackgroundColor(0xFF6347)
            this.optionsView.addSubView(radiusButton)

            let diameterButton = new BoardButton(
                this.application,
                new THREE.Vector3(0, startY - itemHeight / 2 - itemHeight * 3, 0),
                { width, height: itemHeight })
            diameterButton.setFontSize(0.015)
            diameterButton.setTextAlign('left')
            diameterButton.setTextColor(0xffffff)
            diameterButton.setText("为直径作圆")
            diameterButton.onClick(() => {
                this.addDiameterCircle()
                this.optionsView.visible = false
            })
            diameterButton.setBackgroundColor(0xFF6347)
            this.optionsView.addSubView(diameterButton)
        }

        this.optionsView.visible = show
    }

    // 计算两点之间的距离
    calculateDistance(x1, y1, x2, y2) {
        return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
    }

    // 计算垂直线段的终点
    getPerpendicularLine(aPoint, bPoint) {

        const { x: x1, y: y1 } = aPoint
        const { x: x2, y: y2 } = bPoint
        // 计算线段 AB 的斜率
        const slopeAB = (y2 - y1) / (x2 - x1);

        // 计算垂直线的斜率
        const slopePerpendicular = -1 / slopeAB;

        // 计算线段 AB 的中点
        const midX = (x1 + x2) / 2;
        const midY = (y1 + y2) / 2;

        // 计算线段 AB 的长度
        const lengthAB = this.calculateDistance(x1, y1, x2, y2);

        // 计算垂直线的终点坐标
        // 使用单位向量确定方向
        const angle = Math.atan(slopePerpendicular); // 垂直线的倾斜角度
        const deltaX = (lengthAB / 2) * Math.cos(angle);
        const deltaY = (lengthAB / 2) * Math.sin(angle);

        // 两个可能的终点（垂直于中点的两侧）
        const pointC1 = { x: midX + deltaX, y: midY + deltaY };
        const pointC2 = { x: midX - deltaX, y: midY - deltaY };

        return { pointC1, pointC2 };
    }

    addVerticalLine() {
        let color = this.superView.deref()?.prepareColor ?? this.color

        let verticalLineView = new LineView(
            MathGeometry.Line,
            this.application,
            new THREE.Vector3(this.position.x, this.position.y, 0),
            { width: this.size.width, height: this.size.height }, color)
        let result = this.getPerpendicularLine(this.aPoint, this.bPoint)
        if (result.pointC1.x > result.pointC2.x) {
            verticalLineView.aPoint = result.pointC2
            verticalLineView.bPoint = result.pointC1
        }
        else {
            verticalLineView.aPoint = result.pointC1
            verticalLineView.bPoint = result.pointC2
        }
        verticalLineView.updateLine()
        this.setOnEdit(false)
        // verticalLineView.setOnEdit(true)
        this.superView.deref()?.addSubView(verticalLineView)
    }

    addHorizontalLine() {
        let color = this.superView.deref()?.prepareColor ?? this.color
        let horizontalLineView = new LineView(
            MathGeometry.Line,
            this.application,
            new THREE.Vector3(this.position.x, this.position.y, 0),
            { width: this.size.width, height: this.size.height }, color)
        if (Math.abs(this.aPoint.x - this.bPoint.x) < Math.abs(this.aPoint.y - this.bPoint.y)) {
            let A = { x: this.aPoint.x + this.dragAreaSize.width * 2, y: this.aPoint.y }
            let B = { x: this.bPoint.x + this.dragAreaSize.width * 2, y: this.bPoint.y }
            horizontalLineView.aPoint = A
            horizontalLineView.bPoint = B
        }
        else {
            let A = { x: this.aPoint.x, y: this.aPoint.y - this.dragAreaSize.height * 2 }
            let B = { x: this.bPoint.x, y: this.bPoint.y - this.dragAreaSize.height * 2 }
            horizontalLineView.aPoint = A
            horizontalLineView.bPoint = B
        }

        horizontalLineView.updateLine()
        this.setOnEdit(false)
        this.superView.deref()?.addSubView(horizontalLineView)
    }


    addRadiusCircle() {
        let color = this.superView.deref()?.prepareColor ?? this.color

        let distance = this.calculateDistance(this.aPoint.x, this.aPoint.y, this.bPoint.x, this.bPoint.y)
        let circelView = new CircleView(
            MathGeometry.Circle,
            this.application,
            new THREE.Vector3(this.bPoint.x + this.position.x, this.bPoint.y + this.position.y, 0),
            { width: distance * 2, height: distance * 2 }, color)
        this.setOnEdit(false)
        this.superView.deref()?.addSubView(circelView)
    }


    addDiameterCircle() {
        let color = this.superView.deref()?.prepareColor ?? this.color

        let centerX = this.position.x + this.aPoint.x + (this.bPoint.x - this.aPoint.x) / 2
        let centerY = this.position.y + this.aPoint.y + (this.bPoint.y - this.aPoint.y) / 2
        let distance = this.calculateDistance(this.aPoint.x, this.aPoint.y, this.bPoint.x, this.bPoint.y)
        let circelView = new CircleView(
            MathGeometry.Circle,
            this.application,
            new THREE.Vector3(centerX, centerY, 0),
            { width: distance, height: distance }, color)
        this.setOnEdit(false)
        this.superView.deref()?.addSubView(circelView)

    }

    setOnEdit(onEdit) {
        this.onEdit = onEdit
        this.aVertexView.visible = this.onEdit
        this.aVertexView.draggable = this.onEdit
        this.bVertexView.visible = this.onEdit
        this.bVertexView.draggable = this.onEdit
        this.viewButton.draggable = this.onEdit
        this.toolView.visible = this.onEdit
        this.draggable = this.onEdit
        if (!this.onEdit && this.optionsView) {
            this.optionsView.visible = false
        }
        if (onEdit) {
            this.superView.deref().bringSubViewToFront(this)
        }
    }


    updateLine() {

        // 定义直线的两个顶点
        let points = [this.aPoint.x, this.aPoint.y, 0, this.bPoint.x, this.bPoint.y, 0]

        this.line.geometry.dispose()
        let lineGeometry = new LineGeometry();
        // 更新 MeshLineGeometry
        lineGeometry.setPositions(points);

        this.line.geometry = lineGeometry;

        this.aVertexView.position.set(this.aPoint.x - this.dragAreaSize.width, this.aPoint.y, 0)
        this.bVertexView.position.set(this.bPoint.x + this.dragAreaSize.width, this.bPoint.y, 0)

        this.toolView.position.set(this.bPoint.x, this.bPoint.y + this.toolHeight * 2, 0)

        if (this.optionsView) {
            this.optionsView.position.set(
                this.bVertexView.position.x + this.bVertexView.size.width + this.optionsWidth / 2,
                this.toolView.position.y - this.optionsHeight / 2 + this.optionsItemHeight / 2,
                0
            )
        }
    }

    onTouchDown(point) {
        let view = super.onTouchDown(point)
        if (!this.onEdit) {
            return view
        }
        let cvtPoint =  this.convertPoint(point, DrawZ.objcZ)
        this.touchDownPoint = cvtPoint
        if (view == this.aVertexView) {
            this.moveTag = MoveTag.a
        }
        else if (view == this.bVertexView) {
            this.moveTag = MoveTag.b
        }
        else if (view == this.viewButton) {
            this.moveTag = MoveTag.self
        }
        else {
            this.moveTag = MoveTag.none
        }
        return view
    }


    onTouchMove(point) {
        if (!this.onEdit || !this.touchDownPoint) {
            return super.onTouchMove(point)
        }
        let divPoint = point
        let cvtPoint =  this.convertPoint(point, DrawZ.objcZ)
        let spaceX = cvtPoint.x - this.touchDownPoint.x
        let spaceY = cvtPoint.y - this.touchDownPoint.y
        this.touchDownPoint = cvtPoint
        if (this.moveTag === MoveTag.a) {
            let x = this.aPoint.x + spaceX
            let y = this.aPoint.y + spaceY
            this.aPoint = { x, y }
            this.updateLine()
        }
        else if (this.moveTag === MoveTag.b) {
            let x = this.bPoint.x + spaceX
            let y = this.bPoint.y + spaceY
            this.bPoint = { x, y }
            this.updateLine()
        }
        else if (this.moveTag === MoveTag.self) {
            let x = this.position.x + spaceX
            let y = this.position.y + spaceY
            this.position.set(x, y, 0)
        }
        return super.onTouchMove(divPoint)
    }

    onTouchUp(point) {
        let leftX = Math.min(this.aPoint.x, this.bPoint.x)
        let rightX = Math.max(this.aPoint.x, this.bPoint.x)
        let topY = Math.max(this.aPoint.y, this.bPoint.y)
        let bottomY = Math.min(this.aPoint.y, this.bPoint.y)
        let width = rightX - leftX
        let height = topY - bottomY
        this.viewButton.setSize({ width: width > this.size.width ? width : this.size.width, height: height > this.size.height ? height : this.size.height })
        this.viewButton.position.set(leftX + width / 2, bottomY + height / 2, 0)
        this.moveTag = MoveTag.none
        return super.onTouchUp(point)
    }

    isPointOnLine(p1, p2, touchPoint, lineWidth) {
        const { x: x1, y: y1 } = p1;
        const { x: x2, y: y2 } = p2;
        const { x: touchX, y: touchY } = touchPoint;

        // 检查 touchPoint 是否在线段的 x 和 y 范围内
        const isInXRange = touchX >= Math.min(x1, x2) - lineWidth && touchX <= Math.max(x1, x2) + lineWidth;
        const isInYRange = touchY >= Math.min(y1, y2) - lineWidth && touchY <= Math.max(y1, y2) + lineWidth;
        if (!isInXRange || !isInYRange) {
            return false;
        }
        // 处理垂直线段（x1 === x2）
        if (x1 === x2) {
            return Math.abs(touchX - x1) <= lineWidth;
        }
        // 处理水平线段（y1 === y2）
        if (y1 === y2) {
            return Math.abs(touchY - y1) <= lineWidth;
        }
        // 计算点到线的距离
        const numerator = Math.abs((y2 - y1) * touchX - (x2 - x1) * touchY + x2 * y1 - y2 * x1);
        const denominator = Math.sqrt((y2 - y1) ** 2 + (x2 - x1) ** 2);
        const distance = numerator / denominator;
        // 判断距离是否在边的宽度范围内
        return distance <= lineWidth;
    }

    onPointInside(point) {
        if (!this.visible) {
            return false
        }
        for (let subView of this.subViews) {
            if (subView !== this.viewButton) {
                if (subView.onPointInside(point)) {
                    return true
                }
            }
            // else if (this.onEdit) {
            //     if (subView.onPointInside(point)) {
            //         return true
            //     }
            // }
        }
        let cvtPoint = this.convertPoint(point, DrawZ.objcZ)
        let localPoint = this.worldToLocal(cvtPoint)
        let match = this.isPointOnLine(this.aPoint, this.bPoint, localPoint, this.lineWidth)
        return match
    }


    dispose() {
        BoardTool.disposeGroup(this.lineGroup)
        super.dispose()
    }

}