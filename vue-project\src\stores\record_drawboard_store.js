import { RecordBoardApplication } from "@/components/ClassRoomRecord/components/RecordBoard/record_board_application";
import { defineStore } from "pinia";
import { useDrawBoardStore } from "./drawboard_store";
import { markRaw, ref } from "vue";
import { serverHost } from "@/server_request/server_urls";

//随堂记录的随堂测试画板
export const useRecordDrawboardStore = defineStore('recordDrawboard', () => {

    /// 画板画布
    const recordDrawBoardPainter = ref(null)
    const drawBoardStore = useDrawBoardStore()

    const isFullScreen = ref(false)
    //批改主题
    const correctArea = ref(null)
    
    const statisticType = ref(null)

    


    //-------画板操作
    function didStartBoard(width,height){
        // didEndBoard()
        recordDrawBoardPainter.value = markRaw(new RecordBoardApplication( width, height, serverHost.canvasResolution))
        drawBoardStore.setupDefaultStyle() 
        
    }
    function didEndBoard(){
        if (recordDrawBoardPainter.value) {
            recordDrawBoardPainter.value.dispose()
            recordDrawBoardPainter.value = null
        }
    }
    function addImageToBoard(imageUrl,rotated,needMove) {
        if (recordDrawBoardPainter.value) { 
            recordDrawBoardPainter.value.addImage(imageUrl,rotated,needMove)
        }
    }
    function setShowSize(size){
        if (recordDrawBoardPainter.value) { 
            recordDrawBoardPainter.value.setShowSize(size)
        }
    }
    function clearDrawBoard() {
        if (recordDrawBoardPainter.value) {
            recordDrawBoardPainter.value.rootView.clearAllLines()
            recordDrawBoardPainter.value.animate()
        }
    }

    return {
        recordDrawBoardPainter,isFullScreen,correctArea,statisticType,didStartBoard,didEndBoard,addImageToBoard,clearDrawBoard,setShowSize
    }
})