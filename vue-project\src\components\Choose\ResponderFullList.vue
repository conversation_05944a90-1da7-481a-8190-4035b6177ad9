<template>
    <div class="full-content">
        <div class="side">
            <div class="side-button" @click="prePageClick">
                <img src="/img/interact/resp_left_button.svg" alt="">
            </div>
        </div>
        <div class="main-content" id="responder-list">
            <div class="student-list">
                <div v-for="(item, index) in displayList" :key="index">
                    <div class="student" :style="{
                        backgroundColor: selectedList.includes(item.studentId) ? 'turquoise' : 'white',
                        color: selectedList.includes(item.studentId) ? 'white' : 'black'
                    }" @click="selectStudent(item)">
                        <div class="order">{{ currentIndex * 10 + index + 1 }}</div>
                        <div>
                            {{ item.name }}
                        </div>
                        <div class="score-number">
                            {{ item.score ?? '0' }}分
                        </div>
                    </div>
                </div>
            </div>
            <div class="scores" v-if="!loginInstance.evaluation">
                <div class="score-minus" @click="addScoreClick(-1)">-1</div>
                <div class="score-add" @click="addScoreClick(1)">+1</div>
                <div class="score-add" @click="addScoreClick(3)">+3</div>
                <div class="score-add" @click="addScoreClick(5)">+5</div>
            </div>
            <div class="scores" v-if="loginInstance.evaluation">
                <div class="add-button" @click="evaluate">点评</div>
            </div>
            <div class="back" @click="backClick">
                <div class="back-button">返回</div>
            </div>
            <div class="alert" v-if="showAlert">没有更多了</div>
        </div>
        <div class="side" @click="nextPageClick">
            <div class="side-button">
                <img src="/img/interact/resp_right_button.svg" alt="">
            </div>
        </div>
    </div>

</template>


<script setup>
import roomUpdater from '@/classroom/classroom_updater';
import { loginInstance } from '@/login_instance/login_instance';
import { useClassroomUIStore } from '@/stores/classroom_ui_store';
import { useEvaluationScoreStore } from '@/stores/evaluation_score_store';
import { useScoreAudioStore } from '@/stores/score_audio_store';
import { ref, toRefs, watch } from 'vue';
const classroomUIStore = useClassroomUIStore()

const scoreAudioStore = useScoreAudioStore()
const evaluationScoreStore = useEvaluationScoreStore()

const props = defineProps({
    studentList: {
        type: Array,
        require: true
    },
    hiddenCallback: {
        type: Object,
        require: true
    }
})

const { studentList, hiddenCallback } = toRefs(props)

const displayList = ref(studentList.value.slice(0, 10))

const currentIndex = ref(0)

const selectedList = ref([])

const showAlert = ref(false)

const alertTimeout = ref(null)

const { currentStudents } = toRefs(evaluationScoreStore)

const {showStudentResponderScore} = toRefs(classroomUIStore)

watch(studentList, () => {
    displayList.value = studentList.value.slice(currentIndex.value * 10, (currentIndex.value + 1) * 10)
})

function selectStudent(student) {
    if (selectedList.value.includes(student.studentId)) {
        selectedList.value = selectedList.value.filter(item => item !== student.studentId)
    } else {
        selectedList.value.push(student.studentId)
    }
}


function prePageClick() {
    if (currentIndex.value > 0) {
        currentIndex.value--
        displayList.value = studentList.value.slice(currentIndex.value * 10, (currentIndex.value + 1) * 10)
        selectedList.value = []
    }
    else {
        showNoMoreMessage()
    }
}

function nextPageClick() {
    if ((currentIndex.value + 1) * 10 < studentList.value.length) {
        currentIndex.value++
        displayList.value = studentList.value.slice(currentIndex.value * 10, (currentIndex.value + 1) * 10)
        selectedList.value = []
    }
    else {
        showNoMoreMessage()
    }
}

function showNoMoreMessage() {
    showAlert.value = true
    if (alertTimeout.value) {
        clearTimeout(alertTimeout.value)
    }
    alertTimeout.value = setTimeout(() => {
        showAlert.value = false
    }, 1000)
}

async function addScoreClick(score) {
    if (selectedList.value.length > 0) {
        let students = studentList.value.filter(student => selectedList.value.includes(student.studentId));
        for (let student of students) {
            student.score = String(parseInt(student.score ?? '0') + parseInt(score))
        }
        //上传数据
        let success = await roomUpdater.studentsAddScore(Array.from(students), score)
        if (success) {
            // 播放音频 播放动画
            scoreAudioStore.play(score)
        }
    }
    else {
        for (let student of displayList.value) {
            student.score = String(parseInt(student.score ?? '0') + parseInt(score))
        }
        //上传数据
        let success = await roomUpdater.studentsAddScore(displayList.value, score)
        if (success) {
            // 播放音频 播放动画
            scoreAudioStore.play(score)
        }
    }

}

function backClick() {
    if (hiddenCallback.value) {
        hiddenCallback.value()
    }
}

function evaluate() {
    if (selectedList.value.length > 0) {
        let students = studentList.value.filter(student => selectedList.value.includes(student.studentId));
        currentStudents.value = students
    }
    else {
        currentStudents.value = displayList.value
    }
    currentStudents.value.forEach((student) => {
        student.selected = true
    })
    showStudentResponderScore.value = true
    evaluationScoreStore.getEvaluationItemItemList()
}
</script>


<style lang="scss" scoped>
.full-content {
    position: absolute;
    top: 0px;
    width: 100%;
    height: 100%;
    bottom: 2px;
    background-color: white;
    display: flex;
    flex-direction: row;
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE and Edge */
    overflow-x: hidden;
}

.full-content::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari, Opera */
}

.side {
    width: 15%;
    height: 85%;
    padding-top: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    /* 为了调试，给左侧区域一个背景色 */
}

.side-button {
    width: 100%;
    height: 70px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.main-content {
    width: 70%;
    height: 100%;
}

.student-list {
    width: 100%;
    /* 调整宽度 */
    height: 80%;
    overflow-y: auto;
    /* 允许垂直滚动 */
}

.student {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 45px;
    margin-top: 2px;
    margin-bottom: 2px;
    border-radius: 5px;
}

.order {
    width: 40px;
    margin-left: 10px;
    background-color: lightskyblue;
    color: white;
    /* 文字颜色 */
    text-align: center;
    /* 文字居中 */
}

.score-number {
    margin-right: 10px;
}


.scores {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 14%;
    width: 100%;

    .score-minus {
        width: 40px;
        height: 40px;
        border: 2px solid #FC5C67;
        border-radius: 50%;
        line-height: 40px;
        text-align: center;
        color: #FC5C67;
        margin-left: 10px;
        margin-right: 10px;
        cursor: pointer;
    }

    .score-add {
        width: 40px;
        height: 40px;
        border: 2px solid green;
        border-radius: 50%;
        line-height: 40px;
        text-align: center;
        color: green;
        margin-left: 10px;
        margin-right: 10px;
        cursor: pointer;
    }
}

.back {
    display: flex;
    justify-content: center;
    align-items: end;
    height: 6%;
}

.back-button {
    width: 100px;
    height: 30px;
    border: 2px solid #FC5C67;
    border-radius: 5px;
    line-height: 40px;
    text-align: center;
    justify-content: center;
    align-items: center;
    display: flex;
    color: #FC5C67;
    cursor: pointer;
}


.alert {
    position: absolute;
    width: 50%;
    left: 25%;
    height: 36px;
    top: 36%;
    z-index: 999;
    color: white;
    border-radius: 30px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba($color: #000000, $alpha: 0.8);
}

.add-button {
    width: 150px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-color);
    border-radius: 5px;
    color: white;
}
</style>