

export const RBPColors = {
    colorA: '#25B0FA',
    colorB: '#F7B500',
    colorC: '#FF8533',
    colorD: '#67D53B',
    colorE: '#DEF5D5',
    colorF: '#FFE4D2',
    colorG: '#DCF1F8',
    colorH: '#000000',
    colorI: '#FFFFFF',
    colorJ: '#4E5969',
    colorK: '#EAEAEC',
    colorL: '#C8C8C8',
    colorM: '#EBF9F9',
    colorN: '#9DAAB1',
    colorO: '#EFEFEF',
    colorP: '#D0F4FF',
    colorQ: '#1876A7',
    colorR: '#E1E1E1',
    colorS: '#565656',
    colorT: '#96A4B2',
    colorU: '#F6F9F9',
    colorV: '#2E4A66',
    colorW: '#FAFAFC',
    colorX: '#F4F4F4',
    colorY: '#B5B5B5',
    colorZ: '#EFF4ED',
}

// export let RBPColors = {
//     buttons: {
//         normal: '#25B0FA',
//         hover: '#F7B500',
//         active: '#FF8533',
//         disabled: '#67D53B'
//     },
//     base: {

//     }
// }

export function getColor(color) {
    const root = document.documentElement;
    const computedStyle = getComputedStyle(root);
    return computedStyle.getPropertyValue(color).trim();
}