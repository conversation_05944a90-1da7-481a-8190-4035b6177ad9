
import { BoardView } from "./board_view";
import * as THREE from 'three';

export class BoardImageView extends BoardView {
    constructor(application, pos, size, transparent = false) {
        super(application, pos, size);
        this.transparent = transparent
    }

    setImageUrl(imageUrl,callback,id) { //添加回调
        this.imageUrl = imageUrl
        let textureId = btoa(imageUrl)
        let application = this.application?.deref()
        if (application) {
            application.textureCache.loadImageTexture(textureId, imageUrl, (texture) => {
                this.setImageTexture(texture)
                if(callback){
                    const imgAspect = texture.image.width / texture.image.height;
                    callback(imgAspect,id,this)
                }
            })
        }
    }

    setImageTexture(texture) {
        if (!this.imageMesh) {
            let imageGeometry = new THREE.PlaneGeometry(this.size.width, this.size.height)
            const imageMeterial = new THREE.MeshBasicMaterial({
                map: texture, precision: 'highp', transparent: this.transparent
            })
            this.imageMesh = new THREE.Mesh(imageGeometry, imageMeterial)
            this.imageMesh.renderOrder = -1
            this.imageMesh.position.set(0, 0, 0)
            this.add(this.imageMesh)
        }
        else {
            this.imageMesh.material.map = texture
        }
        this.animate()
    }

    setSize(size) {
        this.size = size
        let imageGeometry = new THREE.PlaneGeometry(this.size.width, this.size.height)
        if (this.imageMesh) {
            this.imageMesh.geometry.dispose()
            this.imageMesh.geometry = imageGeometry
            this.animate()
        }
    }
}