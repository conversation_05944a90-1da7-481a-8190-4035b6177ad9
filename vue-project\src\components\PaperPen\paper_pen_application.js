import { BoardApplication } from "@/drawboard/board_application";
import { PageSnapShoot } from "./page_snapshoot";
import { StudentBoardView } from "./student_board_view";
import { DrawZ, PainterOrder } from "@/drawboard/draw_enums";
import * as THREE from 'three';
import { StudentPageView } from "./student_page_view";
import { useDrawBoardStore } from "@/stores/drawboard_store";


export class PaperPenApplication extends BoardApplication {

    constructor(studentList, width, height, quality = DrawQuality.WQHD) {
        // fov 90 大广角视野
        super(width, height, quality, 90)
        this.studentList = studentList
        this.studentBoardsMap = new Map()
        this.onCompare = false
        this.compareStudents = []
        this.updateDisplayBoards()
    }

    updateDisplayBoards() {
        const space = 0.01

        // 固定每行显示数，调整很麻烦都需要重新计算
        // 多个对比就就行对应放大
        const col = 6
        const size = this.cameraInitSize
        let pageRatio = 210.0 / 297.0;

        const groupWidth = (size.width - space * (col - 1)) / col
        const groupHeight = groupWidth / pageRatio

        let students = this.studentList
        if (this.onCompare) {
            students = this.compareStudents
        }
        // 先都设置为不可见
        this.studentList.forEach((student) => {
            let boardView = this.studentBoardsMap.get(student.studentId)
            if (boardView) {
                boardView.visible = false
            }
        })
        const groupSize = { width: groupWidth, height: groupHeight }
        let groupList = []
        students.forEach((value, i) => {
            // 计算 Group 的位置，使第一个 Group 的左上角位于屏幕 (0, 0) 位置
            var currentRow = Math.floor(i / col);
            var currentCol = i % col;

            const groupX = (currentCol * (groupWidth + space)) - (size.width / 2) + (groupWidth / 2)
            const groupY = -(currentRow * (groupHeight + space)) + (size.height / 2) - (groupHeight / 2)

            const studentId = value.studentId
            let boardView = this.studentBoardsMap.get(studentId)
            if (!boardView) {
                boardView = new StudentBoardView(this.rootView.application, new THREE.Vector3(groupX, groupY, 0), groupSize, value)
                boardView.renderOrder = PainterOrder.writePoints
                this.studentBoardsMap.set(studentId, boardView)
                boardView.onDoubleClick((view) => {
                    const drawBoardStore = useDrawBoardStore()
                    if (this.onCompare) {
                        this.setCompare(false, [])
                        drawBoardStore.cancelRandom = false
                    }
                    else {
                        this.setCompare(true, [view.student])
                        drawBoardStore.cancelRandom = true
                    }
                })
                this.rootView.addSubView(boardView)
            }
            else {
                boardView.position.set(groupX, groupY, 0)
            }
            boardView.updateSelectedState()
            boardView.visible = true
            groupList.push(boardView)
        })
        const count = students.length
        if (count > 0 && count < col) {
            const last = groupList[groupList.length - 1]
            const first = groupList[0]
            const x = first.position.x + (last.position.x - first.position.x) / 2
            const y = last.position.y
            const z = DrawZ.objcZ + (DrawZ.initZ - DrawZ.objcZ) * Math.max(count / col, groupHeight / size.height)
            this.camera.position.set(x, y, z)
        }
        else {
            this.camera.position.set(0, 0, DrawZ.initZ)
        }

        this.updateStudentLineWidth()

        this.animate()
    }
    setCameraZScale(z) {
        super.setCameraZScale(z)
        this.updateStudentLineWidth()
    }

    getExponentialEquation([x1, y1], [x2, y2]) {
        const b = Math.log(y2 / y1) / (x2 - x1);
        const a = y1 / Math.exp(b * x1);
        return (x) => a * Math.exp(b * x);
    }
    
    updateStudentLineWidth() {
        if (this.camera.position.z <= DrawZ.initZ) {
            if (this.predictFunc === undefined) {
                this.predictFunc = this.getExponentialEquation([4, 0.002], [6, 0.0005]);
            }
            let lineWidth = this.predictFunc(this.camera.position.z);
            let lineMaterial = StudentPageView.getLineMaterial()
            lineMaterial.lineWidth = lineWidth
            this.animate()
        }
    }

    getPageShooter() {
        if (!this.pageShooter) {
            this.pageShooter = new PageSnapShoot(new WeakRef(this))
        }
        return this.pageShooter
    }

    appendWritePoints(studentId, pageCode, points) {
        let boardView = this.studentBoardsMap.get(studentId)
        if (boardView) {
            boardView.appendWritePoints(pageCode, points)
        }
    }

    setCompare(compare = true, students = []) {
        this.onCompare = compare
        this.compareStudents = students
        this.rootView.setTempDraw(compare)
        this.updateDisplayBoards()
    }


    updateStudentState(studentId) {
        let studentBoard = this.studentBoardsMap.get(studentId)
        if (studentBoard) {
            studentBoard.updateStudentState()
        }
        this.animate()
    }

    updateAllStudentState() {
        this.studentBoardsMap.forEach((studentBoard) => {
            studentBoard.updateStudentState()
        })
        this.animate()
    }


    /// 接收试卷信息
    receivePageInfo(event) {
        const studentId = event.stu_id
        let studentBoard = this.studentBoardsMap.get(studentId)
        if (studentBoard) {
            studentBoard.receivePaperPage(event)
        }
        this.animate()
    }


    /// 接收批改信息
    receiveCheckResult(event) {
        const studentId = event.student_id
        let studentBoard = this.studentBoardsMap.get(studentId)
        if (studentBoard) {
            studentBoard.receiveCheckResult(event)
        }
        this.animate()
    }

    /// 中文练字收到评分
    /// 在外部调用刷新
    receiveWordStars(studentId, pageId, item) {
        let studentBoard = this.studentBoardsMap.get(studentId)
        if (studentBoard) {
            studentBoard.receiveWordStars(pageId, item)
        }
    }


    
    // 设置概率统计图表
    // xValues为横轴值，dataValues为纵轴值
    setProbablityEcharts(studentId, xValues, dataValues) {
        let studentBoard = this.studentBoardsMap.get(studentId)
        if (studentBoard) {
            studentBoard.setEcharts(xValues, dataValues)
        }
    }

    showBackgroundImage(show) {
        this.studentBoardsMap.forEach((studentBoard) => {
            studentBoard.setBackgroundImageShow(show)
        })
        this.animate()
    }

    dispose() {
        if (this.pageShooter) {
            this.pageShooter.dispose()
            this.pageShooter = null
        }
        this.studentBoardsMap.clear()
        super.dispose()
    }
}