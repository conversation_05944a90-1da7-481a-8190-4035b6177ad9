import { LocalRequest } from "@/local_request/local_request"
import { encryptAES, decryptAES, cryptoKeyketang } from '../utils/crypto.js'
import { v4 as uuidv4 } from "uuid"
import { useLoginStore } from '@/stores/login_store'

class LoginInstance {

    constructor() {
        if (!LoginInstance.instance) {
            // this.setup()
            LoginInstance.instance = this
        }
        return LoginInstance.instance
    }

    async setup() {
        let data = await LocalRequest.readDataFromLocal()
        if ((data === null || typeof data === "string") && window.location.host.includes("5173")) {
            const dataStr = localStorage.getItem("loginInfo")
            if (dataStr) {
                data = JSON.parse(dataStr)
            }
        }
        this.configData = { ...data }
        if (data !== null) {
            this.dict = data.dict ?? ''
            this.lastAccount = data.lastAccount ?? ''
            this.remember = data.remember ?? false
            this.users = {}
            this.deviceId = data.deviceId ?? ''
            this.activeInfo = data.activeInfo ?? ''
            this.activeCode = ''
            this.items = data.items ?? ''
            this.grade = data.grade ?? ''
            if (this.activeInfo !== '' && this.activeInfo !== undefined) {
                this.activeInfo = decryptAES(this.activeInfo, cryptoKeyketang)
                let info = JSON.parse(this.activeInfo)
                this.activeCode = info.activiationCode
                this.lastSchoolId = info.schoolId
                this.lastMemberId = info.auth.memberId
            }

            if (data.lastSchoolId && data.lastSchoolId !== '') {
                this.lastSchoolId = data.lastSchoolId
                this.lastMemberId = data.lastMemberId
            }

            const usersStr = data.users ?? ''
            if (usersStr != '') {
                // 解密
                const decrypted = decryptAES(usersStr, cryptoKeyketang)
                this.users = JSON.parse(decrypted)
            }
            this.lastPassword = ''
            if (this.users != '' && this.remember) {
                this.lastPassword = this.users[this.lastAccount]
            }

            if (this.deviceId === '') {
                if (window.electron) {
                    try {
                        let cpuId = await window.electron.getCpuId()
                        // console.log('cpuId', cpuId)
                        this.deviceId = cpuId
                    }
                    catch (error) {
                        console.log('getCpuId error', error)
                    }
                }
                if (this.deviceId === '') {
                    // 生成随机设备id
                    this.deviceId = uuidv4()
                }
                await this.saveToDisk()
            }
        }
    }

    saveToken(token) {
        this.token = token
        localStorage.setItem('token', token)
        // this.configData.token = token
    }

    saveTeacher(teacher) {
        this.teacher = teacher
        // this.configData.teacher = teacher
    }

    saveEvaluation(evaluation) {
        this.evaluation = evaluation
        // this.configData.evaluation = evaluation
    }

    saveSubjectMainId(subjectMainId) {
        this.subjectMainId = subjectMainId
        // this.configData.subjectMainId = subjectMainId
    }

    saveLastSchoolId(schoolId, memberId) {
        this.lastSchoolId = schoolId
        this.lastMemberId = memberId
        // this.configData.schoolId = schoolId
        this.saveToDisk()
    }

    cleanData() {
        this.token = undefined
        this.teacher = undefined
        this.subjectMainId = undefined
        let loginStore = useLoginStore()
        loginStore.cleanData()
    }

    async saveAccount(account, password) {
        this.lastAccount = account

        if (this.users === undefined) {
            this.users = {}
        }

        if (this.remember) {
            this.users[account] = password
        } else {
            delete this.users[account]
        }
        await this.saveToDisk()
    }

    saveRemember(remember) {
        this.remember = remember
    }

    async saveToDisk() {
        let encrypted = '';
        if (Object.keys(this.users).length > 0) {
            const usersStr = JSON.stringify(this.users)
            // 加密
            encrypted = encryptAES(usersStr, cryptoKeyketang);
        }
        let active_info = ''
        if (this.activeInfo != '' && this.activeInfo !== undefined) {
            active_info = encryptAES(this.activeInfo, cryptoKeyketang)
        }
        this.configData.lastAccount = this.lastAccount
        this.configData.lastSchoolId = this.lastSchoolId
        this.configData.lastMemberId = this.lastMemberId
        this.configData.remember = this.remember
        this.configData.users = encrypted
        this.configData.deviceId = this.deviceId
        this.configData.activeInfo = active_info
        this.configData.dict = this.dict

        if (window.location.host.includes("5173")) {
            localStorage.setItem("loginInfo", JSON.stringify(this.configData))
        }
        await LocalRequest.saveDataToLocal(this.configData)
    }

    async removeAccount(account) {
        delete this.users[account]
        if (account == this.lastAccount) {
            this.lastAccount = ''
            this.remember = false
        }
        await this.saveToDisk()
    }
}


const loginInstance = new LoginInstance()

export {
    loginInstance
}