import { HttpEnv, serverHost } from "@/server_request/server_urls";



// AI学伴API URLs
export class AIUrls {
    static baseUrl() {
        // 根据环境选择不同的host
        return HttpEnv.online == serverHost.env 
            ? 'http://api-ai-study.hizuoye.cn' 
            : 'http://api-bot-test.hizuoye.cn';
    }

    static chatOpenUrl() {
        return `${this.baseUrl()}/api/v2/chat_open`;
    }

    static voiceUploadUrl() {
        return `${this.baseUrl()}/api/v2/voice_upload`;
    }
    
    static markResultBatchUrl() {
        return `${this.baseUrl()}/api/v2/mark_result_batch`;
    }
}