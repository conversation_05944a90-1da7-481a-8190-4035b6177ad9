import { defineStore } from "pinia"
import { ref } from "vue"
import { ClassRoomRequest } from '@/server_request/classroom_request'
import { ElLoading, ElMessage } from 'element-plus'

export const useTeachPlanStore = defineStore('teachPlan', () => {
    const treeList = ref([])

    const showTeachPlan = ref(false)

    const mineTeachPlanList = ref([])
    const schoolTeachPlanList = ref([])

    /// 试卷阶段
    const stages = ref([])

    /// 当前选择的导学案
    const currentItem = ref(null)

    const curerntStage = ref(null)

    const mineTeachPlan = ref(true)

    async function show(classroom) {
        treeList.value = []
        let loadingInstance = ElLoading.service({ background: 'transparent' })
        const res = await ClassRoomRequest.getTextbookTree(classroom.gid)
        if (res.code == 1) {
            res.data.forEach(item => {
                let chapters = []
                if (item.chapters) {
                    chapters = childrenForList(item.chapters, 0)
                }
                let child = {
                    name: item.name,
                    chapterId: item.chapterId,
                    open: false,
                    selected: false,
                    root: true,
                    children: chapters,
                    index: 0
                }
                treeList.value.push(child)
            })
            showTeachPlan.value = true
        } else {
            ElMessage.error(res.message)
        }
        loadingInstance.close()
    }
    
    function childrenForList(list, index) {
        index++
        let children = []
        list.forEach(item => {
            let childs = []
            if (item.childs) {
                childs = childrenForList(item.childs, index)
            }
            let child = {
                name: item.name,
                chapterId: item.chapterId,
                open: false,
                selected: false,
                root: false,
                children: childs,
                index: index
            }
            children.push(child)
        })
        return children
    }

    function cleanData() {
        mineTeachPlanList.value = []
        schoolTeachPlanList.value = []
        showTeachPlan.value = false
        treeList.value = []
        stages.value = []
        currentItem.value = null
        curerntStage.value = null
        mineTeachPlan.value = true
    }

    return {
        showTeachPlan,
        treeList,
        mineTeachPlanList,
        schoolTeachPlanList,
        stages,
        currentItem,
        curerntStage,
        mineTeachPlan,
        cleanData,
        show
    }
})