<template>
    <div v-if="showSystemSet" class="systemSet" @click.stop="showSystemSet = false">
        <RBPAlert title="设置" width="1006px" height="670px" @close="showSystemSet = false">
            <template v-slot:rbpDiv>
                <div class="content" @click.stop>
                    <div class="setBody">
                        <div class="left">
                            <div class="basicInfo" :class="{ btnSelected: selected == index }" @click="selected = index" v-for="(item,index) in selectList">{{ item }}</div>
                            
                        </div>
                        <div class="right">
                            <BasicInfo v-if="selected == 0"></BasicInfo>
                            <WordDict v-if="selected == 1"></WordDict>
                            <FeedBack v-if="selected == 3"></FeedBack>
                            <BlackBoardColor v-if="selected == 2" @close="showSystemSet = false"></BlackBoardColor>
                        </div>
                    </div>
                </div>
            </template>
        </RBPAlert>

    </div>
</template>
<script setup>
import RBPAlert from '../baseComponents/RBPAlert.vue'
import { ref, computed } from 'vue'
import BasicInfo from '@/components/SystemSet/BasicInfo.vue'
import WordDict from '@/components/SystemSet/WordDict.vue'
import FeedBack from '@/components/SystemSet/FeedBack.vue'
import BlackBoardColor from '@/components/SystemSet/BlackBoardColor.vue'
import { storeToRefs } from 'pinia'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'

const classroomUIStore = useClassroomUIStore()
const { showSystemSet, mainContentTopSpace } = storeToRefs(classroomUIStore)

const selected = ref(0)
const selectList = ["基本信息","单词听写","黑板","意见反馈"]
</script>
<style lang="scss" scoped>
.systemSet {
    position: absolute;
    height: 100%;
    width: 100%;
    background-color: rgba($color: var(--main-anti-bc-color-rgb), $alpha: 0.2);
    opacity: 1;
    z-index: var(--toolbar-cloud-disk-z-index);
    

    .content {
        width: 100%;
        height: 100%;

        

        .setBody {
            height: 100%;
            width: 100%;
            display: flex;
            position: relative;

            .close {
                position: absolute;
                right: 10px;
                bottom: 10px;
                cursor: pointer;
                height: 42px;
            }

            .left {
                width: 28%;
                height: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                .basicInfo {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    width: calc(100% - 60px);
                    box-sizing: border-box;
                    height: 54px;
                    background: var(--main-bc-color);
                    border-radius: 15px;
                    border: 2px solid var(--border-bar-color);
                    font-weight: 500;
                    font-size: 21px;
                    color: var(--text-color);
                    margin: 0px 30px;
                    margin-top: 42px;
                }
                .btnSelected {
                    background: var(--primary-color);
                    border-radius: 15px;
                    color: var(--anti-text-color);
                }
            }
            .right {
                flex: 1;
                height: 100%;
                box-sizing: border-box;
            }
        }
    }
}
</style>