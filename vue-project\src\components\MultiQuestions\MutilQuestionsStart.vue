<template>
    <RBPQuestionsAlert>
        <template v-slot:rbpTitle>
            <div class="modal-header">
                题目列表
            </div>
        </template>
        <template v-slot:rbpDiv>
            <div class="modal-body">
                <div class="list" v-for="(item, i) in startQuestions" v-bind:key="i">
                    <div :style="{ height: '15px' }"></div>
                    <RBPQuestionsItem :img="getImg(item)" :title="getTitle(item, i)" :content="getContent(i)">
                    </RBPQuestionsItem>
                </div>
            </div>
        </template>
        <template v-slot:rbpBtns>
            <div class="interactBtns">
                <RBPButton btnText="开始讲解" :btnSelected="true" btnType="big" @click="startExplainClick">
                </RBPButton>
                <div :style="{ height: '12px' }"></div>
                <RBPButton btnText="结束互动" :backgroundColor="RBPColors.colorF" :color="RBPColors.colorC" btnType="big"
                    @click="$emit('endInteractionClick')"></RBPButton>
            </div>
        </template>
    </RBPQuestionsAlert>
</template>
<script setup>
import { computed, defineProps } from 'vue'
import { useAnswersStore } from '@/stores/answers_store.js'
import { useClassroomStore } from '@/stores/classroom.js'
import { storeToRefs } from 'pinia'
import { useInteractStore } from '@/stores/interact_store'
import { Interact, interactName, InteractStatus, MultiQusLecture } from '@/classroom/interact_enums.js'
import roomUpdater from '@/classroom/classroom_updater.js'
import { DisplayDirection, ViewStatus } from '@/classroom/frame_enums'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import RBPQuestionsAlert from '@/components/baseComponents/RBPQuestionsAlert.vue'
import RBPLine from '@/components/baseComponents/RBPLine.vue'
import { RBPColors } from '@/components/baseComponents/RBPColors.js'
import RBPQuestionsItem from '@/components/baseComponents/RBPQuestionsItem.vue'
import RBPButton from '@/components/baseComponents/RBPButton.vue'

const classroomUIStore = useClassroomUIStore()
const { displayDirection } = storeToRefs(classroomUIStore)
const answerStore = useAnswersStore()
const { multiQuestionAnswers, startQuestions } = storeToRefs(answerStore)
const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)
const interactStore = useInteractStore()
const { interact, interactStatus, minimize } = storeToRefs(interactStore)
function questionSumbitForIndex(index) {
    let questionAnswerList = Object.values(multiQuestionAnswers.value)
    let count = 0
    questionAnswerList.forEach(answerList => {
        if (answerList[index].length > 0) {
            count++
        }
    })
    return count + '/' + selectedClassroom.value.studentList.length
}
function getImg(interact) {
    if (interact == Interact.trueFalse) {
        return '/img/svg/icon_interact_truefalse.svg'
    } else if (interact == Interact.singleChoice) {
        return '/img/svg/icon_interact_singlechoice.svg'
    } else if (interact == Interact.multiChoice) {
        return '/img/svg/icon_interact_mutilchoice.svg'
    }
}
function getTitle(interact, index) {
    return (index + 1) + '-' + interactName(interact)
}
function getContent(index) {
    return '已提交' + questionSumbitForIndex(index)
}
function startExplainClick() {
    interactStatus.value = InteractStatus.underway
    answerStore.lecture = MultiQusLecture.question
    roomUpdater.stopAnswer()
}
</script>
<style lang="scss" scoped>
@import '@/assets/scss/mixin.scss';

.modal-header {
    font-size: 2.2vh;
    font-weight: bold;
    height: 100%;
    color: var(--text-color);
    width: 100%;
    padding-left: 24px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
}

.modal-body {
    height: 100%;

    .list {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
}

.interactBtns {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
}
</style>