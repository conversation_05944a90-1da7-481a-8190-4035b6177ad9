<template>
    <div class="clear-screen" v-if="showClearScreenAlert">
        <div class="content">
            <div class="title">{{ getTitle() }}</div>
            <div class="content-bottom">
                <RBPButton btnText="取消" :btnSelected="false" @click="showClearScreenAlert = false"></RBPButton>
                <div :style="{ width: '20px' }"></div>
                <RBPButton btnText="确定" :btnSelected="true" @click="clickConfirm"></RBPButton>
            </div>
        </div>
    </div>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { useDrawBoardStore } from '@/stores/drawboard_store'
import RBPButton from '@/components/baseComponents/RBPButton.vue'
import { useInteractStore } from '@/stores/interact_store'
import { useRecordDrawboardStore } from '@/stores/record_drawboard_store'
import { Interact } from '@/classroom/interact_enums'
import { clearType } from './tabbar_enums'
const classroomUIStore = useClassroomUIStore()
const { showClearScreenAlert, showClassRecord, showClassRoomRecord, hideClassRoomRecord } = storeToRefs(classroomUIStore)
const drawBoardStore = useDrawBoardStore()
const recordDrawboardStore = useRecordDrawboardStore()
const { recordDrawBoardPainter } = storeToRefs(recordDrawboardStore)
const interactStore = useInteractStore()
const { showRecordResult } = storeToRefs(classroomUIStore)



function getTitle() {
    let title = '是否清除所有'
    switch (showClearScreenAlert.value) {
        case clearType.image.key:
            title += '图片'
            break;
        case clearType.shape.key:
            title += '图形'
            break;
        case clearType.grid.key:
            title += '线格'
            break;
        case clearType.teachPlan.key:
            title += '导学案'
            break;
        case clearType.all.key:
            title = "是否清除全部"
            break;
        default:
            title += '笔迹'
            break;

    }
    return title + "？"

}

function clickConfirm() {
    switch (showClearScreenAlert.value) {
        case clearType.image.key:
            if (drawBoardStore.blackBoardPainter) {
                drawBoardStore.blackBoardPainter.clearAllImage()
            }
            break;
        case clearType.shape.key:
            if (drawBoardStore.blackBoardPainter) {
                drawBoardStore.blackBoardPainter.clearAllShape()
            }
            break;
        case clearType.teachPlan.key:
            if (drawBoardStore.blackBoardPainter) {
                drawBoardStore.blackBoardPainter.clearAllTeachPlans()
            }
            break;
        case clearType.grid.key:
            if (drawBoardStore.blackBoardPainter) {
                drawBoardStore.blackBoardPainter.clearAllGrids()
            }
            break;
        case clearType.all.key:
            if (drawBoardStore.blackBoardPainter) {
                drawBoardStore.blackBoardPainter.clearAllShape()
                drawBoardStore.blackBoardPainter.clearAllImage()
                drawBoardStore.blackBoardPainter.clearAllTeachPlans()
                drawBoardStore.blackBoardPainter.clearAllGrids()
                drawBoardStore.clearDrawBoard()
            }
            break;
        default:
            let type = interactStore.interactResult.type
            //随堂历史记录画板
            if ((showClassRecord.value || (showClassRoomRecord.value && !hideClassRoomRecord.value) || (showRecordResult.value && (type == Interact.classTest || type == Interact.homework))) && recordDrawBoardPainter.value) {
                recordDrawboardStore.clearDrawBoard()
                return
            }

            drawBoardStore.clearDrawBoard()
            break;

    }

    showClearScreenAlert.value = false
}
</script>
<style lang="scss" scoped>
.clear-screen {
    z-index: var(--toolbar-clear-screen-alert-z-index);
    background-color: var(--main-anti-bc-alpha-color);
    opacity: 1;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    position: fixed;

    .content {
        background-color: var(--main-bc-color);
        position: fixed;
        left: 50%;
        transform: translateX(-50%);
        bottom: 90px;
        border-radius: 6px;
        transition: all 0.2s;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px 30px;

        .title {
            font-size: 30px;
            color: var(--primary-color);
        }

        .content-bottom {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
    }
}
</style>