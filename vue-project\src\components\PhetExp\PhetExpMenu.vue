<template>
    <div class="phet-exp-menu-container" v-if="showPhetExpMenu">
        <RBPAlert :title="`${loginInstance.subjectName}实验`" @close="showPhetExpMenu = false" @click.stop>
            <template v-slot:rbpDiv>
                <div class="content-div">
                    <div class="content-menu">
                        <div class="menu">
                            <div v-if="!isSearch" class="menu-item" :class="{ 'menu-item-selected': index == currentIndex }"
                                v-for="(item, index) in menuList" :key="index" @click="menuItemClick(item, index)">
                                {{ item.knowledge }}
                            </div>
                            <div v-else class="menu-item" :class="{ 'menu-item-selected': true }">全部</div>
                        </div>
                        <div class="search">
                            <input v-model="searchTest" type="text" placeholder="实验名称关键词">
                            <img :src="getSearchIcon()" @click="searchClick">
                        </div>
                    </div>
                    <RBPLine width="6px" height="100%"></RBPLine>
                    <div class="subitems">
                        <div class="items">
                            <div class="item" v-for="item in (isSearch ? searchList : subList)"
                                @click="jumpSubjectClick(item)">
                                <div class="item-icon" :style="{ backgroundImage: `url(${ServerUrls.getMgboardHost() + item.cover})` }"></div>
                                <div class="item-name">
                                    {{ item.title }}
                                </div>
                            </div>
                        </div>
                        <div class="sort">
                            <div class="sort-name">排序</div>
                            <div class="sort-select rbp-select">
                                <el-select popper-class="rbq-select-body" v-model="value" placeholder="请选择" size="large"
                                    style="width: 240px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </RBPAlert>
    </div>
</template>
<script setup>
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { storeToRefs } from 'pinia';
import RBPAlert from '@/components/baseComponents/RBPAlert.vue'
import { usePhetExpStore } from '@/stores/phet_exp_store'
import RBPLine from '@/components/baseComponents/RBPLine.vue'
import { computed, ref, watch } from 'vue';
import { loginInstance } from '@/login_instance/login_instance'
import { ServerUrls } from "@/server_request/server_urls"

const searchTest = ref('')

const value = ref('')

const options = ref([
    {
        value: 'A-Z',
        label: 'A-Z',
    },
    {
        value: 'Z-A',
        label: 'Z-A',
    }
])

watch(() => value.value, (newVal, oldVal) => {
    sort(newVal)
})

function getSearchIcon() {
    if (searchTest.value.length > 0) {
        return '/img/svg/icon_phet_close.svg'
    } else {
        return '/img/svg/icon_phet_search.svg'
    }
}

function sort(rule) {
    if (rule == 'A-Z') {
        subList.value.sort((a, b) => a.title.localeCompare(b.title));
    } else if (rule == 'Z-A') {
        subList.value.sort((a, b) => b.title.localeCompare(a.title));
    }
}

const phetExpStore = usePhetExpStore()
const { menuList, url } = storeToRefs(phetExpStore)

const currentIndex = ref(0)
const subList = computed(() => {
    return menuList.value[currentIndex.value]?.experiments
})

watch(() => subList.value, (newVal, oldVal) => {
    sort(value.value)
})

const classroomUIStore = useClassroomUIStore()
const { showPhetExpMenu, showPhetExp } = storeToRefs(classroomUIStore)

function menuItemClick(item, index) {
    currentIndex.value = index
}

function jumpSubjectClick(item) {
    url.value = ServerUrls.getMgboardHost() + item.download
    showPhetExp.value = true
}
function searchClick() {
    if (searchTest.value.length > 0) {
        searchTest.value = ''
    }
}
watch(() => searchTest.value, (newVal, oldVal) => {
    search(newVal)
})

const allList = computed(() => {
    return menuList.value.map(item => {
        return item.experiments
    }).flat()
})

const isSearch = ref(false)
const searchList = ref([])

function search(search) {
    search = search.trim()
    if (search.length > 0) {
        isSearch.value = true
        searchList.value = allList.value.filter(item => {
            return item.title.includes(search)
        })
    } else {
        isSearch.value = false
    }
}
</script>
<style lang="scss" scoped>
.phet-exp-menu-container {
    position: absolute;
    height: 100%;
    width: 100%;
    z-index: var(--interact-alert-phet-z-index);
}

.content-div {
    height: 95%;
    width: 100%;
    display: flex;
    box-sizing: border-box;

    .content-menu {
        width: 20%;
        height: 100%;
        display: flex;
        flex-direction: column;

        .menu {
            flex: 1;
            width: 100%;
            display: flex;
            flex-direction: column;
            overflow: auto;
            scrollbar-width: none;
            margin: 10px 0;
            box-sizing: border-box;

            &::-webkit-scrollbar {
                display: none;
            }

            .menu-item {
                cursor: pointer;
                padding: 20px;
                display: flex;
                align-items: center;
                box-sizing: border-box;
                font-size: 21px;
                color: var(--text-color);
                border-bottom: 1px solid var(--border-bar-color);
            }

            .menu-item-selected {
                background-color: var(--primary-color);
                color: var(--anti-text-color);
            }
        }

        .search {
            width: 100%;
            height: 80px;
            display: flex;
            align-items: center;
            position: relative;

            input {
                height: 54px;
                width: 100%;
                margin: 0px 16px;
                border-radius: 15px;
                border: 2px solid var(--border-bar-color);
                padding: 0 15px;
                font-size: 21px;
            }

            img {
                width: 30px;
                height: 30px;
                position: absolute;
                right: 30px;
                cursor: pointer;
            }
        }
    }

    .subitems {
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 20px;
        box-sizing: border-box;

        .items {
            flex: 1;
            width: 100%;
            overflow-y: auto;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-gap: 10px;
            box-sizing: border-box;
            overflow-x: hidden;

            .item {
                height: 210px;
                width: 280px;
                background-color: #FFF3E9;
                border: 1px solid var(--border-bar-color);
                border-radius: 26px;
                display: flex;
                flex-direction: column;
                cursor: pointer;
                box-sizing: border-box;

                .item-icon {
                    width: 100%;
                    height: 75%;
                    background-size: cover;
                    background-position: center;
                    border-top-left-radius: 26px;
                    border-top-right-radius: 26px;
                    box-sizing: border-box;
                }

                .item-name {
                    flex: 1;
                    color: var(--text-color);
                    font-size: 21px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    max-height: 25%;
                    padding: 0 10px;
                    box-sizing: border-box;
                    background-color: white;
                    border-bottom-left-radius: 26px;
                    border-bottom-right-radius: 26px;
                    display: flex;
                    align-items: center;
                    box-sizing: border-box;
                }
            }
        }

        .sort {
            margin-top: 20px;
            height: 80px;
            width: 100%;
            display: flex;
            align-items: center;

            .sort-name {
                font-size: 21px;
                color: var(--text-color);
                margin-left: 20px;
            }

            .sort-select {
                height: 100%;
                flex: 1;
                display: flex;
                align-items: center;
                margin-left: 20px;
            }
        }
    }
}
</style>

<style lang="scss">
@import url(@/components/baseComponents/RBPSelect.scss);
</style>