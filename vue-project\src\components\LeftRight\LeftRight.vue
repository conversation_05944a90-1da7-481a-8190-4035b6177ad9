<template>
    <div class="left" @click="changeDisplayDirection" v-if="hideToolBar === false">
        <img src="/img/svg/icon_sidebar_left.svg">
    </div>
    <div class="right" @click="changeDisplayDirection" v-if="hideToolBar === false">
        <img src="/img/svg/icon_sidebar_right.svg">
    </div>
</template>
<script setup>
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import { DisplayDirection } from '@/classroom/frame_enums'
import { storeToRefs } from 'pinia'
import { useDesktopStore } from '@/stores/desktop_store'

const desktopStore = useDesktopStore()
const { hideToolBar } = storeToRefs(desktopStore)
const classroomUIStore = useClassroomUIStore()
const { displayDirection } = storeToRefs(classroomUIStore)
function changeDisplayDirection() {
    if(displayDirection.value == DisplayDirection.Left) {
        displayDirection.value = DisplayDirection.Right
    }else {
        displayDirection.value = DisplayDirection.Left
    }
}
</script>
<style lang="scss" scoped>
.left {
    position: absolute;
    left: 0;
    width: 43px;
    height: 48px;
    // top: 50%;
    // transform: translate(0, -50%);
    bottom: 70px;
    cursor: pointer;
    z-index: 999;

    img {
        width: 100%;
        height: 100%;
    }
}
.right {
    position: absolute;
    right: 0;
    width: 43px;
    height: 48px;
    // top: 50%;
    // transform: translate(0, -50%);
    bottom: 70px;
    cursor: pointer;
    z-index: 999;

    img {
        width: 100%;
        height: 100%;
    }
}
</style>