import { defineStore } from "pinia"
import { ref, computed } from "vue"


export const useScoreStatisticsStore = defineStore('score_statistics', () => {

    const type = ref(2) //1： 小组明细 ；2：学生明细

    const statisticId = ref(null)

    const title = ref('')
    const sort = {
        key: null,
        sort: -1
    }
    
    const remoteFlag = ref(0)

    const currentValue = ref('学生明细')
    const options = ref([
        '学生明细',
        '小组明细'
    ])

    function cleanData() {
        type.value = 2
        statisticId.value = null
        title.value = ''
    }

    return {
        type,
        statisticId,
        title,
        currentValue,
        options,
        sort,
        remoteFlag,

        cleanData
    }
})