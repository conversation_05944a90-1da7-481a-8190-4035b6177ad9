<template>
    <div class="item" @click="itemClick" :class="{ active: props.active }" @dblclick="handleDoubleClick()">
        <img :src="tabbarImage(props.item, props.selected)">
        <span>{{ tabbarName(props.item, props.selected) }}</span>
    </div>
</template>
<script setup>
import { defineProps, defineEmits, computed, ref } from 'vue'
import { TabBarItem, tabbarName, tabbarImage } from '@/components/TabBar/tabbar_enums'
import { UIFrames } from '@/classroom/frame_enums'
import { RBPColors } from '@/components/baseComponents/RBPColors'

const props = defineProps({
    item: {
        type: Number,
        require: true
    },
    active: {
        type: Boolean,
        default: false
    },
    selected: {
        type: Boolean,
        default: false
    }
})

const itemHeight = ref(UIFrames.tabbarHeight - 18)

const emits = defineEmits(['itemClick', 'canvasOptionEraserDoubleClick'])

function itemClick() {
    emits('itemClick', props.item)
}
function handleDoubleClick() {
    if(props.item == TabBarItem.canvasOptionEraser) {
        emits('canvasOptionEraserDoubleClick')
    }
}
</script>

<style lang="scss" scoped>
.item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border-radius: 10px;
    width: 64px;
    height: 60px;
    padding: 4px 0;

    img {
        width: 40px;
        height: 40px;
    }

    span {
        color: var(--explanatory-text-color);
        font-size: 15px;
    }
}

.active {
    // background-color: var(--main-bc-color);
    background-color: var(--toolbar-selected-color);
    span{
        color: var(--primary-color);
    }
}
</style>