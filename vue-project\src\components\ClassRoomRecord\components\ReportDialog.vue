<template>
  <div class="classroom-record-test-report-dialog">
    <RBPAlert title="作答分析" width="1665px" height="921px"  @close="emits('close')">
      <template v-slot:rbpDiv>
        <div class="content">
          <div class="report-header">
            <div class="header-icon">
              <img :src="getStatus().url" />
            </div>
            <div class="header-text" :style="{ color: getStatus().color }">
              <div>{{ getStatus().content }}</div>
            </div>
          </div>

          <div class="stu-condition">
            <!-- 知识点掌握情况图表 -->
            <div class="knowledge-chart">
              <div class="top-content">
                <div class="title" style="margin-bottom: 0;">知识点情况</div>
                <div class="des">{{ `本次测试共考察知识点${analysisAccuracyList.length}个，平均正确率为${Math.round(averageAccuracy * 100)}%` }}</div>
              </div>
              <div class="chart-container">
                <ReportDialogChart :dataList="analysisAccuracyList"></ReportDialogChart>

              </div>
            </div>

            <!-- 完成作答前三和正确率前三 -->
            <div class="ranking-section" v-if="nowStatus != 1">
              <div class="ranking-box">
                <div class="title">正确率前三</div>
                <div class="ranking-list">
                  <div v-for="(student, index) in topThreeList" :key="index" class="ranking-item">
                    <img class="student-avatar" :src="getNumberImg(index + 1)" />
                    <div>{{ student.deviceAlias }}</div>
                  </div>
                </div>
              </div>
              <div style="flex: 1;">
                <!-- <div class="title">完成作答前三</div>
                <div class="ranking-list">
                  <div v-for="(student, index) in completionTop3" :key="index" class="ranking-item">
                    <img class="student-avatar" :src="getNumberImg(index + 1)" />
                    <div>{{ student.name }}</div>
                  </div>
                </div> -->
              </div>
              
            </div>

            <!-- 重点关注 -->
            <div class="focus-section" v-if="nowStatus != 1">
              <div class="title">重点关注</div>
              <div class="focus-list">
                <div v-for="(student, index) in interestStudents" :key="index" class="focus-item">
                  <span class="student-label">{{ student.deviceAlias }}:</span>
                  <div class="knowledge-points">
                    <span  class="knowledge-point">
                      {{ student.knowledgePoints.join("、") }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </RBPAlert>
  </div>
</template>

<script setup>
import RBPAlert from '@/components/baseComponents/RBPAlert.vue';
import { onMounted, ref } from 'vue';
import needImprove from '../assets/need_improve.png'
import continueHard from '../assets/continue_hard.png'
import gradePerfect from '../assets/grade_perfect.png'
import ReportDialogChart from './ReportDialogChart.vue';
import { ClassroomRecordRequest } from '@/server_request/classroom_record';
import { ElMessage } from 'element-plus';
const emits = defineEmits('close')
const nowStatus = ref(1) // 1 完美 2 有待提高 3 继续努力
const props = defineProps({
    taskId:{
        default:''
    },
    interactId:{
        default:''
    }
})
function getStatus(status) {
  let thisStatus = status
  if (!thisStatus) {
    thisStatus = nowStatus.value
  }
  if (thisStatus == 1) {
    return {
      "url": gradePerfect,
      "content": "太棒了！\n所有人都是满分！",
      "color": 'var(--correct-color)'
    }
  } else if (thisStatus == 2) {
    return {
      "url": needImprove,
      "content": "部分知识点\n掌握情况有待提高,\n需继续努力。",
      "color": 'var(--primary-color)'
    }
  } else {
    return {
      "url": continueHard,
      "content": "知识点掌握\n情况不理想！需要更细致的讲解",
      "color": 'var(--error-color)'
    }
  }
}
function getNumberImg(index) {
  if (index == 1) {
    return 'icon/icon_gold.svg'
  } else if (index == 2) {
    return 'icon/icon_silver.svg'
  } else {
    return 'icon/icon_copper.svg'
  }
}

const averageAccuracy = ref(0)
const analysisAccuracyList = ref([])
const interestStudents = ref([])
const topThreeList = ref([])

onMounted(()=>{
  getPaperReport()
})

async function getPaperReport() {
    try {
        let params = {
            interactId: props.interactId,
            taskId: props.taskId,
        }
        
        let res = await ClassroomRecordRequest.getPaperAnalysis(params)
        if(res&&res.data){
          averageAccuracy.value = res.data.averageAccuracy
          analysisAccuracyList.value = res.data.analysisAccuracyList
          interestStudents.value = res.data.interestStudents
          topThreeList.value = res.data.topThreeList
          let avarage = Math.round(averageAccuracy.value  * 100)
          if(avarage < 50){
            nowStatus.value = 3
          }else if(avarage<100){
            nowStatus.value = 2
          }
        }
    } catch (e) {
        ElMessage.error('网络异常')
    }
}
</script>

<style lang="scss" scoped>
.classroom-record-test-report-dialog {
  width: 100vw;
  height: 100vh;
  position: absolute;
  top: 0;
  z-index: 200;

  .content {
    background: #FFFFFF;
    display: flex;
    border-radius: 0 0 26px 26px;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .report-header {
    display: flex;
    align-items: center;
    flex-direction: column;
    width: 415px;
    height: 100%;
    justify-content: end;
    background: linear-gradient(139deg, #FFEEDB4D 0%, #BFFAF34D 100%);

    .header-icon {
      width: 280px;
      height: 280px;
      margin-bottom: 67px;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .header-text {
      margin-bottom: 190px;
      text-align: center;
      width: 353px;
      height: 238px;
      font-weight: 500;
      font-size: 36px;
      color: #25B0FA;
      line-height: 69px;
      text-align: center;
      white-space: pre-line;
    }
  }

  .stu-condition {
    width: calc(100% - 415px);
    box-sizing: border-box;
    padding: 24px 36px 50px 24px;
    height: 100%;
    overflow-y: scroll;

    .title {
      font-size: 24px;
      color: var(--text-color);
      font-weight: 500;
      margin-bottom: 16px;
    }

    .knowledge-chart {
      margin-left: 64px;

      .top-content {
        display: flex;
        align-items: center;
        justify-content: space-between;

        padding-right: 143px;

        .des {
          color: var(--secondary-text-color);
          font-size: 18px;
        }
      }


      .chart-container {
        height: 228px;
        margin-bottom: 42px;

      }
    }

    .ranking-section {
      display: flex;
      gap: 24px;
      margin-bottom: 18px;
      height: 131px;

      .ranking-box {
        flex: 1;
        border-radius: 16px;
        padding: 0px 32px;
        border: 1px solid var(--border-bar-color);
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;


        .ranking-list {
          display: flex;
          align-items: center;

          .ranking-item {
            display: flex;
            align-items: center;
            margin-right: 69px;

            &:last-child {
              margin-right: 0;
            }

            .medal {

              display: flex;
              align-items: center;
              justify-content: center;

              font-weight: 400;
              font-size: 21px;
              color: var(--text-color);
              line-height: 31px;


            }

            .student-avatar {
              width: 25px;
              margin-right: 18px;
            }



          }
        }
      }
    }

    .focus-section {
      border-radius: 16px;
      border: 1px solid var(--border-bar-color);
      height: 315px;
      overflow-y: scroll;
      padding: 0px 32px;
      display: flex;
      flex-direction: column;

      .title {
        margin-top: 12px;
      }

      .focus-list {
        flex: 1;
        overflow-y: scroll;

        .focus-item {
          display: flex;
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .student-label {
            width: 80px;
            font-size: 21px;
            color: var(--text-color);
            font-weight: 500;
          }

          .knowledge-points {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            .knowledge-point {
              font-size: 21px;
              font-weight: 400;
              color: var(--secondary-text-color);
            }
          }
        }
      }
    }
  }
}
</style>



