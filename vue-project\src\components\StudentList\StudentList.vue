<template>
    <div class="student-list" @click.stop>
        <RBPAlert title="学生列表" :showMini="false" @close="studentListBgClick" @click.stop>
            <template v-slot:rbpDiv>
                <div class="content-div">
                    <div class="students">
                        <StudentListSeat v-show="currentSelectedTitle == StudentListType.seat"
                            v-if="selectedClassroom.hasLocation"></StudentListSeat>
                        <StudentListGroup v-show="currentSelectedTitle == StudentListType.group"
                            v-if="selectedClassroom.hasGroup"></StudentListGroup>
                        <StudentListName v-show="currentSelectedTitle == StudentListType.name"></StudentListName>
                    </div>
                    <div class="score" v-if="!loginInstance.evaluation">
                        <div class="scorebtns">
                            <div class="score-btn" v-for="(score, index) in scoreList" :key="index">
                                <RBPAddScore :score="score" @click="addScoreClick(score)"></RBPAddScore>
                                <div :style="{ width: '30px' }"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template #rbpBtns>
                <div class="btns">
                    <div class="btns-left">
                        <RBPButton btnText="取消选择" @click="cancelAllClick" v-if="showCancelAll()"></RBPButton>
                        <div :style="{ width: '129px' }"></div>
                        <RBPButton btnText="展示" @click="showBtnClick" v-if="showBtn()"></RBPButton>
                    </div>
                    <div class="btns-center">
                        <RBPButton :btnText="StudentListType.seat"
                            :btnSelected="currentSelectedTitle == StudentListType.seat"
                            @click="handleClick(StudentListType.seat)" v-if="selectedClassroom.hasLocation"></RBPButton>
                        <div :style="{ width: '12px' }"></div>
                        <RBPButton :btnText="StudentListType.name"
                            :btnSelected="currentSelectedTitle == StudentListType.name"
                            @click="handleClick(StudentListType.name)"></RBPButton>
                        <div :style="{ width: '12px' }"></div>
                        <RBPButton :btnText="StudentListType.group"
                            :btnSelected="currentSelectedTitle == StudentListType.group"
                            @click="handleClick(StudentListType.group)" v-if="selectedClassroom.hasGroup"></RBPButton>
                        
                    </div>
                    <div class="btns-right">
                        <RBPButton btnText="加分" @click="studentCategoryScore" v-if="loginInstance.evaluation">
                        </RBPButton>
                        <div v-else class="btn-disabled">
                            <RBPButton btnText="单独加分" :btnSelected="alone == true" @click="aloneClick"></RBPButton>
                            <div :style="{ width: '12px' }"></div>
                            <div class="same">
                                <RBPButton btnText="同时加分" :btnSelected="alone == false" @click="sameClick"></RBPButton>
                                <RBPDoubt></RBPDoubt>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </RBPAlert>
        <StudentInfo class="block" from="classroom"></StudentInfo>
    </div>
</template>
<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useClassroomStore } from '@/stores/classroom'
import { useClassroomUIStore } from '@/stores/classroom_ui_store'
import StudentListSeat from '@/components/StudentList/StudentListSeat.vue'
import StudentListGroup from '@/components/StudentList/StudentListGroup.vue'
import StudentListName from '@/components/StudentList/StudentListName.vue'
import { storeToRefs } from 'pinia'
import { isWriteMode } from '@/classroom/interact_enums'
import { useInteractStore } from '@/stores/interact_store'
import StudentInfo from '@/components/StudentInfo/StudentInfo.vue'
import { Alert } from '@/utils/alert'
import { useDrawBoardStore } from '@/stores/drawboard_store'
import roomUpdater from '@/classroom/classroom_updater.js'
import { StuStatus } from '@/classroom/interact_enums.js'
import { useStudentInfoStore } from '@/stores/student_info_store'
import { useStudentListStore, StudentListType } from '@/stores/student_list_store'
import { useEvaluationScoreStore } from '@/stores/evaluation_score_store'
import { loginInstance } from '@/login_instance/login_instance'
import { dbHelper } from '@/utils/db_helper'
import { useScoreAudioStore } from '@/stores/score_audio_store'
import { useProbabilityStore } from '@/stores/probability_store'
import RBPButton from '@/components/baseComponents/RBPButton.vue'
import RBPDoubt from '@/components/baseComponents/RBPDoubt.vue'
import RBPAddScore from '@/components/baseComponents/RBPAddScore.vue'
import RBPAlert from '@/components/baseComponents/RBPAlert.vue'
import { remoteControl } from '@/remote_control/remote_control'

const scoreAudioStore = useScoreAudioStore()
const evaluationScoreStore = useEvaluationScoreStore()
const { alone } = storeToRefs(evaluationScoreStore)
const studentListStore = useStudentListStore()
const { currentSelectedTitle,remoteFlag } = storeToRefs(studentListStore)
const studentInfoStore = useStudentInfoStore()
const { deviceList, stuInfo } = storeToRefs(studentInfoStore)
const showAddScoreView = ref(false)
const drawBoardStore = useDrawBoardStore()
const { paperPenPainter, cancelRandom } = storeToRefs(drawBoardStore)
const interactStore = useInteractStore()
const { interact } = storeToRefs(interactStore)
const classroomStore = useClassroomStore()
const { selectedClassroom } = storeToRefs(classroomStore)
const classroomUIStore = useClassroomUIStore()
const { showStudentList, mainContentTopSpace, showStudentCategoryScore } = storeToRefs(classroomUIStore)
const calcTop = computed(() => {
    return `calc(50% + ${mainContentTopSpace.value}px)`
})
const scoreList = ref([
    '-2', '-1', '+1', '+2', '+3', '+5'
])
onMounted(async () => {
    evaluationScoreStore.queryDb()

    if (selectedClassroom.value.hasLocation) {
        currentSelectedTitle.value = StudentListType.seat
    } else {
        if (selectedClassroom.value.hasGroup) {
            currentSelectedTitle.value = StudentListType.group
        } else {
            currentSelectedTitle.value = StudentListType.name
        }
    }
    setTimeout(() => {
        remoteControl.handleInteractState()
    }, 500)
})
function studentCategoryScore() {
    showStudentCategoryScore.value = true
    evaluationScoreStore.getEvaluationItemItemList()
    remoteControl.handleInteractState()
}
function getOnlineStudents() {
    return selectedClassroom.value.studentList.filter(stu => stu.stuStatus == StuStatus.online).length
}
function studentListBgClick() {    
    stuInfo.value = null
    showStudentList.value = false
    selectedClassroom.value.studentList.forEach(student => {
        student.selected = false
    })
    remoteControl.handleInteractState()
}
function handleClick(title) {
    currentSelectedTitle.value = title
    remoteControl.handleInteractState()
}
function showProbabilityBtn() {
    if (isWriteMode(interact.value)) {
        if (interactStore.isProbability == true) {
            return true
        } else {
            return false
        }
    }
    return false
}
function showBtn() {
    if (isWriteMode(interact.value)) {
        if (interactStore.examMode == true) {
            return false
        } else {
            return true
        }
    }
    return false
    // return isWriteMode(interact.value)
}
function showCancelAll() {
    return true
    // return selectedClassroom.value.studentList.some(student => student.selected)
}
function cancelAllClick() {
    selectedClassroom.value.studentList.forEach(student => {
        student.selected = false
    })
    paperPenPainter.value?.setCompare(false, [])
    selectedClassroom.value.groupStudentArray.forEach(item => {
        item.selected = false
    })
    remoteControl.handleInteractState()
}
function showAddScore() {
    //小于7 就是小学 小于等于7是初中高中
    // return selectedClassroom.value.gid < 7 ? true : false
    return false
}
async function addScoreClick(score) {
    let success = await roomUpdater.studentsAddScoreTypeOne(score)
    if (success) {
        // 播放音频 播放动画
        scoreAudioStore.play(score)
    }
    remoteControl.handleInteractState()
}

function showBtnClick() {
    let stuList = []
    selectedClassroom.value.studentList.forEach(stu => {
        if (stu.selected) {
            stuList.push(stu)
        }
    })
    if (stuList.length > 0) {
        cancelRandom.value = true
        studentListBgClick()
        paperPenPainter.value.setCompare(true, stuList)
    } else {
        Alert.showErrorMessage('请选择学生')
        return
    }
}
function showProbabilityBtnClick() {
    let stuList = []
    selectedClassroom.value.studentList.forEach(stu => {
        if (stu.selected) {
            stuList.push(stu)
        }
    })
    if (stuList.length > 0) {
        cancelRandom.value = true
        let probabilityStore = useProbabilityStore()
        probabilityStore.showMergedChartWithStudents(stuList)
        studentListBgClick()
        paperPenPainter.value.setCompare(true, stuList)
    } else {
        Alert.showErrorMessage('请选择学生')
        return
    }
}
function showAddScoreClick() {
    showAddScoreView.value = true
}
async function aloneClick() {
    alone.value = true
    dbHelper.updateAddScoreTypeByTeacherId('1')
    remoteControl.handleInteractState()
}

function sameClick() {
    alone.value = false
    dbHelper.updateAddScoreTypeByTeacherId('2')
    remoteControl.handleInteractState()
}
watch(remoteFlag, (newVal) => { 
    addScoreClick(studentListStore.nowScore)
})

</script>
<style lang="scss" scoped>
.student-list {
    position: absolute;
    height: 100%;
    width: 100%;
    // z-index: var(--toolbar-top-z-index);
    z-index: var(--toolbar-top-student-list-z-index);

    .content {
        position: absolute;
        top: v-bind(calcTop);
        left: 50%;
        transform: translate(-50%, -50%);
        // background-color: v-bind("RBPColors.colorI");
        width: 90%;
        height: 80%;
        border-radius: 26px;
        display: flex;
        flex-direction: column;
    }

    .block {
        position: absolute;
        width: 40%;
        min-width: 250px;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        padding: 30px 0;
        z-index: 999;
    }
}

.content-div {
    height: 100%;
    width: 100%;
    flex: 1;
    border-radius: 26px;
    display: flex;
    flex-direction: column;
    padding: 10px 10px;
    box-sizing: border-box;

    .students {
        flex: 1;
        margin-bottom: 20px;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: start;
        overflow-y: scroll;
        scrollbar-width: none;

        &::-webkit-scrollbar {
            display: none;
            /* 适用于 Chrome, Safari 和 Opera */
        }
    }

    .score {
        height: 80px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;

        .scorebtns {
            display: flex;

            .score-btn {
                display: flex;
            }
        }
    }
}

.btns {
    width: 100%;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    margin-bottom: 20px;

    .btns-left {
        position: absolute;
        // right: calc(50% + 365px);
        left: 27px;
        display: flex;
    }

    .btns-center {
        display: flex;
    }

    .btns-right {
        position: absolute;
        //left: calc(50% + 365px);
        right: 100px;
        display: flex;

        .btn-disabled {
            display: flex;

            .same {
                position: relative;

                .doubt {
                    position: absolute;
                    top: -15px;
                    right: -12px;
                }
            }
        }
    }
}
</style>